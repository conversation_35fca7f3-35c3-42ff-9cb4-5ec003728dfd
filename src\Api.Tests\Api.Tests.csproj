﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<IsPackable>false</IsPackable>
		<AssemblyName>Geidea.BackofficePortal.Backoffice.Api.Tests</AssemblyName>
		<RootNamespace>Geidea.BackofficePortal.Backoffice.Api.Tests</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.msbuild" Version="3.2.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="NSubstitute" Version="4.4.0" />
		<PackageReference Include="nunit" Version="3.13.3" />
		<PackageReference Include="NUnit3TestAdapter" Version="4.3.1">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BackofficeApi\BackofficeApi.csproj" />
	</ItemGroup>

</Project>
