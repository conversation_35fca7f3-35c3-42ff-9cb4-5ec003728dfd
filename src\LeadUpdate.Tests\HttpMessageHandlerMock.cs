﻿using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace LeadUpdate.Tests
{
    public class HttpMessageHandlerMock : HttpMessageHandler
    {
        private readonly HttpStatusCode statusCode;
        private readonly string response;

        public HttpMessageHandlerMock(HttpStatusCode statusCode, string response)
        {
            this.statusCode = statusCode;
            this.response = response;
        }

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            return Task.FromResult(new HttpResponseMessage
            {
                StatusCode = statusCode,
                Content = new StringContent(response)
            });
        }
    }
}