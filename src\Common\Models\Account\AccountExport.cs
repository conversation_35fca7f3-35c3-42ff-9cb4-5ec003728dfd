﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Account;

public class AccountExport
{
    public string? Mid { get; set; }
    public string? BusinessName { get; set; }
    public string? BusinessNameAr { get; set; }
    public string? BusinessId { get; set; }
    public string? ChannelType { get; set; }
    public string? MerchantStatus { get; set; }
    public string? AccountStatus { get; set; }
    public string? MerchantId { get; set; }
    public string? StoreId { get; set; }
    public string? CreatedDate { get; set; }
}