﻿using Common;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Security.KeyCloak;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class KeycloakClientTests
    {
        private const string Token = "efju58gdhku57hkkh78hjk";

        private HttpMessageHandlerMock messageHandler = null!;
        private ILogger<KeycloakClient> logger = Substitute.For<ILogger<KeycloakClient>>();
        private IKeycloakClient keycloakClient = null!;
        private IOptionsMonitor<GsdkSettings> gsdkSettingsOptionsMonitor = Substitute.For<IOptionsMonitor<GsdkSettings>>();
        private IOptionsMonitor<KeycloakOptions> keycloakOptionsMonitor = Substitute.For<IOptionsMonitor<KeycloakOptions>>();
        private GsdkSettings gsdkSettings = null!;
        private KeycloakOptions keycloakOptions = null!;

        [SetUp]
        public void Setup()
        {
            gsdkSettings = new GsdkSettings
            {
                Live = new GsdkEnvironmentSettings
                {
                    BaseUrl = "http://livefakemerchant.com",
                    EgyptBaseUrl = "http://liveegfakemerchant.com",
                    Username = "LiveUsername",
                    Password = "LivePassword"
                },
                Test = new GsdkEnvironmentSettings
                {
                    BaseUrl = "http://testfakemerchant.com",
                    EgyptBaseUrl = "http://testegfakemerchant.com",
                    Username = "TestUsername",
                    Password = "TestPassword"
                },
                IsTest = true,
                KeycloakUrl = "http://keycloak.test/"
            };

            keycloakOptions = new KeycloakOptions
            {
                Authority = "http://keycloak.com/"
            };

            gsdkSettingsOptionsMonitor.CurrentValue.Returns(gsdkSettings);
            keycloakOptionsMonitor.CurrentValue.Returns(keycloakOptions);
        }

        [TestCase(true)]
        [TestCase(false)]
        public async Task GetKeycloakToken_WhenKeycloakTokenIsReturned(bool isTest)
        {
            gsdkSettings.IsTest = isTest;
            CreateHttpClient(HttpStatusCode.OK, new { access_token = Token });

            var result = await keycloakClient.GetKeycloakToken();

            result.Should().Be("Bearer " + Token);
            messageHandler.RequestMessage.RequestUri?.AbsoluteUri.Should().Be(gsdkSettings.IsTest ? gsdkSettings.KeycloakUrl : keycloakOptions.TokenEndpoint);
        }

        [Test]
        public void GetKeycloak_WhenKeycloakUrlIsNull_ShouldThrowException()
        {
            gsdkSettings.IsTest = true;
            gsdkSettings.KeycloakUrl = string.Empty;
            CreateHttpClient(HttpStatusCode.OK, new { access_token = Token });

            keycloakClient.Invoking(x => x.GetKeycloakToken())
                .Should().ThrowAsync<ServiceException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.KeycloakTokenError.Code);
        }

        [Test]
        public void GetKeycloakToken_WhenKeyckoakReturnsInvalidResponse_ShouldThrowException()
        {
            CreateHttpClient(HttpStatusCode.OK, null);

            keycloakClient.Invoking(x => x.GetKeycloakToken())
             .Should().ThrowAsync<ServiceException>()
                     .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.KeycloakTokenError.Code);
        }

        [Test]
        public void GetKeycloakToken_WhenServiceReturnsBadRequest_ShouldThrowException()
        {
            CreateHttpClient(HttpStatusCode.BadRequest, null);

            keycloakClient.Invoking(x => x.GetKeycloakToken())
                .Should().ThrowAsync<ServiceException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.KeycloakTokenError.Code);
        }

        private void CreateHttpClient(HttpStatusCode statusCode, object? response)
        {
            messageHandler = new HttpMessageHandlerMock(statusCode, response == null ? string.Empty : JsonSerializer.Serialize(response));
            var httpClient = new HttpClient(messageHandler);
            keycloakClient = new KeycloakClient(
                httpClient,
                gsdkSettingsOptionsMonitor,
                keycloakOptionsMonitor,
                logger);
        }
    }
}
