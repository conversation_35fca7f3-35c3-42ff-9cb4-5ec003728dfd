﻿using Geidea.Utils.Messaging;
using Microsoft.AspNetCore.Builder;
using Services.Messaging;

namespace BackofficeApi.Extensions
{
    public static class ApplicationBuilderExtensions
    {
        public static IApplicationBuilder StartMessageConsumption(this IApplicationBuilder builder)
        {
            builder.StartMessageConsumption<EPosMessagingClient>(false);
            builder.StartMessageConsumption<ActiveCampaignMessagingClient>(false);
            builder.StartMessageConsumption<MMSOrderUpdateMessagingClient>(false);
            builder.StartMessageConsumption<GleComposePayloadMessagingClient>(false);
            builder.StartMessageConsumption<EPosMessagingSoftwareTypeClient>(false);

            return builder;
        }
    }
}
