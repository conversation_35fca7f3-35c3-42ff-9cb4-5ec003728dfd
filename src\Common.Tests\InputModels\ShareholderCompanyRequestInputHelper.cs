﻿using Microsoft.AspNetCore.JsonPatch;

namespace Common.Tests.InputModels;

public static class ShareholderCompanyRequestInputHelper
{
    public static JsonPatchDocument<MerchantShareholderCompanyResponse> GetSaudiValidJsonPatchRequest()
    {
        var jsonPatchRequest = new JsonPatchDocument<MerchantShareholderCompanyResponse>();
        jsonPatchRequest.Replace(x => x.OwnershipPercentage, 12);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyName, "CompanyName");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyType, Constants.BusinessType.LegalEnterprise);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyLicense, "CompanyLicense");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.LicenseExpiryDate, DateTime.Now);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.IssueDate, DateTime.Now);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.MccCode, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.PhoneNumber, "*********");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.PhonePrefix, "+966");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Country, "CO");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.City, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Area, "Area");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Address, "Address");

        return jsonPatchRequest;
    }

    public static JsonPatchDocument<MerchantShareholderCompanyResponse> GetEgyptValidJsonPatchRequest()
    {
        var jsonPatchRequest = new JsonPatchDocument<MerchantShareholderCompanyResponse>();
        jsonPatchRequest.Replace(x => x.OwnershipPercentage, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyName, "CompanyName");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyType, Constants.BusinessType.Other);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyLicense, "CompanyLicense");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.LicenseExpiryDate, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.IssueDate, DateTime.Now);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.MccCode, "MccCode");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.PhoneNumber, "*********");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.PhonePrefix, "+966");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Country, "CO");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Governorate, "Governorate");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.City, "City");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Address, "Address");

        return jsonPatchRequest;
    }

    public static JsonPatchDocument<MerchantShareholderCompanyResponse> GetOtherCounterpartyValidJsonPatchRequest()
    {
        var jsonPatchRequest = new JsonPatchDocument<MerchantShareholderCompanyResponse>();
        jsonPatchRequest.Replace(x => x.OwnershipPercentage, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyName, "CompanyName");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyType, Constants.BusinessType.LegalEnterprise);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.CompanyLicense, "CompanyLicense");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.LicenseExpiryDate, DateTime.Now);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.IssueDate, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.MccCode, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.PhoneNumber, null);
        jsonPatchRequest.Replace(x => x.ShareholderCompany.PhonePrefix, "+966");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Country, "CO");
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Address, "Address");

        return jsonPatchRequest;
    }

    public static JsonPatchDocument<MerchantShareholderCompanyResponse> GetValidJsonPatchRequestByCounterparty(string counterparty)
    {
        if (counterparty == Constants.CounterParty.Egypt)
            return GetEgyptValidJsonPatchRequest();

        if (counterparty == Constants.CounterParty.Saudi)
            return GetSaudiValidJsonPatchRequest();

        return GetOtherCounterpartyValidJsonPatchRequest();
    }

    public static JsonPatchDocument<MerchantShareholderCompanyResponse> GetInvalidCountryJsonPatchRequest()
    {
        var jsonPatchRequest = new JsonPatchDocument<MerchantShareholderCompanyResponse>();
        jsonPatchRequest.Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Country, "zz");
        return jsonPatchRequest;
    }
}
