﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<NoWarn>$(NoWarn);1591</NoWarn>
		<ProjectGuid>{6d40e246-0a5c-4dd3-a2e0-3aa93a9199c2}</ProjectGuid>
		<DocumentationFile>BackofficeApi.xml</DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.0" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
		<PackageReference Include="CsvHelper" Version="30.0.1" />
		<PackageReference Include="FluentValidation" Version="11.4.0" />
		<PackageReference Include="Geidea.PaymentGateway.ConfigServiceClient" Version="3.1.123" />
		<PackageReference Include="Geidea.Utils.HeaderValidation" Version="2.0.222" />
		<PackageReference Include="Geidea.Utils.HealthChecks" Version="1.1.342" />
		<PackageReference Include="Geidea.Utils.Logging" Version="1.1.279" />
		<PackageReference Include="Geidea.Utils.Policies" Version="2.0.366" />
		<PackageReference Include="Geidea.Utils.Security" Version="2.0.137" />
		<PackageReference Include="Geidea.Utils.UserInfo" Version="1.0.154" />
		<PackageReference Include="Geidea.Utils.Versioning" Version="1.1.247" />
		<PackageReference Include="GeideaPaymentGateway.Utils.CommonModels" Version="3.0.39" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="6.0.12" />
		<PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="6.0.12" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.12" />
		<PackageReference Include="Microsoft.AspNetCore.OData" Version="8.0.11" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.12">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Configuration" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="6.0.0" />
		<PackageReference Include="Microsoft.FeatureManagement" Version="2.5.1" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.11" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
		<PackageReference Include="Serilog.Enrichers.Process" Version="2.0.2" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
		<PackageReference Include="Serilog.Formatting.Compact" Version="1.1.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.Json" Version="6.0.7" />
		<PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.19.0" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Templates\ReportTemplate.html">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="Templates\TableHeaderTemplate.html">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Include="Templates\TableRowTemplate.html">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</EmbeddedResource>
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
		<ProjectReference Include="..\Services\Services.csproj" />
	</ItemGroup>

</Project>
