﻿using System;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Services.Messaging
{
    public class EPosMessagingSoftwareTypeClient : MessageClient
    {
        public EPosMessagingSoftwareTypeClient(
            IHttpContextAccessor contextAccessor,
            ILogger<MessageClient> logger,
            IOptionsMonitor<RabbitMqOptions> rabbitMqOptions
        )
            : base(contextAccessor, logger, rabbitMqOptions, new QueueSettings()
            {
                ExchangeName = "MMS.Order.Exchange",
                QueueName = "MMS.Order.EposUpdateTerminal",
                MessageSender = "ProductAPI",
                RoutingKey = "MMS.Order.EposUpdateTerminal.SoftwareType",
                Durable = true
            })
        {
            //
        }

        public virtual void SendMessageToEPos<T>(T payload) where T : class, new()
        {
            try
            {
                logger.LogInformation("Sending ePos Software Type update message.");
                base.SendMessage(payload);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Exception occurred while trying to send Software Type update message to EPos Queue - {@payload}", payload);
                throw;
            }
        }
    }
}
