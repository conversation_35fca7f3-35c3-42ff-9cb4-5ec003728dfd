﻿using Common.Models.Checkout;
using CsvHelper.Configuration;

namespace BackofficeApi.Formatters.ClassMappers
{
    public class EgyptProductExportResponseMap : ClassMap<EgyptProductExportResponse>
    {
        public EgyptProductExportResponseMap()
        {
            Map(m => m.TID).Name("TID");
            Map(m => m.MID).Name("MID");
            Map(m => m.ProductCode).Name("Product Name");
        }
    }
}
