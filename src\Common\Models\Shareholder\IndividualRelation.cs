﻿using System;

namespace Common.Models.Shareholder;

public class IndividualRelation
{
    public string Type { get; set; } = string.Empty;
    public Guid? ShareholderCompanyId { get; set; }
    public Guid? MerchantId { get; set; }
    public bool? IsPrincipal { get; set; }
    public decimal? OwnershipPercentage { get; set; }
    public int? WathqRelationId { get; set; }
    public string? OrganizationRole { get; set; }
    public string? ShareholderCompanyName { get; set; } = null!;
}