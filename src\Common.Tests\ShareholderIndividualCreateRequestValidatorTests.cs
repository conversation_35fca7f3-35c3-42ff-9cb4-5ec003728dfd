using Common.Tests.InputModels;
using FluentValidation;
using FluentValidation.TestHelper;

namespace Common.Tests;

public class ShareholderIndividualCreateRequestValidatorTests
{
    [TestCase("")]
    [TestCase(" ")]
    [TestCase("This is a very long first name and should really not be allowed as it is well over 64 ch.")]
    public void ShareholderIndividualCreateRequestValidator_InvalidFirstName_ShouldFail(string firstName)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(new ShareholderIndividualCreateRequest
        {
            FirstName = firstName
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualCreateRequest.FirstName))
            .WithErrorMessage(Errors.ShareholderIndividualFirstNameValidation.Message)
            .WithErrorCode(Errors.ShareholderIndividualFirstNameValidation.Code);
    }

    [TestCase("")]
    [TestCase(" ")]
    [TestCase("This is a very long last name and should really not be allowed as it is well over 64 ch.")]
    public void ShareholderIndividualCreateRequestValidator_InvalidLastName_ShouldFail(string lastname)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(new ShareholderIndividualCreateRequest
        {
            LastName = lastname
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualCreateRequest.LastName))
            .WithErrorMessage(Errors.ShareholderIndividualLastNameValidation.Message)
            .WithErrorCode(Errors.ShareholderIndividualLastNameValidation.Code);
    }

    [Test, TestCaseSource(typeof(ShareholderIndividualCreateRequestInput), nameof(ShareholderIndividualCreateRequestInput.InvalidDOB))]
    public void ShareholderIndividualCreateRequestValidator_InvalidDob_ShouldFail(ShareholderIndividualCreateRequest input)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(input);

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualCreateRequest.DOB))
            .WithErrorMessage(Errors.ShareholderIndividualDobValidation.Message)
            .WithErrorCode(Errors.ShareholderIndividualDobValidation.Code);
    }

    [TestCase("  ")]
    [TestCase("1234 1234 1234 1234")]
    public void ShareholderIndividualCreateRequestValidator_InvalidPhoneNumberLength_ShouldFail(string phoneNumber)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(new ShareholderIndividualCreateRequest
        {
            PhoneNumber = phoneNumber
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualCreateRequest.PhoneNumber))
            .WithErrorMessage(Errors.PhoneLengthValidation.Message)
            .WithErrorCode(Errors.PhoneLengthValidation.Code);
    }

    [TestCase("abcabcbac")]
    [TestCase("12#5")]
    [TestCase("12345628635")]
    public void ShareholderIndividualCreateRequestValidator_InvalidPhoneNumberFormat_ShouldFail(string phoneNumber)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(new ShareholderIndividualCreateRequest
        {
            PhoneNumber = phoneNumber
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualCreateRequest.PhoneNumber))
            .WithErrorMessage(Errors.PhoneValidation.Message)
            .WithErrorCode(Errors.PhoneValidation.Code);
    }

    [TestCase("no +")]
    [TestCase("+125367823583245")]
    public void ShareholderIndividualCreateRequestValidator_InvalidPhonePrefix_ShouldFail(string phonePrefix)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(new ShareholderIndividualCreateRequest
        {
            PhonePrefix = phonePrefix
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualCreateRequest.PhonePrefix))
            .WithErrorMessage(Errors.CountryPrefixLengthValidation.Message)
            .WithErrorCode(Errors.CountryPrefixLengthValidation.Code);
    }

    [TestCase("  ")]
    [TestCase("This is a very long last name and should really not be allowed as it is well over 64 ch.")]
    public void ShareholderIndividualCreateRequestValidator_InvalidNationality_ShouldFail(string nationality)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(new ShareholderIndividualCreateRequest
        {
            Nationality = nationality
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualCreateRequest.Nationality))
            .WithErrorMessage(Errors.NationalityLengthValidation.Message)
            .WithErrorCode(Errors.NationalityLengthValidation.Code);
    }

    [Test, TestCaseSource(typeof(ShareholderIndividualCreateRequestInput), nameof(ShareholderIndividualCreateRequestInput.InvalidAddress))]
    public void ShareholderIndividualCreateRequestValidator_InvalidAddresss_ShouldFail(ShareholderIndividualCreateRequest input, string propertyName,
        (string message, string code) expectedError)
    {
        var result = new ShareholderIndividualCreateRequestValidator().TestValidate(input);

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(propertyName)
            .WithErrorMessage(expectedError.message)
            .WithErrorCode(expectedError.code);
    }
}