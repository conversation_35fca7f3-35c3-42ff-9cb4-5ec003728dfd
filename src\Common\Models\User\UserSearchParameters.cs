﻿using System;
using System.Collections.Generic;

namespace Common.Models.User
{
    public class UserSearchParameters
    {
        public Guid? LeadId { get; set; }

        public Guid? MerchantId { get; set; }

        public Guid? UserId { get; set; }

        public string? Email { get; set; }

        public string? PhoneNumber { get; set; }

        public string? CountryPrefix { get; set; }

        public string? SalesId { get; set; }

        public List<string>? Groups { get; set; }
    }
}