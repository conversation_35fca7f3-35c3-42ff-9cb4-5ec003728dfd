﻿using System;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using LeadUpdate.Models;
using LeadUpdate.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using NUnit.Framework;

namespace LeadUpdate.Tests
{
    public class DocumentServiceTests
    {
        private readonly ILogger<DocumentService> logger;
        private readonly IOptions<UrlSettings> options;
        private DocumentService? documentService;
        private HttpClient? httpClient;

        public DocumentServiceTests()
        {
            logger = Substitute.For<ILogger<DocumentService>>();
            options = Options.Create(new UrlSettings
            {
                DocumentServiceBaseUrl = "http://documentService",
                SearchServiceBaseUrl = "http://searchService",
                DocumentServiceBaseUrlNS = "http://documentService",
                SearchServiceBaseUrlNS = "http://searchService"
            });
        }

        [Test]
        public async Task GetDocumentsAsync_ReturnsDocuments()
        {
            var documentsResponse = new[]
            {
                new DocumentWithContent
                {
                    Content = Encoding.UTF8.GetBytes("test content"),
                    LeadId = Guid.NewGuid(),
                    Id = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    PersonOfInterestId = Guid.NewGuid()
                },
                new DocumentWithContent
                {
                    Content = Encoding.UTF8.GetBytes("another test content"),
                    LeadId = Guid.NewGuid(),
                    Id = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    PersonOfInterestId = Guid.NewGuid()
                }
            };
            var messageHandler = new HttpMessageHandlerMock(HttpStatusCode.OK, JsonSerializer.Serialize(documentsResponse));
            httpClient = new HttpClient(messageHandler);
            documentService = new DocumentService(logger, options, httpClient);

            var result = await documentService.GetDocumentsAsync(new DocumentSearchRequest());
            result.Should().HaveCount(2);
            result.Should().BeEquivalentTo(documentsResponse);
        }

        [Test]
        public void GetIncompleteLeadsAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            httpClient = new HttpClient(new HttpMessageHandlerMock(HttpStatusCode.BadRequest, ""));
            documentService = new DocumentService(logger, options, httpClient);

            documentService.Invoking(x => x.GetDocumentsAsync(new DocumentSearchRequest())).Should().ThrowAsync<PassthroughException>();
        }
    }
}