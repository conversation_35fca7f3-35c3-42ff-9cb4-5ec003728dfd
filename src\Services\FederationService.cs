﻿using System;
using System.Net.Http;
using System.Threading.Tasks;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Services
{
    public class FederationService : IFederationService
    {
        private string GeideaFederationServiceBaseUrl => $"{urlSettingsOptions.GeideaFederationServiceBaseUrl}/api/v1";

        private readonly HttpClient client;
        private readonly UrlSettings urlSettingsOptions;
        private readonly ILogger<FederationService> logger;

        public FederationService(HttpClient client, IOptionsMonitor<UrlSettings> urlSettingsOptions, ILogger<FederationService> logger)
        {
            this.client = client;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.logger = logger;
        }

        public async Task<string> GetMerchantOrganizationIdByPhoneAsync(Guid merchantId, string phone)
        {
            string requestUrl = $"{GeideaFederationServiceBaseUrl}/gsdk/organizationId/phone?MerchantId={merchantId}&Phone={phone}";

            return await CallFederationService(merchantId, requestUrl);
        }

        public async Task<string> GetMerchantOrganizationIdByEmailAsync(Guid merchantId, string email)
        {
            string requestUrl = $"{GeideaFederationServiceBaseUrl}/gsdk/organizationId/email?MerchantId={merchantId}&Email={email}";

            return await CallFederationService(merchantId, requestUrl);
        }

        private async Task<string> CallFederationService(Guid merchantId, string requestUrl)
        {
            logger.LogInformation($"Calling federation service @url: {0}", requestUrl);

            var response = await client.GetAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling federation service for {merchantId}. Error was {StatusCode} {@responseBody}", merchantId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            return responseBody;
        }
    }
}