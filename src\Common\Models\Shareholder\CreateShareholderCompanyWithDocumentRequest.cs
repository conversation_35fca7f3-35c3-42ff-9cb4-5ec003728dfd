﻿using Microsoft.AspNetCore.Http;
using System.Collections.Generic;

namespace Common.Models.Shareholder
{
    public class CreateShareholderCompanyWithDocumentRequest : ShareholderCompanyCreateRequest
    {
        public List<IFormFile> FreelanceIds { get; set; } = new List<IFormFile>();
        public List<IFormFile> CommercialRegistrations { get; set; } = new List<IFormFile>();
        public List<IFormFile> LegalEnterpriseLicenses { get; set; } = new List<IFormFile>();
    }
}
