﻿using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Comment;
using Common.Models.Merchant;
using Common.Models.User;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection.Metadata;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using BackofficeApi;
using Common.Models.Product;
using Common.Models.Tasks;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.AspNetCore.JsonPatch.Operations;
using NSubstitute;
using System.Runtime.ConstrainedExecution;
using Common.Models.TerminalDataSet;
using Common.Models.ProductInstance;
using static Common.Constants;
using Common.Models.Checks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class MerchantServiceTests
    {
        private readonly Mock<ILogger<MerchantService>> logger = new Mock<ILogger<MerchantService>>();
        private MerchantService merchantService = null!;
        private readonly Mock<IUserService> userService = new Mock<IUserService>();
        private readonly Mock<ISubordinateMerchantService> subordinateMerchantService = new Mock<ISubordinateMerchantService>();
        private readonly Mock<IMerchantClient> merchantClient = new Mock<IMerchantClient>();
        private readonly Mock<ISearchService> searchService = new Mock<ISearchService>();
        private readonly Mock<ICheckoutService> checkoutService = new Mock<ICheckoutService>();
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
        private readonly Mock<IOptionsMonitor<ApplicationOptions>> appOptions = new Mock<IOptionsMonitor<ApplicationOptions>>();
        private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
        private readonly Mock<IActiveCampaignService> activeCampaignService = new Mock<IActiveCampaignService>();
        private readonly IMapper mapper;
        private static readonly Guid MerchantId = Guid.NewGuid();
        private static readonly Guid LeadId = Guid.NewGuid();
        private static readonly DateTime createdDate = DateTime.UtcNow;
        private IOptions<TmsIntegrationFeatureToggle> tmsIntegrationFeatureToggle = null!;
        private readonly Mock<IProductService> productService = new Mock<IProductService>();
        private readonly Mock<IMixPanelService> mixPanelService = new Mock<IMixPanelService>();
        private readonly Mock<IShareholderService> shareholderService = new Mock<IShareholderService>();
        private readonly Mock<IDueDiligenceService> dueDiligenceService = new Mock<IDueDiligenceService>();
        private readonly Mock<INotificationService> notificationService = new Mock<INotificationService>();
        private readonly IOptions<KsaTeamAndDeisgnationFilterToggle> ksaTeamAndDesignationFilterToggle;
        private readonly IOptions<UaeTeamAndDeisgnationFilterToggle> UaeTeamAndDesignationFilterToggle;

        public MerchantServiceTests()
        {
            var profile = new AutoMapping();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
            mapper = new Mapper(configuration);
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
            appOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.AppOptions);
            tmsIntegrationFeatureToggle = Substitute.For<IOptions<TmsIntegrationFeatureToggle>>();
            tmsIntegrationFeatureToggle.Value.Returns(new TmsIntegrationFeatureToggle() { EnableTmsIntegration = true });
            ksaTeamAndDesignationFilterToggle = Substitute.For<IOptions<KsaTeamAndDeisgnationFilterToggle>>();
            UaeTeamAndDesignationFilterToggle = Substitute.For<IOptions<UaeTeamAndDeisgnationFilterToggle>>();

        }
        private readonly MerchantSearchFilters merchantSearchFilters = new MerchantSearchFilters()
        {
            BusinessDomain = new List<string> { "BusinessDomain" },
            AcquiringLedgers = new List<string> { "AcquiringLedgers" },
            OutletType = "OutletType",
            MerchantStatus = new List<string> { "MerchantStatus" },
            MemberId = "MemberId",
            AnnualTurnover = 1,
            ReferralChannels = new List<string>() { "UNASSIGNED" },
            ProductCodes = new List<string>() { "TEST" },
            DateInterval = new DateInterval()
            {
                FromDate = DateTime.UtcNow,
                ToDate = DateTime.UtcNow
            }
        };

        private static readonly MerchantSearchResult merchantSearchResult = new MerchantSearchResult()
        {
            MerchantId = MerchantId,
            LeadId = LeadId,
            FirstName = "FirstName",
            LastName = "LastName",
            BusinessDomain = "BusinessDomain",
            Currency = "Currency",
            OutletType = "OutletType",
            MerchantStatus = "MerchantStatus",
            MemberId = "MemberId",
            AnnualTurnover = 1,
            CreatedDate = createdDate,
            ReferralChannel = "UNASSIGNED",
            BusinessName = "BusinessName",
            BusinessNameAr = "BusinessNameAr",
            Orders = new List<OrderResponse>()
            {
                new OrderResponse()
                {
                    OrderItem = new List<OrderItemResponse>()
                    {
                        new OrderItemResponse()
                        {
                            ProductCode = "TEST",
                            OrderItemCategories = new List<OrderItemCategory>()
                            {
                                new OrderItemCategory()
                                {
                                    CategoryCode = "TEST"
                                }
                            }
                        }
                    }
                }
            }
        };

        private static readonly MerchantApiResult merchantApiResult = new MerchantApiResult()
        {
            MerchantId = MerchantId,
            LeadId = LeadId,
            FirstName = "FirstName",
            LastName = "LastName",
            BusinessDomain = "BusinessDomain",
            Currency = "Currency",
            OutletType = "OutletType",
            MerchantStatus = "MerchantStatus",
            MemberId = "MemberId",
            AnnualTurnover = 1,
            CreatedDate = createdDate,
            ReferralChannel = "UNASSIGNED",
            BusinessName = "BusinessName",
            BusinessNameAr = "BusinessNameAr",
            Products = new List<MerchantProduct>()
            {
                new MerchantProduct()
                {
                    Quantity = 1,
                    Categories = new List<string>(){"TEST"},
                    Code = "TEST"
                }
            }
        };

        private readonly MerchantSearchResponse<MerchantSearchResult> merchantSearchResponse = new MerchantSearchResponse<MerchantSearchResult>()
        {
            Records = new List<MerchantSearchResult>()
            {
               merchantSearchResult
            },
            ReturnedRecordCount = 1,
            TotalRecordCount = 1
        };

        private readonly ContactDetails contactDetails = new ContactDetails()
        {
            MerchantContactId = Guid.NewGuid(),
            MerchantPersonOfInterestId = Guid.NewGuid(),
            Email = "Email",
            PhoneNumber = "123451",
            CountryPrefix = "+966",
            AlternativeCountryPrefix = "+20",
            AlternativePhoneNumber = "678165",
            Website = "Website",
            MerchantContactReason = "DEFAULT"
        };

        private readonly MerchantDeleteRequest merchantDeleteRequest = new MerchantDeleteRequest()
        {
            MerchantId = new Guid[] { Guid.NewGuid() }
        };

        private readonly MerchantDetails merchantDetails = new MerchantDetails()
        {
            MerchantDetailsId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            CityCr = "City",
            AddressCr = "Address",
            MerchantName = "Name"
        };

        private readonly IReadOnlyCollection<AdditionalMccUpdate> updateMerchantAdditionalMccsRequest = new List<AdditionalMccUpdate>()
        {
            new AdditionalMccUpdate
            {
                MCC = "1234"
            }
        };

        private readonly MerchantPersonOfInterest merchantPersonOfInterest = new MerchantPersonOfInterest()
        {
            MerchantPersonOfInterestId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            LeadId = Guid.NewGuid(),
            NationalId = "NationalId",
            InterestEntityType = "InterestEntityType",
            Nationality = "Nationality",
            Title = "Title",
            FirstName = "FirstName",
            LastName = "LastName",
            FirstNameAr = "FirstNameAr",
            LastNameAr = "LastNameAr",
            OrganizationRole = "OrganizationRole",
            OwnershipPercentage = 1,
            DeletedFlag = false,
            IsPrincipal = true,
            ValidFrom = DateTime.UtcNow,
            ValidTo = DateTime.UtcNow,
            DOB = DateTime.UtcNow,
            Gender = "Male"
        };

        private readonly MerchantExportResponse merchantExportResponse = new MerchantExportResponse()
        {
            Merchant = new MerchantExport()
            {
                MerchantId = Guid.NewGuid(),
                LeadId = Guid.NewGuid(),
                MerchantType = "MerchantStatus",
                CreatedBy = "CreatedBy",
                UpdatedBy = "UpdatedBy",
                MerchantStatus = "MerchantStatus",
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                MerchantDetails = new MerchantDetails()
                {
                    MerchantDetailsId = Guid.NewGuid(),
                    BusinessType = "BusinessType",
                    OutletType = "OutletType",
                    MCC = "MCC",
                    BusinessDomain = "BusinessDomain",
                    LegalName = "LegalName",
                    TradingName = "TradingName",
                    Nickname = "Nickname",
                    TradingCurrency = "TradingCurrency",
                    RegistrationNumber = "RegistrationNumber",
                    VatNumber = "VatNumber",
                    FoundationDate = DateTime.UtcNow,
                    MunicipalLicenseNumber = "MunicipalLicenseNumber",
                    VatAppliedFlag = true,
                    TaxExempt = true,
                    AnnualTurnover = 1,
                    AdditionalTradingInformation = "AdditionalTradingInformation",
                    MIDMerchantReference = "MIDMerchantReference",
                    Website = "Website",
                    Region = "Region",
                    DefaultLanguage = "DefaultLanguage",
                    UnifiedId = "UnifiedId",
                    CreatedBy = "CreatedBy",
                    UpdatedBy = "UpdatedBy",
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = DateTime.UtcNow
                },
                Addresses = new List<MerchantAddress>() { new MerchantAddress() {
                    MerchantId = Guid.NewGuid(),
                    AddressId = Guid.NewGuid(),
                    Country = "Country",
                    City = "City",
                    Street = "Street",
                    Zip = "Zip",
                    Email = "Email",
                    AddressType = "AddressType",
                    Url = "Url",
                    Purpose = "Purpose",
                    IsDefaultAddress = false,
                    CityKey = 1,
                    ValidFrom = DateTime.UtcNow,
                    ValidTo = DateTime.UtcNow
                } },
                Contacts = new List<MerchantContact>() { new MerchantContact() {
                    MerchantContactId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    Email = "Email",
                    CountryPrefix = "+966",
                    PhoneNumber = "643493",
                    AlternativePhoneNumber = "43565436",
                    AlternativeCountryPrefix = "+966",
                    Website = "Website",
                    ContactReason = "DEFAULT",
                    CreatedBy = "CreatedBy",
                    UpdatedBy = "UpdatedBy",
                    ValidFrom = DateTime.UtcNow,
                    ValidTo = DateTime.UtcNow,
                    TimeLength = 1
                } },
                ExternalIdentifiers = new List<MerchantExternalIdentifier>() { new MerchantExternalIdentifier() {
                    Id = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    IdentifierType = "IdentifierType",
                    IdentifierKey = "IdentifierKey",
                    IdentifierValue = "IdentifierValue",
                    ExternalSourceId = "ExternalSourceId"
                } },
                BankAccounts = new List<MerchantBankAccount>() { new MerchantBankAccount() {
                    MerchantBankAccountId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    CountryCode = "CountryCode",
                    AccountHolderName = "AccountHolderName",
                    City = "City",
                    IBAN = "IBAN",
                    Swift = "Swift",
                    AccountName = "AccountName",
                    DDReference = "DDReference",
                    ValidFrom = DateTime.UtcNow,
                    ValidTo = DateTime.UtcNow,
                } },
                PersonsOfInterest = new List<MerchantExportPersonOfInterest>() { new MerchantExportPersonOfInterest() {
                    MerchantPersonOfInterestId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    LeadId = Guid.NewGuid(),
                    NationalId = "NationalId",
                    InterestEntityType = "InterestEntityType",
                    Nationality = "Nationality",
                    Title = "Title",
                    FirstName = "FirstName",
                    LastName = "LastName",
                    OrganizationRole = "OrganizationRole",
                    OwnershipPercentage = 1,
                    DeletedFlag = false,
                    IsPrincipal = true,
                    ValidFrom = DateTime.UtcNow,
                    ValidTo = DateTime.UtcNow,
                } }
            }
        };

        private readonly List<MerchantStatusResponse> merchantStatusResponse = new List<MerchantStatusResponse>
        {
            new MerchantStatusResponse
            {
                MerchantId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                MerchantStatus = "REGISTERED",
                MerchantStatusStartDate = DateTime.Today,
                UpdatedBy = new Guid("5e72fe84-1e25-41a9-be0a-17453b2d0b68").ToString()
            },
            new MerchantStatusResponse
            {
                MerchantId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                MerchantStatus = "REGISTERED",
                MerchantStatusStartDate = DateTime.Today,
                UpdatedBy = Guid.NewGuid().ToString()
            }
        };

        private readonly MerchantShortExportResponse merchantShortExport = new MerchantShortExportResponse
        {
            AccountHolderName = "account holder name",
            City = "city",
            Email = "emai",
            IBAN = "iban",
            LeadId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            Phone = "phone",
            MerchantStatus = "BOARDING_COMPLETED"
        };

        private readonly MerchantCommentResponse merchantCommentResponse = new MerchantCommentResponse()
        {
            CommentId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            CommentText = "test"
        };

        private readonly Catalogue[] catalogueResponse = new Catalogue[]
        {
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Product,
                Key = "TEST",
                Value = "Test"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.MerchantStatus,
                Key = "MerchantStatus",
                Value = "MerchantStatus"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.BankCheckStatus,
                Key = "BANK_CHECK_PENDING",
                Value = "Bank Check Pending"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Cities,
                Key = "Test",
                Value = "Test"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Governorates,
                Key = "Test",
                Value = "Test"
            },

        };

        private readonly List<EgyptMerchantExport> egyptMerchantExport = new List<EgyptMerchantExport>()
        {
            new EgyptMerchantExport()
            {
                FirstName = "FirstName",
                LastName = "LastName",
                MerchantStatus = "MerchantStatus",
                MemberId = "MemberId",
                BusinessName = "BusinessName",
                BankCheckStatus = "BANK_CHECK_PENDING",
                BankCheckStatusDate = DateTime.Today,
                City = "Test",
                Governorate = "Test",
                Orders = new List<OrderResponse>()
                {
                    new OrderResponse()
                    {
                        OrderItem = new List<OrderItemResponse>()
                        {
                            new OrderItemResponse()
                            {
                                ProductCode = "TEST",
                                OrderItemCategories = new List<OrderItemCategory>()
                                {
                                    new OrderItemCategory()
                                    {
                                        CategoryCode = "TEST"
                                    }
                                }
                            },
                            new OrderItemResponse()
                            {
                                ProductCode = "TEST",
                                OrderItemCategories = new List<OrderItemCategory>()
                                {
                                    new OrderItemCategory()
                                    {
                                        CategoryCode = "TEST"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };

        private readonly List<EgyptMerchantExportResponse> egyptMerchantExportResponse = new List<EgyptMerchantExportResponse>()
        {
            new EgyptMerchantExportResponse()
            {
                FirstName = "FirstName",
                LastName = "LastName",
                MerchantStatus = "MerchantStatus",
                BusinessID = "MemberId",
                BusinessName = "BusinessName",
                Products = "Test (2)",
                City = "Test"

            }
        };

        private readonly MerchantExternalContractMapping externalContractMapping = new MerchantExternalContractMapping()
        {
            GsdkTmscId = Guid.NewGuid(),
            GsdkTmscName = "different name",
            LedgerKey = "ledger",
            MerchantId = Guid.NewGuid(),
            PaymentWay = "paymentway"
        };

        private readonly MerchantUpdateResponse merchantUpdateResponse = new MerchantUpdateResponse()
        {
            MerchantId = MerchantId,
            MerchantDetails = new MerchantDetails()
            {
                MerchantId = MerchantId
            },
            MerchantStatus = Constants.MerchantStatus.Verified,
            Tag = Constants.MerchantTag.Wholesaler
        };
        private MerchantService GetMerchantService(HttpClient httpClient)
        {
            return new MerchantService(
                logger.Object,
                urlSettingsOptions.Object,
                appOptions.Object,
                httpClient,
                userService.Object,
                checkoutService.Object,
                counterpartyProvider.Object,
                activeCampaignService.Object, mapper, referenceService.Object,
                merchantClient.Object,
                searchService.Object,
                subordinateMerchantService.Object,
                tmsIntegrationFeatureToggle,
                productService.Object,
                mixPanelService.Object,
                shareholderService.Object,
                dueDiligenceService.Object,
                notificationService.Object,
                ksaTeamAndDesignationFilterToggle,
                UaeTeamAndDesignationFilterToggle);
        }

        [Test]
        public async Task FindAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantSearchResponse)));

            var result = await merchantService.FindAsync(merchantSearchFilters, Guid.NewGuid());

            result.Records.Count.Should().Be(1);
            result.Records[0].Should().BeEquivalentTo(merchantApiResult);
        }

        [Test]
        public void FindAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.FindAsync(merchantSearchFilters, Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void GetContactDetailsAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            merchantService.Invoking(x => x.GetContactDetailsAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void GetContactDetailsAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.GetContactDetailsAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetMerchantContactsAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new ContactDetails[] { contactDetails })));

            var result = await merchantService.GetMerchantContactsAsync(Guid.NewGuid());

            result.Should().BeEquivalentTo(contactDetails);
        }

        [Test]
        public void GetMerchantContactsAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.GetMerchantContactsAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteMerchantAsync()
        {
            userService.Setup(x => x.RemoveRolesForMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);


            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));
            merchantService.Invoking(x => x.DeleteMerchantsAsync(merchantDeleteRequest)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteMerchantAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.DeleteMerchantsAsync(merchantDeleteRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void PatchMerchantAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            activeCampaignService.Invocations.Clear();

            merchantService.Invoking(x => x.PatchMerchantAsync(Guid.NewGuid(), new JsonPatchDocument<PatchMerchantRequest>()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
            activeCampaignService.Verify(x => x.SendActiveCampaignRequest(It.IsAny<CreateActiveCampaignRequest>()), Times.Never);
        }

        [Test]
        public void PatchMerchantAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            activeCampaignService.Invocations.Clear();

            merchantService.Invoking(x => x.PatchMerchantAsync(Guid.NewGuid(), new JsonPatchDocument<PatchMerchantRequest>()))
                .Should().NotThrowAsync<PassthroughException>();
        }

        [Test]
        [TestCase("tag", "wJHz29fufKuX8coQZjJfR6tJYFe1mGrfZVnhzSd0qvRDbC6z7bF", "BA_NewTagLengthValidation")]
        [TestCase("tag", "", "BA_NewTagLengthValidation")]
        [TestCase("merchantStatus", "63l97ZPHnTdCtktFnB0FGf1oMONL3eEWechYtci7U4BnkCFD32V0QRuZeVKoqhRAc", "BA_NewStatusLengthValidation")]
        [TestCase("merchantStatus", null, "BA_NewStatusLengthValidation")]
        [TestCase("merchantDetails/acquirerReview", "NFsRLqoGFxpMBe7Tb", "BA_AcquirerReviewLengthValidation")]
        [TestCase("merchantDetails/acquirerReview", null, "BA_AcquirerReviewLengthValidation")]
        [TestCase("merchantDetails/mcc", "12345", "BA_NewMccLengthValidation")]
        [TestCase("merchantDetails/mcc", "", "BA_NewMccLengthValidation")]
        public async Task PatchMerchantAsync_WhenValidationFails_ShouldReturnValidationError(string path, object value, string errorCode)
        {

            var merchantPatch = new JsonPatchDocument<PatchMerchantRequest>();

            merchantPatch.Operations.Add(new Operation<PatchMerchantRequest>("replace", path, null, value));

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            activeCampaignService.Invocations.Clear();

            await merchantService.Invoking(async (x) => await x.PatchMerchantAsync(Guid.NewGuid(), merchantPatch))
                .Should().ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                                 && (TestsHelper.HasErrorCode(x, errorCode)));
        }

        [Test]
        public void PatchMerchantStatusAsync_ToVerified()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.Verified
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant()));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            merchantClient.Setup(x => x.GetStoresAsync(merchantId))
                .Returns(Task.FromResult(TestsHelper.expectedStore));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void PatchMerchantStatusAsync_ToRejected()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.Rejected
                    });
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant()));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId }, new OrderResponse { OrderId = secondOrderId } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();

            checkoutService.Verify(x => x.SearchOrderAsync(
                It.Is<OrderSearchCriteria>(o => o.MerchantId == merchantId)));
            checkoutService.Verify(x => x.UpdateOrderAsync(firstOrderId, It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid?>(), It.IsAny<bool>()));
            checkoutService.Verify(x => x.UpdateOrderAsync(secondOrderId, It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid?>(), It.IsAny<bool>()));
        }
        [Test]
        public void PatchMerchantStatusAsync_MerchantUnderwritingToVerified()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckPositiveHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.Verified
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantBoardingCompletedStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            merchantClient.Setup(x => x.GetStoresAsync(merchantId))
                .Returns(Task.FromResult(TestsHelper.expectedStore));
            shareholderService.Setup(x => x.GetMerchantIndividualsAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.merchantShareholderIndividual));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void PatchMerchantStatusAsync_MerchantUnderwritingToVerified_withKYC_CHeck()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.Verified
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantBoardingCompletedStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            merchantClient.Setup(x => x.GetStoresAsync(merchantId))
                .Returns(Task.FromResult(TestsHelper.expectedStore));
            shareholderService.Setup(x => x.GetMerchantIndividualsAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.merchantShareholderIndividual));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void PatchMerchantStatusAsync_ComplianceApprovalToVerified()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.Verified
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantComplianceApprovalStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
               .Returns(Task.FromResult(merchantChecks));
            merchantClient.Setup(x => x.GetStoresAsync(merchantId))
                .Returns(Task.FromResult(TestsHelper.expectedStore));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void PatchMerchantStatusAsync_RiskApprovalToVerified()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.Verified
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantBoardingRiskApprovalStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void PatchMerchantStatusAsync_ComplianceToRiskApproval()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.RiskApproval
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantComplianceApprovalStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void PatchMerchantStatusAsync_MerchantUnderwritingToCompliance()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.ComplianceApproval
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantBoardingCompletedStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void PatchMerchantStatusAsync_MerchantUnderwritingToRiskApproval()
        {
            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringHigh,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.RiskApproval
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantBoardingCompletedStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public async Task PatchMerchant_UpdateMcc_UpdateBusinessDomain()
        {
            var ordersWithInstancesResponseList = new List<OrdersWithInstancesResponse>();

            ordersWithInstancesResponseList.Add(new OrdersWithInstancesResponse
            {
                OrderNumber = "EX_10145",
                OrderId = Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d"),
                OrderIsDeleted = false,
                Instances = new List<InstanceWithTerminalData>()
                        {
                            new InstanceWithTerminalData()
                            {
                                ProductInstanceId = Guid.NewGuid()
                            }
                        },
                StoreId = Guid.NewGuid(),
                OrderStatus = "VERIFIED",
                CheckoutDate = DateTime.UtcNow

            });

            List<Guid> orderIds = new List<Guid>();
            orderIds.Add(Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d"));

            var merchantId = Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d");
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantDetails/mcc",
                        value = "1711"
                    });

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);

            merchantClient.Setup(x => x.GetCoreMerchantAsync(merchantId))
                .Returns(Task.FromResult(new Merchant()));

            checkoutService.Setup(x => x.GetMerchantOrdersNotPassedProductRegisterd(merchantId))
                     .Returns(Task.FromResult(new MerchantOrders
                     {
                         OrdersIds = new List<Guid> {
                     Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d") }
                     }));

            searchService.Setup(x => x.GetOrdersProductInstancesWithTerminalDataAsync(orderIds)).
            Returns(Task.FromResult(ordersWithInstancesResponseList));

            productService.Setup(x => x.UpdateOrderTerminalDataSetsMcc(It.IsAny<TerminalDataRequestMcc>()))
                .Returns(Task.FromResult(TestsHelper.terminalDataSetResponse));

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantUpdateResponse { MerchantId = merchantId, MerchantDetails = new MerchantDetails() { MCC = "1711", BusinessDomain = "5" } })));

            merchantClient.Setup(x => x.GetStoresAsync(merchantId))
                .Returns(Task.FromResult(TestsHelper.expectedStore));

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantUpdateResponse { MerchantId = merchantId, MerchantDetails = new MerchantDetails() { MCC = "1711", BusinessDomain = "5" } })));

            var result = await merchantService.PatchMerchantAsync(merchantId, patchDocument);

            var patchOperations = patchDocument.Operations.Select(operation =>
                    new Operation(operation.op, operation.path, operation.from, operation.value))
                    .ToList();

            var mccOperation = patchOperations.FirstOrDefault(op => op.path == "merchantDetails/mcc");

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            var addPatchOperation = patchDocument.Operations.Select(x => new Operation(x.op, "merchantDetails/businessDomain", x.from, "5")).ToList();
            patchOperations = patchOperations.Concat(addPatchOperation).ToList();

            var businessDomainOperation = patchOperations.FirstOrDefault(op => op.path == "merchantDetails/businessDomain");

            Assert.That(result, Is.Not.Null);
            Assert.That(result.MerchantDetails?.MCC, Is.EqualTo(mccOperation?.value.ToString()));
            Assert.That(result.MerchantDetails?.BusinessDomain, Is.EqualTo(businessDomainOperation?.value.ToString()));

        }
        [Test]
        public async Task PatchMerchant_UpdateMcc_UpdateNotRegisteredOrders()
        {
            var ordersWithInstancesResponseList = new List<OrdersWithInstancesResponse>();

            ordersWithInstancesResponseList.Add(new OrdersWithInstancesResponse
            {
                OrderNumber = "EX_10145",
                OrderId = Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d"),
                OrderIsDeleted = false,
                Instances = new List<InstanceWithTerminalData>()
                        {
                            new InstanceWithTerminalData()
                            {
                                ProductInstanceId = Guid.NewGuid()
                            }
                        },
                StoreId = Guid.NewGuid(),
                OrderStatus = "VERIFIED",
                CheckoutDate = DateTime.UtcNow

            });

            List<Guid> orderIds = new List<Guid>();
            orderIds.Add(Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d"));

            var merchantId = Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d");
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantDetails/mcc",
                        value = "1711"
                    });
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = "BOARDING_COMPLETED"
                    });

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            merchantClient.Setup(x => x.GetCoreMerchantAsync(merchantId))
                .Returns(Task.FromResult(new Merchant()));

            checkoutService.Setup(x => x.GetMerchantOrdersNotPassedProductRegisterd(merchantId))
                     .Returns(Task.FromResult(new MerchantOrders
                     {
                         OrdersIds = new List<Guid> {
                     Guid.Parse("9f6d5d8c-0dee-4831-98a6-059f0064f06d") }
                     }));

            searchService.Setup(x => x.GetOrdersProductInstancesWithTerminalDataAsync(orderIds)).
            Returns(Task.FromResult(ordersWithInstancesResponseList));

            productService.Setup(x => x.UpdateOrderTerminalDataSetsMcc(It.IsAny<TerminalDataRequestMcc>()))
                .Returns(Task.FromResult(TestsHelper.terminalDataSetResponse));

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantUpdateResponse { MerchantId = merchantId, MerchantDetails = new MerchantDetails() { MCC = "1711" } })));

            var result = await merchantService.PatchMerchantAsync(merchantId, patchDocument);

            var patchOperations = patchDocument.Operations.Select(operation =>
                    new Operation(operation.op, operation.path, operation.from, operation.value))
                    .ToList();

            var mccOperation = patchOperations.FirstOrDefault(op => op.path == "merchantDetails/mcc");


            Assert.That(result, Is.Not.Null);
            Assert.That(result.MerchantDetails?.MCC, Is.EqualTo(mccOperation?.value.ToString()));

        }

        [Test]
        public async Task PatchMerchant_UpdateMcc_UpdateNotRegisteredOrders_No_RegisteredOrders()
        {
            var ordersWithInstancesResponseList = new List<OrdersWithInstancesResponse>();

            ordersWithInstancesResponseList.Add(new OrdersWithInstancesResponse
            {
                OrderNumber = "EX_10145",
                OrderId = Guid.Parse("9f6d5d8c-0dee-4830-98a6-059f0064f06d"),
                OrderIsDeleted = false,
                Instances = new List<InstanceWithTerminalData>()
                        {
                            new InstanceWithTerminalData()
                            {
                                ProductInstanceId = Guid.NewGuid()
                            }
                        },
                StoreId = Guid.NewGuid(),
                OrderStatus = "VERIFIED",
                CheckoutDate = DateTime.UtcNow

            });

            List<Guid> orderIds = new List<Guid>();
            orderIds.Add(Guid.Parse("9f6d5d8c-0dee-4830-98a6-059f0064f06d"));

            var merchantId = Guid.Parse("9f6d5d8c-0dee-4830-98a6-059f0064fd6d");
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantDetails/mcc",
                        value = "1711"
                    });
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = "BOARDING_COMPLETED"
                    });

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            merchantClient.Setup(x => x.GetCoreMerchantAsync(merchantId))
                .Returns(Task.FromResult(new Merchant()));

            checkoutService.Setup(x => x.GetMerchantOrdersNotPassedProductRegisterd(Guid.NewGuid()))
                     .Returns(Task.FromResult(new MerchantOrders
                     {
                         OrdersIds = new List<Guid> { Guid.Parse("9f6d5d8c-0dee-4830-98a6-059f0064f06d") }
                     }));
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantUpdateResponse { MerchantId = merchantId, MerchantDetails = new MerchantDetails() { MCC = "1721" } })));

            var result = await merchantService.PatchMerchantAsync(merchantId, patchDocument);

            var patchOperations = patchDocument.Operations.Select(operation =>
                    new Operation(operation.op, operation.path, operation.from, operation.value))
                    .ToList();

            var mccOperation = patchOperations.FirstOrDefault(op => op.path == "merchantDetails/mcc");

            Assert.That(result, Is.Not.Null);
            Assert.That(result.MerchantDetails?.MCC, Is.Not.EqualTo(mccOperation?.value.ToString()));

        }

        [Test]
        public async Task GetMerchantDetailsAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantDetails[] { merchantDetails })));

            var result = await merchantService.GetMerchantsDetailsAsync();
            result.First().Should().BeEquivalentTo(merchantDetails);
        }

        [Test]
        public async Task ExportMerchantAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantExportResponse)));

            var result = await merchantService.ExportMerchantAsync(Guid.NewGuid());

            result.Should().BeEquivalentTo(merchantExportResponse);
        }

        [Test]
        public void ExportMerchantAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.ExportMerchantAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task ExportAllMerchantsAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantExportResponse[] { merchantExportResponse })));

            var result = await merchantService.ExportAllMerchantsAsync();

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(merchantExportResponse);
        }

        [Test]
        public void ExportAllMerchantsAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.ExportAllMerchantsAsync())
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetPeopleByMerchantIdAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantPersonOfInterest[] { merchantPersonOfInterest })));

            var result = await merchantService.GetPeopleByMerchantIdAsync(Guid.NewGuid());

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(merchantPersonOfInterest);
        }

        [Test]
        public void GetPeopleByMerchantIdAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.GetPeopleByMerchantIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetMerchantStatusHistoryAsync()
        {
            var expectedMerchantStatusResponse = new List<MerchantStatusResponse>
            {
                new MerchantStatusResponse
                {
                    MerchantId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                    MerchantStatus = "REGISTERED",
                    MerchantStatusStartDate = DateTime.Today,
                    UpdatedBy = "FirstName LastName"
                },
                new MerchantStatusResponse
                {
                    MerchantId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                    MerchantStatus = "REGISTERED",
                    MerchantStatusStartDate = DateTime.Today,
                    UpdatedBy = Constants.User.DefaultUserValue
                }
            };

            var user = new Common.Models.User.User
            {
                Id = new Guid("5e72fe84-1e25-41a9-be0a-17453b2d0b68"),
                FirstName = "FirstName",
                LastName = "LastName",
                Email = "<EMAIL>"
            };
            userService.Setup(x => x.GetAllUsersAsync())
                .Returns(Task.FromResult(new Common.Models.User.User[] { user }));

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantStatusResponse)));

            var result = await merchantService.GetMerchantStatusHistoryAsync(Guid.NewGuid());

            result.Should().BeEquivalentTo(expectedMerchantStatusResponse);
        }

        [Test]
        public void GetMerchantStatusHistoryAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.GetMerchantStatusHistoryAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetMerchantShortExport()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new List<MerchantShortExportResponse> { merchantShortExport })));

            var result = await merchantService.GetMerchantShortExportAsync(new IdsRequest());

            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result[0].Should().BeEquivalentTo(merchantShortExport);
        }

        [Test]
        public void SearchGsdkMerchants_ThrowsException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, JsonSerializer.Serialize("Error code")));

            merchantService.Invoking(x => x.SearchGsdkMerchants(TestsHelper.SearchGsdkMerchantsRequest))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task SearchGsdkMerchants_ReturnsSuccessfully()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new[] { TestsHelper.GsdkMerchant })));

            var result = await merchantService.SearchGsdkMerchants(TestsHelper.SearchGsdkMerchantsRequest);

            result.First().Should().BeEquivalentTo(TestsHelper.GsdkMerchant);
        }

        [Test]
        public async Task CreateMerchantExternalIdentifier_ReturnsSuccessfully()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(string.Empty)));

            var result = await merchantService.CreateMerchantExternalIdentifier(TestsHelper.MerchantExternalIdentifier);

            Assert.That(result, Is.True, "Result should be true");
        }

        [Test]
        public async Task CreateMerchantExternalIdentifier_DoesNotThrowsExceptionOnBadRequest()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, JsonSerializer.Serialize("Error code")));

            var result = await merchantService.CreateMerchantExternalIdentifier(TestsHelper.MerchantExternalIdentifier);

            Assert.That(result, Is.False, "Result should be false");
        }

        [Test]
        public async Task CreateMerchantExternalIdentifier_DoesNotThrowsExceptionServiceException()
        {
            var messageHandler = new ExceptionHttpMessageHandlerMock(new HttpRequestException());
            var httpClient = new HttpClient(messageHandler);

            merchantService = GetMerchantService(httpClient);

            var result = await merchantService.CreateMerchantExternalIdentifier(TestsHelper.MerchantExternalIdentifier);

            Assert.That(result, Is.False, "Result should be false");
        }

        [Test]
        public async Task CreateCommentAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantCommentResponse)));

            var result = await merchantService.CreateCommentAsync(It.IsAny<Guid>(), new CommentCreateRequest() { CommentText = "test" });

            result.Should().BeEquivalentTo(merchantCommentResponse);
        }

        [Test]
        public void CreateCommentAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.CreateCommentAsync(It.IsAny<Guid>(), new CommentCreateRequest() { CommentText = "test" }))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void GetCommentByMerchantIdAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new List<MerchantCommentResponse> { merchantCommentResponse })));

            merchantService.Invoking(x => x.GetCommentByMerchantIdAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void GetCommentByMerchantIdAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.GetCommentByMerchantIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void GetCommentByIdAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantCommentResponse)));

            merchantService.Invoking(x => x.GetCommentByIdAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void GetCommentByIdAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.GetCommentByIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void UpdateCommentAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantCommentResponse)));

            merchantService.Invoking(x => x.UpdateCommentAsync(Guid.NewGuid(), new CommentUpdateRequest() { CommentText = "test" })).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateCommentAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.UpdateCommentAsync(Guid.NewGuid(), new CommentUpdateRequest() { CommentText = "test" }))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteCommentAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantCommentResponse)));

            merchantService.Invoking(x => x.DeleteCommentAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteCommentAsync_PassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.DeleteCommentAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void CreateCommentAsync_CommentValidator_ShouldThrowValidationException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            var commentCreateRequest = new CommentCreateRequest()
            {
                CommentText = "vcsvctpcinhudgghcirmzdjuzilvlrtwantfjwxqnxuzlehtbcpbjasmwssahmoztojbecjdkrsgzhxpxawuggnbluzpxbxjsrlaemgttapnyyjlwizqhdosdaxaevgivwypenrzjwsxincsagvngltlmcudbjwzfnmdlgekgfbrhtxfhlbbnyocesepqqtejuadetxunsuujkaprglckehkbcxzequexygpqxkbpoauwvwzkqscfqakwoxfoetuoihwseupwkqylhttvyzeisvrivvfidsohbrchyruipkzqwmfcrsvnbtaxjjxaymjqhbbzvvhdwvuefnykgvhkkidmdsbqzdasyyhsbmvezjlkaancoyjrpgmydvzzfoksyrhfzuvmgvhujxratyvgxfqsjsfgzyglhodsfefcwzyfwqqwmcfnyxntoisftfyrkkfbeggoocghmpvgtonsgyjjubmvocijnjnmwpxqbyaflatvftswaholfxwdjwvn"
            };

            merchantService.Invoking(x => x.CreateCommentAsync(Guid.NewGuid(), commentCreateRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.CommentLengthValidation.Code));
        }

        [Test]
        public void CreateCommentAsync_CommentValidator_ShouldNotThrowValidationException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            var commentCreateRequest = new CommentCreateRequest()
            {
                CommentText = "CommentText"
            };

            merchantService.Invoking(x => x.CreateCommentAsync(Guid.NewGuid(), commentCreateRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        public void UpdateCommentAsync_CommentValidator_ShouldThrowValidationException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            var commentUpdateRequest = new CommentUpdateRequest()
            {
                CommentText = "vcsvctpcinhudgghcirmzdjuzilvlrtwantfjwxqnxuzlehtbcpbjasmwssahmoztojbecjdkrsgzhxpxawuggnbluzpxbxjsrlaemgttapnyyjlwizqhdosdaxaevgivwypenrzjwsxincsagvngltlmcudbjwzfnmdlgekgfbrhtxfhlbbnyocesepqqtejuadetxunsuujkaprglckehkbcxzequexygpqxkbpoauwvwzkqscfqakwoxfoetuoihwseupwkqylhttvyzeisvrivvfidsohbrchyruipkzqwmfcrsvnbtaxjjxaymjqhbbzvvhdwvuefnykgvhkkidmdsbqzdasyyhsbmvezjlkaancoyjrpgmydvzzfoksyrhfzuvmgvhujxratyvgxfqsjsfgzyglhodsfefcwzyfwqqwmcfnyxntoisftfyrkkfbeggoocghmpvgtonsgyjjubmvocijnjnmwpxqbyaflatvftswaholfxwdjwvn"
            };

            merchantService.Invoking(x => x.UpdateCommentAsync(Guid.NewGuid(), commentUpdateRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.CommentLengthValidation.Code));
        }

        [Test]
        public void UpdateCommentAsync_CommentValidator_ShouldNotThrowValidationException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            var commentUpdateRequest = new CommentUpdateRequest()
            {
                CommentText = "CommentText"
            };

            merchantService.Invoking(x => x.UpdateCommentAsync(Guid.NewGuid(), commentUpdateRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        public void UpdateSalesIdAsync_WhenMerchantServiceReturnsSuccess_ShouldNotThrowException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            merchantService.Invoking(x => x.UpdateSalesIdAsync("GDS00001", "GDS0002")).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateSalesIdAsync_WhenMerchantServiceThrowsException_ShouldThrowPassthroughError()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.UpdateSalesIdAsync("GDS00001", "GDS0002"))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void UpdateMerchantSalesIdByLead_WhenMerchantServiceReturnsSuccess_ShouldNotThrowException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, ""));

            merchantService.Invoking(x => x.UpdateMerchantSalesIdByLead(Guid.NewGuid(), "GDS0002")).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateMerchantSalesIdByLead_WhenMerchantServiceThrowsException_ShouldThrowPassthroughException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            merchantService.Invoking(x => x.UpdateMerchantSalesIdByLead(Guid.NewGuid(), "GDS0002"))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void PatchMerchantByLeadId_WhenMerchantServiceReturnsSuccess_ShouldNotThrowException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, ""));

            merchantService
                .Invoking(x => x.PatchMerchantByLeadIdAsync(Guid.NewGuid(), new JsonPatchDocument<Merchant>())).Should()
                .NotThrowAsync<Exception>();
        }

        [Test]
        public void PatchMerchantByLeadId_WhenMerchantServiceReturnsSuccess_ShouldThrowException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound, ""));

            merchantService
                .Invoking(x => x.PatchMerchantByLeadIdAsync(Guid.NewGuid(), new JsonPatchDocument<Merchant>()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void PatchMerchantStatusAsync_ToVerificationError_SendToActiveCampaign()
        {
            activeCampaignService.Invocations.Clear();

            var firstOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.VerificationError
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant()));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();

            activeCampaignService.Verify(x => x.SendActiveCampaignRequest(It.Is<CreateActiveCampaignRequest>(
                x => x.MerchantId == merchantId && x.CounterParty == "GEIDEA_SAUDI" && x.OnboardingStatus == Utils.Common.Constants.ActiveCampaign.MerchantVerificationCompleted)), Times.Once);
        }

        [Test]
        public void PatchMerchantStatusAsync_FromCACApproval_ToCACApproval_WithUnfinishedTasks_ShouldNotThrow()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.RequireCACApproval
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant() { MerchantStatus = Constants.MerchantStatus.RequireCACApproval }));

            searchService.Setup(x => x.TaskAdvancedSearch(It.IsAny<TaskSearchRequest>()))
                .Returns(Task.FromResult(new SearchResponse<TaskResponse>() { Records = new List<TaskResponse>() { new() { MerchantId = merchantId, TaskStatus = "TEST" } } }));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void PatchMerchantStatusAsync_FromCACApproval_ToOtherStatus_WithoutAnyTasks_ShouldNotThrow()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                new Operation<PatchMerchantRequest>()
                {
                    op = "replace",
                    path = "merchantStatus",
                    value = Constants.MerchantStatus.Verified
                });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant() { MerchantStatus = Constants.MerchantStatus.RequireCACApproval }));

            searchService.Setup(x => x.TaskAdvancedSearch(It.IsAny<TaskSearchRequest>()))
                .Returns(Task.FromResult(new SearchResponse<TaskResponse>() { Records = new List<TaskResponse>() }));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new { Result = merchantUpdateResponse })));

            merchantClient.Setup(x => x.GetMerchantBusinessInformationAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(TestsHelper.validMerchantBusinessInformation));

            Assert.DoesNotThrowAsync(async () => await merchantService.PatchMerchantAsync(merchantId, patchDocument));
        }

        [Test]
        public void PatchMerchantStatusAsync_FromCACApproval_ToOtherStatus_WithUnfinishedTasks_ShouldThrow()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                new Operation<PatchMerchantRequest>()
                {
                    op = "replace",
                    path = "merchantStatus",
                    value = Constants.MerchantStatus.Verified
                });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant() { MerchantStatus = Constants.MerchantStatus.RequireCACApproval }));

            searchService.Setup(x => x.TaskAdvancedSearch(It.IsAny<TaskSearchRequest>()))
                .Returns(Task.FromResult(new SearchResponse<TaskResponse>()
                {
                    Records = new List<TaskResponse>()
                    {
                        new TaskResponse()
                        {
                            TaskStatus = Constants.TaskStatusesKeys.Done
                        },
                        new TaskResponse()
                        {
                            TaskStatus = Constants.TaskStatusesKeys.Open
                        }
                    }
                }));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            merchantClient.Setup(x => x.GetMerchantBusinessInformationAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(TestsHelper.validMerchantBusinessInformation));

            Assert.ThrowsAsync<ServiceException>(async () => await merchantService.PatchMerchantAsync(merchantId, patchDocument));
        }

        [Test]
        public void PatchMerchantStatusAsync_FromCACApproval_ToOtherStatus_WithTaskFinished_ShouldNotThrow()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                new Operation<PatchMerchantRequest>()
                {
                    op = "replace",
                    path = "merchantStatus",
                    value = Constants.MerchantStatus.Verified
                });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant() { MerchantStatus = Constants.MerchantStatus.RequireCACApproval }));

            searchService.Setup(x => x.TaskAdvancedSearch(It.IsAny<TaskSearchRequest>()))
                .Returns(Task.FromResult(new SearchResponse<TaskResponse>()
                {
                    Records = new List<TaskResponse>()
                    {
                        new TaskResponse()
                        {
                            TaskStatus = Constants.TaskStatusesKeys.Done
                        },
                        new TaskResponse()
                        {
                            TaskStatus = Constants.TaskStatusesKeys.Done
                        }
                    }
                }));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantUpdateResponse)));

            merchantClient.Setup(x => x.GetMerchantBusinessInformationAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(TestsHelper.validMerchantBusinessInformation));

            Assert.DoesNotThrowAsync(async () => await merchantService.PatchMerchantAsync(merchantId, patchDocument));
        }

        [Test]
        public async Task PatchMerchantAsync_UpdateMerchantTagToRetail_OperationSuccessful()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                new Operation<PatchMerchantRequest>()
                {
                    op = "replace",
                    path = "tag",
                    value = Constants.MerchantTag.Retail
                });
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = "BOARDING_COMPLETED"
                    });

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantUpdateResponse { MerchantId = merchantId })));

            var result = await merchantService.PatchMerchantAsync(merchantId, patchDocument);
            Assert.That(result.TagUpdateMessage, Is.EqualTo(Errors.OperationSuccessful.Code));
            Assert.That(result.MerchantId, Is.EqualTo(merchantId));
        }

        [Test]
        public async Task PatchMerchantAsync_UpdateMerchantTagToRetailSaudi_OperationSuccessful()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                new Operation<PatchMerchantRequest>()
                {
                    op = "replace",
                    path = "tag",
                    value = Constants.MerchantTag.Retail
                });

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(new MerchantUpdateResponse { MerchantId = merchantId })));

            var result = await merchantService.PatchMerchantAsync(merchantId, patchDocument);
            Assert.That(result.MerchantId, Is.EqualTo(merchantId));
            Assert.IsNull(result.TagUpdateMessage);
        }

        [Test]
        public async Task PatchMerchantAsync_UpdateMerchantTagToSub_OperationSuccessfulWithWarning()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                new Operation<PatchMerchantRequest>()
                {
                    op = "replace",
                    path = "tag",
                    value = Constants.MerchantTag.SubBusiness
                });

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantUpdateResponse)));

            var result = await merchantService.PatchMerchantAsync(merchantId, patchDocument);
            Assert.That(result.TagUpdateMessage, Is.EqualTo(Errors.UpdateMerchantTagOperationSuccessfulWithWarning.Code));
        }

        [Test]
        public void PatchMerchantStatus_To_VerifiedStatus_With_Empty_MCC_ShouldNotThrow()
        {
            var merchantId = Guid.NewGuid();

            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                new Operation<PatchMerchantRequest>()
                {
                    op = "replace",
                    path = "merchantStatus",
                    value = Constants.MerchantStatus.Verified
                });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant() { MerchantDetails = new MerchantDetails { MCC = "" } }));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new { Result = merchantUpdateResponse })));

            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void ValidateLicenseIdIsUsedAsync_WhenValidData_AndIdIsNotUsed_ShouldNotThrowException()
        {
            var licenseIdIsNotUsed = new IsLicenseIdUsedRespone { Used = false };

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(licenseIdIsNotUsed)));

            merchantService.Invoking(x => x.ValidateLicenseIdIsUsedAsync(null, "businessType", "1234")).Should().NotThrowAsync();
        }

        [Test]
        public void ValidateLicenseIdIsUsedAsync_WhenValidData_AndIdIsUsed_ShouldThrowServiceException()
        {
            var licenseIdIsUsed = new IsLicenseIdUsedRespone { Used = true };
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(licenseIdIsUsed)));

            merchantService.Invoking(x => x.ValidateLicenseIdIsUsedAsync(null, "businessType", "1234"))
                 .Should().ThrowAsync<ServiceException>()
                 .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void ValidateLicenseIdIsUsedAsync_WhenInvalidData_ShouldThrowAsyncPassthroughException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error"));

            merchantService.Invoking(x => x.ValidateLicenseIdIsUsedAsync(null, "businessType", "1234"))
                 .Should().ThrowAsync<PassthroughException>();
        }

        [Test]
        public void ValidateLicenseIdIsUsedAsync_AllParamsNull_ShouldNotThrowException()
        {
            var licenseIdIsNotUsed = new IsLicenseIdUsedRespone { Used = false };
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(licenseIdIsNotUsed)));

            merchantService.Invoking(x => x.ValidateLicenseIdIsUsedAsync(null, null, null, null)).Should().NotThrowAsync();
        }

        [Test]
        public void UpdateMerchantAdditionalMccsAsync()
        {
            userService.Setup(x => x.RemoveRolesForMerchantAsync(It.IsAny<Guid>()))
            .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            merchantService.Invoking(x => x.UpdateMerchantAdditionalMccsAsync(Guid.NewGuid(), updateMerchantAdditionalMccsRequest)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateMerchantAdditionalMccsAsync_UpdateMerchantAdditionalMccsRequestValidator_ShouldThrowValidationException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            var updateMerchantAdditionalMccs = new List<AdditionalMccUpdate>()
            {
                new AdditionalMccUpdate()
                {
                    MCC = "123456789"
                }
            };

            merchantService.Invoking(x => x.UpdateMerchantAdditionalMccsAsync(Guid.NewGuid(), updateMerchantAdditionalMccs)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.AdditionalMccLengthValidation.Code));
        }

        [Test]
        public void UpdateMerchantAdditionalMccsAsync_UpdateMerchantAdditionalMccsRequestValidator_ShouldNotThrowValidationException()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new MerchantSearchResult[] { merchantSearchResult })));

            merchantService.Invoking(x => x.UpdateMerchantAdditionalMccsAsync(Guid.NewGuid(), updateMerchantAdditionalMccsRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        public async Task ExportEgyptMerchantsAsync()
        {
            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(egyptMerchantExport)));

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            var result = await merchantService.ExportEgyptMerchantsAsync(merchantSearchFilters, Guid.NewGuid());

            result.Count.Should().Be(1);
            result.Should().BeEquivalentTo(egyptMerchantExportResponse);
        }


        [Test]
        public async Task IsMerchantBusinessesValid_WhenValidBusinessInformation_ShouldNotThrowValidationException()
        {
            SetupValidateMerchantBusinessInformationDependencies();

            await merchantService.Invoking(x => x.ValidateMerchantBusinessInformation(new Guid(), TestsHelper.patchMerchantRequest)).Should().NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("business name 2", "اسم الشركه")]
        public async Task IsMerchantBusinessesValid_WhenInValidBusinessInformation_ShouldThrowValidationException(string legalNameAr, string legalName)
        {
            SetupValidateMerchantBusinessInformationDependencies();
            merchantClient.Setup(x => x.GetMerchantBusinessInformationAsync(It.IsAny<Guid[]>()))
                .Returns(Task.FromResult(new MerchantBusinessInformation[] { new MerchantBusinessInformation { LegalName = legalName, LegalNameAr = legalNameAr } }));

            await merchantService.Invoking(x => x.ValidateMerchantBusinessInformation(new Guid(), TestsHelper.patchMerchantRequest)).Should().ThrowAsync<ValidationException>();
        }

        [Test]
        public async Task IsMerchantBusinessesValid_WhenValidBusinessInformationAndTmsIntegrationNotAllowed_ShouldReturnValidStatusAndNotApplyValidation()
        {
            SetupValidateMerchantBusinessInformationDependencies();
            tmsIntegrationFeatureToggle.Value.Returns(new TmsIntegrationFeatureToggle() { EnableTmsIntegration = false });

            await merchantService.Invoking(x => x.ValidateMerchantBusinessInformation(new Guid(), TestsHelper.patchMerchantRequest)).Should().NotThrowAsync<ValidationException>();
        }
        private void SetupValidateMerchantBusinessInformationDependencies()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            TestsHelper.patchMerchantRequest.Operations.Add(new Operation<PatchMerchantRequest>
            {
                path = "merchantStatus",
                value = "VERIFIED",
                op = "replace"
            });

            merchantClient.Setup(x => x.GetMerchantBusinessInformationAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(TestsHelper.validMerchantBusinessInformation));
        }
        [Test]
        public void PatchMerchantAsync_ShouldTriggerDueDeligence()
        {

            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.BoardingCompleted
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.coreMerchantBoardingCompletedStatus));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            merchantClient.Setup(x => x.GetStoresAsync(merchantId))
                .Returns(Task.FromResult(TestsHelper.expectedStore));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().NotThrowAsync<Exception>();

            dueDiligenceService.Verify(x => x.TriggerChecks(merchantId));


        }
        [Test]
        public void PatchMerchantAsync_ShouldThrowExceptionWhenRequiredKYBIsNullOrZero()
        {

            var firstOrderId = Guid.NewGuid();
            var secondOrderId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            IReadOnlyCollection<MerchantCheck> merchantChecks = new List<MerchantCheck>
            {
            new MerchantCheck { CheckType = CheckType.BankCheck, CheckStatus = CheckStatus.BankCheckPassed, MerchantId= merchantId},
            new MerchantCheck { CheckType = CheckType.WorldCheckOne, CheckStatus = CheckStatus.WorldCheckOneKYCPending,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.MatchCheck, CheckStatus = CheckStatus.MatchCheckNoHit,MerchantId= merchantId },
            new MerchantCheck { CheckType = CheckType.RiskScoring, CheckStatus = CheckStatus.RiskScoringLow,MerchantId= merchantId }
            };
            var patchDocument = new JsonPatchDocument<PatchMerchantRequest>();
            patchDocument.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.BoardingCompleted
                    });

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant()));
            merchantClient.Setup(x => x.GetChecksForMerchant(It.IsAny<Guid>()))
                .Returns(Task.FromResult(merchantChecks));
            merchantClient.Setup(x => x.GetStoresAsync(merchantId))
                .Returns(Task.FromResult(TestsHelper.expectedStore));
            checkoutService.Setup(x => x.SearchOrderAsync(It.IsAny<OrderSearchCriteria>()))
                     .Returns(Task.FromResult(new OrderResponse[] { new OrderResponse { OrderId = firstOrderId, OrderStatus = OrderStatus.VerificationInProgress }, new OrderResponse { OrderId = secondOrderId, OrderStatus = OrderStatus.VerificationInProgress } }));
            checkoutService.Setup(x => x.UpdateOrderAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                  .Returns(Task.CompletedTask);

            merchantService = GetMerchantService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            merchantService.Invoking(x => x.PatchMerchantAsync(merchantId, patchDocument)).Should().ThrowAsync<Exception>();
        }

    }
}