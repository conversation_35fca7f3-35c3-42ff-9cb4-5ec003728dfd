﻿using BackofficeApi.Controllers;
using Common.Models.Shareholder;
using Common.Services;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Api.Tests;

public class ShareholderControllerTests
{
    private Authorized authorized = null!;
    private ICounterpartyProvider counterpartyProvider = null!;
    private IShareholderService shareholderService = null!;
    private ShareholderController controller = null!;

    [SetUp]
    public void Setup()
    {
        shareholderService = Substitute.For<IShareholderService>();
        counterpartyProvider = Substitute.For<ICounterpartyProvider>();
    }

    [Test]
    public async Task CreateShareholderIndividual_Unauthorized_ReturnsForbid()
    {
        InitControllerWithAuthorization("");

        var response = await controller.CreateShareholderIndividual(new ShareholderIndividualCreateRequest());

        Assert.That(response is ForbidResult);
    }

    [Test]
    public async Task SearchShareholderIndividuals_Authorized_ReturnsOk()
    {
        var input = new ShareholderIndividualsSearchRequest();
        InitControllerWithAuthorization(Actions.View);

        var response = await controller.SearchShareholderIndividuals(input);

        Assert.That(response is OkObjectResult);

        await shareholderService.Received(1).SearchShareholderIndividualsAsync(input);
    }

    [Test]
    public async Task SearchShareholderIndividuals_NonAuthorized_ReturnsFobbiden()
    {
        var input = new ShareholderIndividualsSearchRequest();
        InitControllerWithAuthorization(Actions.Create);

        var response = await controller.SearchShareholderIndividuals(input);

        Assert.That(response is ForbidResult);

        await shareholderService.Received(0).SearchShareholderIndividualsAsync(input);
    }


    [Test]
    public async Task PatchShareholderCompany_NonAuthorized_ReturnsFobbiden()
    {
        var input = new ShareholderCompanyPatchRequest() {
            JsonPatchDocument = new JsonPatchDocument<MerchantShareholderCompanyResponse>(),
            ShareholderCompanyId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid()
        };
        InitControllerWithAuthorization(Actions.Create, false);

        var response = await controller.PatchShareholderCompany(input);

        Assert.That(response is ForbidResult);

        await shareholderService.Received(0).PatchShareholderCompanyAsync(input);
    }

    [Test]
    public async Task PatchShareholderCompany_WhenValidAndAuthorizedRequest_ShouldCallServiceAndReturnExpectedData()
    {
        var input = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = new JsonPatchDocument<MerchantShareholderCompanyResponse>(),
            ShareholderCompanyId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid()
        };
        input.JsonPatchDocument.Replace(x => x.ShareholderCompany.CompanyName, "adress valid");
        InitControllerWithAuthorization(Actions.Patch, false);

        var response = await controller.PatchShareholderCompany(input);

        Assert.That(response is OkObjectResult);
        await shareholderService.Received(1).PatchShareholderCompanyAsync(input);
        
    }

    private void InitControllerWithAuthorization(string actionToAuthorize, bool authorizeIndividual = true)
    {
        var evaluator = Substitute.For<IEvaluator>();
        evaluator.EvalAsync(Arg.Any<object>(), authorizeIndividual ? ResourceTypes.ShareholderIndividual : ResourceTypes.ShareholderCompany, actionToAuthorize).Returns(Task.FromResult(true));

        var toActions = new ToActions(new View(evaluator), new Utils.Policies.Evaluation.List(evaluator), new Create(evaluator), new Update(evaluator),
            new Delete(evaluator), new Send(evaluator), new Utils.Policies.Evaluation.Convert(evaluator), new Calculate(evaluator), new Checkout(evaluator),
            new Upload(evaluator), new Generate(evaluator), new Patch(evaluator), new Export(evaluator),
            new Initiate(evaluator), new Approve(evaluator), new Apply(evaluator));
        authorized = new Authorized(evaluator, toActions);

        controller = new ShareholderController(authorized, shareholderService, counterpartyProvider)
        {
            ControllerContext =
                {
                    HttpContext = new DefaultHttpContext()
                }
        };
    }
}
