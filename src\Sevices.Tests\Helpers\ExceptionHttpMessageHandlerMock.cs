﻿using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers
{
    public class ExceptionHttpMessageHandlerMock : HttpMessageHandler
    {
        private readonly Exception exception;

        public ExceptionHttpMessageHandlerMock(Exception exception)
        {
            this.exception = exception;
        }

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            throw exception;
        }
    }
}