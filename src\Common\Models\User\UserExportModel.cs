﻿using System.Diagnostics.CodeAnalysis;

namespace Common.Models.User
{
    [ExcludeFromCodeCoverage]
    public class UserExportModel
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? SalesId { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Designation { get; set; }
        public string? Team { get; set; }
        public string? AccessRole { get; set; }
        public string? ReportingManager { get; set; }
        public string? Counterparty { get; set; }
        public string? Status { get; set; }

    }
}
