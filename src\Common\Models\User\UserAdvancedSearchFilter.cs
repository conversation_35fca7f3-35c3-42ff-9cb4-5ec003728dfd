﻿using System.Collections.Generic;

namespace Common.Models.User
{
    public class UserAdvancedSearchFilter : BaseSearchCriteria
    {
        public string? Keyword { get; set; }

        public string[]? SearchIn { get; set; }

        public List<string>? Counterparty { get; set; }

        public List<string>? Teams { get; set; }

        public List<string>? Designations { get; set; }

        public List<string>? Groups { get; set; }

        public List<string>? Status { get; set; }
    }
}
