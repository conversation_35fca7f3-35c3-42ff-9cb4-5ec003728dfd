﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public abstract class MerchantBaseClass
{
    [Display(Name = "Name", Order = 0)]
    public string Name { get; set; } = string.Empty;

    [Display(Name = "Doing business as name", Order = 1)]
    public string DoingBusinessAsName { get; set; } = string.Empty;

    [Display(Name = "Phone Number", Order = 7)]
    public string PhoneNumber { get; set; } = string.Empty;

    [Display(Name = "Alternative phone Number", Order = 8)]
    public string AltPhoneNumber { get; set; } = string.Empty;

    [Display(Name = "Country sub division Tax Id", Order = 3)]
    public string CountrySubdivisionTaxId { get; set; } = string.Empty;

    [Display(Name = "National Tax Id", Order = 4)]
    public string NationalTaxId { get; set; } = string.Empty;

    [Display(Name = "Service Provider Legal", Order = 5)]
    public string ServiceProvLegal { get; set; } = string.Empty;

    [Display(Name = "Service Provider DBA", Order = 6)]
    public string ServiceProvDBA { get; set; } = string.Empty;
}