﻿using System.Threading.Tasks;
using Common.Models;

namespace Common.Services
{
    public interface INotificationService
    {
        Task SendEmailInfoCheckAsync(SendEmailInfoCheckRequest request);
        Task SendSmsInfoCheckAsync(SendSmsInfoCheckRequest request);
        Task SendCustomEmailAsync(SendCustomEmailRequest request);
        Task SendCustomSmsAsync(SendCustomSmsRequest request);
        Task SendWelcomeEmialForCPMerchantAccount(SendWelcomeEmialRequest request);
        Task SendWelcomeEmialForCNPMerchantAccount(SendWelcomeEmialRequest request);
        Task SendEmailForBusinessStatusChange(SendStatusUpdateEmailRequest request);
        Task SendEmaillForOrderStatusChange(SendStatusUpdateEmailRequest request);
        Task TriggerAutoEmailForStatusChange(string newStatus, SendStatusUpdateEmailRequest emailRequest);
    }
}
