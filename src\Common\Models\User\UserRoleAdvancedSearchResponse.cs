﻿using System;

namespace Common.Models.User
{
    public class UserRoleAdvancedSearchResponse
    {
        public Guid Id { get; set; }
        public string? Counterparty { get; set; }
        public string? RoleName { get; set; }
        public int? ActiveUsersNumber { get; set; }
        public string? CreatedDate { get; set; }
        public string? UpdatedDate { get; set; }
        public string? UpdatedBy { get; set; }
        public bool IsDisabled { get; set; }
    }
}
