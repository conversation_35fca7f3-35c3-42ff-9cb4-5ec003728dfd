﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Gsdk
{
    public class GlobalContractRuleDto
    {
        public string? CardBrand { get; set; }
        public OutProviderAccountDto OutProviderAccount { get; set; } = new OutProviderAccountDto();
    }

    public class OutProviderAccountDto
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
    }
}