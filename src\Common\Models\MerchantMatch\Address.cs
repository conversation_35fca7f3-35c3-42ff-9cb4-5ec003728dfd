﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class Address
{
    [Display(Name = "Line 1")]
    public string Line1 { get; set; } = string.Empty;

    [Display(Name = "Line 2")]
    public string Line2 { get; set; } = string.Empty;

    [Display(Name = "City")]
    public string City { get; set; } = string.Empty;

    [Display(Name = "Country Sub Division")]
    public string CountrySubDivision { get; set; } = string.Empty;

    [Display(Name = "Province")]
    public string Province { get; set; } = string.Empty;

    [Display(Name = "Postal Code")]
    public string PostalCode { get; set; } = string.Empty;

    [Display(Name = "Country")]
    public string Country { get; set; } = string.Empty;

    public override string ToString()
    {
        return $"Line 1: '{Line1}'," +
            $"{Environment.NewLine}Line 2: '{Line2}'," +
            $"{Environment.NewLine}City: '{City}'," +
            $"{Environment.NewLine}Country Sub Division: '{CountrySubDivision}'," +
            $"{Environment.NewLine}Provinience: '{Province}'," +
            $"{Environment.NewLine}Postal code: '{PostalCode}'," +
            $"{Environment.NewLine}Country: '{Country}'.";
    }
}
