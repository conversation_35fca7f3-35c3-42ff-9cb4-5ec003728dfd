﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Messaging.ApexProvider
{
    [ExcludeFromCodeCoverage]
    public class BaseEventArgs : EventArgs
    {
        public string? Counterparty { get; }
        public Guid? CorrelationId { get; }

        public BaseEventArgs(string? counterparty, Guid? correlationId)
        {
            Counterparty = counterparty;
            CorrelationId = correlationId;
        }
    }
}
