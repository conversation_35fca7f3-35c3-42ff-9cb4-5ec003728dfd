﻿using Geidea.Messages.Epos.Messages;

namespace Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Common;
    using Common.Enums;
    using Common.Models;
    using Common.Models.Checkout;
    using Common.Models.Merchant;
    using Common.Models.Product;
    using Common.Models.User;
    using Common.Options;
    using Common.Services;
    using Geidea.Utils.Counterparty.Providers;
    using Geidea.Utils.Exceptions;
    using Microsoft.AspNetCore.JsonPatch;
    using Microsoft.AspNetCore.JsonPatch.Operations;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Services.Messaging;
    using UtilsConstants = Geidea.Utils.Common.Constants;

    public class EPosMessagingService : IEPosMessagingService
    {
        private readonly ICheckoutClient checkoutClient;
        private readonly ICounterpartyProvider counterpartyProvider;
        private readonly EPosMessagingClient ePosMessagingClient;
        private readonly EPosMessagingSoftwareTypeClient ePosMessagingSoftwareTypeClient;
        private readonly ILogger<EPosMessagingService> logger;
        private readonly IMerchantClient merchantClient;
        private readonly IProductService productService;
        private readonly IReferenceService referenceService;
        private readonly IUserService userService;
        private readonly IOptions<FreelancerTerminalTypeFeatureToggle> freelancerTerminalTypeFeatureToggle;

        private enum SoftwareType
        {
            WithoutBillPayment,
            WithBillPayment
        };

        public EPosMessagingService(
            EPosMessagingClient ePosMessagingClient,
            EPosMessagingSoftwareTypeClient ePosMessagingSoftwareTypeClient,
            IMerchantClient merchantClient,
            ICheckoutClient checkoutClient,
            IProductService productService,
            IReferenceService referenceService,
            IUserService userService,
            ICounterpartyProvider counterpartyProvider,
            IOptions<FreelancerTerminalTypeFeatureToggle> freelancerTerminalTypeFeatureToggle,
            ILogger<EPosMessagingService> logger)
        {
            this.ePosMessagingClient = ePosMessagingClient;
            this.ePosMessagingSoftwareTypeClient = ePosMessagingSoftwareTypeClient;
            this.merchantClient = merchantClient;
            this.checkoutClient = checkoutClient;
            this.productService = productService;
            this.referenceService = referenceService;
            this.userService = userService;
            this.counterpartyProvider = counterpartyProvider;
            this.freelancerTerminalTypeFeatureToggle = freelancerTerminalTypeFeatureToggle;
            this.logger = logger;
        }

        public async Task<EPosAction> GetEPosActions(Guid orderId)
        {
            var order = await checkoutClient.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                logger.LogCritical("Error: {error} - {orderId}", Errors.OrderNotFound.Message, orderId);
                throw new ServiceException(System.Net.HttpStatusCode.NotFound, Errors.OrderNotFound).WithArguments(orderId);
            }
            var orderProductInstances = new List<ProductInstance>();
            await GetAllProductInstancesForOrder(order, orderProductInstances);

            if (OrderWasSentToEPos(orderProductInstances) > 0)
            {
                return EPosAction.CreateEPosTicket;
            }

            return TerminalsWithOutOfSyncSwType(orderProductInstances).Any()
                ? EPosAction.UpdateEPosSwType
                : EPosAction.NoAction;
        }

        public async Task CreateOrderEPosTicketAsync(Guid orderId, bool shouldTriggerError = false)
        {
            logger.LogInformation("Started the processing for order {@orderId}", orderId);
            var order = await checkoutClient.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                logger.LogCritical(Errors.OrderNotFound.Message, orderId);
                throw new ServiceException(System.Net.HttpStatusCode.NotFound, Errors.OrderNotFound).WithArguments(orderId);
            }

            if (order.OrderStatus == null || order.OrderStatus.ToUpper() != "PRODUCTS_REGISTERED")
            {
                logger.LogCritical(Errors.OrderDoesNotHaveStatusRegistered.Message, orderId);
                throw new ServiceException(System.Net.HttpStatusCode.InternalServerError, Errors.OrderDoesNotHaveStatusRegistered).WithArguments(orderId);
            }

            var merchantEPosInfo = await merchantClient.GetMerchantEPosInformationAsync(order.MerchantId);

            var salesEmail = await GetSalesEmail(merchantEPosInfo.SalesId);

            var allProductInstances = new List<ProductInstance>();
            await GetAllProductInstancesForOrder(order, allProductInstances);

            var productInstancesWithTerminals = OrderHasTerminals(allProductInstances);

            if (productInstancesWithTerminals == 0)
            {
                return;
            }

            var productInstancesNotSentToEpos = OrderWasSentToEPos(allProductInstances);
            if (productInstancesNotSentToEpos == 0)
            {
                logger.LogCritical(Errors.OrderAlreadySentToEPos.Message, order.OrderId);

                if (shouldTriggerError)
                {
                    throw new ServiceException(System.Net.HttpStatusCode.InternalServerError, Errors.OrderAlreadySentToEPos).WithArguments(order.OrderId);
                }

                return;
            }

            var catalogues = await referenceService.GetCataloguesAsync(new string[] { Constants.Catalogues.Areas, Constants.Catalogues.Governorates,
                Constants.Catalogues.NbeGovernarotes, Constants.Catalogues.AlxGovernarotes, Constants.Catalogues.AcquiringLedger, Constants.Catalogues.NbeCityToEPos,
                Constants.Catalogues.AlxCityToEPos });

            var counterparty = counterpartyProvider.GetCode();

            foreach (var productInstance in allProductInstances)
            {
                await SendEposTicketRequest(counterparty, productInstance, order, merchantEPosInfo, salesEmail, catalogues, allProductInstances);
            }
        }

        public async Task SendEPosSwTypeUpdate(Guid orderId)
        {
            logger.LogInformation("Started the update in ePos for order {@orderId}.", orderId);

            var order = await checkoutClient.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                logger.LogCritical("Error: {error} - {orderId}", Errors.OrderNotFound.Message, orderId);
                throw new ServiceException(System.Net.HttpStatusCode.NotFound, Errors.OrderNotFound).WithArguments(orderId);
            }

            var orderProductInstances = new List<ProductInstance>();
            await GetAllProductInstancesForOrder(order, orderProductInstances);

            if (OrderWasSentToEPos(orderProductInstances) > 0)
            {
                logger.LogError("{errorMessage}. Order id: {orderId}.", Errors.OrderNotSentToEPos.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.OrderNotSentToEPos).WithArguments(order.OrderId);
            }

            var outOfSyncBpTerminals = TerminalsWithOutOfSyncSwType(orderProductInstances).ToList();

            if (!outOfSyncBpTerminals.Any())
            {
                logger.LogError("{errorMessage}. Order id: {orderId}.", Errors.OrderUpToDateInEPos.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.OrderUpToDateInEPos).WithArguments(order.OrderId);
            }

            if (outOfSyncBpTerminals.Any(t => string.IsNullOrWhiteSpace((t.Metadata as TerminalData)?.TId)))
            {
                logger.LogError("{errorMessage}. Order id: {orderId}.", Errors.TerminalMissingTId.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.TerminalMissingTId).WithArguments(order.OrderId);
            }

            foreach (var bpTerminal in outOfSyncBpTerminals)
            {
                ePosMessagingSoftwareTypeClient.SendMessageToEPos(new UpdateTerminalSoftwareMessage()
                {
                    SoftwareType = (int)SoftwareType.WithBillPayment,
                    TerminalId = (bpTerminal.Metadata as TerminalData)?.TId!,
                    ProductInstanceId = bpTerminal.ProductInstanceId
                });
            }
        }

        private async Task<string> GetSalesEmail(string? salesId)
        {
            if (!string.IsNullOrEmpty(salesId))
            {
                var userSearchParameters = new UserSearchParameters
                {
                    SalesId = salesId
                };

                var salesInfo = await userService.SearchUsersAsync(userSearchParameters);

                if (salesInfo != null && salesInfo.Length == 1)
                {
                    return salesInfo[0].Email;
                }
            }

            return string.Empty;
        }

        private CreateEPosTicketPayload CreateEposTicketRequest(
            string counterparty,
            ProductInstance productInstance,
            OrderResponse order,
            MerchantEPosInformation merchantEPosInformation,
            string salesEmail,
            Catalogue[] catalogues,
            decimal? parentPrice,
            List<ProductInstance> orderProductInstances)
        {
            if (productInstance == null ||
                productInstance.Metadata == null ||
                productInstance.Product == null)
            {
                return null!;
            }

            var terminalData = productInstance.Metadata as TerminalData;

            var providerBank = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.AcquiringLedger && x.Key == terminalData?.ProviderBank);

            var cityId = GetCityId(providerBank, catalogues, merchantEPosInformation?.CityId, counterparty);

            if (cityId == null)
            {
                return null!;
            }

            var terminalType = GetTerminalType(counterparty, merchantEPosInformation?.BusinessType, productInstance.Product.Code);

            var eposCreateTicketRequest = new CreateEPosTicketPayload
            {
                TerminalId = terminalData!.TId,
                RetailerName = ComputeRetailerName(terminalData, merchantEPosInformation),
                RetailerId = terminalData.MIDMerchantReference,
                TRSM = terminalData.POSDataCode,
                RegType = GetRegType(terminalData!.ExternalBankTId),
                OldTerminal = terminalData!.ExternalBankTId ?? string.Empty,
                ProviderBank = GetProviderBankValue(providerBank, counterparty),
                Street = ComputeStreet(counterparty, providerBank, catalogues, merchantEPosInformation),
                Mobile = (String.IsNullOrEmpty(terminalData!.CountryPrefix) && String.IsNullOrEmpty(terminalData!.PhoneNumber)) ? (merchantEPosInformation?.PrincipalTelephone ?? string.Empty) : terminalData!.CountryPrefix + terminalData!.PhoneNumber,
                CityId = cityId,
                TerminalType = terminalType,
                MCC = terminalData.Mcc,
                MposMode = SetMode(order.PaymentMethod),
                MposRefNo = order.OrderNumber,
                MposPrice = GetProductPrice(productInstance.Product) ?? parentPrice,
                MposSalesEmail = salesEmail,
                ProductInstanceId = productInstance.ProductInstanceId,
                SoftwareType = GetSoftwareType(productInstance, orderProductInstances),
                Counterparty = counterparty
            };
            return eposCreateTicketRequest;
        }

        private static int? GetCityId(Catalogue? providerBank, Catalogue[] catalogues, string? cityKey, string counterparty)
        {
            if (counterparty == UtilsConstants.CounterpartyEgypt && providerBank != null)
            {
                var providerCityValue = catalogues.FirstOrDefault(x =>
                x.CatalogueName == $"{providerBank.Key}_{Constants.CityToEPos}" &&
                string.Equals(x.Key, cityKey, StringComparison.OrdinalIgnoreCase));

                if (providerCityValue != null)
                    cityKey = providerCityValue.Value;
            }

            if (!Int32.TryParse(cityKey, out int cityId))
            {
                return null;
            }

            return cityId;
        }

        private static string GetProviderBankValue(Catalogue? providerBank, string counterparty)
        {
            if (providerBank != null)
            {
                if (counterparty == UtilsConstants.CounterpartyEgypt
                    && !string.IsNullOrEmpty(providerBank.Value))
                    return providerBank.Value.Substring(0, 2);
                else
                    return providerBank.Value;
            }

            return string.Empty;
        }

        private static int? GetSoftwareType(ProductInstance currentProductInstance, List<ProductInstance> orderProductInstances)
        {
            var hasBpSibling = HasBillPaymentSibling(currentProductInstance, orderProductInstances);

            return hasBpSibling ? (int)SoftwareType.WithBillPayment : (int)SoftwareType.WithoutBillPayment;
        }

        private static bool HasBillPaymentSibling(ProductInstance currentProductInstance, List<ProductInstance> orderProductInstances)
        {
            if (currentProductInstance.ParentId == null)
                return false;

            var parent =
                orderProductInstances.SingleOrDefault(pi => pi.ProductInstanceId == currentProductInstance.ParentId);

            return parent != null && parent.Children.Any(child =>
                child.Product.Code == Constants.ProductCode.BillPayment &&
                child.ProductInstanceId != currentProductInstance.ProductInstanceId);
        }

        private static IEnumerable<ProductInstance> TerminalsWithOutOfSyncSwType(List<ProductInstance> orderProductInstances)
        {
            var terminalsWithOutOfSyncSwType = new List<ProductInstance>();
            foreach (var orderProductInstance in orderProductInstances)
            {
                var outOfSyncTerminals = GetTerminalsWithBillPayments(orderProductInstances, orderProductInstance)
                    .Where(t => !t.EPosBillPayments ?? true);
                terminalsWithOutOfSyncSwType.AddRange(outOfSyncTerminals);
            }

            return terminalsWithOutOfSyncSwType;
        }

        private static IEnumerable<ProductInstance> GetTerminalsWithBillPayments(List<ProductInstance> orderProductInstances, ProductInstance currentProductInstance)
        {
            var terminalsWithBillPayments = new List<ProductInstance>();

            if ((currentProductInstance.Product.Type == ProductType.M_POS.ToString() ||
                currentProductInstance.Product.Type == ProductType.TERMINAL.ToString()) &&
                HasBillPaymentSibling(currentProductInstance, orderProductInstances))
            {
                terminalsWithBillPayments.Add(currentProductInstance);
            }

            foreach (var child in currentProductInstance.Children)
            {
                terminalsWithBillPayments.AddRange(GetTerminalsWithBillPayments(orderProductInstances, child));
            }

            return terminalsWithBillPayments;
        }

        private string GetTerminalType(string counterparty, string? businessType, string productCode)
        {
            string[] freelancerProductsCodes = { "M_POS", "TERMINAL", "D135_READER", "MOBILE_POS_SP530", "SMARTPOS_A920", "P2", "A920", "SUNMI_P2", "PAX_A920" };

            if (counterparty == UtilsConstants.CounterpartySaudi &&
                freelancerTerminalTypeFeatureToggle.Value.EnableT300TerminalType &&
                businessType == Constants.BusinessType.SoleTrader &&
              freelancerProductsCodes.Contains(productCode))
            {
                return "T300_4G";
            }

            return productCode;
        }

        private static decimal? GetProductPrice(ProductShortResponse product)
        {
            var computableChargeTypes = new string[] { Constants.ChargeType.Reccurrring, Constants.ChargeType.SetupCharge, Constants.ChargeType.RetailPrice };
            var price = product.Prices.Where(p => computableChargeTypes.Contains(p.ChargeType)).Sum(x => x.PerItemPrice);

            if (price.HasValue && price != 0)
                return (decimal)price / 100;

            return null;
        }

        private static string GetRegType(string? externalBankTId)
        {
            if (!string.IsNullOrEmpty(externalBankTId))
            {
                return "M";
            }

            return "I";
        }

        private static string SetMode(string? paymentMethod)
        {
            switch (paymentMethod)
            {
                case "ON_DELIVERY": return "2";
                case "ONLINE": return "4";
                case "E_INV": return "5";
                case "NOT_REQUIRED": return "6";
                case "REQUEST": return "7";
                default:
                    break;
            }

            return string.Empty;
        }

        private static string ComputeRetailerName(TerminalData terminalData, MerchantEPosInformation? merchantEPosInformation)
        {
            if (!string.IsNullOrEmpty(terminalData!.ShortName_EN))
            {
                return terminalData!.ShortName_EN;
            }

            if (!string.IsNullOrEmpty(terminalData!.ShortName_AR))
            {
                return terminalData!.ShortName_AR;
            }

            return merchantEPosInformation!.BusinessName ?? merchantEPosInformation!.BusinessNameAr!;
        }

        private static string ComputeStreet(string counterparty, Catalogue? providerBank, Catalogue[] catalogues, MerchantEPosInformation? merchantEPosInformation)
        {
            var street = string.Empty;


            if (counterparty == UtilsConstants.CounterpartySaudi)
            {
                var areaName = merchantEPosInformation?.Area ?? string.Empty;
                var area = catalogues.FirstOrDefault(a => a.CatalogueName == Constants.Catalogues.Areas && a.Key == areaName);

                if (area != default)
                {
                    areaName = area.Value;
                }
                street = $"{areaName}";
            }
            else if (counterparty == UtilsConstants.CounterpartyEgypt)
            {
                var addressLine1 = merchantEPosInformation?.AddressLine1 ?? string.Empty;
                var governorateName = merchantEPosInformation?.Governorate ?? string.Empty;

                // if the provider bank was not provided, we would use the default governorate like the old behavior
                string govCatalogueName = providerBank != null ? 
                    Geidea.Utils.ReferenceData.AcquirerHelper.GetCatalogueName(acquirer: providerBank.Key, defaultCatalogueName: Constants.Catalogues.Governorates,
                    catalogues.Select(c => new Geidea.Utils.ReferenceData.ReferenceData
                    {
                        CatalogueName = c.CatalogueName,
                        Key = c.Key,
                        Value = c.Value,
                    }).ToList()) : Constants.Catalogues.Governorates;

                var governorate = catalogues.FirstOrDefault(a => a.CatalogueName == govCatalogueName && a.Key == governorateName);

                if (governorate != default)
                {
                    governorateName = governorate.Value;
                }
                street = $"{governorateName} {addressLine1}";
            }
            return street;
        }

        private static bool EposCreateTicketRequestIsValid(CreateEPosTicketPayload payload)
        {
            return (payload != null &&
                payload.CityId != null &&
                !string.IsNullOrWhiteSpace(payload.MCC) &&
                !string.IsNullOrWhiteSpace(payload.MposMode) &&
                !string.IsNullOrWhiteSpace(payload.MposRefNo) &&
                !string.IsNullOrWhiteSpace(payload.RetailerId) &&
                !string.IsNullOrWhiteSpace(payload.TRSM) &&
                !string.IsNullOrWhiteSpace(payload.ProviderBank) &&
                !string.IsNullOrWhiteSpace(payload.RetailerName) &&
                !string.IsNullOrWhiteSpace(payload.TerminalId) &&
                !string.IsNullOrWhiteSpace(payload.TerminalType));
        }

        private async Task GetAllProductInstancesForOrder(OrderResponse order, List<ProductInstance> allProductInstances)
        {
            if (order.OrderItem == null)
            {
                logger.LogCritical(Errors.NoOrderItems.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.NotFound, Errors.NoOrderItems).WithArguments(order.OrderId);
            }

            foreach (var orderItem in order.OrderItem)
            {
                if (!orderItem.ProductInstanceIds.Any())
                {
                    logger.LogInformation($"Order item {orderItem.OrderItemId} does not have any product instance");
                    continue;
                }

                var productInstances = await productService.GetProductInstances(orderItem.ProductInstanceIds);

                if (productInstances.Count == 0)
                {
                    logger.LogInformation($"No product instances found for order item {orderItem.OrderItemId}");
                    continue;
                }
                allProductInstances.AddRange(productInstances);
            }
        }

        private async Task MarkProductInstanceAsSendToEpos(ProductInstance productInstance)
        {
            var patchProductInstace = new JsonPatchDocument<UpdateProductInstanceRequest>();
            patchProductInstace.Operations.Add(new Operation<UpdateProductInstanceRequest>
            {
                op = "replace",
                path = "/ePosTicketId",
                value = "0"
            });

            await productService.PatchAsync(productInstance.ProductInstanceId, patchProductInstace);
        }

        private int OrderWasSentToEPos(List<ProductInstance> productInstances)
        {
            var productInstancesNotSentToEpos = 0;
            foreach (var productInstance in productInstances)
            {
                productInstancesNotSentToEpos += ProductInstanceNotSentToEpos(productInstance);
            }
            return productInstancesNotSentToEpos;
        }

        private int ProductInstanceNotSentToEpos(ProductInstance productInstance)
        {
            if (productInstance == null)
            {
                return 0;
            }

            var productInstancesNotSentToEpos = 0;
            if ((productInstance.Product.Type == ProductType.M_POS.ToString() ||
                productInstance.Product.Type == ProductType.TERMINAL.ToString()) &&
                string.IsNullOrEmpty(productInstance.EPosTicketId))
            {
                productInstancesNotSentToEpos += 1;
            }

            foreach (var childProductInstance in productInstance.Children)
            {
                productInstancesNotSentToEpos += ProductInstanceNotSentToEpos(childProductInstance);
            }

            return productInstancesNotSentToEpos;
        }

        private async Task SendEposTicketRequest(
            string counterparty,
            ProductInstance productInstance,
            OrderResponse order,
            MerchantEPosInformation merchantEPosInformation,
            string salesEmail,
            Catalogue[] catalogues,
            List<ProductInstance> orderProductInstances,
            decimal? parentPrice = null
            )
        {
            if (productInstance == null || order == null || merchantEPosInformation == null)
            {
                logger.LogInformation("SendEposTicketRequest: Invalid arguments");
                return;
            }

            if (!string.IsNullOrEmpty(productInstance.EPosTicketId))
            {
                logger.LogInformation($"Product instance {productInstance.ProductInstanceId} has already been sent to EPos");
                return;
            }

            if (productInstance.Product.Type == ProductType.M_POS.ToString() ||
                productInstance.Product.Type == ProductType.TERMINAL.ToString())
            {
                var eposCreateTicketRequest = CreateEposTicketRequest(counterparty, productInstance, order, merchantEPosInformation, salesEmail, catalogues, parentPrice, orderProductInstances);
                if (!EposCreateTicketRequestIsValid(eposCreateTicketRequest))
                {
                    logger.LogCritical(Errors.TerminalIsNotConfigured.Message, productInstance.ProductInstanceId);
                    throw new ServiceException(System.Net.HttpStatusCode.InternalServerError, Errors.TerminalIsNotConfigured).WithArguments(productInstance.ProductInstanceId);
                }

                ePosMessagingClient.SendMessageToEPos(eposCreateTicketRequest);
                await MarkProductInstanceAsSendToEpos(productInstance);
                return;
            }

            var price = GetProductPrice(productInstance.Product);
            foreach (var childProductInstance in productInstance.Children)
            {
                await SendEposTicketRequest(counterparty, childProductInstance, order, merchantEPosInformation, salesEmail, catalogues, orderProductInstances, price);
            }
        }

        private int OrderHasTerminals(List<ProductInstance> productInstances)
        {
            var productInstancesWithTerminals = 0;
            foreach (var productInstance in productInstances)
            {
                productInstancesWithTerminals += ProductInstanceWithTerminal(productInstance);
            }
            return productInstancesWithTerminals;
        }

        private int ProductInstanceWithTerminal(ProductInstance productInstance)
        {
            if (productInstance == null)
            {
                return 0;
            }

            var productInstancesWithTerminal = 0;
            if ((productInstance.Product.Type == ProductType.M_POS.ToString() ||
                productInstance.Product.Type == ProductType.TERMINAL.ToString()))
            {
                productInstancesWithTerminal += 1;
            }

            foreach (var childProductInstance in productInstance.Children)
            {
                productInstancesWithTerminal += ProductInstanceWithTerminal(childProductInstance);
            }

            return productInstancesWithTerminal;
        }
    }
}