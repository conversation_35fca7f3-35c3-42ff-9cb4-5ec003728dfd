﻿using Common.Models.User;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Common;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [Route("api/v1")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserService userService;
        private readonly Authorized authorized;
        private readonly ILogger<UserController> logger;

        public UserController(IUserService userService, Authorized authorized, ILogger<UserController> logger)
        {
            this.userService = userService;
            this.authorized = authorized;
            this.logger = logger;
        }

        /// <summary>
        /// Get users for merchant id
        /// </summary>
        /// <response code="204">Returns array with user ids</response>
        /// <response code="400">If there is a problem</response> 

        [HttpGet("merchant/{merchantId}/users")]
        [ProducesResponseType(typeof(UserShortResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetUsers(Guid merchantId)
        {
            if (!await authorized.To.List.User(merchantId))
            {
                return Forbid();
            }

            return Ok(await userService.GetUsersAsync(merchantId));
        }

        /// <summary>
        /// Activate user based on user id.
        /// </summary>
        /// <response code="204">Returns no content if the user has been activated</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="401">If the user is unauthorized.</response> 
        /// <response code="400">If the user does not have the permission to activate other users.</response> 

        [HttpPost("user/{userId}/activate")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ActivateUser(Guid userId)
        {
            if (!await authorized.To.Update.UserBackoffice())
                return Forbid();

            await userService.ActivateUserAsync(userId);
            return NoContent();
        }

        /// <summary>
        /// Disable user based on user id.
        /// </summary>
        /// <response code="204">Returns no content if the user has been disabled</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="401">If the user is unauthorized.</response> 
        /// <response code="400">If the user does not have the permission to disable other users.</response> 

        [HttpPost("user/{userId}/disable")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DisableUser(Guid userId)
        {
            if (!await authorized.To.Update.UserBackoffice())
                return Forbid();

            await userService.DisableUserAsync(userId);
            return NoContent();
        }

        /// <summary>
        /// Search users based on criteria
        /// </summary>
        /// <response code="200">Returns users according to search criteria.</response>
        /// <response code="400">Returns the error.</response> 
        /// <response code="401">If the user is unauthorized.</response> 
        /// <response code="400">If the user does not have the permission to list other users.</response> 

        [HttpPost("users/search")]
        [ProducesResponseType(typeof(UserShortResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SearchUsers(UserSearchParameters userSearchParameters)
        {
            if (userSearchParameters.Groups != null && userSearchParameters.Groups.Contains("BANK-AUDITOR", StringComparer.OrdinalIgnoreCase) && !await authorized.To.List.UserBankAuditor())
                return Forbid();

            if (!await authorized.To.List.UserBackoffice())
                return Forbid();

            return Ok(await userService.SearchUsersAsync(userSearchParameters));
        }

        /// <summary>
        /// Update back office user data based on userId.
        /// </summary>
        /// <response code="204">Returns no content if the user has been updated.</response>
        /// <response code="400">Returns the error.</response> 
        /// <response code="401">If the user is unauthorized.</response> 
        /// <response code="400">If the user does not have the permission to update other users.</response> 

        [HttpPatch("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateUsers(Guid userId, [FromBody] JsonPatchDocument<UpdateBackOfficeUserRequest> updateUserDocument)
        {
            var properties = updateUserDocument.Operations?.Select(op => op.path);
            if (!await authorized.To.Patch.UserBackoffice(properties?.ToArray()))
                return Forbid();

            var updateRequest = new UpdateBackOfficeUserRequest();
            try
            {
                updateUserDocument.ApplyTo(updateRequest, ModelState);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Invalid patch.");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatch);
            }

            TryValidateModel(updateRequest);

            await userService.PatchBackOfficeUserAsync(userId, updateUserDocument);
            return NoContent();
        }

        /// <summary>
        /// Get user based on userId
        /// </summary>
        /// <response code="200">Returns the user.</response>
        /// <response code="400">Returns the error.</response> 
        /// <response code="401">If the user is unauthorized.</response> 
        /// <response code="403">If the user does not have the permission to get user.</response> 

        [HttpGet("user/{userId}")]
        [ProducesResponseType(typeof(User), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetUserById(Guid userId)
        {
            if (!await authorized.To.View.UserBackoffice())
                return Forbid();

            return Ok(await userService.GetUserByIdAsync(userId));
        }
    }
}
