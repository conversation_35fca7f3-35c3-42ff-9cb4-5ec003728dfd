﻿using System.Net.Http;
using System.Threading.Tasks;
using Common.Options;
using Common.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Text;
using Geidea.Utils.Exceptions;
using Common.Models.Merchant;
using Geidea.Utils.Json;
using System.Text.Json;
using Newtonsoft.Json;
using Common.Models;
using Common.Helpers;
using Common.Models.Checks;
using Microsoft.AspNetCore.JsonPatch;
using System.Reflection.Metadata;


namespace Services
{
    public partial class MerchantClient : IMerchantClient
    {
        private readonly ILogger<MerchantClient> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly HttpClient client;

        private string MerchantServiceBaseUrl => $"{urlSettingsOptions.MerchantServiceBaseUrlNS}";
        private readonly string MerchantEndpoint = "/api/v1/Merchant";
        private readonly string SubordinateMerchantEndpoint = "/api/v1/SubordinateMerchant";
        private const string MerchantExternalContractEndpoint = "/api/v1/externalContractMapping";
        private const string MerchantsBusinessInformationEndpoint = "/api/v1/merchants/business-information";
        private const string MerchantCheckEndpoint = "api/v1/Merchant/Checks";
        private string DueDiligenceServiceBaseUrl => $"{urlSettingsOptions.DueDiligenceServiceBaseUrlNS}";
        private readonly string DueDiligenceFinscanCheckEndpoint = "api/v1/Validation";

        public MerchantClient(
            ILogger<MerchantClient> logger,
            IOptionsMonitor<UrlSettings> urlSettingsOptions,
            HttpClient client)
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.client = client;
        }

        public async Task<Merchant> GetCoreMerchantAsync(Guid merchantId)
        {
            string url = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/{merchantId}/core";

            using (logger.BeginScope("GetCoreMerchantAsync({merchantId}, {url})", merchantId, url))
            {
                logger.LogInformation("Calling merchant API to get core merchant with id @merchantId: {0}", merchantId);

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to get core merchant. Error was Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                logger.LogInformation("Success calling merchant API");
                return Json.Deserialize<Merchant>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<CoreMerchantWithHierarchy> GetCoreMerchantWithHierarchy(Guid merchantId)
        {
            var url = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/{merchantId}/CoreWithHierarchy";

            using (logger.BeginScope("GetCoreMerchantWithHierarchy({@url})", url))
            {
                logger.LogInformation("Calling merchant service for merchant with id '{merchantId}'.", merchantId);

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service for merchant with id {merchantId}. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"Received merchant.");

                return Json.Deserialize<CoreMerchantWithHierarchy>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<CoreMerchantWithHierarchy> GetMerchantWithAllHierarchies(Guid merchantId)
        {
            var url = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/{merchantId}/MerchantWihAllHierarchies";

            using (logger.BeginScope("GetMerchantWithAllHierarchies({@url})", url))
            {
                logger.LogInformation("Calling merchant service for merchant with id '{merchantId}'.", merchantId);

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service for merchant with id {merchantId}. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"Received merchant.");

                return Json.Deserialize<CoreMerchantWithHierarchy>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<MerchantEPosInformation> GetMerchantEPosInformationAsync(Guid merchantId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/{merchantId}/eposinfo";

            using (logger.BeginScope("GetMerchantEPosInformationAsync({@serviceUrl})", merchantId))
            {
                logger.LogInformation("Calling merchant service to retrieve epos information.");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service, get merchant epos information. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }
                return Json.Deserialize<MerchantEPosInformation>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<MerchantAcquiringLedgerInfo> GetMerchantAcquiringLedgerByStoreIdAsync(Guid storeId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/store/{storeId}/basicMerchantStoreInfo";

            using (logger.BeginScope("GetMerchantAcquiringLedgerByStoreIdAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to get acquiringLedger by storeId");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to get acquiringLedger by storeId. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<MerchantAcquiringLedgerInfo>(responseBody,
                         new JsonSerializerOptions
                         {
                             PropertyNameCaseInsensitive = true
                         });
            }
        }

        public async Task CreateBusinessHierarchyAsync(Guid parentMerchantId, IReadOnlyCollection<Guid> subordinateMerchantIds)
        {
            var serviceUrl = $"{MerchantServiceBaseUrl}{SubordinateMerchantEndpoint}/createBusinessHierarchy/{parentMerchantId}";
            var requestData = new StringContent(JsonConvert.SerializeObject(subordinateMerchantIds), Encoding.UTF8, "application/json");

            using (logger.BeginScope($"CreateBusinessHierarchyAsync({serviceUrl})"))
            {
                logger.LogInformation("Calling merchant service to create a new business hierarchy for parent merchant with {@id}.", parentMerchantId);

                var response = await client.PostAsync(serviceUrl, requestData);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to create business hierarchy. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                logger.LogInformation("Created a business hierarchy for parent: {parentId} and the following subordinate ids: {childrenIds}", parentMerchantId, subordinateMerchantIds);
            }
        }

        public async Task DeleteBusinessHierarchyAsync(Guid parentMerchantId, Guid subordinateMerchantId)
        {
            var serviceUrl = $"{MerchantServiceBaseUrl}{SubordinateMerchantEndpoint}/deleteBusinessHierarchy/{parentMerchantId}/{subordinateMerchantId}";

            using (logger.BeginScope("DeleteBusinessHierarchyAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to delete the business hierarchy for parent merchant with {@id} and subordinate business with {@subordinateId}.", parentMerchantId, subordinateMerchantId);

                var response = await client.DeleteAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to delete business hierarchy. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
            }
        }

        public async Task<PoiSearchResponse> SearchPersonOfInterest(PoiSearchCriteriaRequest request)
        {
            var serviceUrl = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/Poi/advancedSearch";
            var requestData = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

            using (logger.BeginScope($"SearchPersonOfInteres({serviceUrl})"))
            {
                logger.LogInformation("Calling merchant service to search POIs by criteria.");

                var response = await client.PostAsync(serviceUrl, requestData);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to search POIs by criteria. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
                return Json.Deserialize<PoiSearchResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<List<MerchantExternalContractMappingResponse>> GetMerchantMmsContract(SearchContractMappingFilter filter)
        {
            var url = UrlBuilder.AddQueryParams($"{MerchantServiceBaseUrl}{MerchantExternalContractEndpoint}/search", filter);
            using (logger.BeginScope("Get({@url})", url))
            {
                logger.LogInformation(
                    "GetMerchantMmsContract, Calling merchant service to retrieve contract mappings for merchantId '{merchantId}'.",
                    filter.MerchantId);

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical(
                    "Error when calling 'GetMerchantMmsContract' merchant service to retrieve contract mappings. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<MerchantExternalContractMappingResponse>>(responseBody);
            }
        }

        public async Task<MerchantBusinessInformation[]> GetMerchantBusinessInformationAsync(Guid[] merchantIds)
        {
            string url = $"{MerchantServiceBaseUrl}{MerchantsBusinessInformationEndpoint}";
            var requestBody = new StringContent(JsonConvert.SerializeObject(merchantIds), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetMerchantBusinessInformationAsync({@url})", url))
            {
                logger.LogInformation("Calling merchant service to get merchant business information.");

                var response = await client.PostAsync(url, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to get merchant business information. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<MerchantBusinessInformation[]>(responseBody);
            }
        }
        public async Task<IReadOnlyCollection<MerchantCheck>> GetChecksForMerchant(Guid merchantId)
        {
            var requestUri = $"{MerchantServiceBaseUrl}/{MerchantCheckEndpoint}?merchantId={merchantId}";

            using (logger.BeginScope("GetChecksByMerchantId({@url})", requestUri))
            {
                logger.LogInformation($"Calling merchant service to get merchant checks by merchant id.");

                var response = await client.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogError("Error when calling merchant service. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var merchantCheckResult = Json.Deserialize<List<MerchantCheck>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                return merchantCheckResult;
            }
        }
        public async Task PatchMerchantAsync(Guid merchantId, JsonPatchDocument<PatchMerchantRequest> updateMerchantStatus)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/?merchantId={merchantId}";

            var body = new StringContent(JsonConvert.SerializeObject(updateMerchantStatus), Encoding.UTF8, "application/json");

            using (logger.BeginScope("PatchMerchantAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to patch merchant with id '{merchantId}'.", merchantId);

                var response = await client.PatchAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to patch merchant with id '{merchantId}'. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                logger.LogInformation("Updated merchant with id '{merchantId}'.", merchantId);
            }
        }
        public async Task<List<Merchant>> GetStoresAsync(Guid merchantId)
        {
            var url = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/{merchantId}/Stores";

            using (logger.BeginScope("GetStoresAsync({@url})", url))
            {
                logger.LogInformation("Calling merchant service to get stores of merchant '{merchantId}'.", merchantId);

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service get merchant. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var stores = Json.Deserialize<List<Merchant>>(responseBody);
                logger.LogInformation("Received {numberOfStores} Stores.", stores.Count);

                return stores;
            }
        }
        public async Task<Merchant> GetStoreAsync(Guid companyId, Guid storeId)
        {
            var requestUri = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/{companyId}/store/{storeId}";

            using (logger.BeginScope("GetStoreAsync({uri})", requestUri))
            {
                logger.LogInformation($"Calling merchant service to get store.");

                var response = await client.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to get store. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<Merchant>(responseBody);
            }
        }
        public async Task<MerchantMemberStatusResponse> GetMerchantMemberStatusByMerchantId(Guid merchantId)
        {
            var url = $"{MerchantServiceBaseUrl}{MerchantEndpoint}/{merchantId}/memberId";

            using (logger.BeginScope("GetAllCompanyRolesShortInfoAsync({@url})", url))
            {
                logger.LogInformation("Calling merchant service to get merchant member status for '{companyId}'.", merchantId);

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service GetMerchantMemberStatusByMerchantId. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<MerchantMemberStatusResponse>(responseBody);
            }

        }
        public async Task<MerchantCheckResponseModel> GetAllChecks(Guid merchantId)
        {
            var requestUri = $"{DueDiligenceServiceBaseUrl}/{DueDiligenceFinscanCheckEndpoint}/{merchantId}";

            using (logger.BeginScope("GetAllMerchantChecksInfoAsync({@url})", requestUri))
            {
                logger.LogInformation($"Calling duedilligence service to get merchant checks");

                var response = await client.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling duedilligence service Get. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<MerchantCheckResponseModel>(responseBody);
            }

        }




    }
}