﻿using Common.Models.NexusBridgeLog;
using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NuGet.Configuration;
using System.Threading.Tasks;

namespace BackofficeApi.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class RequestLogController : ControllerBase
{
    private readonly INexusBridgeLogService service;
    private readonly ILogger<RequestLogController> logger;
    public RequestLogController(INexusBridgeLogService service, ILogger<RequestLogController> logger)
    {
        this.service = service;
        this.logger = logger;
    }

    /// <summary>
    /// Search
    /// </summary>
    /// <param name="filters"></param>
    /// <returns></returns>
    [HttpPost("search")]
    public async Task<IActionResult> Search([FromBody] SearchFilters filters)
    {
        logger.LogInformation("Search providers by filters");

        var result = await service.Search(filters);

        return Ok(result);
    }
}