﻿using Common;
using Common.Services;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using BackofficeApi;
using Common.Models.Merchant;
using Common.Models.Merchant.SubordinateMerchant;
using NSubstitute;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Microsoft.Extensions.Logging;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests;

public class SubordinateMerchantServiceTests
{
    private readonly SubordinateMerchantService subordinateMerchantService;
    private readonly IMerchantClient merchantClient;
    private readonly ISearchService searchService;
    private readonly IProductService productService;
    private readonly ILogger<ISubordinateMerchantService> logger;
    private readonly IMapper mapper;

    private static Guid MerchantId = Guid.NewGuid();

    private static SubordinateMerchantSearchRequestDto SubordinateSearchRequest => new()
    {
        Take = 10,
        Skip = 0
    };

    private static SubordinateMerchantSearchFilters SubordinateMerchantSearchFilters => new()
    {
        Take = 10,
        Skip = 0
    };

    [SetUp]
    public void Setup()
    {
        searchService.ClearReceivedCalls();
        merchantClient.ClearReceivedCalls();
    }

    public SubordinateMerchantServiceTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        mapper = new Mapper(configuration);
        merchantClient = Substitute.For<IMerchantClient>();
        searchService = Substitute.For<ISearchService>();
        productService = Substitute.For<IProductService>();
        logger = Substitute.For<ILogger<SubordinateMerchantService>>();
        subordinateMerchantService = new SubordinateMerchantService(mapper, logger, merchantClient, searchService, productService);
    }

    [Test]
    public async Task GetMerchantTagForSubordinateMerchantSearch_WhenTagIsMasterbusiness_ShouldReturnTheCorrectSubBusinessAndPerformSearch()
    {
        var merchantId = Guid.NewGuid();
        var merchant = TestsHelper.GetCoreMerchantWithHierarchy(MerchantId);
        merchant.Tag = Constants.MerchantTag.MasterBusiness;

        merchantClient.GetCoreMerchantWithHierarchy(merchantId)
            .Returns(Task.FromResult(merchant));

        Assert.DoesNotThrowAsync(async () => await subordinateMerchantService.FindAssociatedAndAvailableSubordinateMerchants(merchantId, SubordinateSearchRequest));
        await searchService.Received(1).SearchSubordinateMerchants(Arg.Any<SubordinateMerchantSearchFilters>());
    }


    [Test]
    public async Task GetMerchantTagForSubordinateMerchantSearch_WhenReturnedTagIsNotValidForSearch_ShouldThrowBadRequest()
    {
        var merchantId = Guid.NewGuid();

        var merchant = TestsHelper.GetCoreMerchantWithHierarchy(MerchantId);
        merchant.Tag = Constants.MerchantTag.Retail;

        merchantClient.GetCoreMerchantWithHierarchy(merchantId)
            .Returns(Task.FromResult(merchant));

        await subordinateMerchantService
            .Invoking(x => x.FindAssociatedSubordinateMerchants(merchantId, SubordinateSearchRequest))
            .Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                        && x.ProblemDetails.Type == Errors.InvalidMerchantTagForSearch.Code);
    }

    [Test]
    public async Task GetMerchantTagForSubordinateMerchantSearch_WhenTagIsWholesaler_ShouldReturnTheCorrectSubWholesalerAndPerformSearch()
    {
        var merchantId = Guid.NewGuid();
        var merchant = TestsHelper.GetCoreMerchantWithHierarchy(MerchantId);
        merchant.Tag = Constants.MerchantTag.Wholesaler;

        merchantClient.GetCoreMerchantWithHierarchy(merchantId)
            .Returns(merchant);
        searchService.SearchSubordinateMerchants(SubordinateMerchantSearchFilters)
            .Returns(Task.FromResult(new SubordinateMerchantSearchResponse() { }));

        Assert.DoesNotThrowAsync(async () => await subordinateMerchantService.FindAssociatedAndAvailableSubordinateMerchants(merchantId, SubordinateSearchRequest));
        await searchService.Received(1).SearchSubordinateMerchants(Arg.Any<SubordinateMerchantSearchFilters>());
    }

    [Test]
    public async Task PerformValidationForUpdatingMerchantTag_MerchantHasStatusVerified_ShouldThrowException()
    {
        var merchantId = Guid.NewGuid();
        var merchant = TestsHelper.GetCoreMerchantWithHierarchy(merchantId);
        merchant.MerchantStatus = Constants.MerchantStatus.Verified;

        merchantClient.GetMerchantWithAllHierarchies(merchantId)
            .Returns(merchant);

        await subordinateMerchantService
            .Invoking(x => x.PerformValidationForUpdatingMerchantTag(merchantId))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.UpdateMerchantTagErrorVerified.Code));
    }

    [Test]
    public async Task PerformValidationForUpdatingMerchantTag_MerchantSentToGle_ShouldThrowException()
    {
        var merchantId = Guid.NewGuid();
        var merchant = TestsHelper.GetCoreMerchantWithHierarchy(merchantId);
        merchant.MerchantStatus = Constants.MerchantStatus.BoardingInProgress;

        merchantClient.GetMerchantWithAllHierarchies(merchantId)
            .Returns(merchant);
        productService.IsMerchantRegisteredInGle(merchantId)
            .Returns(true);

        await subordinateMerchantService
            .Invoking(x => x.PerformValidationForUpdatingMerchantTag(merchantId))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.UpdateMerchantTagErrorBillPaymentsSent.Code));
    }

    // Merchant Has associations
    [Test]
    public async Task PerformValidationForUpdatingMerchantTag_MerchantHasBusinessTagAndAssociations_ShouldThrowException()
    {
        var merchantId = Guid.NewGuid();
        var merchant = TestsHelper.GetCoreMerchantWithHierarchy(merchantId);
        merchant.MerchantStatus = Constants.MerchantStatus.BoardingInProgress;
        merchant.Tag = Constants.MerchantTag.SubBusiness;

        merchantClient.GetMerchantWithAllHierarchies(merchantId)
            .Returns(merchant);
        productService.IsMerchantRegisteredInGle(merchantId)
            .Returns(false);

        await subordinateMerchantService
            .Invoking(x => x.PerformValidationForUpdatingMerchantTag(merchantId))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.UpdateMerchantTagErrorActiveAssociation.Code));
    }

    [Test]
    public void
        PerformValidationForUpdatingMerchantTag_MerchantHasBusinessTagAndNoBusinessAssociations_ShouldNotThrowException()
    {
        var merchantId = Guid.NewGuid();
        var merchant = TestsHelper.GetCoreMerchantWithHierarchy(merchantId);
        merchant.MerchantStatus = Constants.MerchantStatus.BoardingInProgress;
        merchant.Tag = Constants.MerchantTag.SubBusiness;
        merchant.Hierarchies = new List<MerchantHierarchy>()
        {
            new MerchantHierarchy()
            {
                HierarchyId = Guid.NewGuid(),
                HierarchyType = Constants.HierarchyType.Structural,
                MerchantId = Guid.NewGuid(),
                ParentMerchantId = merchantId,
                TopLevelMerchantId = merchantId
            }
        };

        merchantClient.GetMerchantWithAllHierarchies(merchantId)
            .Returns(merchant);
        productService.IsMerchantRegisteredInGle(merchantId)
            .Returns(false);

        Assert.DoesNotThrowAsync(async () => await subordinateMerchantService.PerformValidationForUpdatingMerchantTag(merchantId));
    }

    [Test]
    [TestCase(Constants.MerchantTag.MasterBusiness, Constants.MerchantTag.SubBusiness)]
    [TestCase(Constants.MerchantTag.Wholesaler, Constants.MerchantTag.SubWholesaler)]
    public async Task CreateBusinessHierarchyAsync_WhenValidRequest_ShouldCallService(string parentTag, string childTag)
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessIds = new List<Guid>() { Guid.NewGuid(), Guid.NewGuid() };

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy { Tag = parentTag });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.First()).Returns(new CoreMerchantWithHierarchy { Tag = childTag });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.Last()).Returns(new CoreMerchantWithHierarchy { Tag = childTag });
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(false);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds))
            .Should()
            .NotThrowAsync();

        await merchantClient.Received(1).CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds);
    }

    [Test]
    public async Task CreateBusinessHierarchyAsync_WhenSentToGLE_ShouldThrowValidationException()
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessIds = new List<Guid>() { Guid.NewGuid(), Guid.NewGuid() };

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.MasterBusiness });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.First()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.Last()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(true);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.UpdateMerchantTagErrorBillPaymentsSent.Code));

        await merchantClient.DidNotReceive().CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds);
    }

    [Test]
    public async Task CreateBusinessHierarchyAsync_WhenAlreadyPartOfTheHierarchy_ShouldThrowValidationException()
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessIds = new List<Guid>() { Guid.NewGuid(), Guid.NewGuid() };

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy
        {
            Tag = Constants.MerchantTag.Wholesaler,
            Hierarchies = new List<MerchantHierarchy>
            {
                new()
                {
                    MerchantId = subordinateBusinessIds.First(),
                    HierarchyType = Constants.HierarchyType.Business
                }
            }
        });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.First()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubWholesaler });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.Last()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubWholesaler });
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(false);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.SubordinateBusinessAlreadyLinkedToParent.Code));

        await merchantClient.DidNotReceive().CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds);
    }

    [Test]
    public async Task CreateBusinessHierarchyAsync_WhenInvalidParentTag_ShouldThrowValidationException()
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessIds = new List<Guid>() { Guid.NewGuid(), Guid.NewGuid() };

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.First()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.Last()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(false);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.InvalidParentBusinessTag.Code));

        await merchantClient.DidNotReceive().CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds);
    }

    [Test]
    public async Task CreateBusinessHierarchyAsync_WhenInvalidChildTag_ShouldThrowValidationException()
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessIds = new List<Guid>() { Guid.NewGuid(), Guid.NewGuid() };

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.Wholesaler });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.First()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.Last()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(false);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.InvalidSubordinateBusinessTag.Code));

        await merchantClient.DidNotReceive().CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds);
    }

    [Test]
    public async Task CreateBusinessHierarchyAsync_WhenChildHasHierarchy_ShouldThrowValidationException()
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessIds = new List<Guid>() { Guid.NewGuid(), Guid.NewGuid() };

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.MasterBusiness });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.First()).Returns(new CoreMerchantWithHierarchy { Tag = Constants.MerchantTag.SubBusiness });
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessIds.Last()).Returns(
            new CoreMerchantWithHierarchy
            {
                Tag = Constants.MerchantTag.SubBusiness,
                Hierarchies = new List<MerchantHierarchy>
                {
                    new()
                    {
                        TopLevelMerchantId = Guid.NewGuid(),
                        HierarchyType = Constants.HierarchyType.Business
                    }
                }
            });
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(false);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.UpdateMerchantTagErrorActiveAssociation.Code));

        await merchantClient.DidNotReceive().CreateBusinessHierarchyAsync(parentMerchantId, subordinateBusinessIds);
    }

    [Test]
    public async Task DeleteBusinessHierarchyAsync_WhenSentToGLE_ShouldThrowValidationException()
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessId = Guid.NewGuid();

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy());
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessId).Returns(new CoreMerchantWithHierarchy());
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(true);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.DeleteBusinessHierarchyAsync(parentMerchantId, subordinateBusinessId))
            .Should()
            .ThrowAsync<ValidationException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                        TestsHelper.HasErrorCode(x, Errors.UpdateMerchantTagErrorBillPaymentsSent.Code));

        await merchantClient.DidNotReceive().DeleteBusinessHierarchyAsync(parentMerchantId, subordinateBusinessId);
    }

    [Test]
    public async Task DeleteBusinessHierarchyAsync_WhenValidRequest_ShouldCallService()
    {
        //Arrange
        var parentMerchantId = Guid.NewGuid();
        var subordinateBusinessId = Guid.NewGuid();

        merchantClient.GetMerchantWithAllHierarchies(parentMerchantId).Returns(new CoreMerchantWithHierarchy());
        merchantClient.GetMerchantWithAllHierarchies(subordinateBusinessId).Returns(new CoreMerchantWithHierarchy());
        productService.IsMerchantRegisteredInGle(Arg.Any<Guid>()).Returns(false);

        //Act & Assert
        await subordinateMerchantService
            .Invoking(x => x.DeleteBusinessHierarchyAsync(parentMerchantId, subordinateBusinessId))
            .Should()
            .NotThrowAsync();

        await merchantClient.Received(1).DeleteBusinessHierarchyAsync(parentMerchantId, subordinateBusinessId);
    }
}