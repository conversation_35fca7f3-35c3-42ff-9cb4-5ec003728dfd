﻿using Geidea.Utils.ReferenceData;
using System;

namespace Common.Models.Checkout
{
    public class OrderUpdateRequest
    {
        public Guid AgreementId { get; set; }

        public Guid MerchantId { get; set; }

        public Guid UserId { get; set; }

        public Guid? AddressId { get; set; }

        public string? PaymentReference { get; set; }

        public string? TrackingNumber { get; set; }

        public string? TrackingUrl { get; set; }

        public string? Shipper { get; set; }

        public DateTime? ShippedDate { get; set; }

        public string? CouponCode { get; set; }

        [ReferenceData("PAYMENT_METHOD")]
        public string? PaymentMethod { get; set; }

        public string? CompanyRegNo { get; set; }

        public int? Amount { get; set; }

        public string? SalesName { get; set; }

        public string? SubscriptionPlan { get; set; }

        [ReferenceData("PROJECT_NAME")]
        public string? ProjectName { get; set; }

        public string? Note { get; set; }

        [ReferenceData("ORDER_STATUS")]
        public string OrderStatus { get; set; } = null!;

        public string MerchantName { get; set; } = null!;

        [ReferenceData("CURRENCY")]
        public string? Currency { get; set; }
    }
}
