﻿using Geidea.Utils.ReferenceData;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;

namespace Common.Models.Lead
{
    public class LeadCreateRequest
    {
        [ReferenceData("LEAD_STATUS")]
        public string LeadStatus { get; set; } = Constants.LeadStatus.New;
        public string? NationalId { get; set; }
        public string? BusinessDomain { get; set; }
        public string PhoneNumber { get; set; } = null!;
        public string CountryPrefix { get; set; } = null!;
        public string PhoneWithPrefix => CountryPrefix + PhoneNumber;
        public string? OwnerFirstName { get; set; }
        public string? OwnerLastName { get; set; }
        public string? OwnerFirstNameAr { get; set; }
        public string? OwnerLastNameAr { get; set; }
        public string? OwnerEmail { get; set; }
        public string? LegalName { get; set; }
        public string? LegalNameAr { get; set; }
        public string? AddressLine { get; set; }
        public string? City { get; set; }
        public string? Governorate { get; set; }
        [ReferenceData("AREAS")]
        public string? Area { get; set; }
        public string? Country { get; set; }
        public string? UTM { get; set; }
        public string? CRMLeadId { get; set; }
        public string? TahakomTransactionId { get; set; }
        public string? SalesId { get; set; }
        public string? SalesPartnerId { get; set; }
        public string? Nationality { get; set; }
        public string? Gender { get; set; }
        [ReferenceData("REFERRAL_CHANNEL")]
        public string? ReferralChannel { get; set; }
        public DateTimeOffset? DOB { get; set; }
        public bool ElmCheck { get; set; } = false;
        public LeadDetails LeadDetails { get; set; } = new LeadDetails();
        public List<Guid> LeadProductIds { get; set; } = new List<Guid>();
        public List<IFormFile> BankDocuments { get; set; } = new List<IFormFile>();
        public List<IFormFile> GeneralContractDocuments { get; set; } = new List<IFormFile>();
        public List<IFormFile> MunicipalityDocuments { get; set; } = new List<IFormFile>();
        public List<IFormFile> LegalEnterpriseDocuments { get; set; } = new List<IFormFile>();
        public List<IFormFile> NationalIdDocuments { get; set; } = new List<IFormFile>();
        public List<IFormFile> CommercialRegistrationDocuments { get; set; } = new List<IFormFile>();
        public List<IFormFile> ShopPhotoDocuments { get; set; } = new List<IFormFile>();
        public List<IFormFile> AdditionalDocuments { get; set; } = new List<IFormFile>();
    }
}
