﻿using System.Collections.Generic;
using System.Threading.Tasks;
using BackofficeApi.Controllers;
using Common.Models.Gsdk;
using Common.Services;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using NUnit.Framework;

namespace Geidea.BackofficePortal.Backoffice.Api.Tests
{
    public class GsdkControllerTest
    {
        private IGsdkMerchantMappingService service = null!;
        private GsdkController controller = null!;

        private readonly List<GsdkMappingResponse> notMappedMerchants = new List<GsdkMappingResponse>();

        [SetUp]
        public void Setup()
        {
            service = Substitute.For<IGsdkMerchantMappingService>();
            service.MapExistingMerchants().Returns(notMappedMerchants);

            controller = new GsdkController(service);
        }

        [Test]
        public async Task MapExistingMerchantsSuccessfully()
        {
            var response = await controller.MapExistingMerchants();

            var result = response as OkObjectResult;

            Assert.That(result?.Value, Is.EqualTo(notMappedMerchants), "Result is not correct");
            await service.Received(1).MapExistingMerchants();
        }
    }
}