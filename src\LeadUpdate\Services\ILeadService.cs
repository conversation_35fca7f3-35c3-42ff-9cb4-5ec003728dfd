﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LeadUpdate.Models;
using Microsoft.AspNetCore.JsonPatch;

namespace LeadUpdate.Services
{
    public interface ILeadService
    {
        Task<IList<Lead>> GetIncompleteLeadsAsync(int skip, int take, LeadUpdateType leadUpdateType, string sort);
        Task PatchLeadAsync(Guid leadId, JsonPatchDocument<Lead> patchDocument);
        Task<int> GetLeadsCountAsync();
    }
}
