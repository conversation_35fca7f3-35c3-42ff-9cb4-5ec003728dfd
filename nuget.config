﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
   <packageRestore>
        <!-- Allow NuGet to download missing packages -->
        <add key="enabled" value="True" />

        <!-- Automatically check for missing packages during build in Visual Studio -->
        <add key="automatic" value="True" />
    </packageRestore>

    <packageSources>
        <add key="NuGet Gallery" value="https://api.nuget.org/v3/index.json" />
		<add key="Geidea Azure Feed" value="https://pkgs.dev.azure.com/GeideaPaymentGateway/_packaging/GeideaPaymentGateway/nuget/v3/index.json" />
    </packageSources>
</configuration>