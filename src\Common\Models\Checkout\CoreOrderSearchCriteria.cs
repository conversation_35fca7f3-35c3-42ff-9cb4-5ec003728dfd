﻿using System;
using System.Collections.Generic;

namespace Common.Models.Checkout
{
    public class CoreOrderSearchCriteria : BaseSearchCriteria
    {
        public string? Keyword { get; set; }
        public List<string> SearchIn { get; set; } = new List<string>();
        public List<string> OrderStatus { get; set; } = new List<string>();
        public List<string> MerchantStatus { get; set; } = new List<string>();
        public List<AmountInterval> AmountIntervals { get; set; } = new List<AmountInterval>();
        public List<string> AcquiringBanks { get; set; } = new List<string>();
        public string? TerminalTID { get; set; }
        public DateInterval? DateInterval { get; set; }
        public Guid? MerchantId { get; set; }
        public Guid? OrderId { get; set; }
        public string? SalesId { get; set; }
        public List<Guid> StoreIds { get; set; } = new();
        public List<string> ProjectName { get; set; } = new List<string>();
        public List<Guid> ProductIds { get; set; } = new List<Guid>();
        public List<string> ProductCodes { get; set; } = new List<string>();
        public List<bool?> ProofOfDeliveries { get; set; } = new();
        public List<string> CityCodes { get; set; } = new List<string> { };
        public string? MID { get; set; }

    }
}
