﻿using Geidea.Utils.ReferenceData;
using System;
using System.Collections.Generic;

namespace Common.Models.Lead
{
    public class Lead
    {
        public Guid LeadId { get; set; }
        [ReferenceData("LEAD_STATUS")]
        public string? LeadStatus { get; set; }
        [ReferenceData("BUSINESS_DOMAINS")]
        public string? BusinessDomain { get; set; }
        public string? PhoneNumber { get; set; }
        public string? CountryPrefix { get; set; }
        public string? PhoneWithPrefix => CountryPrefix + PhoneNumber;
        public string? OwnerFirstName { get; set; }
        public string? OwnerLastName { get; set; }
        public string? OwnerFirstNameAr { get; set; }
        public string? OwnerLastNameAr { get; set; }
        public string? OwnerEmail { get; set; }
        public string? LegalName { get; set; }
        public string? LegalNameAr { get; set; }
        public string? NationalId { get; set; }
        public string? AddressLine { get; set; }
        [ReferenceData("CITIES")]
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? UTM { get; set; }
        public string? SalesId { get; set; }
        public string? SalesPartnerId { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? Nationality { get; set; }
        public string? Gender { get; set; }
        public DateTimeOffset? DOB { get; set; }
        [ReferenceData("REFERRAL_CHANNEL")]
        public string? ReferralChannel { get; set; }
        public bool ElmCheck { get; set; }
        public List<LeadProduct> LeadProducts { get; set; } = new List<LeadProduct>();
        public LeadDetails? LeadDetails { get; set; }
    }
}
