﻿namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    using System;
    using Microsoft.AspNetCore.Http;
    using NSubstitute;
    using GeideaPaymentGateway.Utils.CommonModels;

    public class MessageClientTests
    {
        protected static IHttpContextAccessor MockHttpContextAccessor(Guid correlationId)
        {
            var context = new DefaultHttpContext();
            context.Request.Headers.Append(Constants.CorrelationIdHeaderName, correlationId.ToString());

            var httpContextAccessor = Substitute.For<IHttpContextAccessor>();
            httpContextAccessor.HttpContext.Returns(context);

            return httpContextAccessor;
        }
    }
}
