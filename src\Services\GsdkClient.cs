﻿using Common;
using Common.Models.Gsdk;
using Common.Options;
using Common.Services;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Services
{
    public class GsdkClient : IGsdkClient
    {
        private readonly ILogger<GsdkClient> logger;
        private readonly HttpClient httpClient;
        private readonly IKeycloakClient keycloakClient;
        private readonly ICounterpartyProvider counterpartyProvider;

        private readonly string GsdkBaseUrl;
        private const string GlobalContractsEndpoint = "/api/v1/management/global-contracts/view";
        private readonly JsonSerializerOptions jsonSerializerOptions = new JsonSerializerOptions
        {
            DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new JsonStringEnumConverter() }
        };

        public GsdkClient(ILogger<GsdkClient> logger,
              HttpClient httpClient,
              IKeycloakClient keycloakClient,
            IOptionsMonitor<GsdkSettings> gsdkSettings,
            ICounterpartyProvider counterpartyProvide)
        {
            this.logger = logger;
            this.httpClient = httpClient;
            this.keycloakClient = keycloakClient;
            this.counterpartyProvider = counterpartyProvide;
            var gsdkEnvironmentSetting = gsdkSettings.CurrentValue.IsTest
                ? gsdkSettings.CurrentValue.Test
                : gsdkSettings.CurrentValue.Live;

            GsdkBaseUrl = counterpartyProvide.GetCode() switch
            {
                Constants.CounterParty.Egypt =>
                    gsdkEnvironmentSetting.EgyptBaseUrl,
                Constants.CounterParty.Saudi =>
                    gsdkEnvironmentSetting.BaseUrl,
                _ => string.Empty
            };
        }

        public async Task<List<GlobalContractDto>> FindGlobalContracts(GlobalContractsFilter filter, string? ledger, string? counterparty = null)
        {
            var url = $"{GsdkBaseUrl}{GlobalContractsEndpoint}";

            using (logger.BeginScope("{name}({@url})", nameof(FindGlobalContracts), url))
            {
                logger.LogInformation("Calling GSDK external service to search after global contracts.");
                var requestData = JsonSerializer.Serialize(new GlobalContractsFilterDto { Filter = filter }, options: jsonSerializerOptions);

                var request = new HttpRequestMessage
                {
                    RequestUri = new Uri(url),
                    Method = HttpMethod.Post,
                    Content = new StringContent(requestData, Encoding.UTF8, MediaTypeNames.Application.Json)
                };

                request.Headers.Add(Constants.GsdkHeaders.LedgerHeaderName, GetLedgerHeaderValue(ledger));
                request.Headers.Add(Constants.GsdkHeaders.AuthorizationHeaderName, await keycloakClient.GetKeycloakToken());

                var response = await httpClient.SendAsync(request);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical(
                        "Error when calling GSDK external service to search after global contracts. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var responseDto = Json.Deserialize<GlobalContractResponseDto>(responseBody);
                return responseDto.Records;
            }

        }

        private string GetLedgerHeaderValue(string? ledger)
        {
            if (counterpartyProvider.GetCode() == Constants.CounterParty.Egypt)
                return Constants.GsdkLedgers.EgyptDefault;

            if (counterpartyProvider.GetCode() == Constants.CounterParty.Saudi)
            {
                if (ledger == Constants.MmsLedgers.SaudiDefault)
                    return Constants.GsdkLedgers.SaudiDefault;
                else if (ledger == Constants.MmsLedgers.SaudiSABB)
                    return Constants.GsdkLedgers.SaudiSABB;
            }

            return String.Empty;
        }

    }
}
