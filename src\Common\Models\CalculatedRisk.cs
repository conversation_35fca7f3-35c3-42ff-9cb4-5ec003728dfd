﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models
{
    public class CalculatedRisk
    {
        public string BusinessId { get; set; } = string.Empty;
        public string MerchantStatus { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string Counterparty { get; set; } = string.Empty;
        public ComplianceRisks? ComplianceRisk { get; set; }
        public AcquiringRisks? AcquiringRisk { get; set; }
    }

    public class ComplianceRisks
    {
        public string RiskLevel { get; set; } = string.Empty;
        public string RiskScore { get; set; } = string.Empty;
        public string DueDiligenceLevel { get; set; } = string.Empty;
        public ComplianceRiskdescription? complianceRiskdescription { get; set; }

    }
    public class AcquiringRisks
    {
        public string BusinessID { get; set; } = string.Empty;
        public string AggregateRiskLevel { get; set; } = string.Empty;
        public string OfficeType { get; set; } = string.Empty;
        public string MerchantLocation { get; set; } = string.Empty;
        public string ServiceDelivery { get; set; } = string.Empty;
        public List<TransactionDetails> TransactionDetails { get; set; } = new List<TransactionDetails>();
        public string HighestSingleTransactionValue { get; set; } = string.Empty;
        public string MCCRisk { get; set; } = string.Empty;
    }
    public class ComplianceRiskdescription
    {
        public ComplianceRiskParameterRiskDetail? CompanyLegalType { get; set; }
        public ComplianceRiskParameterRiskDetail? HighestSingleTransaction { get; set; }
        public ComplianceRiskParameterRiskDetail? MaxMonthlyTransaction { get; set; }
        public ComplianceRiskParameterRiskDetail? MCC { get; set; }
        public ComplianceRiskParameterRiskDetail? Shareholdersnationality { get; set; }
        public ComplianceRiskParameterRiskDetail? ShareholdersPEP { get; set; }
    }
    public class ComplianceRiskParameterRiskDetail
    {
        public string? Value { get; set; }
        public int? Score { get; set; }
    }
}
