﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Geidea.Utils.ConditionalSerialization;
using UtilsConstants = Geidea.Utils.Common.Constants;

namespace Common.Models.Merchant
{
    [ExcludeFromCodeCoverage]
    public class MerchantSearchFilters : BaseSearchCriteria
    {
        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public List<string> BusinessDomain { get; set; } = new List<string>();
        public List<string>? AcquiringLedgers { get; set; }

        public decimal? AnnualTurnover { get; set; }

        public string? OutletType { get; set; }

        public List<string> MerchantStatus { get; set; } = new List<string> { };

        public List<string>? ProductCodes { get; set; } = new();

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public List<string>? ReferralChannels { get; set; } = new();

        public DateInterval? DateInterval { get; set; }

        public string? MemberId { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public List<string> Tag { get; set; } = new List<string> { };

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? SalesPartnerId { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public List<string>? SalesPartnerIds { get; set; }

        public List<string> AcquirerReview { get; set; } = new List<string> { };

        public Guid? LeadId { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public string? Keyword { get; set; }

        public string[]? SearchIn { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? SalesId { get; set; }
        public string? AcquiringLedger { get; set; }
        public int? RefBankId { get; set; }

        public Guid? MerchantId { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public List<string>? SalesIds { get; set; }

        public List<string> BusinessType { get; set; } = new List<string>();

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public List<string> BankCheckStatus { get; set; } = new List<string>();

        public bool AfterUpdate { get; set; } = false;
        public bool IsSalesTeam { get; set; } = false;
        public List<string>? DesignationSalesId { get; set; }
    }
}
