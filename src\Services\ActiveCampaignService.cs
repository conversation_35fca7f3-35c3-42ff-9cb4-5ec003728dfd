﻿using Common.Models;
using Common.Services;
using Services.Messaging;

namespace Services
{
    public class ActiveCampaignService : IActiveCampaignService
    {
        private readonly ActiveCampaignMessagingClient activeCampaignMessagingClient;

        public ActiveCampaignService(ActiveCampaignMessagingClient activeCampaignMessagingClient)
        {
            this.activeCampaignMessagingClient = activeCampaignMessagingClient;
        }

        public void SendActiveCampaignRequest(CreateActiveCampaignRequest activeCampaignRequest)
        {
            activeCampaignMessagingClient.SendMessageToActiveCampaign(activeCampaignRequest);
        }
    }
}
