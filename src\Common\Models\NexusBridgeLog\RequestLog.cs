﻿using System;
namespace Common.Models.NexusBridgeLog;
public class RequestLog
{
    public Guid CorrelationId { get; set; }
    public Guid ParentCorrelationId { get; set; }
    public string RequestId { get; set; } = string.Empty;
    public Guid ProviderId { get; set; }

    public string RequestMetaData { get; set; } = string.Empty;
    public string ResponseMetaData { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime EntryDate { get; set; }
    public string Entity { get; set; } = string.Empty;
    public Guid UserId { get; set; }
    public string RequestType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
}