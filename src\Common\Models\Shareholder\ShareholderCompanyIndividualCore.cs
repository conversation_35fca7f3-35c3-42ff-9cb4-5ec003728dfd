﻿using System;
namespace Common.Models.Shareholder;
public class ShareholderCompanyIndividualCore
{
    public Guid PersonOfInterestId { get; set; }
    public Guid ShareholderCompanyId { get; set; }
    public string NationalId { get; set; } = string.Empty;
    public string? Nationality { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? FirstNameAr { get; set; }
    public string? LastNameAr { get; set; }
    public DateTime? IdExpiryDate { get; set; }
    public DateTime? DOB { get; set; }
    public string? OrganizationRole { get; set; }
    public string ShareholderCompanyName { get; set; } = null!;
}