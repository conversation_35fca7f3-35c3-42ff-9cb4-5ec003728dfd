﻿using Common;
using Common.Models;
using Common.Models.Document;
using Common.Options;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class DocumentClientTests
    {
        private readonly Mock<ILogger<DocumentClient>> logger = new Mock<ILogger<DocumentClient>>();
        private DocumentClient documentClient = null!;
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();

        public DocumentClientTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        }

        private static readonly Guid merchantId = Guid.NewGuid();
        private static IFormFile file = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy file")), 0, 0, "Data", "dummy.txt");

        private readonly DocumentRequest documentRequest = new DocumentRequest
        {
            DocumentType = "txt",
            Language = "en",
            LeadId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            OwnerUserId = Guid.NewGuid(),
            PersonOfInterestId = Guid.NewGuid(),
            ProviderId = Guid.NewGuid(),
            File = file
        };

        private readonly DocumentMetadata documentMetadata = new DocumentMetadata()
        {
            Id = Guid.NewGuid(),
            StreamId = Guid.NewGuid(),
            OwnerUserId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            PersonOfInterestId = Guid.NewGuid(),
            DocumentType = "DocumentType",
            Language = "Language",
            ProvidedBy = "ProvidedBy",
            MimeType = "MimeType",
            Uri = "Uri",
            Name = "Name",
            CreatedBy = "CreatedBy",
            UpdatedBy = "UpdatedBy",
            Version = 1,
            Size = 1,
            IsDeleted = false,
            CreatedDateUtc = DateTime.UtcNow,
            UpdatedDateUtc = DateTime.UtcNow
        };
        private readonly DocumentWithContent[] documentsWithContent = new DocumentWithContent[]
        {
                new DocumentWithContent
                {
                    MerchantId = merchantId,
                    DocumentType = Constants.DocumentType.GeneralContract,
                    FileName = "test.txt",
                    Content = Encoding.UTF8.GetBytes("{"+
                               "\"crName\":\"test name\","+
                               "\"parties\":["+
                                  "{"+
                                     "\"identity\":{"+
                                                    "\"id\":\"1010454206\","+
                                                    "\"type\":\"crno\""+
                                                   "}"+
                                    "}"+
                                   "],"+
                               "\"address\":{"+
                                            "\"general\":{"+
                                                "\"address\":\"test address 112\""+
                                  "}"+
                               "},"+
                               "\"location\":{"+
                                            "\"id\":\"1010\","+
                                            "\"name\":\"location name\""+
                                    "}"+
                               "}")
                },
                new DocumentWithContent
                {
                    MerchantId = merchantId,
                    DocumentType = "TEST",
                    FileName = "test2.txt",
                    Content = new byte[1000]
                },
                new DocumentWithContent
                {
                    MerchantId = Guid.NewGuid(),
                    FileName = "test3.txt",
                    Content = Encoding.UTF8.GetBytes("{"+
                               "\"user_info\":["+
                                  "{"+
                                     "\"arabic_name\":\"arabic name test\""+
                                    "}"+
                                   "]"+
                                    "}")
                }
        };

        [Test]
        public void CreateDocumentAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            documentClient = new DocumentClient(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            documentClient.Invoking(x => x.CreateDocumentAsync(documentRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task CreateDocumentAsync_WhenCoreServiceReturnsMetadata_returnsMetadata()
        {
            documentClient = new DocumentClient(
            logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentMetadata)));

            var result = await documentClient.CreateDocumentAsync(documentRequest);
            result.Should().BeEquivalentTo(documentMetadata);
        }
    }
}
