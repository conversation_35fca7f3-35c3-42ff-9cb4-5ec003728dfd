﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Merchant;
using Common.Models.Shareholder;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Services
{
    public interface IMerchantClient
    {
        Task<Merchant> GetCoreMerchantAsync(Guid merchantId);
        Task<CoreMerchantWithHierarchy> GetCoreMerchantWithHierarchy(Guid merchantId);
        Task<CoreMerchantWithHierarchy> GetMerchantWithAllHierarchies(Guid merchantId);
        Task<MerchantEPosInformation> GetMerchantEPosInformationAsync(Guid merchantId);
        Task<MerchantAcquiringLedgerInfo> GetMerchantAcquiringLedgerByStoreIdAsync(Guid storeId);
        Task CreateBusinessHierarchyAsync(Guid parentMerchantId, IReadOnlyCollection<Guid> subordinateMerchantIds);
        Task DeleteBusinessHierarchyAsync(Guid parentMerchantId, Guid subordinateMerchantId);
        Task<PoiSearchResponse> SearchPersonOfInterest(PoiSearchCriteriaRequest request);
        Task<string> GetShareholderCompaniesBase(ShareholderCompaniesRequest shareholderCompaniesRequest, bool isFilteredSearch = false);
        Task CreateShareholderIndividualAssociations(ShareholderIndividualAssociationsCreateRequest request);
        Task<List<MerchantExternalContractMappingResponse>> GetMerchantMmsContract(SearchContractMappingFilter filter);
        Task<MerchantBusinessInformation[]> GetMerchantBusinessInformationAsync(Guid[] merchantIds);
        Task<IReadOnlyCollection<MerchantCheck>> GetChecksForMerchant(Guid merchantId);
        Task PatchMerchantAsync(Guid merchantId, JsonPatchDocument<PatchMerchantRequest> updateMerchantStatus);
        Task<List<Merchant>> GetStoresAsync(Guid merchantId);
        Task<Merchant> GetStoreAsync(Guid companyId, Guid storeId);
    }
}