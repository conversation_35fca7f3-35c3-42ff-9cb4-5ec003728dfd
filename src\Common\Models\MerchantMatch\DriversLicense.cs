﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class DriversLicense
{
    [Display(Name = "Number")]
    public string Number { get; set; } = string.Empty;

    [Display(Name = "Country Sub Division")]
    public string CountrySubDivision { get; set; } = string.Empty;

    [Display(Name = "Country")]
    public string Country { get; set; } = string.Empty;

    public override string ToString()
    {
        return $"{Environment.NewLine}Country: '{Country}'," +
            $"{Environment.NewLine}Country Sub Division: '{CountrySubDivision}'," +
            $"{Environment.NewLine}Number: '{Number}'.";
    }
}