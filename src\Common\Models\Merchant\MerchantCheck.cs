﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Merchant
{
    public class MerchantCheck
    {
        public Guid MerchantCheckId { get; set; }

        public Guid MerchantId { get; set; }

        public string CheckType { get; set; } = string.Empty;

        public DateTime CheckDate { get; set; }

        public string CheckStatus { get; set; } = string.Empty;

        public int CheckScore { get; set; }

        public int CheckProvider { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime? ValidTo { get; set; }

        public ICollection<CheckComment> CheckComments { get; set; } = new List<CheckComment>();

        public ICollection<MerchantCheckPayload> MerchantCheckPayloads { get; set; } = new List<MerchantCheckPayload>();
    }
}
