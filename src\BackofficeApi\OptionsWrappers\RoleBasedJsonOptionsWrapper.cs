﻿using System;
using Geidea.Utils.ConditionalSerialization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BackofficeApi.OptionsWrappers
{
    public class RoleBasedJsonOptionsWrapper : IConfigureOptions<MvcNewtonsoftJsonOptions>
    {
        private readonly IServiceProvider serviceProvider;
        private readonly ILogger<RoleBasedJsonOptionsWrapper> logger;

        public RoleBasedJsonOptionsWrapper(IServiceProvider serviceProvider, ILogger<RoleBasedJsonOptionsWrapper> logger)
        {
            this.serviceProvider = serviceProvider;
            this.logger = logger;
        }

        public void Configure(MvcNewtonsoftJsonOptions options)
        {
            options.SerializerSettings.ContractResolver = new RoleBasedContractResolver(serviceProvider, logger);
        }
    }
}
