﻿using Common;
using Common.Models;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Geidea.Utils.Security.KeyCloak;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Services
{
    public class KeycloakClient : IKeycloakClient
    {
        private readonly HttpClient httpClient;
        private GsdkSettings gsdkSettings;
        private KeycloakOptions keycloakOptions;
        private readonly ILogger<KeycloakClient> logger;

        public KeycloakClient(
            HttpClient httpClient,
            IOptionsMonitor<GsdkSettings> gsdkOptions,
            IOptionsMonitor<KeycloakOptions> keycloakOptions,
            ILogger<KeycloakClient> logger)
        {
            this.httpClient = httpClient;
            this.gsdkSettings = gsdkOptions.CurrentValue;
            this.keycloakOptions = keycloakOptions.CurrentValue;
            this.logger = logger;
            gsdkOptions.OnChange(settings => gsdkSettings = settings);
            keycloakOptions.OnChange(settings => this.keycloakOptions = settings);
        }

        public async Task<string> GetKeycloakToken()
        {
            var requestUrl = gsdkSettings.IsTest ? gsdkSettings.KeycloakUrl : keycloakOptions.TokenEndpoint;

            if (string.IsNullOrEmpty(requestUrl))
            {
                logger.LogError("GSDK: Keycloak token cannot be retrieved as the request url is not specified");
                throw new ServiceException(Errors.KeycloakTokenError);
            }

            var requestBody = new Dictionary<string, string>
            {
                {"client_id", "portal"},
                {"grant_type", "password"},
                {"username", gsdkSettings.IsTest ? gsdkSettings.Test.Username : gsdkSettings.Live.Username},
                {"password", gsdkSettings.IsTest ? gsdkSettings.Test.Password : gsdkSettings.Live.Password}
            };

            var requestContent = new FormUrlEncodedContent(requestBody!);

            HttpResponseMessage responseMessage;

            try
            {
                logger.LogInformation("GSDK: Sending a request to url {@requestUrl}", requestUrl);
                responseMessage = await httpClient.PostAsync(requestUrl, requestContent);
            }
            catch (HttpRequestException ex)
            {
                logger.LogError("GSDK: A call to {@requestUrl} failed with {@exception}", requestUrl, ex.Message);
                throw new ServiceException(Errors.KeycloakTokenError);
            }

            if (!responseMessage.IsSuccessStatusCode)
            {
                logger.LogError("GSDK: The call to {@requestUrl} returned {@statusCode}", requestUrl, responseMessage.StatusCode);
                throw new ServiceException(Errors.KeycloakTokenError);
            }

            var accessToken = await GetAccessToken(responseMessage, requestUrl);

            string? keycloakToken = "Bearer " + accessToken;
            return keycloakToken;
        }

        private async Task<string> GetAccessToken(HttpResponseMessage responseMessage, string requestUrl)
        {
            var jsonResult = await responseMessage.Content.ReadAsStringAsync();

            if (string.IsNullOrEmpty(jsonResult))
            {
                logger.LogError("GSDK: No response received from {@requestUrl}", requestUrl);
                throw new ServiceException(Errors.KeycloakTokenError);
            }

            var keycloakAuthentication = Json.Deserialize<KeycloakAuthentication>(jsonResult, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new JsonStringEnumConverter() }
            });

            return keycloakAuthentication.AccessToken;
        }
    }
}
