﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class SearchCriteria
{
    [Display(Name = "Search all")]
    public string SearchAll { get; set; } = string.Empty;

    [Display(Name = "Regions")]

    public string[] Region { get; set; } = Array.Empty<string>();

    [Display(Name = "Countries")]
    public string[] Country { get; set; } = Array.Empty<string>();

    [Display(Name = "Minimum possible matches count")]
    public string MinPossibleMatchCount { get; set; } = string.Empty;
}
