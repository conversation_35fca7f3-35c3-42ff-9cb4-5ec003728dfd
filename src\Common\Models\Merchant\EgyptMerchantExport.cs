﻿using Common.Models.Checkout;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Merchant
{
    public class EgyptMerchantExport
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? NationalId { get; set; }
        public string? City { get; set; }
        public string? Governorate { get; set; }
        public string? AddressLine { get; set; }
        public string? AcquiringLedger { get; set; }
        public string? BusinessName { get; set; }
        public string? MemberId { get; set; }
        public string? RegistrationNumber { get; set; }
        public string? Mcc { get; set; }
        public string? MerchantStatus { get; set; }
        public List<OrderResponse> Orders { get; set; } = new();
        public string? PosMid { get; set; }
        public string? Website { get; set; }
        public string? BankCheckStatus { get; set; }
        public DateTime? BankCheckStatusDate { get; set; }
    }
}
