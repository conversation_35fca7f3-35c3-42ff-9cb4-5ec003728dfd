﻿using Common.Models.TaskComments;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Cleanup;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class TaskCommentServiceTests
    {
        private readonly Mock<ILogger<TaskCommentService>> logger = new Mock<ILogger<TaskCommentService>>();
        private readonly Mock<IOptions<UrlSettings>> urlSettingsOptions = new Mock<IOptions<UrlSettings>>();
        private readonly CleanupService cleanupService = null!;
        private readonly Mock<ILogger<CleanupService>> loggerCleanup = new Mock<ILogger<CleanupService>>();

        public TaskCommentServiceTests()
        {
            urlSettingsOptions.Setup(x => x.Value).Returns(TestsHelper.UrlSettingsOptions);
            cleanupService = new CleanupService(loggerCleanup.Object);
        }

        [Test]
        public async Task TaskCommentService_should_create_task_comment()
        {
            var taskId = Guid.NewGuid();
            var request = new TaskCommentCreateRequest()
            {
                CommentText = "Test"
            };

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(new TaskCommentResponse()
                    {
                        CommentText = request.CommentText
                    })),
                })
                .Verifiable();


            var taskCommentService = new TaskCommentService(
                client: new HttpClient(handlerMock.Object),
                urlSettingsOptions: urlSettingsOptions.Object,
                logger: logger.Object);


            // Act
            var operation = await taskCommentService.CreateCommentAsync(taskId, request);

            // Assert
            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.MerchantServiceBaseUrlNS}/api/v1/merchant/task/{taskId}/comment");
            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Post && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());
        }

        [Test]
        public async Task TaskCommentService_should_retrieve_comment_by_task_id()
        {
            var taskId = Guid.NewGuid();
            var response = new List<TaskCommentResponse>()
            {
                new TaskCommentResponse()
                {
                    CommentText = "Test"
                }
            };

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(response)),
                })
                .Verifiable();


            var taskCommentService = new TaskCommentService(
                client: new HttpClient(handlerMock.Object),
                urlSettingsOptions: urlSettingsOptions.Object,
                logger: logger.Object);


            // Act
            var operation = await taskCommentService.GetCommentByTaskIdAsync(taskId);

            // Assert
            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.MerchantServiceBaseUrlNS}/api/v1/merchant/task/{taskId}/comment");
            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Get && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());

            operation.First().CommentText.Should().Be(response.First().CommentText);
            operation.Count.Should().Be(1);
        }


        [Test]
        public async Task TaskCommentService_should_retrieve_comment_by_comment_id()
        {
            var commentId = Guid.NewGuid();
            var response = new TaskCommentResponse()
            {
                CommentText = "Test"
            };

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(response)),
                })
                .Verifiable();


            var taskCommentService = new TaskCommentService(
                client: new HttpClient(handlerMock.Object),
                urlSettingsOptions: urlSettingsOptions.Object,
                logger: logger.Object);


            // Act
            var operation = await taskCommentService.GetCommentByIdAsync(commentId);

            // Assert
            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.MerchantServiceBaseUrlNS}/api/v1/merchant/task/comment/{commentId}");
            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Get && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());

            operation.CommentText.Should().Be(response.CommentText);
        }

    }
}
