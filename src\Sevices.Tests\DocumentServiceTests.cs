﻿using Common.Models;
using Common.Options;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Common.Models.Document;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.Json;
using Common;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class DocumentServiceTests
    {
        private readonly Mock<ILogger<DocumentService>> logger = new Mock<ILogger<DocumentService>>();
        private DocumentService documentService = null!;
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();

        public DocumentServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        }

        private static readonly Guid merchantId = Guid.NewGuid();

        private readonly DocumentMetadata documentMetadata = new DocumentMetadata()
        {
            Id = Guid.NewGuid(),
            StreamId = Guid.NewGuid(),
            OwnerUserId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            PersonOfInterestId = Guid.NewGuid(),
            DocumentType = "DocumentType",
            Language = "Language",
            ProvidedBy = "ProvidedBy",
            MimeType = "MimeType",
            Uri = "Uri",
            Name = "Name",
            CreatedBy = "CreatedBy",
            UpdatedBy = "UpdatedBy",
            Version = 1,
            Size = 1,
            IsDeleted = false,
            CreatedDateUtc = DateTime.UtcNow,
            UpdatedDateUtc = DateTime.UtcNow
        };

        private readonly DocumentWithContent[] documentsWithContent = new DocumentWithContent[]
        {
                new DocumentWithContent
                {
                    MerchantId = merchantId,
                    DocumentType = Constants.DocumentType.GeneralContract,
                    FileName = "test.txt",
                    Content = Encoding.UTF8.GetBytes("{"+
                               "\"crName\":\"test name\","+
                               "\"parties\":["+
                                  "{"+
                                     "\"identity\":{"+
                                                    "\"id\":\"1010454206\","+
                                                    "\"type\":\"crno\""+
                                                   "}"+
                                    "}"+
                                   "],"+
                               "\"address\":{"+
                                            "\"general\":{"+
                                                "\"address\":\"test address 112\""+
                                  "}"+
                               "},"+
                               "\"location\":{"+
                                            "\"id\":\"1010\","+
                                            "\"name\":\"location name\""+
                                    "}"+
                               "}")
                },
                new DocumentWithContent
                {
                    MerchantId = merchantId,
                    DocumentType = "TEST",
                    FileName = "test2.txt",
                    Content = new byte[1000]
                },
                new DocumentWithContent
                {
                    MerchantId = Guid.NewGuid(),
                    FileName = "test3.txt",
                    Content = Encoding.UTF8.GetBytes("{"+
                               "\"user_info\":["+
                                  "{"+
                                     "\"arabic_name\":\"arabic name test\""+
                                    "}"+
                                   "]"+
                                    "}")
                }
        };
        private static IFormFile file = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy file")), 0, 0, "Data", "dummy.txt");
        private readonly DocumentRequest documentRequest = new DocumentRequest
        {
            DocumentType = "txt",
            Language = "en",
            LeadId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            OwnerUserId = Guid.NewGuid(),
            PersonOfInterestId = Guid.NewGuid(),
            ProviderId = Guid.NewGuid(),
            File = file
        };

        [Test]
        public async Task GetDocumentMetadataAsync()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentMetadata)));

            var result = await documentService.GetDocumentMetadataAsync(Guid.NewGuid());

            result.Should().BeEquivalentTo(documentMetadata);
        }

        [Test]
        public void GetDocumentMetadataAsync_PassthroughtError()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            documentService.Invoking(x => x.GetDocumentMetadataAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DownloadDocumentAsync()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentMetadata)));

            documentService.Invoking(x => x.DownloadDocumentAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DownloadDocumentAsync_PassthroughtError()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            documentService.Invoking(x => x.DownloadDocumentAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void GetDocumentsWithContent()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentsWithContent)));

            documentService.Invoking(x => x.GetDocumentWithContentAsync(new DocumentSearchCriteria())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void CreateDocumentAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            documentService.Invoking(x => x.CreateDocumentAsync(documentRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task CreateDocumentAsync_WhenCoreServiceReturnsMetadata_returnsMetadata()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentMetadata)));

            var result = await documentService.CreateDocumentAsync(documentRequest);
            result.Should().BeEquivalentTo(documentMetadata);
        }

        [Test]
        public void DeleteDocumentsForLeadAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            documentService.Invoking(x => x.DeleteDocumentsForLeadAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteDocumentsForLeadAsync_WhenCoreServiceReturnsOk_NotThrowException()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentMetadata)));

            documentService.Invoking(x => x.DeleteDocumentsForLeadAsync(Guid.NewGuid()))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DownloadZippedDocuments_WhenNoDocuments_ThrowsException()
        {
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentsWithContent.Where(d => d.DocumentType == Constants.DocumentType.GeneralContract))));

            documentService.Invoking(x => x.DownloadZippedDocuments(Guid.NewGuid()))
                .Should()
                .ThrowAsync<ServiceException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }


        [Test]
        public async Task DownloadZippedDocuments_WhenAvailableDocuments_ExcludeContractDocuments()
        {
            var generator = new Random();
            generator.NextBytes(documentsWithContent[1].Content);
            documentService = new DocumentService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(documentsWithContent)));

            var zipFile = await documentService.DownloadZippedDocuments(merchantId);
            var unzippedFiles = await UnzipFile(zipFile);

            Assert.AreEqual(
                documentsWithContent.Count(d => d.DocumentType != Constants.DocumentType.GeneralContract),
                unzippedFiles.Count);
        }

        private static async Task<List<DocumentWithContent>> UnzipFile(DocumentWithContent zipFile)
        {
            var extractedFiles = new List<DocumentWithContent>();
            using var zipStream = new MemoryStream(zipFile.Content);
            using var archive = new ZipArchive(zipStream, ZipArchiveMode.Read, false);
            var archiveEntries = archive.Entries;
            foreach (var archiveEntry in archiveEntries)
            {
                await using var entryStream = archiveEntry.Open();
                using var fileUnzippedStream = new MemoryStream();
                await entryStream.CopyToAsync(fileUnzippedStream);
                extractedFiles.Add(new DocumentWithContent()
                {
                    Content = fileUnzippedStream.ToArray(),
                    FileName = archiveEntry.Name
                });
            }

            archive.Dispose();

            return extractedFiles;
        }
    }
}
