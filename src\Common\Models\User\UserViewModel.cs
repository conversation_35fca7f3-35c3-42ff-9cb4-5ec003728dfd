﻿using System;
using System.Collections.Generic;

namespace Common.Models.User
{
    public class UserViewModel
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Designation { get; set; }
        public string? Team { get; set; }
        public List<string>? Roles { get; set; }
        public bool IsDisabled { get; set; }
        public DateTime? DisabledDate { get; set; }
        public string? SalesId { get; set; }
        public Guid? ReportingManagerId { get; set; }
        public string? ReportingManagerName { get; set; }
        public string? Counterparty { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? CreatedDate { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedDate { get; set; }
    }
}
