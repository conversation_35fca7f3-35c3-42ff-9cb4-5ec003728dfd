﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<IsPackable>false</IsPackable>
		<AssemblyName>Geidea.BackofficePortal.Backoffice.Services.Tests</AssemblyName>
		<RootNamespace>Geidea.BackofficePortal.Backoffice.Services.Tests</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.msbuild" Version="3.2.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="FluentAssertions" Version="6.8.0" />
		<PackageReference Include="Moq" Version="4.18.3" />
		<PackageReference Include="NSubstitute" Version="4.4.0" />
		<PackageReference Include="nunit" Version="3.13.3" />
		<PackageReference Include="NUnit3TestAdapter" Version="4.3.1">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>

		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.1" />
		<PackageReference Include="coverlet.collector" Version="3.2.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BackofficeApi\BackofficeApi.csproj" />
		<ProjectReference Include="..\Common.Tests\Common.Tests.csproj" />
		<ProjectReference Include="..\Services\Services.csproj" />
	</ItemGroup>

</Project>
