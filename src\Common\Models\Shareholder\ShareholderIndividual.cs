﻿using System;

namespace Common.Models.Shareholder
{
    public class ShareholderIndividual
    {
        public Guid Id { get; set; }
        public string? NationalId { get; set; }
        public string? Nationality { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public DateTime? IdExpiryDate { get; set; }
        public DateTime? DOB { get; set; }
        public string? PhonePrefix { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PassportNo { get; set; }
        public DateTime? PassportExpirationDate { get; set; }
        public string? KYCCheck { get; set; }
        public bool? PEP { get; set; }
        public ShareholderIndividualAddress? Address { get; set; }
    }
}