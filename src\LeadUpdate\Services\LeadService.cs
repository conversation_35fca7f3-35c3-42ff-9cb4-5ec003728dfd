﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.Exceptions;
using LeadUpdate.Models;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace LeadUpdate.Services
{
    public class LeadService : ILeadService
    {
        private readonly HttpClient httpClient;
        private readonly IOptions<UrlSettings> urlOptions;
        private readonly ILogger<LeadService> logger;

        private string LeadServiceBasePath => urlOptions.Value?.LeadServiceBaseUrl ?? string.Empty;
        private string SearchServiceBasePath => urlOptions.Value?.SearchServiceBaseUrl ?? string.Empty;
        private const string LeadEndpoint = "/api/v1/Lead";
        private const string LeadAdvancedSearchEndpoint = "/api/v1/Lead/advancedSearch";

        public LeadService(HttpClient httpClient, IOptions<UrlSettings> urlOptions, ILogger<LeadService> logger)
        {
            this.httpClient = httpClient;
            httpClient.DefaultRequestHeaders.Add("X-CounterpartyCode", "GEIDEA_SAUDI");
            this.urlOptions = urlOptions;
            this.logger = logger;
        }

        public async Task<IList<Lead>> GetIncompleteLeadsAsync(int skip, int take, LeadUpdateType leadUpdateType, string sort)
        {
            var leads = await SearchLeadsAsync(new LeadSearchParameters
            {
                Skip = skip,
                Take = take,
                Sort = sort
            });

            var filteredLeads = leads.Records;

            if (leadUpdateType == LeadUpdateType.NamesAndSasInfo)
            {
                filteredLeads = filteredLeads.Where(l => string.IsNullOrWhiteSpace(l.OwnerFirstName) ||
                                                         string.IsNullOrWhiteSpace(l.OwnerLastName) ||
                                                         l.DOB == null ||
                                                         string.IsNullOrWhiteSpace(l.Nationality) ||
                                                         string.IsNullOrWhiteSpace(l.Gender))
                                             .ToList();
            }

            logger.LogInformation($"Retrieved {filteredLeads.Count} filtered leads.");
            foreach(var lead in filteredLeads)
                logger.LogInformation($"Lead to be updated: {lead.LeadId}");

            return filteredLeads;
        }

        public async Task PatchLeadAsync(Guid leadId, JsonPatchDocument<Lead> patchDocument)
        {
            string leadServiceUrl = $"{LeadServiceBasePath}{LeadEndpoint}/{leadId}";
            var body = new StringContent(JsonConvert.SerializeObject(patchDocument), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateLeadAsync({@leadServiceUrl})", leadServiceUrl))
            {
                logger.LogInformation($"Calling lead service to update lead with id '{leadId}'.");

                var response = await httpClient.PatchAsync(leadServiceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {

                    logger.LogCritical("Error when calling lead service update lead with id '{leadId}'. Error was {StatusCode} {@responseBody}",
                        leadId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"Updated lead with id '{leadId}'.");
            }
        }

        public async Task<int> GetLeadsCountAsync()
        {
            var leadsResponse = await SearchLeadsAsync(new LeadSearchParameters
            {
                Skip = 0,
                Take = 1
            });
            logger.LogInformation($"All leads count: {leadsResponse.TotalRecordCount}");
            return leadsResponse.TotalRecordCount;
        }

        private async Task<LeadsResponse> SearchLeadsAsync(LeadSearchParameters searchParameters)
        {
            string serviceUrl = $"{SearchServiceBasePath}{LeadAdvancedSearchEndpoint}";

            using (logger.BeginScope("GetAllLeadsAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling lead service to get leads (skip: {searchParameters.Skip}, take: {searchParameters.Take}.");

                var response = await httpClient.PostAsync(serviceUrl, new StringContent(JsonConvert.SerializeObject(searchParameters), Encoding.UTF8, "application/json"));
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling lead service to get leads. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var leads = JsonConvert.DeserializeObject<LeadsResponse>(responseBody);
                logger.LogInformation($"Retrieved {leads.TotalRecordCount} leads.");
                return leads;
            }
        }
    }
}
