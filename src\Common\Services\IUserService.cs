﻿using System;
using System.Threading.Tasks;
using Common.Models.Sales;
using Common.Models.User;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Services
{
    public interface IUserService
    {
        Task<UserShortResponse[]> GetUsersAsync(Guid merchantId);
        Task RemoveRolesForMerchantAsync(Guid merchantId);
        Task<User[]> GetAllUsersAsync();
        Task ActivateUserAsync(Guid userId);
        Task DisableUserAsync(Guid userId);
        Task<UserResponse[]> SearchUsersAsync(UserSearchParameters userSearchParameters);
        Task PatchBackOfficeUserAsync(Guid userId, JsonPatchDocument<UpdateBackOfficeUserRequest> updateBackOfficeUserRequest);
        Task<UserSalesIdResponse> UpdateUserSalesIdAsync(UserSalesIdRequest userSalesIdRequest);
        Task<User> GetUserByIdAsync(Guid userId);
        Task<UserExistsResponse> CheckUserExistsAsync(UserExistsRequest userExistsRequest);
    }
}
