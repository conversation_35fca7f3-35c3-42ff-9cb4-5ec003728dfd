﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Document;

namespace Common.Services
{
    public interface IDocumentService
    {
        Task<HttpContent> DownloadDocumentAsync(Guid documentId);
        Task<DocumentMetadata> GetDocumentMetadataAsync(Guid documentId);
        Task<DocumentWithContent[]> GetDocumentWithContentAsync(DocumentSearchCriteria searchCriteria);
        Task<DocumentWithContent> DownloadZippedDocuments(Guid merchantId);
        Task<DocumentMetadata> CreateDocumentAsync(DocumentRequest documentRequest);
        Task DeleteDocumentsForLeadAsync(Guid leadId);
    }
}
