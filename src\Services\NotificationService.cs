﻿using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Common.Models;
using Common.Options;
using Common.Services;
using Common.Validators;
using FluentValidation;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Validation;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Services 
{
    public class NotificationService : INotificationService
    {
        private readonly ILogger<NotificationService> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly HttpClient client;

        private string NotificationServiceBaseUrl => $"{urlSettingsOptions.NotificationServiceBaseUrl}/api/v1";

        public NotificationService(ILogger<NotificationService> logger, IOptionsMonitor<UrlSettings> urlSettingsOptions, HttpClient client)
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.client = client;
        }

        public async Task SendEmailInfoCheckAsync(SendEmailInfoCheckRequest request)
        {
            new ValidationHelpers().Validate(request, new SendEmailInfoCheckRequestValidator(), logger, "SendEmailInfoCheckRequest length validation failed!");

            string notificationServiceUrl = $"{NotificationServiceBaseUrl}/notification/SendInfoCheckEmail";

            using (logger.BeginScope("SendEmailInfoCheckAsync({notificationServiceUrl}, {@request})", notificationServiceUrl, request))
            {
                await NotifyAsync<SendEmailInfoCheckRequestValidator, SendEmailInfoCheckRequest>(notificationServiceUrl, request);
            }
        }

        public async Task SendSmsInfoCheckAsync(SendSmsInfoCheckRequest request)
        {
            new ValidationHelpers().Validate(request, new SendSmsInfoCheckRequestValidator(), logger, "SendSmsInfoCheckRequest length validation failed!");

            string notificationServiceUrl = $"{NotificationServiceBaseUrl}/notification/SendInfoCheckSms";

            using (logger.BeginScope("SendSmsInfoCheckAsync({notificationServiceUrl}, {@request})", notificationServiceUrl, request))
            {
                await NotifyAsync<SendSmsInfoCheckRequestValidator, SendSmsInfoCheckRequest>(notificationServiceUrl, request);
            }
        }

        public async Task SendCustomEmailAsync(SendCustomEmailRequest request)
        {
            new ValidationHelpers().Validate(request, new SendCustomEmailRequestValidator(), logger, "SendCustomEmailRequest length validation failed!");

            string notificationServiceUrl = $"{NotificationServiceBaseUrl}/notification/SendCustomEmail";

            using (logger.BeginScope("SendCustomEmailAsync({notificationServiceUrl}, {@request})", notificationServiceUrl, request))
            {
                await NotifyAsync<SendCustomEmailRequestValidator, SendCustomEmailRequest>(notificationServiceUrl, request);
            }
        }

        public async Task SendCustomSmsAsync(SendCustomSmsRequest request)
        {
            new ValidationHelpers().Validate(request, new SendCustomSmsRequestValidator(), logger, "SendCustomSmsRequest length validation failed!");

            string notificationServiceUrl = $"{NotificationServiceBaseUrl}/notification/SendCustomSms";

            using (logger.BeginScope("SendCustomSmsAsync({notificationServiceUrl}, {@request})", notificationServiceUrl, request))
            {
                await NotifyAsync<SendCustomSmsRequestValidator, SendCustomSmsRequest>(notificationServiceUrl, request);
            }
        }
        public async Task SendWelcomeEmialForCPMerchantAccount(SendWelcomeEmialRequest request)
        {
            new ValidationHelpers().Validate(request, new SendWelcomeEmialRequestValidator(), logger, "SendWelcomeEmialRequest length validation failed!");

            string notificationServiceUrl = $"{NotificationServiceBaseUrl}/notification/SendWelcomeEmialForCPMerchantAccount";

            using (logger.BeginScope("SendWelcomeEmialForCPMerchantAccountAsync({notificationServiceUrl}, {@request})", notificationServiceUrl, request))
            {
                await NotifyAsync<SendWelcomeEmialRequestValidator, SendWelcomeEmialRequest>(notificationServiceUrl, request);
            }
        }
        public async Task SendWelcomeEmialForCNPMerchantAccount(SendWelcomeEmialRequest request)
        {
            new ValidationHelpers().Validate(request, new SendWelcomeEmialRequestValidator(), logger, "SendWelcomeEmialRequest length validation failed!");

            string notificationServiceUrl = $"{NotificationServiceBaseUrl}/notification/SendWelcomeEmialForCNPMerchantAccount";

            using (logger.BeginScope("SendWelcomeEmialForCNPMerchantAccountAsync({notificationServiceUrl}, {@request})", notificationServiceUrl, request))
            {
                await NotifyAsync<SendWelcomeEmialRequestValidator, SendWelcomeEmialRequest>(notificationServiceUrl, request);
            }
        }

        private async Task NotifyAsync<TValidator, TData>(string url, TData data) 
            where TValidator: AbstractValidator<TData>, new()
        {
            var validationResult = new TValidator().Validate(data);

            if (!validationResult.IsValid)
            {
                var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
                logger.LogError("Request validation failed: {@errors}", errorDescription);

                throw new Geidea.Utils.Exceptions.ValidationException(validationResult);
            }

            logger.LogInformation("Calling notification service.");

            var response = await client.PostAsync(url, new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json"));

            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
                logger.LogCritical("Error when calling notification service. Error was {StatusCode} {@responseBody}", 
                    (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }
        }
    }
}
