﻿using Common.Models.Checkout;
using Common.Models.Search;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Tasks;
using System;
using Common.Models.Merchant.SubordinateMerchant;
using Common.Models.TerminalDataSet;
using Common.Models.User;

namespace Common.Services
{
    public interface ISearchService
    {
        Task<List<OrdersExport>> ExportOrdersAsync(OrderSearchCriteria orderSearchCriteria);
        Task<SearchResponse<TaskResponse>> TaskAdvancedSearch(TaskSearchRequest searchRequest);
        Task<OrderWithInstances> GetOrderProductInstancesWithTerminalDataAsync(Guid orderId, List<string> productTypes);
        Task<SubordinateMerchantSearchResponse> SearchSubordinateMerchants(SubordinateMerchantSearchFilters filters);
        Task<List<OrdersWithInstancesResponse>> GetOrdersProductInstancesWithTerminalDataAsync(List<Guid> ordersIds);
        Task<UserSearchResponse<UserAdvancedSearchResponse>> FindUsersAsync(UserAdvancedSearchFilter filter);
        Task<UserRoleSearchResponse<UserRoleAdvancedSearchResponse>> FindUsersRoleAsync(UserRoleSearchFilter filter);
    }
}
