﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Geidea.Utils.Common;

namespace BackofficeApi.Filters 
{
    [ExcludeFromCodeCoverage]
    public class CounterpartyHeaderFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = Constants.CounterpartyHeaderName,
                In = ParameterLocation.Header,
                Required = false,
                Example = new OpenApiString(Constants.CounterpartyUae)
            });
        }
    }
}
