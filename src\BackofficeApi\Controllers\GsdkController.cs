﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models.Gsdk;
using Common.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace BackofficeApi.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class GsdkController : ControllerBase
    {
        private readonly IGsdkMerchantMappingService service;

        public GsdkController(IGsdkMerchantMappingService service)
        {
            this.service = service;
        }

        [HttpPost]
        [ProducesResponseType(typeof(IReadOnlyCollection<GsdkMappingResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> MapExistingMerchants()
        {
            var notMappedMerchants = await service.MapExistingMerchants();

            return Ok(notMappedMerchants);
        }
    }
}