﻿using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Merchant;
using Common.Models.NexusBridge;
using Common.Models.Search;
using Common.Models.Shareholder;
using Common.Services;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Threading.Tasks;
using static Common.Constants;

namespace Services
{
    public class NexusBridgeService : INexusBridgeService
    {
        private readonly INexusBridgeClient nexusClient;
        private readonly ILogger<NexusBridgeService> logger;
        private readonly IReferenceService referenceService;
        private readonly IDocumentService documentService;

        public NexusBridgeService(
             INexusBridgeClient nexusClient, ILogger<NexusBridgeService> logger,
             IReferenceService referenceService, IDocumentService documentService)
        {
            this.nexusClient = nexusClient;
            this.logger = logger;
            this.referenceService = referenceService;
            this.documentService = documentService;
        }

        public async Task CreateMerchant(Merchant merchant, List<MerchantShareholderIndividual> contact, Merchant store, OrderResponse order, bool orderFlag)
        {
            logger.LogInformation("Mapping values to Nexus Bridge Merchant Model");

            var externalProducts = await nexusClient.GetMerchantExternalProducts();
            var address = await nexusClient.GetMerchantAddress(merchant.MerchantId);
            var bank = await nexusClient.GetBankAccountDetails(store.MerchantId);
            var param = merchant.Counterparty == CounterParty.Saudi ? order.OrderNumber : store.MerchantDetails?.Mid;
            var terminal = await nexusClient.GetTerminalDetails(param);
            terminal = terminal.Where(x => x.OrderNumber == order.OrderNumber).ToList();
            string risk = await GetRiskLevel(merchant);

            logger.LogInformation("Fetching values from reference data for Nexus Bridge Merchant Model");

            var catalogues = await referenceService.GetCataloguesAsync(new[] {
                Catalogues.NbeGovernarotes,Catalogues.Governorates, Catalogues.AlxGovernarotes, Catalogues.AlxCities,
                Catalogues.NbeCities, Catalogues.Cities, Catalogues.Areas,
                Catalogues.NbeCityToEPos, Catalogues.AlxCityToEPos ,Catalogues.AcquiringLedger, Catalogues.Banks
                });

            string mapcity = ReturnCatalogue(merchant.MerchantDetails?.AcquiringLedger, catalogues, Catalogues.Cities);
            string mapArea = ReturnCatalogue(merchant.MerchantDetails?.AcquiringLedger, catalogues, Catalogues.Areas);
            string governorate = ReturnCatalogue(merchant.MerchantDetails?.AcquiringLedger, catalogues, Catalogues.Governorates);

            NBMerchant nMerchant = new()
            {
                MerchantId = merchant.MerchantId,
                MerchantStatus = merchant.MerchantStatus,
                Counterparty = merchant.Counterparty,
                Tag = merchant.Tag,
                MerchantType = merchant.MerchantType,
                PersonOfInterests = MapShareholdersToPersonOfInterest(contact, catalogues, mapcity, governorate, merchant),
                ExternalProducts = MapMerchantExternalProducts(externalProducts),
                CommissionTypes = MapCommissionTypes(store, order),
                AccountConfig = MapMerchantAccountConfig(merchant, store, bank),
                MerchantDetails = MapMerchantDetails(merchant, store, risk),
                Addresses = MapMerchantAddresses(address, catalogues, mapcity, governorate, merchant.MerchantDetails?.BusinessEmail, mapArea),
                BankAccounts = MapBankAccounts(bank, catalogues),
                MerchantTerminals = MapMerchantTerminals(terminal, merchant.Counterparty, order)
                //MerchantFee = MapFee(feeList),
            };

            logger.LogInformation("Calling NexusBridgeAPI");
            await nexusClient.ConnectToNexusBridge(nMerchant, orderFlag);
        }

        private async Task<string> GetRiskLevel(Merchant merchant)
        {
            string riskLevel = Constants.Risk.Low;
            try
            {
                var docId = await documentService.GetDocumentByMerchantId(merchant.MerchantId);
                if (docId == null)
                {
                    return riskLevel;
                }
                var results = await documentService.DownloadDocumentAsync(docId.Id);
                if (results == null)
                {
                    return riskLevel;
                }
                var file = await results.ReadAsStringAsync();
                var riskDetail = JObject.Parse(file);
                var riskValue = riskDetail["Risk"]?.ToString();
                if (!string.IsNullOrEmpty(riskValue))
                {
                    if (riskValue!.Equals(Constants.Risk.HRisk, StringComparison.OrdinalIgnoreCase))
                    {
                        riskLevel = Constants.Risk.High;
                    }
                    else if (riskValue.Equals(Constants.Risk.MRisk, StringComparison.OrdinalIgnoreCase))
                    {
                        riskLevel = Constants.Risk.Medium;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Error while fetching risk level for apex from document service {ex}", ex);
            }

            return riskLevel;
        }

        public static string ReturnCatalogue(string? acquiringLedger, Catalogue[] catalogues, string catalogueName)
        {
            if (acquiringLedger != null && catalogues.Any(r => r.CatalogueName.StartsWith(acquiringLedger, StringComparison.OrdinalIgnoreCase)))
                catalogueName = ConcatAcquirerWithCatalogueName(acquiringLedger, catalogueName);

            return catalogueName;
        }

        public static string ConcatAcquirerWithCatalogueName(string acquirer, string catalogueName)
        {
            return $"{acquirer.ToUpper()}_{catalogueName}";
        }

        private static List<MerchantBankAccountResponse> MapBankAccounts(List<MerchantBankAccountResponse> merchants, Catalogue[] catalogues)
        {
            var bankAccounts = new List<MerchantBankAccountResponse>();

            foreach (var merchant in merchants)
            {
                string? bankName = catalogues.Where(x => x.CatalogueName == Catalogues.Banks && x.Key == merchant.RefBankId.ToString()).Select(x => x.Value).FirstOrDefault();

                var account = new MerchantBankAccountResponse()
                {
                    CountryCode = merchant.CountryCode,
                    AccountHolderName = merchant.AccountHolderName,
                    RefBankId = merchant.RefBankId,
                    BankCheckId = merchant.BankCheckId,
                    City = merchant.City,
                    IBAN = merchant.IBAN,
                    Swift = merchant.Swift,
                    AccountName = merchant.AccountName,
                    DDReference = merchant.DDReference,
                    ValidFrom = merchant.ValidFrom,
                    ValidTo = merchant.ValidTo,
                    BankAccountNumber = merchant.BankAccountNumber,
                    Balance = merchant.Balance,
                    Currency = merchant.Currency,
                    AccountType = merchant.AccountType,
                    MerchantId = merchant.MerchantId,
                    BankName = bankName,
                    MerchantBankAccountId = merchant.MerchantBankAccountId,
                };

                bankAccounts.Add(account);
            }

            return bankAccounts;
        }

        private static List<MerchantTerminal> MapMerchantTerminals(List<MerchantTerminal> merchantTerminals, string? counterparty, OrderResponse order)
        {
            var terminals = new List<MerchantTerminal>();

            foreach (var terminal in merchantTerminals)
            {
                terminals.Add(new MerchantTerminal
                {
                    MerchantId = terminal.MerchantId,
                    Brand = terminal.Brand,
                    Model = terminal.Model,
                    TerminalStatus = terminal.TerminalStatus,
                    TerminalId = terminal.TerminalId,
                    FullTerminalId = terminal.FullTerminalId,
                    OrderNumber = counterparty == CounterParty.Saudi ? order.OrderNumber : null,
                    OrderId = counterparty == CounterParty.Saudi ? order.OrderId.ToString() : null
                });
            }
            return terminals;
        }

        private static List<MerchantFee> MapFee(List<MerchantFee> merchants)
        {
            var fee = new List<MerchantFee>();

            foreach (var merchant in merchants)
            {
                fee.Add(new MerchantFee
                {
                    MerchantId = merchant.MerchantId,
                    Type = merchant.Type,
                    Frequency = merchant.Frequency,
                    Amount = merchant.Amount,
                    Sign = merchant.Sign,
                    InactivityAmountThreshold = merchant.InactivityAmountThreshold,
                    PaymentMethod = merchant.PaymentMethod,
                    ReportingOnly = merchant.ReportingOnly,
                });
            }

            return fee;
        }

        private static List<Common.Models.NexusBridge.MerchantAddress> MapMerchantAddresses(List<Common.Models.NexusBridge.MerchantAddress> merchants, Catalogue[] catalogues, string mapcity, string governorate, string? email, string mapArea)
        {
            var addresses = new List<Common.Models.NexusBridge.MerchantAddress>();

            foreach (var merchant in merchants)
            {
                var address = new Common.Models.NexusBridge.MerchantAddress()
                {
                    Street = merchant.Street,
                    City = catalogues.FirstOrDefault(x => x.CatalogueName == mapcity && x.Key == merchant?.City)?.Value,
                    Country = Nexus.Country,
                    District = catalogues.FirstOrDefault(x => x.CatalogueName == governorate && x.Key == merchant.Governorate)?.Value,
                    Coordinates = merchant.Coordinates,
                    MerchantId = merchant.MerchantId,
                    AddressId = merchant.AddressId,
                    Governorate = catalogues.FirstOrDefault(x => x.CatalogueName == governorate && x.Key == merchant?.Governorate)?.Value,
                    Area = catalogues.FirstOrDefault(x => x.CatalogueName == mapArea && x.Key == merchant?.Area)?.Value,
                    POBox = merchant.POBox,
                    Email = !string.IsNullOrEmpty(merchant.Email) ? merchant.Email : email,
                    Url = merchant.Url,
                    AddressType = merchant.AddressType,
                    AddressPin = merchant.AddressPin,
                    IsDefaultAddress = merchant.IsDefaultAddress,
                    ValidFrom = merchant.ValidFrom,
                    ValidTo = merchant.ValidTo,
                    OfficeLocationType = merchant.OfficeLocationType,
                    IsBranchSelected = merchant.IsBranchSelected,
                    Zip = merchant.Zip,
                    Purpose = merchant.Purpose,
                };

                addresses.Add(address);
            }

            return addresses;
        }

        private static MerchantDetails MapMerchantDetails(Merchant merchant, Merchant store, string riskLevel)
        {
            return new MerchantDetails
            {
                MerchantId = merchant.MerchantId,
                BusinessType = merchant.MerchantDetails?.BusinessType,
                BusinessId = merchant.MerchantDetails?.BusinessId,
                OutletType = merchant.MerchantDetails?.OutletType,
                MCC = merchant.MerchantDetails?.MCC,
                BusinessDomain = merchant.MerchantDetails?.BusinessDomain,
                LegalName = merchant.MerchantDetails?.LegalName,
                LegalNameAr = merchant.MerchantDetails?.LegalNameAr,
                TradingName = merchant.MerchantDetails?.TradingName,
                Nickname = merchant.MerchantDetails?.Nickname,
                TradingCurrency = merchant.MerchantDetails?.TradingCurrency,
                RegistrationNumber = merchant.MerchantDetails?.RegistrationNumber,
                VatNumber = merchant.MerchantDetails?.VatNumber,
                FoundationDate = merchant.MerchantDetails?.FoundationDate ?? default(DateTime),
                MunicipalLicenseNumber = merchant.MerchantDetails?.MunicipalLicenseNumber,
                AcquirerReview = merchant.MerchantDetails?.AcquirerReview,
                AcquiringLedger = store.MerchantDetails?.AcquiringLedger, // test
                VatAppliedFlag = merchant.MerchantDetails?.VatAppliedFlag ?? false,
                TaxExempt = merchant.MerchantDetails?.TaxExempt ?? false,
                AdditionalTradingInformation = merchant.MerchantDetails?.AdditionalTradingInformation,
                MIDMerchantReference = merchant.MerchantDetails?.MIDMerchantReference,
                Website = merchant.MerchantDetails?.Website,
                AnnualTurnover = merchant.MerchantDetails?.AnnualTurnover ?? 0m,
                Region = merchant.MerchantDetails?.Region,
                DefaultLanguage = merchant.MerchantDetails?.DefaultLanguage,
                UnifiedId = merchant.MerchantDetails?.UnifiedId,
                CreatedDate = merchant.MerchantDetails?.CreatedDate ?? default(DateTime),
                UpdatedDate = merchant.MerchantDetails?.UpdatedDate,
                CreatedBy = merchant.MerchantDetails?.CreatedBy ?? string.Empty,
                UpdatedBy = merchant.MerchantDetails?.UpdatedBy ?? string.Empty,
                CityCr = merchant.MerchantDetails?.CityCr,
                AddressCr = merchant.MerchantDetails?.AddressCr,
                MerchantName = merchant.MerchantDetails?.MerchantName,
                ReferralChannel = merchant.MerchantDetails?.ReferralChannel,
                Mid = store.MerchantDetails?.Mid,
                BusinessEmail = merchant.MerchantDetails?.BusinessEmail,
                ChannelType = merchant.MerchantDetails?.ChannelType,
                CompanyPhoneNumber = merchant.MerchantDetails?.CompanyPhoneNumber,
                PayoutMinimumCap = merchant.MerchantDetails!.PayoutMinimumCap == 0 ? 10 : merchant.MerchantDetails!.PayoutMinimumCap,
                PayoutTransferFeeAmount = merchant.MerchantDetails.PayoutTransferFeeAmount,
                TLIssueDate = merchant.MerchantDetails.TLIssueDate,
                TLExpiryDate = merchant.MerchantDetails.TLExpiryDate,
                MaxMonthlyTransaction = merchant.MerchantDetails.MaxMonthlyTransaction ?? 0,
                HighestSingleTransaction = merchant.MerchantDetails.HighestSingleTransaction ?? 0,
                salesAgent = merchant.MerchantDetails.salesAgent ?? 0,
                salesManager = merchant.MerchantDetails.salesManager ?? 0,
                Iban = merchant.MerchantDetails.Iban,
                AccountNumber = merchant.MerchantDetails.AccountNumber,
                BeneficiaryFullName = merchant.MerchantDetails.BeneficiaryFullName,
                MerchantSegment = store.MerchantDetails?.ChannelType == Nexus.PGW ? Nexus.Ecom : store.MerchantDetails?.ChannelType,

                ParentMerchantId = merchant.MerchantDetails?.BusinessId ?? string.Empty,
                AlternativeMerchantName = merchant.MerchantDetails?.AlternativeMerchantName ?? merchant.MerchantDetails?.DoingBusinessAsName,
                StoreName = store.MerchantDetails?.StoreName ?? store.MerchantDetails?.LegalName,
                AlternativeStoreName = store.MerchantDetails?.AlternativeStoreName ?? store.MerchantDetails?.LegalName,
                RiskLevel = merchant.Counterparty == Constants.CounterParty.Saudi ? riskLevel : merchant.MerchantDetails?.RiskLevel ?? Constants.Nexus.RiskLevel,
            };
        }

        private static Common.Models.NexusBridge.MerchantAccountConfig MapMerchantAccountConfig(Merchant merchant, Merchant store, List<MerchantBankAccountResponse> bank)
        {
            return new Common.Models.NexusBridge.MerchantAccountConfig
            {
                MerchantId = store.MerchantId,
                AcceptedPaymentMethods = store.AccountConfig.AcceptedPaymentMethods,
                SettlementCurrency = store.AccountConfig.SettlementCurrency,
                PayoutSchedule = store.AccountConfig.PayoutSchedule,
                PayoutDay = store.AccountConfig.PayoutDay,
                PayoutCapAmount = store.AccountConfig.PayoutCapAmount,
                Bic = bank?.FirstOrDefault(x => x.MerchantId == merchant.MerchantId)?.Swift,
                SettlementTimeFrame = store.AccountConfig?.SettlementTimeFrame,
                PayoutMinimumCap = merchant.MerchantDetails!.PayoutMinimumCap == 0 ? 10 : merchant.MerchantDetails!.PayoutMinimumCap
            };
        }

        private static List<Common.Models.NexusBridge.MerchantCommissionConfig> MapCommissionTypes(Merchant store, OrderResponse order)
        {
            var commissionConfig = new List<Common.Models.NexusBridge.MerchantCommissionConfig>();

            foreach (var commission in store.CommissionTypes)
            {
                var mappedCommission = new Common.Models.NexusBridge.MerchantCommissionConfig
                {
                    CommissionType = commission.CommissionType,
                    ProductCode = commission.ProductCode,
                    MerchantId = store.MerchantId,
                    Value = commission.Value ?? 0,
                    VatPercentage = Nexus.Vat,
                    IsInterchange = Nexus.IsInterChange,
                    TransactionType = store.AccountConfig.TransactionType,

                };

                commissionConfig.Add(mappedCommission);
            }
            return commissionConfig;
        }

        private static List<MerchantExternalProduct> MapMerchantExternalProducts(List<MerchantExternalProduct> extProducts)
        {
            var mappedProducts = new List<MerchantExternalProduct>();

            foreach (var extProduct in extProducts)
            {
                var mappedProduct = new MerchantExternalProduct
                {
                    ProductCategory = extProduct.ProductCategory,
                    ProductCode = extProduct.ProductCode,
                    ProductType = extProduct.ProductType,
                };

                mappedProducts.Add(mappedProduct);
            }

            return mappedProducts;
        }

        private static List<Common.Models.NexusBridge.MerchantPersonOfInterest> MapShareholdersToPersonOfInterest(List<MerchantShareholderIndividual> shareholders, Catalogue[] catalogues, string mapcity, string governorate, Merchant merchant)
        {
            var personOfInterests = new List<Common.Models.NexusBridge.MerchantPersonOfInterest>();
            foreach (var shareholder in shareholders)
            {
                var personOfInterest = new Common.Models.NexusBridge.MerchantPersonOfInterest
                {
                    MerchantPersonOfInterestId = shareholder.PersonOfInterestId,
                    FirstName = shareholder.FirstName,
                    LastName = shareholder.LastName,
                    DateOfBirth = shareholder.DOB?.ToString("yyyyMMdd"),
                    MobileNumber = !string.IsNullOrEmpty(shareholder.PhoneNumber) ? shareholder.PhoneNumber : merchant.MerchantDetails?.CompanyPhoneNumber,
                    NationalId = shareholder.NationalId,
                    Nationality = shareholder.Nationality,
                    FirstNameAr = shareholder.FirstNameAr,
                    LastNameAr = shareholder.LastNameAr,
                    IsPrincipal = GetPrinicpalOwner(shareholder),
                    IdExpiryDate = shareholder.IdExpiryDate,
                    PhonePrefix = shareholder.PhonePrefix,
                    PassportExpirationDate = shareholder.PassportExpirationDate,
                    PassportNo = shareholder.PassportNo,
                    KYCCheck = shareholder.KYCCheck,
                    PEP = shareholder.PEP ?? false,
                    Email = !string.IsNullOrEmpty(shareholder.Email) ? shareholder.Email : merchant.MerchantDetails?.BusinessEmail,
                    Address = MapAddress(shareholder, catalogues, mapcity, governorate),
                    MerchantId = merchant.MerchantId,
                };

                personOfInterests.Add(personOfInterest);
            }

            return personOfInterests;
        }

        private static PersonOfInterestAddress MapAddress(MerchantShareholderIndividual shareholder, Catalogue[] catalogues, string mapcity, string governorate)
        {
            return new PersonOfInterestAddress
            {
                City = catalogues.FirstOrDefault(x => x.CatalogueName == mapcity && x.Key == shareholder.Address?.City)?.Value,
                Country = !string.IsNullOrEmpty(shareholder.Address?.Country) ? shareholder.Address?.Country : Nexus.Country,
                District = shareholder.Address?.Area,
                PhoneNumber = shareholder.Address?.PhoneNumber ?? shareholder.PhoneNumber,
                Email = shareholder.Address?.Email,
                Governorate = catalogues.FirstOrDefault(x => x.CatalogueName == governorate && x.Key == shareholder.Address?.Governorate)?.Value,
                Street = shareholder.Address?.AddressInfo ?? shareholder.Address?.Area,
                Area = shareholder.Address?.Area,
            };
        }
        private static bool GetPrinicpalOwner(MerchantShareholderIndividual shareholder)
        {
            if (shareholder.Relations != null)
            {
                var relation = shareholder.Relations.FirstOrDefault();
                if (relation != null)
                {
                    return relation.IsPrincipal!.Value;
                }
            }
            return false;
        }

    }
}