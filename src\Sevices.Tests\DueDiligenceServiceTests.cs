﻿using Common.Models.Match;
using Common.Options;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Cleanup;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using NSubstitute.ExceptionExtensions;
using NUnit.Framework;
using Services;
using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class DueDiligenceServiceTests
    {
        private readonly Mock<ILogger<DueDiligenceService>> logger = new Mock<ILogger<DueDiligenceService>>();
        private readonly Mock<IOptions<UrlSettings>> urlSettingsOptions = new Mock<IOptions<UrlSettings>>();
        private readonly CleanupService cleanupService = null!;
        private readonly Mock<ILogger<CleanupService>> loggerCleanup = new Mock<ILogger<CleanupService>>();

        public DueDiligenceServiceTests()
        {
            urlSettingsOptions.Setup(x => x.Value).Returns(TestsHelper.UrlSettingsOptions);
            cleanupService = new CleanupService(loggerCleanup.Object);
        }

        [Test]
        public async Task DueDiligenceService_should_call_correct_endpoint_and_return_deserialized_value()
        {
            // Arrange
            var instance = new MatchResponse();

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(instance)),
                })
                .Verifiable();

            var dueDiligenceService = new DueDiligenceService(
                client: new HttpClient(handlerMock.Object),
                logger: logger.Object,
                urlSettingsOptions: urlSettingsOptions.Object);

            // Act
            var report = await dueDiligenceService.RetrieveMerchantMatchReport(Guid.Empty);

            // Assert
            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.DueDiligenceServiceBaseUrl}/api/v1/match/{Guid.Empty}");

            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Get && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());

            report.Should().NotBeNull();
        }

        [Test]
        public async Task DueDiligenceService_ShouldtriggerWorldCheckOne()
        {
            // Arrange
            var instance = new MatchResponse();

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject("Success")),
                })
                .Verifiable();

            var dueDiligenceService = new DueDiligenceService(
                client: new HttpClient(handlerMock.Object),
                logger: logger.Object,
                urlSettingsOptions: urlSettingsOptions.Object);

            // Act
            await dueDiligenceService.SendWorldCheckOneRequest(Guid.Empty);

            // Assert
            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.DueDiligenceServiceBaseUrl}/api/v1/WorldCheckOne/Business/Create/{Guid.Empty}");

            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Post && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());

            
        }
            

    }
}
