﻿using Common.Models.Chain;
using FluentValidation;
using Geidea.Utils.ReferenceData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators.Chain;

public class ChainContactsCreateRequestValidator : AbstractValidator<ChainContactsCreateRequest>
{
    public ChainContactsCreateRequestValidator(List<ReferenceData> refData)
    {
        RuleFor(x => x.ChainId).NotNull().NotEmpty();
        RuleForEach(x => x.ChainContacts).NotNull().SetValidator(new ChainContactModelValidator(refData));
    }
}
