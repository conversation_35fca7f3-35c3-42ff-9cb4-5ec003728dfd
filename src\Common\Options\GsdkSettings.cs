﻿namespace Common.Options
{
    public class GsdkSettings
    {
        public bool IsTest { get; set; }
        public GsdkEnvironmentSettings Live { get; set; } = new GsdkEnvironmentSettings();
        public GsdkEnvironmentSettings Test { get; set; } = new GsdkEnvironmentSettings();
        public string KeycloakUrl { get; set; } = string.Empty;
        public GsdkFeatureManagement FeatureManagement { get; set; } = new();
    }
}