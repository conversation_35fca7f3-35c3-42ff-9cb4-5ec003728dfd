﻿using Microsoft.AspNetCore.JsonPatch;
using System.Collections.Generic;
using System;

namespace Common.Models.Shareholder
{
    public class ShareholderIndividualPatchRequest
    {
        public Guid MerchantId { get; set; }
        public Guid PersonOfInterestId { get; set; }
        public JsonPatchDocument<ShareholderIndividual> ShareholderIndividualJsonPatch { get; set; } = null!;
        public JsonPatchDocument<MerchantShareholderIndividualRelation> MerchantShareholderIndividualJsonPatch { get; set; } = null!;
        public List<ShareholderCompanyPersonOfInterestEditRequest> ShareholderCompanyPersonOfInterestEditRequests { get; set; } = null!;
    }
}