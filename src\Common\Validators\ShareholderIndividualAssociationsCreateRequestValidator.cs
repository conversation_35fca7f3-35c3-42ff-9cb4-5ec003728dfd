﻿using Common.Models.Shareholder;
using FluentValidation;
using System;
using System.Linq;

namespace Common.Validators;

public class ShareholderIndividualAssociationsCreateRequestValidator : AbstractValidator<ShareholderIndividualAssociationsCreateRequest>
{
    public ShareholderIndividualAssociationsCreateRequestValidator()
    {
        RuleFor(x => x.ShareholderIndividualId)
            .Must(x => x != Guid.Empty)
            .WithErrorCode(Errors.InvalidIndividualShareholderId.Code)
            .WithMessage(Errors.InvalidIndividualShareholderId.Message);

        RuleFor(x => x.Merchant)
            .Must(x => x != null)
            .WithErrorCode(Errors.InvalidMerchantIndividualLink.Code)
            .WithMessage(Errors.InvalidMerchantIndividualLink.Message);

        RuleFor(x => x.Merchant!)
           .SetValidator(new MerchantIndividualLinkValidator());

        When(x => x.ShareholderCompanies != null && x.ShareholderCompanies.Any(), () =>
        {
            RuleForEach(x => x.ShareholderCompanies)
                .SetValidator(new ShareholderCompanyIndividualLinkValidator());
        });


    }
}
