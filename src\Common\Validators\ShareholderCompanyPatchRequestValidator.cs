﻿using Common.Models.Shareholder;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators;

public class ShareholderCompanyPatchRequestValidator : AbstractValidator<ShareholderCompanyPatchRequest>
{
    public ShareholderCompanyPatchRequestValidator()
    {
        RuleFor(x => x.MerchantId)
            .Must(x => x != Guid.Empty)
            .WithErrorCode(Errors.InvalidShareholderCompanyMerchantId.Code)
            .WithMessage(Errors.InvalidShareholderCompanyMerchantId.Message);

        RuleFor(x => x.ShareholderCompanyId)
            .Must(x => x != Guid.Empty)
            .WithErrorCode(Errors.InvalidShareHolderCompanyId.Code)
            .WithMessage(Errors.InvalidShareHolderCompanyId.Message);

        RuleFor(x => x.JsonPatchDocument)
            .Must(x => x != null)
            .WithErrorCode(Errors.InvalidPatch.Code)
            .WithMessage(Errors.InvalidPatch.Message);
    }
}
