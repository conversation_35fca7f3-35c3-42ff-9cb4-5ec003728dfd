﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Chain;

public class ChainSearchFilters : BaseSearchCriteria
{
    public string? ChainId { get; set; }
    public string? ChainName { get; set; }
    public DateInterval? DateInterval { get; set; }
    public string? Keyword { get; set; }
    public string[]? SearchIn { get; set; }
}

