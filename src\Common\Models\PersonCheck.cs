﻿using Common.Models.Merchant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models
{
    public class PersonCheckRequest
    {
        public Guid PersonCheckId { get; set; }

        public Guid MerchantPersonOfInterestId { get; set; }

        public string CheckType { get; set; } = string.Empty;

        public DateTime CheckDate { get; set; }

        public string CheckStatus { get; set; } = string.Empty;

        public int CheckScore { get; set; }

        public int CheckProvider { get; set; }

        public string? CheckTriggerType { get; set; }

        public bool? IsFinscanTriggerAutomatic { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime? ValidTo { get; set; }

        public IList<CheckComment> PersonCheckComments { get; set; } = new List<CheckComment>();
    }
}

