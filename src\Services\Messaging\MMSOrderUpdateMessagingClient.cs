﻿namespace Services.Messaging
{
    using Geidea.Utils.Messaging;
    using Geidea.Utils.Messaging.Base;
    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;

    public class MMSOrderUpdateMessagingClient : MessageClient
    {
        public MMSOrderUpdateMessagingClient(
           ILogger<MessageClient> logger,
           IHttpContextAccessor contextAccessor,
           IOptionsMonitor<RabbitMqOptions> rabbitMqOptions)
           : base(contextAccessor, logger, rabbitMqOptions, new QueueSettings
           {
               ExchangeName = "MMS.Order",
               MessageSender = "BackofficeAPI",
               QueueName = "MMS.Order.Update.ProductsRegistered",
               RoutingKey = "MMS.Order.Update.ProductsRegistered",
               Durable = true
           })
        {
        }
    }
}
