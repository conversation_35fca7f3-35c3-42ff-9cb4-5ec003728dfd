﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models
{
    public class RiskResponseModel
    {
        public ComplianceRiskModel? ComplianceRisk { get; set; }
        public AcquiringRiskModel? AcquiringRisk { get; set; }
    }

    public class ComplianceRiskModel
    {
        public int RiskScore { get; set; }
        public string? RiskLevel { get; set; }
        public string? DueDiligenceLevel { get; set; }
    }

    public class AcquiringRiskModel
    {
        public string? RiskLevel { get; set; }
    }
}
