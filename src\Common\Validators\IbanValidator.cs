﻿using FluentValidation;

namespace Common.Validators
{
    public class IbanValidator : AbstractValidator<string?>
    {
        public IbanValidator()
        {
            When(x => x != "" && x != null, () =>
            {
                RuleFor(x => x)
                    .Matches(@"^[SA]{1,2}[0-9]{22}$")
                    .WithErrorCode(Errors.Iban_Max24.Code).WithMessage(Errors.Iban_Max24.Message);
            });
        }
    }
}
