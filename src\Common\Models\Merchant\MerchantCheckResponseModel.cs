﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants;

namespace Common.Models.Merchant
{
    public class MerchantCheckResponseModel
    {
        public Guid MerchantId { get; set; }
        public List<MerchantCheck>? MerchantChecks { get; set; } = new List<MerchantCheck>();
        public List<MerchantPersonOfInterest>? MerchantPeopleOfInterest { get; set; } = new List<MerchantPersonOfInterest>();
        public List<PersonChecks>? PersonChecks { get; set; } = new List<PersonChecks>();

    }
}
