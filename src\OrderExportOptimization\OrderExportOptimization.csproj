﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="appsettings.json" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.0" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
		<PackageReference Include="Geidea.PaymentGateway.ConfigServiceClient" Version="3.1.123" />
		<PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
		<PackageReference Include="Geidea.Utils.HttpClientExtensions" Version="1.1.160" />
		<PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="6.0.12" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="6.0.0" />
		<PackageReference Include="Serilog" Version="2.12.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
		<ProjectReference Include="..\Services\Services.csproj" />
	</ItemGroup>

</Project>
