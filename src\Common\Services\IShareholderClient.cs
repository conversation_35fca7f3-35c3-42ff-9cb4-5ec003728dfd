﻿using Common.Models.Shareholder;
using System;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IShareholderClient
    {
        Task<ShareholderCompanyResponse> CreateShareholderCompanyAsync(ShareholderCompanyCreateRequest request);
        Task DeleteShareholderCompanyAsync(Guid shareholderCompanyId);
        Task<ShareholderIndividualResponse> CreateShareholderIndividualAsync(ShareholderIndividualCreateRequest request);
        Task DeleteShareholderIndividualAsync(Guid shareholderIndividualId);
    }
}
