﻿using Common.Models.Merchant.SubordinateMerchant;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models.Merchant;

namespace Common.Services;

public interface ISubordinateMerchantService
{
    Task<SubordinateMerchantSearchResponse> FindAssociatedAndAvailableSubordinateMerchants(Guid merchantId, SubordinateMerchantSearchRequestDto request);
    Task<SubordinateMerchantSearchResponse> FindAssociatedSubordinateMerchants(Guid merchantId, SubordinateMerchantSearchRequestDto request);
    Task<SubordinateMerchant?> FindAssociatedParent(Guid merchantId);
    Task PerformValidationForUpdatingMerchantTag(Guid merchantId);
    Task CreateBusinessHierarchyAsync(Guid parentMerchantId, IReadOnlyCollection<Guid> subordinateMerchantIds);
    Task DeleteBusinessHierarchyAsync(Guid parentMerchantId, Guid subordinateMerchantId);
}