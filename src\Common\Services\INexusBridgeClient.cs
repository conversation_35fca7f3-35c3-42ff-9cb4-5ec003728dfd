﻿using Common.Models.Match;
using Common.Models.NexusBridge;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface INexusBridgeClient
    {

        Task<List<MerchantExternalProduct>> GetMerchantExternalProducts();
        Task<List<MerchantAddress>> GetMerchantAddress(Guid MerchantId);
        Task<string> ConnectToNexusBridge(NBMerchant nBMerchant, bool orderFlag);
        Task<string> ConnectToNexusTerminal(NBProduct product);


        Task<List<MerchantBankAccountResponse>> GetBankAccountDetails(Guid MerchantId);
        Task<List<MerchantTerminal>> GetTerminalDetails(string? accountId);

    }
}

