﻿using System;

namespace Common.Models.Merchant.SubordinateMerchant;

public class SubordinateMerchant
{
    public Guid MerchantId { get; set; }
    public string MerchantTag { get; set; } = string.Empty;
    public Guid? ParentMerchantId { get; set; }
    public string? ParentMerchantTag { get; set; }

    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string NationalId { get; set; } = string.Empty;
    public string? MemberId { get; set; }
    public string? BusinessName { get; set; }
    public string? PhoneNumber { get; set; }

    public bool IsAssociatedToParentMerchant { get; set; }
    public bool IsSentToGle { get; set; }
    public string? Counterparty { get; set; }
    public DateTime? MerchantCreatedDate { get; set; }
}