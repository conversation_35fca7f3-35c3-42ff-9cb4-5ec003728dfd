﻿using Geidea.Utils.Messaging;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Services.Messaging;
using Services.Messaging.Consumers;

namespace BackofficeApi.Extensions
{
    public static class ApplicationBuilderExtensions
    {
        public static IApplicationBuilder StartMessageConsumption(this IApplicationBuilder builder)
        {
            builder.StartMessageConsumption<EPosMessagingClient>(false);
            builder.StartMessageConsumption<ActiveCampaignMessagingClient>(false);
            builder.StartMessageConsumption<MMSOrderUpdateMessagingClient>(false);
            builder.StartMessageConsumption<GleComposePayloadMessagingClient>(false);
            builder.StartMessageConsumption<EPosMessagingSoftwareTypeClient>(false);

            var apexResponseService = builder.ApplicationServices.GetService<ApexResponseService>();
            apexResponseService?.BeginReceive();

            return builder;
        }
    }
}
