﻿using Common.Models.Shareholder;
using FluentValidation;
using System;

namespace Common.Validators;

public class ShareholderCompanyIndividualLinkValidator : AbstractValidator<ShareholderCompanyIndividualLink>
{
	public ShareholderCompanyIndividualLinkValidator()
	{
		RuleFor(x => x.ShareHolderCompanyId)
			.Must(x => x != Guid.Empty)
			.WithErrorCode(Errors.InvalidShareholderCompanyIndividualLinkCompanyId.Code)
			.WithMessage(Errors.InvalidShareholderCompanyIndividualLinkCompanyId.Message);

		RuleFor(x => x.OrganizationRole)
			.Must(x => !string.IsNullOrWhiteSpace(x))
            .WithErrorCode(Errors.InvalidShareholderCompanyIndividualLinkRole.Code)
            .WithMessage(Errors.InvalidShareholderCompanyIndividualLinkRole.Message);
    }
}
