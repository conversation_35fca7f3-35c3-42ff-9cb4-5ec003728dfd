﻿using System;
using System.Collections.Generic;
using System.Linq;
using Common.Models.Merchant;
using FluentValidation;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace Common.Validators;

public class MerchantPatchValidator : AbstractValidator<JsonPatchDocument<PatchMerchantRequest>>
{
    public MerchantPatchValidator()
    {
        RuleFor(x => FindOperation(x, "MerchantDetails/AcquirerReview"))
            .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o.value.ToString()!.Length <= 16)
            .When(o => FindOperation(o, "MerchantDetails/AcquirerReview") != null)
            .WithErrorCode(Errors.AcquirerReviewLengthValidation.Code)
            .WithMessage(Errors.AcquirerReviewLengthValidation.Message);

        RuleFor(x => FindOperation(x, "Tag"))
            .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o.value.ToString()?.Length <= 50)
            .When(o => FindOperation(o, "Tag")?.value != null)
            .WithErrorCode(Errors.NewTagLengthValidation.Code)
            .WithMessage(Errors.NewTagLengthValidation.Message);

        RuleFor(x => FindOperation(x, "MerchantStatus"))
            .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o.value?.ToString()?.Length <= 64)
            .When(o => FindOperation(o, "MerchantStatus") != null)
            .WithErrorCode(Errors.NewStatusLengthValidation.Code)
            .WithMessage(Errors.NewStatusLengthValidation.Message);

        RuleFor(x => FindOperation(x, "MerchantDetails/MCC"))
            .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o.value.ToString()!.Length <= 4)
            .When(o => FindOperation(o, "MerchantDetails/MCC")?.value != null)
            .WithErrorCode(Errors.NewMccLengthValidation.Code)
            .WithMessage(Errors.NewMccLengthValidation.Message);
    }

    private static Operation<PatchMerchantRequest>? FindOperation(JsonPatchDocument<PatchMerchantRequest> o, string pathName)
    {
        return o.Operations.Find(x => x.path.Contains(pathName, StringComparison.InvariantCultureIgnoreCase));
    }
}

