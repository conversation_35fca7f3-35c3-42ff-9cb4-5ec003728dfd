﻿using System;
using System.Collections.Generic;

namespace Common.Models.Merchant
{
    public class MerchantExport
    {
        public Guid MerchantId { get; set; }

        public Guid? LeadId { get; set; }

        public string? MerchantType { get; set; }

        public string? MerchantStatus { get; set; }

        public string CreatedBy { get; set; } = string.Empty;

        public string? UpdatedBy { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public MerchantDetails MerchantDetails { get; set; } = new MerchantDetails();

        public ICollection<MerchantAddress> Addresses { get; set; } = new List<MerchantAddress>();

        public ICollection<MerchantContact> Contacts { get; set; } = new List<MerchantContact>();

        public ICollection<MerchantExportStore> MerchantStores { get; set; } = new List<MerchantExportStore>();

        public ICollection<MerchantExternalIdentifier> ExternalIdentifiers { get; set; } = new List<MerchantExternalIdentifier>();

        public ICollection<MerchantExportPersonOfInterest> PersonsOfInterest { get; set; } = new List<MerchantExportPersonOfInterest>();

        public ICollection<MerchantBankAccount> BankAccounts { get; set; } = new List<MerchantBankAccount>();
    }
}