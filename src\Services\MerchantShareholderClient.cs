﻿using Common.Models.Shareholder;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Services
{
    public partial class MerchantClient : IMerchantClient
    {
        private static string MerchantServiceShareholderBaseUrl => "/api/v1/shareholder";

        public async Task<string> GetShareholderCompaniesBase(ShareholderCompaniesRequest shareholderCompaniesRequest, bool isFilteredSearch = false)
        {
            var serviceUrl = $"{MerchantServiceBaseUrl}{MerchantServiceShareholderBaseUrl}/shareholdercompanies";
            if (isFilteredSearch)
            {
                serviceUrl = $"{serviceUrl}/search";
            }

            using (logger.BeginScope("GetShareholderCompaniesAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to get Shareholder Companies");

                var body = new StringContent(JsonConvert.SerializeObject(shareholderCompaniesRequest), Encoding.UTF8,
                    "application/json");

                var response = await client.PostAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to get Shareholder Companies by merchant id." +
                                       " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return responseBody;
            }
        }

        public async Task CreateShareholderIndividualAssociations(ShareholderIndividualAssociationsCreateRequest request)
        {
            var serviceUrl = $"{MerchantServiceBaseUrl}{MerchantServiceShareholderBaseUrl}/individual/create-associations";

            using (logger.BeginScope("CreateShareholderIndividualAssociations({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling Merchant service to save new roles and relations for individual shareholders");

                var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8,
                    "application/json");

                var response = await client.PostAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling Merchant API to save new roles and relations for individual " +
                                       "shareholder for {shareholderId}." +
                                       " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody, request.ShareholderIndividualId);
                    throw new PassthroughException(response);
                }
            }
        }
    }
}