﻿using Common.Models.Checkout;
using System;
using System.Collections.Generic;

namespace Common.Models.Merchant
{
    public class Merchant
    {
        public Guid MerchantId { get; set; }
        public string? MerchantType { get; set; }
        public string Counterparty { get; set; } = String.Empty;

        public string Tag { get; set; } = String.Empty;

        public MerchantDetails? MerchantDetails { get; set; }

        public string? MerchantStatus { get; set; }
        public MerchantAccountConfig AccountConfig { get; set; } = new MerchantAccountConfig();

        public IList<MerchantCommissionConfig> CommissionTypes { get; set; } = new List<MerchantCommissionConfig>();

    }
}
