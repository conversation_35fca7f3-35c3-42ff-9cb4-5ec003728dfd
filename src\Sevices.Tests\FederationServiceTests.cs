﻿using System;
using System.Net;
using System.Threading.Tasks;
using Common.Options;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using NUnit.Framework;
using Services;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class FederationServiceTests
    {
        [Test]
        public async Task GetMerchantOrganizationIdByPhoneShouldCallTheProperUrlWithTheProperArguments()
        {
            const string? responseContent = "e991d31f-a807-4f93-919f-790da48cceb9";
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, responseContent);

            var optionsMonitorMock = Substitute.For<IOptionsMonitor<UrlSettings>>();
            optionsMonitorMock.CurrentValue.Returns(TestsHelper.UrlSettingsOptions);
            var federationService = new FederationService(httpClient, optionsMonitorMock, Substitute.For<ILogger<FederationService>>());

            var organizationId = await federationService.GetMerchantOrganizationIdByPhoneAsync(Guid.NewGuid(), "************");
            Assert.AreEqual(organizationId, responseContent);
        }

        [Test]
        public async Task GetMerchantOrganizationIdByEmailShouldCallTheProperUrlWithTheProperArguments()
        {
            const string? responseContent = "e991d31f-a807-4f93-919f-790da48cceb9";
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, responseContent);

            var optionsMonitorMock = Substitute.For<IOptionsMonitor<UrlSettings>>();
            optionsMonitorMock.CurrentValue.Returns(TestsHelper.UrlSettingsOptions);
            var federationService = new FederationService(httpClient, optionsMonitorMock, Substitute.For<ILogger<FederationService>>());

            var organizationId = await federationService.GetMerchantOrganizationIdByEmailAsync(Guid.NewGuid(), "<EMAIL>");
            Assert.AreEqual(organizationId, responseContent);
        }
    }
}