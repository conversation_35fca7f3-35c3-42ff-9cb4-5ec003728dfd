﻿using System;
using System.Collections.Generic;

namespace Common.Models.Search
{
    public class OrderItemResponse
    {
        public Guid OrderItemId { get; set; }
        public Guid OrderId { get; set; }
        public DateTime CreatedDate { get; set; }
        public Guid ProductId { get; set; }
        public string? ProductCode { get; set; }

        public List<ProductInstanceResponse>? ProductInstances { get; set; }
    }
}
