﻿using FluentValidation;

namespace Common.Validators
{
    public class CountryPrefixValidator : AbstractValidator<string>
    {
        public CountryPrefixValidator()
        {
            RuleFor(p => p).Matches(@"^\+[0-9]{1,5}$")
                           .WithErrorCode(Errors.InvalidCountryPrefix.Code)
                           .WithMessage(Errors.InvalidCountryPrefix.Message);
        }
    }
}