using System;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using LeadUpdate.Models;
using LeadUpdate.Services;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using NSubstitute;
using NUnit.Framework;

namespace LeadUpdate.Tests
{
    public class LeadServiceTests
    {
        private HttpClient? httpClient;
        private readonly ILogger<LeadService> logger;
        private readonly IOptions<UrlSettings> options;
        private LeadService? leadService;

        private readonly Lead leadWithDetails = new Lead
        {
            DOB = DateTimeOffset.Now,
            Gender = "Female",
            LeadId = Guid.NewGuid(),
            Nationality = "nationality",
            OwnerFirstName = "first name",
            OwnerFirstNameAr = "first name ar",
            OwnerLastName = "last name",
            OwnerLastNameAr = "last name ar"
        };

        private readonly Lead leadWithoutNames = new Lead
        {
            DOB = DateTimeOffset.Now,
            Gender = "Female",
            LeadId = Guid.NewGuid(),
            Nationality = "nationality"
        };

        private readonly Lead leadWithoutGender = new Lead
        {
            DOB = DateTimeOffset.Now,
            LeadId = Guid.NewGuid(),
            Nationality = "nationality",
            OwnerFirstName = "first name",
            OwnerFirstNameAr = "first name ar",
            OwnerLastName = "last name",
            OwnerLastNameAr = "last name ar"
        };


        public LeadServiceTests()
        {
            logger = Substitute.For<ILogger<LeadService>>();
            options = Options.Create(new UrlSettings
            {
                DocumentServiceBaseUrl = "http://documentService",
                SearchServiceBaseUrl = "http://searchService",
                LeadServiceBaseUrl = "http://leadService",
                DocumentServiceBaseUrlNS = "http://documentService",
                SearchServiceBaseUrlNS = "http://searchService",
                LeadServiceBaseUrlNS = "http://leadService"
            });
        }

        [Test]
        public async Task GetIncompleteLeadsAsync_ReturnsCorrectLeads()
        {
            var leadsResponse = new LeadsResponse
            {
                Records = new[] { leadWithDetails, leadWithoutGender, leadWithoutNames }
            };
            var messageHandler = new HttpMessageHandlerMock(HttpStatusCode.OK, JsonSerializer.Serialize(leadsResponse));
            httpClient = new HttpClient(messageHandler);
            leadService = new LeadService(httpClient, options, logger);

            var result = await leadService.GetIncompleteLeadsAsync(0, 10, LeadUpdateType.NamesAndSasInfo, "asc");
            result.Should().HaveCount(2);
            result.Should().Contain(l => l.LeadId == leadWithoutGender.LeadId);
            result.Should().Contain(l => l.LeadId == leadWithoutNames.LeadId);
        }

        [Test]
        public void GetIncompleteLeadsAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            httpClient = new HttpClient(new HttpMessageHandlerMock(HttpStatusCode.BadRequest, ""));
            leadService = new LeadService(httpClient, options, logger);

            leadService.Invoking(x => x.GetIncompleteLeadsAsync(0, 10, LeadUpdateType.NamesAndSasInfo, "asc")).Should().ThrowAsync<PassthroughException>();
        }

        [Test]
        public void PatchLeadAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            httpClient = new HttpClient(new HttpMessageHandlerMock(HttpStatusCode.BadRequest, ""));
            leadService = new LeadService(httpClient, options, logger);

            leadService.Invoking(x => x.PatchLeadAsync(Guid.NewGuid(), new JsonPatchDocument<Lead>())).Should().ThrowAsync<PassthroughException>();
        }

        [Test]
        public void PatchLeadAsync_WhenNoError_UpdatesLead()
        {
            var messageHandler = new HttpMessageHandlerMock(HttpStatusCode.OK, "");
            httpClient = new HttpClient(messageHandler);
            leadService = new LeadService(httpClient, options, logger);

            leadService.Invoking(x => x.PatchLeadAsync(Guid.NewGuid(), new JsonPatchDocument<Lead>())).Should().NotThrowAsync();
        }
    }
}