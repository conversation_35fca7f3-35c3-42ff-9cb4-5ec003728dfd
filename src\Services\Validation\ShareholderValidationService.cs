﻿using Common;
using Common.Models;
using Common.Models.Shareholder;
using Common.Services;
using Common.Services.Validators;
using Common.Validators;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Geidea.Utils.Validation;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using static Common.Constants;

namespace Services.Validation;

public class ShareholderValidationService : IShareholderValidationService
{
    private readonly IReferenceService referenceService;
    private readonly IMerchantClient merchantClient;
    private readonly ILogger<ShareholderValidationService> logger;
    private readonly ICounterpartyProvider counterpartyProvider;
    private static readonly string shareholderCompanyPathName = nameof(MerchantShareholderCompanyResponse.ShareholderCompany);
    private static readonly string shareholderCompanyAdressPathName = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress);

    public ShareholderValidationService(IReferenceService referenceService,
        IMerchantClient merchantClient,
        ILogger<ShareholderValidationService> logger,
        ICounterpartyProvider counterpartyProvider)
    {
        this.referenceService = referenceService;
        this.merchantClient = merchantClient;
        this.logger = logger;
        this.counterpartyProvider = counterpartyProvider;
    }

    public async Task ValidateCreateIndividualRequest(ShareholderIndividualCreateRequest request)
    {
        new ValidationHelpers().Validate(request, new ShareholderIndividualCreateRequestValidator(), logger,
            "Shareholder Individual Create - validation failed");

        var catalogues = await LoadCatalogues();

        ValidateNationality(request?.Nationality, catalogues);
        if (request!.Address != null)
        {
            ValidateCountry(request!.Address.Country, catalogues);
        }

        if (NationalityAndCounterpartyAreSame(request?.Nationality, request?.Counterparty))
        {
            ValidateNationalIdFields(request!);
            await ValidateNationalIdUniqueness(request!.NationalId);
        }
        else
        {
            if (string.IsNullOrWhiteSpace(request?.PassportNo) && string.IsNullOrWhiteSpace(request?.NationalId))
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.ShareholderIndividualPassportOrNationalIdMandatory);

            if (!string.IsNullOrWhiteSpace(request?.NationalId))
                ValidateNationalIdFields(request!, false);

            if (!string.IsNullOrWhiteSpace(request?.PassportNo))
                ValidatePassportFields(request!);
        }
        await ValidateCompaniesRelations(request!.ShareholderCompanies, request!.Merchant!.MerchantId, catalogues);
    }

    public async Task ValidateCreateCompanyRequest(ShareholderCompanyCreateRequest request)
    {
        new ValidationHelpers().Validate(request, new ShareholderCompanyCreateRequestValidator(), logger,
           "Shareholder Company Create - validation failed");

        if (!string.IsNullOrWhiteSpace(request.Country))
        {
            var catalogues = await LoadCatalogues();

            ValidateCountry(request.Country, catalogues);
        }
    }

    public async Task ValidateEditCompanyRequest(ShareholderCompanyPatchRequest request)
    {
        new ValidationHelpers().Validate(request, new ShareholderCompanyPatchRequestValidator(), logger,
          "Shareholder Company Patch request - validation failed");
        new ValidationHelpers().Validate(request.JsonPatchDocument, new ShareholderCompanyJsonPatchDocumentRequestValidator(counterpartyProvider.GetCode()), logger,
           "Shareholder Company JsonPatchDocument - validation failed");

        var countryOperation = FindOperation(request.JsonPatchDocument, GetShareHolderCompanyAdressFullPropertyName(nameof(ShareholderCompanyAddressResponse.Country)));
        if (countryOperation is not null && countryOperation.value != null)
        {
            var catalogues = await LoadCatalogues();
            ValidateCountry(countryOperation.value.ToString(), catalogues);
        }
    }

    public async Task ValidateShareholderIndividualAssociationsCreateRequest(ShareholderIndividualAssociationsCreateRequest request)
    {
        new ValidationHelpers().Validate(request, new ShareholderIndividualAssociationsCreateRequestValidator(), logger,
            "Shareholder Individual Associations - validation failed");
        var catalogue = await referenceService.GetCataloguesAsync(new string[] {
                Catalogues.RelationToCompany
            });

        await ValidateCompaniesRelations(request!.ShareholderCompanies, request!.Merchant!.MerchantId, catalogue);
    }

    public void ValidateIndividualsPatchTemp(ShareholderIndividualPatchRequest request)
    {
        var nationalIdOperation = FindShareholderIndividualOperation(request.ShareholderIndividualJsonPatch, nameof(ShareholderIndividual.NationalId));
        
        if (nationalIdOperation != null && (nationalIdOperation.value == null || string.IsNullOrWhiteSpace(nationalIdOperation.value.ToString())))
        {
            var passportNoOperation = FindShareholderIndividualOperation(request.ShareholderIndividualJsonPatch, nameof(ShareholderIndividual.PassportNo));
            if(passportNoOperation == null || passportNoOperation.value == null || string.IsNullOrWhiteSpace(passportNoOperation.value.ToString()))
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.ShareholderIndividualPassportOrNationalIdMandatory.Code);
            }
            request.ShareholderIndividualJsonPatch.Operations.Remove(nationalIdOperation);
            request.ShareholderIndividualJsonPatch.Replace(x => x.NationalId, passportNoOperation.value);
        }

        var ownershipOperation = request.MerchantShareholderIndividualJsonPatch.Operations.FirstOrDefault(
            x=>x.OperationType == OperationType.Replace &&
            x.path.Replace("/", "").Equals(nameof(MerchantPersonOfInterest.OwnershipPercentage), StringComparison.InvariantCultureIgnoreCase));

        if(ownershipOperation != null && ownershipOperation.value == null)
        {
            request.MerchantShareholderIndividualJsonPatch.Operations.Remove(ownershipOperation);
            request.MerchantShareholderIndividualJsonPatch.Replace(o => o.OwnershipPercentage, 0);
        }
    }

    private static Operation<ShareholderIndividual>? FindShareholderIndividualOperation(JsonPatchDocument<ShareholderIndividual> o, string path) =>
        o.Operations.FirstOrDefault(x => x.OperationType == OperationType.Replace &&
        x.path.Replace("/", "").Equals(path, StringComparison.InvariantCultureIgnoreCase));

    private async Task<Catalogue[]> LoadCatalogues()
    {
        return await referenceService.GetCataloguesAsync(
            new string[] {
                Catalogues.Areas,
                Catalogues.Governorates,
                Catalogues.Cities,
                Catalogues.Nationalities,
                Catalogues.CountriesIso3166,
                Catalogues.RelationToCompany
            });
    }

    private static void ValidateNationality(string? nationality, Catalogue[] catalogues)
    {
        if (!catalogues.Any(x => x.CatalogueName == Constants.Catalogues.Nationalities && x.Key == nationality))
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.NationalityValidation);
        }
    }

    private static void ValidateCountry(string? country, Catalogue[] catalogues)
    {
        if (string.IsNullOrEmpty(country))
        {
            return;
        }

        if (!catalogues.Any(x => x.CatalogueName == Constants.Catalogues.CountriesIso3166 && x.Key == country))
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.CountryValidation);
        }
    }

    private static bool NationalityAndCounterpartyAreSame(string? nationality, string? counterparty)
    {
        return (counterparty == Constants.CounterParty.Saudi && nationality == Constants.CountryCodes.GeideaSaudi)
            || (counterparty == Constants.CounterParty.Egypt && nationality == Constants.CountryCodes.GeideaEgypt);
    }

    private void ValidateNationalIdFields(ShareholderIndividualCreateRequest request, bool validateId = true)
    {
        if (!IsDateValid(request.IdExpiryDate))
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.ShareholderIndividualIdExpiryDateValidation);
        }

        if (!validateId)
            return;

        if (request.Counterparty == CounterParty.Saudi)
        {
            new ValidationHelpers().Validate(request.NationalId, new SaudiNationalIdValidator(), logger,
                    "NationalId validation failed");
        }
        else if (request.Counterparty == CounterParty.Egypt)
        {
            new ValidationHelpers().Validate(request.NationalId, new EgyptNationalIdValidator(), logger,
                "NationalId validation failed");
        }
    }

    private static void ValidatePassportFields(ShareholderIndividualCreateRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.PassportNo))
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.ShareholderIndividualPassportValidation);
        }
        if (!IsDateValid(request.PassportExpirationDate))
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.ShareholderIndividualPassportExpiryDateValidation);
        }
    }

    private static bool IsDateValid(DateTime? dateTime)
    {
        return dateTime != null
            && !dateTime.Equals(DateTime.MinValue)
            && !dateTime.Equals(DateTime.MaxValue);
    }

    private async Task ValidateNationalIdUniqueness(string? nationalId)
    {
        var existingPoiResponse = await merchantClient.SearchPersonOfInterest(new PoiSearchCriteriaRequest
        {
            NationalId = nationalId,
            Take = 1
        });
        if (existingPoiResponse != null && existingPoiResponse.TotalRecordCount > 0)
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.NationalIdIsNotUnique);
        }
    }

    private async Task ValidateCompaniesRelations(List<ShareholderCompanyIndividualLink>? shareholderCompanyIndividualLinks, Guid merchantId,
        Catalogue[] catalogues)
    {
        if (shareholderCompanyIndividualLinks == null || !shareholderCompanyIndividualLinks.Any())
        {
            return;
        }

        var merchantCompanyIds = await GetMerchantCompanyIds(merchantId);
        foreach (var relation in shareholderCompanyIndividualLinks)
        {
            if (relation is null)
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidShareholderCompanyIndividualLinkEntry);
            }
            if (!catalogues.Any(x => x.CatalogueName == Catalogues.RelationToCompany && x.Key == relation.OrganizationRole))
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidShareholderCompanyIndividualLinkRole);
            }
            if (!merchantCompanyIds.Contains(relation.ShareHolderCompanyId))
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidShareholderCompanyIndividualLinkCompanyId);
            }
        }
    }

    private async Task<List<Guid>> GetMerchantCompanyIds(Guid merchantId)
    {
        var merchantCompaniesRawResponse = await merchantClient.GetShareholderCompaniesBase(new ShareholderCompaniesRequest { MerchantId = merchantId });
        var response = Json.Deserialize<List<MerchantShareholderCompanyResponse>>(merchantCompaniesRawResponse, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        return response.Select(c => c.ShareholderCompanyId).ToList();
    }

    private static Operation<MerchantShareholderCompanyResponse>? FindOperation(JsonPatchDocument<MerchantShareholderCompanyResponse> o, string pathName)
      => o.Operations.Find(x => x.path.Equals(pathName, StringComparison.InvariantCultureIgnoreCase));

    private static string GetShareHolderCompanyAdressFullPropertyName(string propName) =>
        string.Format("/{0}/{1}/{2}", shareholderCompanyPathName, shareholderCompanyAdressPathName, propName);
}
