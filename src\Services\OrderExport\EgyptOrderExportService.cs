﻿using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Product;
using Common.Models.Search;
using Common.Services;
using Geidea.Utils.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Services.OrderExport
{
    public class EgyptOrderExportService : IEgyptOrderExportService
    {
        private readonly IReferenceService referenceService;
        private readonly ISearchService searchService;

        private readonly List<string> supportedTypes = new List<string>()
        {
            "WSB",
            "GWAY",
            "GWAY_ADDON",
            "MINI_ECR",
            "SERVICES"
        };

        public EgyptOrderExportService(IReferenceService referenceService, ISearchService searchService)
        {
            this.referenceService = referenceService;
            this.searchService = searchService;
        }

        public async Task<EgyptOrderExportResponse> ExportOrderByIdAsync(Guid orderId)
        {
            var orderSearchCriteria = new OrderSearchCriteria() { OrderId = orderId };
            var result = (await ExportOrdersAsync(orderSearchCriteria)).FirstOrDefault();

            if (result == null)
            {
                throw new ServiceException(HttpStatusCode.NotFound);
            }

            return result;
        }

        public async Task<List<EgyptOrderExportResponse>> ExportOrdersAsync(OrderSearchCriteria orderSearchCriteria)
        {
            var catalogues = await referenceService.GetCataloguesAsync(new string[] { Constants.Catalogues.AcquiringLedger, Constants.Catalogues.MerchantTag, Constants.Catalogues.Banks,
                                                                                    Constants.Catalogues.BusinessDomain, Constants.Catalogues.Governorates,Constants.Catalogues.Cities, Constants.Catalogues.MerchantCategoryCode,
                                                                                    Constants.Catalogues.NbeBusinessDomain, Constants.Catalogues.NbeGovernarotes, Constants.Catalogues.NbeCities, Constants.Catalogues.NbeMerchantCategoryCode,
                                                                                    Constants.Catalogues.AlxBusinessDomain, Constants.Catalogues.AlxGovernarotes, Constants.Catalogues.AlxCities, Constants.Catalogues.AlxMerchantCategoryCode });

            ApplyOrderSearchCriteria(orderSearchCriteria, catalogues);

            var orders = await searchService.ExportOrdersAsync(orderSearchCriteria);
            var ordersExport = MapOrderExport(orders, catalogues);

            return ordersExport.ToList();
        }

        private static void ApplyOrderSearchCriteria(OrderSearchCriteria orderSearchCriteria, Catalogue[] catalogues)
        {
            // to remove StoreCity or  from SearchIn array before sending to order search service
            // we will keep the value of keyword in case of multiple values in SearchIn
            // Also, in search service if it found a value in the keyword property without any values in SearchIn, it would be ignored
            // Also, check if the keyword is not empty or null becuase if I'm not check the all cities codes will be sent.
            if (orderSearchCriteria.SearchIn.Remove(Constants.OrderSearchProperties.StoreCity) && !string.IsNullOrEmpty(orderSearchCriteria.Keyword))
            {
                var cityCatalogues = new string[] { Constants.Catalogues.Cities, Constants.Catalogues.NbeCities, Constants.Catalogues.AlxCities };

                orderSearchCriteria.CityCodes = catalogues.Where(c => c.Value.Contains(orderSearchCriteria.Keyword!, StringComparison.OrdinalIgnoreCase)
                     && cityCatalogues.Contains(c.CatalogueName)).Select(c => c.Key).Distinct().ToList();

                // Because if there was no city matches the keyword, it would return empty list so the search result would be empty and it will returns all orders
                if (orderSearchCriteria.CityCodes.Count == 0)
                    orderSearchCriteria.CityCodes.Add(orderSearchCriteria.Keyword!);
            }

            // Also, check if the keyword is not empty or null becuase if I'm not check the terminal TID will be sent with empty string
            // and this may cause a performance issue
            if (orderSearchCriteria.SearchIn.Remove(Constants.OrderSearchProperties.TerminalTID) && !string.IsNullOrEmpty(orderSearchCriteria.Keyword))
            {
                orderSearchCriteria.TerminalTID = orderSearchCriteria.Keyword;
            }
        }

        private List<EgyptOrderExportResponse> MapOrderExport(List<OrdersExport> orders, Catalogue[] catalogues)
        {
            var ordersExports = new List<EgyptOrderExportResponse>();

            foreach (var order in orders)
            {
                EgyptOrderExportResponse orderExport = new EgyptOrderExportResponse();

                SetOrderExport(order, orderExport, catalogues);
                SetValuesForCatalogues(order, orderExport, catalogues);

                if (order.OrderItem != null)
                {
                    SetProductExport(order, orderExport, catalogues);
                }
                ordersExports.Add(orderExport);
            }
            return ordersExports;
        }

        private static void SetOrderExport(OrdersExport order, EgyptOrderExportResponse orderExport, Catalogue[] catalogues)
        {
            orderExport.BusinessName = order.LegalName;
            orderExport.OrderNumber = order.OrderNumber;
            orderExport.MemberId = order.MemberId;
            orderExport.Phone = order.PhoneNumber;
            orderExport.FirstName = order.FirstName;
            orderExport.LastName = order.LastName;
            orderExport.NationalId = order.NationalId;
            orderExport.Email = order.Email;
            orderExport.BusinessAddress = order.AddressLine1;
            orderExport.CommercialRegistration = order.RegistrationNumber;
            orderExport.SalesPersonFirstName = order.SalesPersonFirstName;
            orderExport.SalesPersonLastName = order.SalesPersonLastName;
            orderExport.DeliveryMethod = order.DeliveryMethod;
            orderExport.DeliveryDays = order.DeliveryDays;
            orderExport.ProofOfDelivery = order.ProofOfDelivery;
            orderExport.BillPayments = order.BillPayments;
            orderExport.OrderStatus = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.OrderStatus
                                       && x.Key == order.OrderStatus) != null ? catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.OrderStatus
                                       && x.Key == order.OrderStatus)!.Value : order.OrderStatus;
            orderExport.MerchantStatus = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.MerchantStatus
                                   && x.Key == order.MerchantStatus) != null ? catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.MerchantStatus
                                   && x.Key == order.MerchantStatus)!.Value : order.MerchantStatus;

            if (order.BankAccounts!.Count > 0)
            {
                var firstBankAccount = order.BankAccounts!.FirstOrDefault()!;
                orderExport.AccountHolderName = firstBankAccount.AccountHolderName;
                orderExport.BankAccountNumber = firstBankAccount.BankAccountNumber;
                orderExport.IBAN = firstBankAccount.Iban;
            }
        }

        private static void SetValuesForCatalogues(OrdersExport order, EgyptOrderExportResponse orderExport, Catalogue[] catalogues)
        {
            string governorateCatalogue = ReturnCatalogue(order.AcquiringLedger, catalogues, Constants.Catalogues.Governorates);
            string cityCatalouge = ReturnCatalogue(order.AcquiringLedger, catalogues, Constants.Catalogues.Cities);
            string businessDomaincatalogue = ReturnCatalogue(order.AcquiringLedger, catalogues, Constants.Catalogues.BusinessDomain);
            string mccCatalouge = ReturnCatalogue(order.AcquiringLedger, catalogues, Constants.Catalogues.MerchantCategoryCode);

            orderExport.BusinessDomain = catalogues.Where(x => x.CatalogueName == businessDomaincatalogue && x.Key == order.BusinessDomain).Select(x => x.Value).FirstOrDefault();
            orderExport.MerchantType = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.MerchantTag && x.Key == order.MerchantType).Select(x => x.Value).FirstOrDefault();
            orderExport.Governorate = catalogues.Where(x => x.CatalogueName == governorateCatalogue && x.Key == order.Governorate).Select(x => x.Value).FirstOrDefault();
            orderExport.MCC = catalogues.Where(x => x.CatalogueName == mccCatalouge && x.Key == order.MCC).Select(x => x.Key).FirstOrDefault();
            orderExport.City = catalogues.Where(x => x.CatalogueName == cityCatalouge && x.Key == order.City).Select(x => x.Value).FirstOrDefault();
            orderExport.Acquirer = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.AcquiringLedger && x.Key == order.AcquiringLedger).Select(x => x.Value).FirstOrDefault();
            orderExport.StoreCity = catalogues.Where(x => x.CatalogueName == cityCatalouge && x.Key == order.StoreCity).Select(x => x.Value).FirstOrDefault();

            if (order.BankAccounts!.Count > 0)
            {
                var firstBankAccount = order.BankAccounts!.FirstOrDefault()!;
                orderExport.BankName = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.Banks && x.Key == firstBankAccount.RefBankId.ToString()).Select(x => x.Value).FirstOrDefault();
            }
        }

        private static string ReturnCatalogue(string? acquiringLedger, Catalogue[] catalogues, string catalogueName)
        {
            if (acquiringLedger != null && catalogues.Any(r => r.CatalogueName.StartsWith(acquiringLedger, StringComparison.OrdinalIgnoreCase)))
                catalogueName = ConcatAcquirerWithCatalogueName(acquiringLedger, catalogueName);

            return catalogueName;
        }

        public static string ConcatAcquirerWithCatalogueName(string acquirer, string catalogueName)
        {
            return $"{acquirer.ToUpper()}_{catalogueName}";
        }

        private void SetProductExport(OrdersExport order, EgyptOrderExportResponse orderExport, Catalogue[] catalogues)
        {
            var orderItems = order.OrderItem!.Where(x => x.ProductInstances != null).ToList();
            var productInstances = orderItems.SelectMany(x => x.ProductInstances!).ToList();
            orderExport.Quantity = 0;

            if (!productInstances.Any())
            {
                foreach (var orderItem in order.OrderItem!)
                {
                    var exportedProduct = new EgyptProductExportResponse();
                    exportedProduct.ProductCode = orderItem.ProductCode;

                    orderExport.ProductExport.Add(exportedProduct);
                    ++orderExport.Quantity;
                }
            }

            foreach (var prodInstance in productInstances)
            {
                var exportedProduct = new EgyptProductExportResponse();
                if (supportedTypes.Contains(prodInstance.ProductType!))
                {
                    exportedProduct.ProductCode = prodInstance.ProductCode;
                    orderExport.ProductExport.Add(exportedProduct);
                    ++orderExport.Quantity;
                }
                else if (prodInstance.ProductType == Constants.ProductType.Mpos || prodInstance.ProductType == Constants.ProductType.Terminal)
                {
                    var terminalData = prodInstance.Metadata as TerminalData;

                    exportedProduct.ProductCode = prodInstance.ProductCode;
                    AddTerminalData(exportedProduct, terminalData!);

                    orderExport.ProductExport.Add(exportedProduct);
                    ++orderExport.Quantity;
                }

                if (prodInstance.Children != null)
                {
                    orderExport.ProductExport.AddRange(SetProductInstanceChildren(catalogues, prodInstance, orderExport));
                }
            }
        }

        private List<EgyptProductExportResponse> SetProductInstanceChildren(Catalogue[] catalogues, ProductInstanceResponse prodInstance, EgyptOrderExportResponse orderExport)
        {
            var list = new List<EgyptProductExportResponse>();
            foreach (var children in prodInstance.Children!)
            {
                var exportedProduct = new EgyptProductExportResponse();
                if (supportedTypes.Contains(children.ProductType!))
                {
                    exportedProduct.ProductCode = children.ProductCode;
                    list.Add(exportedProduct);
                    ++orderExport.Quantity;
                }
                else if (children.ProductType == Constants.ProductType.Mpos || children.ProductType == Constants.ProductType.Terminal)
                {
                    var terminalData = children.Metadata as TerminalData;

                    exportedProduct.ProductCode = children.ProductCode;
                    AddTerminalData(exportedProduct, terminalData!);

                    list.Add(exportedProduct);
                    ++orderExport.Quantity;
                }
            }
            return list;
        }

        private static void AddTerminalData(EgyptProductExportResponse exportedProduct, TerminalData terminalData)
        {
            exportedProduct.TID = terminalData?.TId;
            exportedProduct.MID = terminalData?.MIDMerchantReference;
        }
    }
}
