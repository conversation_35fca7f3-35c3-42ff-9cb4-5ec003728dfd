﻿using System;

namespace Common.Models.Product
{
    public class FindProductRequest
    {
        public Guid? Id { get; set; }
        public string? Availability { get; set; }
        public string? Flow { get; set; }
        public string? SalesChannel { get; set; }
        public string? Code { get; set; }
        public string? Type { get; set; }
        public string? Description { get; set; }
        public Guid? CategoryId { get; set; }
        public bool OnlyValid { get; set; } = true;
    }
}
