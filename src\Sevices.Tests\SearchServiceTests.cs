﻿using AutoMapper;
using BackofficeApi;
using Common.Models.Checkout;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Tasks;
using static Common.Constants;
using Common.Models.Search;
using Common.Models.Merchant.SubordinateMerchant;
using NSubstitute;
using System.Net.Http;
using Common.Models.User;
using Common.Models.Merchant;
using Geidea.Utils.Counterparty.Providers;


namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class SearchServiceTests
    {
        private readonly Mock<ILogger<SearchService>> logger = new Mock<ILogger<SearchService>>();
        private SearchService searchService = null!;
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
        private readonly Mock<IProductService> productService = new Mock<IProductService>();
        private ICounterpartyProvider counterpartyProvider = null!;
        private readonly Mapper mapper;


        private readonly SearchResponse<TaskResponse> taskSearchResponse = new()
        {
            Records = new List<TaskResponse>()
            {
                new()
                {
                    Assignee = "test",
                    AssigneeUserId = Guid.NewGuid(),
                    BusinessName = "test",
                    CreatedDate = DateTime.UtcNow,
                    FirstName = "test",
                    LastName = "test",
                    MerchantId = Guid.NewGuid(),
                    Resolution = "test",
                    TaskCommentsCount = 0,
                    TaskId = Guid.NewGuid(),
                    TaskNumber = 1,
                    TaskStatus = "status",
                    TaskType = "type"
                }
            },
            ReturnedRecordCount = 1,
            TotalRecordCount = 1
        };

        private static readonly SearchResponse<SubordinateMerchant> subordinateMerchants = new()
        {
            TotalRecordCount = 1,
            ReturnedRecordCount = 1,
            Records = new List<SubordinateMerchant>
                {
                    new()
                    {
                        MerchantId = Guid.NewGuid(),
                        ParentMerchantId = null,
                        FirstName = "FirstName1",
                        LastName = "LastNameTest1",
                        NationalId = "45678965",
                        PhoneNumber = "564366063",
                        BusinessName = "BusinessName1",
                        MemberId = "MemberId2",
                        MerchantTag = "Sub-Business",
                        MerchantCreatedDate = DateTime.UtcNow,
                        IsAssociatedToParentMerchant = false,
                        IsSentToGle = false,
                        Counterparty = Geidea.Utils.Common.Constants.CounterpartyEgypt
                    },
                    new()
                    {
                        MerchantId = Guid.NewGuid(),
                        ParentMerchantId = Guid.NewGuid(),
                        FirstName = "FirstName2",
                        LastName = "LastNameTest2",
                        NationalId = "11118965",
                        PhoneNumber = "2222366063",
                        BusinessName = "BusinessName2",
                        MemberId = "MemberId2",
                        MerchantTag = "Sub-Business",
                        MerchantCreatedDate = DateTime.UtcNow,
                        IsAssociatedToParentMerchant = true,
                        IsSentToGle = false,
                        Counterparty = Geidea.Utils.Common.Constants.CounterpartyEgypt
                    },
                    new()
                    {
                        MerchantId = Guid.NewGuid(),
                        ParentMerchantId = Guid.NewGuid(),
                        FirstName = "FirstName3",
                        LastName = "LastNameTest3",
                        NationalId = "11118968",
                        PhoneNumber = "2222366063",
                        BusinessName = "BusinessName3",
                        MemberId = "MemberId3",
                        MerchantTag = "Sub-Business",
                        MerchantCreatedDate = DateTime.UtcNow,
                        IsAssociatedToParentMerchant = true,
                        IsSentToGle = true,
                        Counterparty = Geidea.Utils.Common.Constants.CounterpartyEgypt
                    }
                }
        };

        private static readonly UserAdvancedSearchResponse userAdvancedSearchResponse = new()
        {
            Id = Guid.NewGuid(),
            FirstName = "FirstName",
            LastName = "LastName"
        };

        private static readonly UserRoleAdvancedSearchResponse userRoleSearchResponse = new()
        {
            Id = Guid.NewGuid(),
            RoleName = "memeber"
        };

        private readonly UserSearchResponse<UserAdvancedSearchResponse> userSearchResponse = new()
        {
            Records = new List<UserAdvancedSearchResponse>()
            {
               userAdvancedSearchResponse
            },
            ReturnedRecordCount = 1,
            TotalRecordCount = 1
        };

        private readonly UserRoleSearchResponse<UserRoleAdvancedSearchResponse> userRolesSearchResponse = new()
        {
            Records = new List<UserRoleAdvancedSearchResponse>()
            {
               userRoleSearchResponse
            },
            ReturnedRecordCount = 1,
            TotalRecordCount = 1
        };

        private readonly UserAdvancedSearchFilter filter = new()
        {
            SearchIn = new string[] { "<EMAIL>" },
            Keyword = "<EMAIL>",
            Groups = new List<string> { "firstGroup", "secondGroup" }
        };

        private readonly UserRoleSearchFilter rolesfilter = new()
        {
            SearchIn = new string[] { "<EMAIL>" },
            Keyword = "<EMAIL>",
            RoleName = new List<string> { "memeber" }
        };

        public SearchServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
            var profile = new AutoMapping();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
            counterpartyProvider = Substitute.For<ICounterpartyProvider>();
            mapper = new Mapper(configuration);
        }

        [Test]
        public void ExportOrderAsync()
        {
            searchService = new SearchService(logger.Object,
                mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                urlSettingsOptions.Object,
                productService.Object, counterpartyProvider);

            searchService.Invoking(x => x.ExportOrdersAsync(new OrderSearchCriteria() { ProductCodes = new List<string>() { "TEST" } })).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void ExportOrderAsync_PassthroughtError()
        {
            searchService = new SearchService(logger.Object,
                mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object,
                productService.Object, counterpartyProvider);

            searchService.Invoking(x => x.ExportOrdersAsync(new OrderSearchCriteria()))
                .Should().ThrowAsync<PassthroughException>()
                    .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task TaskAdvancedSearch_WhenReceiveResponse_ShouldReturnResponse()
        {
            searchService = new SearchService(logger.Object,
                mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(taskSearchResponse)),
                urlSettingsOptions.Object,
                productService.Object, counterpartyProvider);

            var result = await searchService.TaskAdvancedSearch(new TaskSearchRequest());

            result.Records.Count.Should().Be(1);
            result.Records[0].Should().BeEquivalentTo(taskSearchResponse.Records[0]);
        }

        [Test]
        public async Task TaskAdvancedSearch_WhenResponseNotSuccessful_ShouldThrow()
        {
            searchService = new SearchService(logger.Object,
                mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest),
                urlSettingsOptions.Object,
                productService.Object, counterpartyProvider);

            await searchService.Invoking(x => x.TaskAdvancedSearch(new TaskSearchRequest()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task GetOrderProductInstancesWithTerminalDataAsync_ShouldReturnResponse()
        {
            var orderId = Guid.NewGuid();
            var orderWithInstances = new OrderWithInstances
            {
                CheckoutDate = DateTime.Now,
                OrderIsDeleted = false,
                OrderId = orderId,
                OrderNumber = "test",
                OrderStatus = "VERFIED",
                StoreId = Guid.NewGuid(),
                Instances = new List<InstanceWithTerminalData>() {
                new InstanceWithTerminalData{
                Mid=null,
                ParentProductCode=null,
                ParentProductInstanceId=Guid.NewGuid(),
                ParentProductInstanceIsDeleted=false,
                ParentProductType=null,
                ProductCode=null,
                ProductInstanceId=Guid.NewGuid(),
                ProductInstanceIsDeleted=true,
                ProductType=null,
                TerminalId=null,
                Tid=null,
                Trsm=null,
                }
                }
            };

            searchService = new SearchService(logger.Object,
              mapper,
              TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(orderWithInstances)),
              urlSettingsOptions.Object,
              productService.Object, counterpartyProvider);

            var result = await searchService.GetOrderProductInstancesWithTerminalDataAsync(Guid.NewGuid(), new List<string> { ProductType.Terminal });
            result.Should().NotBeNull();
        }

        [Test]
        public async Task GetOrderProductInstancesWithTerminalDataAsync_ShouldThrowException()
        {
            var orderId = Guid.NewGuid();
            var orderWithInstances = new OrderWithInstances
            {
                CheckoutDate = DateTime.Now,
                OrderIsDeleted = false,
                OrderId = orderId,
                OrderNumber = "test",
                OrderStatus = "VERFIED",
                StoreId = Guid.NewGuid(),
                Instances = new List<InstanceWithTerminalData>() {
                new InstanceWithTerminalData{
                Mid=null,
                ParentProductCode=null,
                ParentProductInstanceId=Guid.NewGuid(),
                ParentProductInstanceIsDeleted=false,
                ParentProductType=null,
                ProductCode=null,
                ProductInstanceId=Guid.NewGuid(),
                ProductInstanceIsDeleted=true,
                ProductType=null,
                TerminalId=null,
                Tid=null,
                Trsm=null,
                }
                }
            };

            searchService = new SearchService(logger.Object,
              mapper,
              TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, JsonSerializer.Serialize(orderWithInstances)),
              urlSettingsOptions.Object,
              productService.Object, counterpartyProvider);

            try
            {
                await searchService.GetOrderProductInstancesWithTerminalDataAsync(Guid.NewGuid(), new List<string> { ProductType.Terminal });
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.AreEqual(ex.GetType(), typeof(PassthroughException));
            }
        }

        [Test]
        public void SearchSubordinateMerchants_WhenServiceReturnsError_ShouldThrowPassthroughException()
        {
            searchService = new SearchService(
                logger.Object, mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error code"),
                urlSettingsOptions.Object,
                productService.Object, counterpartyProvider);

            searchService.Invoking(x => x.SearchSubordinateMerchants(new SubordinateMerchantSearchFilters()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task SearchSubordinateMerchants_ShouldReturnAllEntries()
        {
            searchService = new SearchService(
                logger.Object, mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(subordinateMerchants)),
                urlSettingsOptions.Object,
                productService.Object, counterpartyProvider);

            var result = await searchService.SearchSubordinateMerchants(new SubordinateMerchantSearchFilters());

            result.Should().BeEquivalentTo(subordinateMerchants);
        }

        [Test]
        public async Task FindAsync_Returns_Users()
        {
            searchService = new SearchService(logger.Object, mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(userSearchResponse)),
                urlSettingsOptions.Object, productService.Object, counterpartyProvider);

            var result = await searchService.FindUsersAsync(filter);

            result.Records.Count.Should().Be(1);
            result.Records[0].Should().BeEquivalentTo(userAdvancedSearchResponse);
        }

        [Test]
        public void FindAsync_PassthroughError()
        {
            searchService = new SearchService(logger.Object, mapper, TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                                              urlSettingsOptions.Object, productService.Object, counterpartyProvider);

            searchService.Invoking(x => x.FindUsersAsync(filter))
                         .Should().ThrowAsync<PassthroughException>()
                         .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void FindUsersGroupAsync_PassthroughError()
        {
            searchService = new SearchService(logger.Object, mapper, TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                                              urlSettingsOptions.Object, productService.Object, counterpartyProvider);

            searchService.Invoking(x => x.FindUsersRoleAsync(rolesfilter))
                         .Should().ThrowAsync<PassthroughException>()
                         .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task FindUsersGroupAsync_Returns_UsersGroup()
        {
            searchService = new SearchService(logger.Object, mapper,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(userRolesSearchResponse)),
                urlSettingsOptions.Object, productService.Object, counterpartyProvider);

            var result = await searchService.FindUsersRoleAsync(rolesfilter);

            result.Records.Count.Should().Be(1);
            result.Records[0].Should().BeEquivalentTo(userRolesSearchResponse.Records[0]);
        }
    }
}
