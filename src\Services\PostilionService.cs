﻿using Common.Models.Postilion;
using Common.Services;
using Services.Messaging;

namespace Services
{
    public class PostilionService : IPostilionService
    {
        private readonly PostilionMessageClient messageClient;

        public PostilionService(PostilionMessageClient messageClient)
        {
            this.messageClient = messageClient;
        }

        public void SendMessageToPostilion(PostilionMessageBody messageBody)
        {
            messageClient.Send(messageBody);
        }
    }
}
