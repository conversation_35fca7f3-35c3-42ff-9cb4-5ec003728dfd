﻿using System;

namespace Common.Models.Merchant
{
    public class MerchantShortExportResponse
    {
        public Guid MerchantId { get; set; }
        public Guid? LeadId { get; set; }
        public string? City { get; set; } = null!;
        public string? Area { get; set; } = null!;
        public string? AccountHolderName { get; set; } = null!;
        public string? IBAN { get; set; } = null!;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? FirstNameAr { get; set; }
        public string? LastNameAr { get; set; }
        public string? NationalId { get; set; }
        public string? CityCr { get; set; }
        public string? AddressCr { get; set; }
        public string? MerchantName { get; set; }
        public string? MCC { get; set; }
        public string? MerchantStatus { get; set; }
    }
}
