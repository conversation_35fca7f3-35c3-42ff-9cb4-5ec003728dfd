﻿using Common.Models;
using FluentValidation;

namespace Common.Validators
{
    public class SendStatusUpdateEmialRequestValidator : AbstractValidator<SendStatusUpdateEmailRequest>
    {
        public SendStatusUpdateEmialRequestValidator()
        {
            RuleFor(a => a.RecipientName).NotNull().NotEmpty();
            RuleFor(a => a.Recipient).NotNull().NotEmpty();
            RuleFor(a => a.CurrentStatus).NotNull().NotEmpty();
        }
    }
}
