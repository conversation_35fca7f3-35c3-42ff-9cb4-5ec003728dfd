﻿namespace Services.Messaging
{
    using Geidea.Utils.Messaging;
    using Geidea.Utils.Messaging.Base;
    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using System;
    using Common.Helpers;

    public class EPosMessagingClient : MessageClient
    {
        public EPosMessagingClient(
            ILogger<MessageClient> logger,
            IHttpContextAccessor contextAccessor,
            QueueSettings queue,
            IOptionsMonitor<RabbitMqOptions> rabbitMqOptions)
            : base(contextAccessor, logger, rabbitMqOptions, queue)
        {
        }

        public virtual void SendMessageToEPos<T>(T payload) where T : class, ILogable, new()
        {
            try
            {
                base.SendMessage(payload);
            }
            catch (Exception ex)
            {
                var gdprCompliantString = payload.ToGDPRCompliantString();
                logger.LogError(ex, "Exception occured while sending message cu EPos Queue - {@gdprCompliantString}", gdprCompliantString);
                throw;
            }
        }
    }
}
