<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BackofficeApi</name>
    </assembly>
    <members>
        <member name="M:BackofficeApi.Controllers.AccountController.Find(Common.Models.Account.AccountSearchFilters,System.Threading.CancellationToken)">
            <summary>
            Finds all accounts based o search criteria in the form of an OData query.
            </summary>
            <returns>An array of accounts objects matching the search criteria.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.Find(Common.Models.Chain.ChainSearchFilters,System.Threading.CancellationToken)">
            <summary>
            Finds all chains based on search criteria in the form of an OData query.
            </summary>
            <returns>An array of chains objects matching the search criteria.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.ExportChains(Common.Models.Chain.ChainSearchFilters)">
            <summary>
            Export all chains based on search criteria in the form of an OData query.
            </summary>
            <returns>An array of chains objects matching the search criteria.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.CreateChain(Common.Models.Chain.ChainCreateRequest)">
            <summary>
            Create Chain.
            </summary>
            <returns>An Created Chain object.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.UpdateChain(Common.Models.Chain.ChainUpdateRequest)">
            <summary>
            Update Chain.
            </summary>
            <returns>An Created Chain object.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.GetChain(System.String)">
            <summary>
            Get Chain.
            </summary>
            <returns>Chain object will return.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.DeleteChain(System.String)">
            <summary>
            Delete Chain.
            </summary>
            <returns>Delete Chain.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.UpdateChainMerchants(System.String,System.Collections.Generic.List{Common.Models.Chain.ChainMerchantsLinkRequest})">
            <summary>
            Update chain merchants association.
            </summary>
            <returns>Merchants list associated with the chain will return.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.GetChainMerchants(System.String)">
            <summary>
            Get merchants associated with the chain.
            </summary>
            <returns>Merchants list associated with the chain will return..</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.GetChainContacts(System.String)">
            <summary>
            Get contacts associated with the chain.
            </summary>
            <returns>Chain Contacts list associated with the chain will return..</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.GetChainContact(System.Guid)">
            <summary>
            Get contact based on contactid.
            </summary>
            <returns>Chain Contact associated with the contactId will return..</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.DeleteChainContact(System.Guid)">
            <summary>
            Delete Chain Contact.
            </summary>
            <returns>Delete Chain Contact.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.CreateChainContacts(Common.Models.Chain.ChainContactsCreateRequest)">
            <summary>
            Create Chain Contacts.
            </summary>
            <returns>Created Chain Contacts will return.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ChainController.UpdateChainContacts(Common.Models.Chain.ChainContactsUpdateRequest)">
            <summary>
            Update Chain Contacts.
            </summary>
            <returns>Created Chain Contacts will return.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.DocumentController.GetCheckAttributes(System.Guid,System.String)">
            <summary>
            Returns the check attributes
            </summary>
            <param name="documentId">The id of the document</param>
            <param name="checkType">The check type of the document</param>
        </member>
        <member name="M:BackofficeApi.Controllers.DocumentController.GetDocument(System.Guid)">
            <summary>
            Returns a document as a stream.
            </summary>
            <param name="documentId">The id of the document</param>
            <response code="200">The document's data as a byte stream</response>
            <response code="403">User does not have access to that document</response>
            <response code="404">No document was found with the specified id</response>
        </member>
        <member name="M:BackofficeApi.Controllers.DocumentController.GetZippedDocuments(System.Guid)">
            <summary>
            Returns a zip file containing all the documents of the merchant, except the contracts.
            </summary>
            <param name="merchantId">The id of the merchant.</param>
            <response code="200">The document's data as a byte stream.</response>
            <response code="403">User does not have access to this resource.</response>
            <response code="404">No documents were found for the specified merchant id.</response>
        </member>
        <member name="M:BackofficeApi.Controllers.DocumentController.GetDocumentByMerchantId(System.Guid)">
            <summary>
            Returns a id.
            </summary>
            <param name="merchantId">The id of the merchant.</param>
            <response code="200">The document's data as a byte stream.</response>
            <response code="403">User does not have access to this resource.</response>
            <response code="404">No documents were found for the specified merchant id.</response>
        </member>
        <member name="M:BackofficeApi.Controllers.LeadController.SearchLeads(Common.Models.Lead.LeadSearchParameters)">
            <summary>
            Returns a list of leads.
            </summary>
            <param name="searchParameters">The search parameters used to filter the list of leads.</param>
            <response code="200">Returns the lead list.</response>        
            <response code="400"></response> 
            <response code="401">If the request in unauthorized</response> 
            <response code="403">If there user is not a backoffice admin</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.LeadController.UpdateLead(System.Guid,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{Common.Models.Lead.Lead})">
            <summary>
            Updates an existing lead based on id.
            </summary>
            <param name="leadId">The id of the lead to be updated.</param>
            <param name="leadPatch">The json document patch.</param>
            <response code="204">Returns no content if the lead has been updated</response>
            <response code="400"></response> 
            <response code="401">If the request in unauthorized</response> 
            <response code="403">If there user is not a backoffice admin</response> 
            <response code="404">If there is no lead with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.LeadController.ExportLeads(Common.Models.Lead.LeadExportParameters)">
            <summary>
            Export leads based on search criteria
            </summary>
            <response code="200">Returns the leads export</response>
            <response code="400">Returns the error</response> 
            <response code="401">If the request is unauthorized</response> 
            <response code="403">If if the user does not have the correct role</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.LeadController.CreateLead(Common.Models.Lead.LeadCreateRequest)">
            <summary>
            Create new lead.
            </summary>
            <response code="201">Returns the new lead.</response>
            <response code="400">Returns the error.</response> 
            <response code="401">If the request in unauthorized.</response> 
            <response code="403">If if the user does not have the correct role.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.Find(Common.Models.Merchant.MerchantSearchFilters,System.Threading.CancellationToken)">
            <summary>
            Finds all merchants based o search criteria in the form of an OData query.
            </summary>
            <returns>An array of merchant objects matching the search criteria.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.GetContactDetailsById(System.Guid)">
            <summary>
            Returns a contact's details by the person of interest id
            </summary>
            <returns>An object representing the contact's details as presented by the merchant service</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.GetMerchantContactByMerchantId(System.Guid)">
            <summary>
            Returns the default's contact details of a merchant by the merchant's id
            </summary>
            <param name="merchantId">The id of the merchant</param>
            <returns>A ContactDetails object</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.DeleteMerchant(Common.Models.MerchantDeleteRequest)">
            <summary>
            Delete merchants based on merchantId.
            </summary>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.ExportMerchant(System.Guid)">
            <summary>
            Exports a merchant.
            </summary>
            <param name="merchantId">The ID of the merchant to export. Must be an integer greater than 0.</param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.ExportMerchants">
            <summary>
            Exports all merchants.
            </summary>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.GetPeopleByMerchantId(System.Guid)">
            <summary>
            Get merchant POI
            </summary>
            <param name="merchantId"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.GetMerchantStatusHistory(System.Guid)">
            <summary>
            Get merchant status history
            </summary>
            <param name="merchantId"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.PatchMerchant(System.Guid,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{Common.Models.Merchant.PatchMerchantRequest})">
            <summary>
            Updates the merchant.
            </summary>
            <response code="200">Returns the updated merchant.</response>
            <response code="401">If the request in unauthorized.</response> 
            <response code="403">If the user does not have the correct role.</response> 
            <response code="404">If the merchant with that id does not exists.</response>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.CreateMerchantCommentAction(System.Guid,Common.Models.Comment.CommentCreateRequest)">
            <summary>
            Create a new comment for a specific merchant.
            </summary>
            <param name="merchantId"></param>
            <param name="commentCreateRequest"></param>
            <response code="201">Returns the newly created comment </response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no merchant with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.GetCommentByMerchantIdAction(System.Guid)">
            <summary>
            Gets all comments for an merchant based on merchantId.
            </summary>
            <param name="merchantId"> Merchant id</param>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.GetMerchantsCommentById(System.Guid)">
            <summary>
            Gets a specific comment based on comment id.
            </summary>
            <param name="commentId"> The id based on which we are looking for the comment</param>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.UpdateMerchantsCommentAction(System.Guid,Common.Models.Comment.CommentUpdateRequest)">
            <summary>
            Update comment based on comment id.
            </summary>
            <param name="commentId"> CommentId</param>
            <param name="commentUpdateRequest"></param>
        </member>
        <member name="M:BackofficeApi.Controllers.MerchantController.DeleteMerchantsCommentAction(System.Guid)">
            <summary>
            Soft delete for comments based on comment id.
            </summary>
            <param name="commentId"> Comment id</param>
        </member>
        <member name="M:BackofficeApi.Controllers.NotificationController.SendEmailInfoCheck(Common.Models.SendEmailInfoCheckRequest)">
            <summary>
            Sends out a "check your email" type of notification to the sepcified recipient.
            </summary>
            <param name="request">Contains the subject, the type of onboarding check, recipient's address and custom comments if any.</param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.ExportOrderById(System.Guid)">
            <summary>
            Gets a specific order from databse based on a given Id.
            </summary>
            <param name="orderId"> The id based on which we are looking for the order</param>
            <response code="200">Returns the order</response>
            <response code="400">Returns the error</response> 
            <response code="401">If the request in unauthorized</response> 
            <response code="403">If you are not the merchant for that order</response> 
            <response code="404">If there is no order with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.UpdateOrderDetails(System.Guid,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{Common.Models.Checkout.OrderUpdateRequest})">
             <summary>
             Updates an existing order based on id.
             </summary>
             <remarks>
             Sample request:
            
                 PATCH /order
                 [
                     { "op": "replace", "path": "TrackingNumber", "value": "123"},
                 	{ "op": "replace", "path": "AgreementId", "value": 59e5a908-60ef-457f-91f1-adeddd4781e2}
                 ]
            
             </remarks>
             <response code="204">Returns no content if the order has been updated</response>
             <response code="400"></response> 
             <response code="401">If the request in unauthorized</response> 
             <response code="403">If if the user does not have the same merchantId as the one in the order</response> 
             <response code="404">If there is no order with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.BulkUpdateOrdersToProductRegistered(System.String[])">
            <summary>
            Bulk upload orders to product registered
            </summary>
            <response code="200">Returns a list with all errors that occur</response>
            <response code="400">Returns the error.</response> 
            <response code="401">If the request in unauthorized.</response> 
            <response code="403">If the user does not have the correct role.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.DeleteOrders(Common.Models.Checkout.OrderDeleteRequest)">
            <summary>
            Soft delete for all orders based on an array of OrderIds.
            </summary>
            <response code="204">Returns no content if all the orders have been deleted.</response>
            <response code="400">Returns the error.</response> 
            <response code="401">If the request in unauthorized.</response> 
            <response code="403">If the user does not have the correct role.</response> 
            <response code="404">If the order with a certain orderId does not exists.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.ExportOrders(Common.Models.Checkout.OrderSearchCriteria)">
            <summary>
            Export orders based on search criteria
            </summary>
            <response code="200">Returns the orders export</response>
            <response code="400">Returns the error</response> 
            <response code="401">If the request in unauthorized</response> 
            <response code="403">If if the user does not have the correct role</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.GetOrderItemById(System.Guid[])">
            <summary>
            Gets a specific order item from databse based on a given Id.
            </summary>
            <response code="200">Returns the order item</response>
            <response code="400">Returns the error</response> 
            <response code="401">If the request in unauthorized</response> 
            <response code="403">If if the user does not have the correct role</response> 
            <response code="404">If there is no order item with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.UpdateOrderItemDetails(System.Guid,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{Common.Models.Checkout.OrderItemUpdateRequest})">
             <summary>
             Updates an existing order item based on id.
             </summary>
             <remarks>
             Sample request:
            
                 PATCH /orderitem
                 [
                     { "op": "replace", "path": "TrackingNumber", "value": "123"},
                 	{ "op": "replace", "path": "AgreementId", "value": 59e5a908-60ef-457f-91f1-adeddd4781e2}
                 ]
            
             </remarks>
             <response code="204">Returns no content if the order item has been updated</response>
             <response code="400"></response> 
             <response code="404">If there is no order item with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.CreateCommentAction(System.Guid,Common.Models.Comment.CommentCreateRequest)">
            <summary>
            Creeate a new comment for a specific order.
            </summary>
            <param name="orderId"></param>
            <param name="commentCreateRequest"></param>
            <response code="201">Returns the newly created comment </response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no order with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.GetCommentByOrderIdAction(System.Guid)">
            <summary>
            Gets all comments for an order based on orderId.
            </summary>
            <param name="orderId"> Order id</param>
            <response code="200">Returns a list of comments.</response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no order with that id.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.GetCommentById(System.Guid)">
            <summary>
            Gets a specific comment based on comment id.
            </summary>
            <param name="commentId"> The id based on which we are looking for the comment</param>
            <response code="200">Returns the comment</response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no comment with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.UpdateCommentAction(System.Guid,Common.Models.Comment.CommentUpdateRequest)">
            <summary>
            Update comment based on comment id.
            </summary>
            <param name="commentId"> CommentId</param>
            <param name="commentUpdateRequest"></param>
            <response code="200">Returns the updated comment</response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no comment with given id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.DeleteCommentAction(System.Guid)">
            <summary>
            Soft delete for comments based on comment id.
            </summary>
            <param name="commentId"> Comment id</param>
            <response code="204"></response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no comment with given id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.GetOrderStatusHistory(System.Guid)">
            <summary>
            Gets status history for an order based on orderId.
            </summary>
            <param name="orderId"> Order id</param>
            <response code="200">Returns a list of statuses.</response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no order with that id.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.CreateOrderEPosTicket(System.Guid)">
            <summary>
            Creates an EPos ticket for the order
            </summary>
            <param name="orderId"></param>
            <response code="204"></response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no order with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.UpdateEPosSwType(System.Guid)">
            <summary>
            Sends ePos software type update messages for eligible terminals of the order.
            </summary>
            <param name="orderId"></param>
            <response code="204"></response>
            <response code="400">Returns the error</response> 
            <response code="404">If there is no order with that id</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.OrderController.GetEPosAvailableAction(System.Guid)">
            <summary>
            Gets the available ePos action for an order based on its terminals state.
            </summary>
            <param name="orderId"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.RequestLogController.Search(Common.Models.NexusBridgeLog.SearchFilters)">
            <summary>
            Search
            </summary>
            <param name="filters"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.SaleController.UpdateLeadSalesId(Common.Models.User.LeadSalesIdRequest)">
            <summary>
            Update salesId for only one lead based on leadId.
            </summary>
            <param name="leadSalesIdRequest"></param>
            <response code="204">If the sales link is updated</response>
            <response code="403">If ther user is allowed to perform the action</response> 
            <response code="404">If ther user is not found</response> 
            <response code="500">If there user salesId is invalid</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.SaleController.UpdateUserSalesId(Common.Models.User.UserSalesIdRequest)">
            <summary>
            Update salesId for user and for all leads created with users old salesId
            </summary>
            <param name="userSalesIdRequest"></param>
            <response code="204">If the sales id is updated</response>
            <response code="403">If ther user is allowed to perform the action</response> 
            <response code="404">If ther user is not found</response> 
            <response code="500">If there user salesId is invalid</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.GetShareholderCompanies(System.Guid)">
            <summary>
            Gets all Shareholder Companies by MerchantId.
            </summary>
            <param name="merchantId"></param>
            <returns>All Shareholder Companies that are associated on the Merchant.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.SearchShareholderCompanies(Common.Models.Shareholder.ShareholderCompanySearchRequest)">
            <summary>
            Search for Shareholder Companies by keyword ( CompanyName and Company License(CR) ).
            </summary>
            <param name="request"></param>
            <returns>All Shareholder Companies that match the keyword.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.CreateShareholderCompany(Common.Models.Shareholder.CreateShareholderCompanyWithDocumentRequest)">
            <summary>
            Create ShareholderCompany and associates it with the current merchant.
            </summary>
            <param name="request"></param>
            <returns>Created ShareholderCompany</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.PatchShareholderCompany(Common.Models.Shareholder.ShareholderCompanyPatchRequest)">
            <summary>
            Patch ShareholderCompany.
            </summary>
            <param name="request"></param>
            <returns>Edited ShareholderCompany</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.SearchShareholderIndividuals(Common.Models.Shareholder.ShareholderIndividualsSearchRequest)">
            <summary>
            Search for Shareholder individuals by keyword ( first name / last name / nationalId / passport number ).
            </summary>
            <param name="request"></param>
            <returns>All Shareholder individuals that match the keyword.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.CreateShareholderCompanyMerchantAssociation(Common.Models.Shareholder.ShareholderCompanyMerchantAssociationRequest)">
            <summary>
            Create Association between ShareholderCompany and the current merchant.
            </summary>
            <param name="request"></param>
            <returns>Created ShareholderCompany</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.CreateShareholderIndividual(Common.Models.Shareholder.CreateShareholderIndividualWithDocumentRequest)">
            <summary>
            Create ShareholderIndividual and associates it with the current merchant.
            </summary>
            <param name="request"></param>
            <returns>Created ShareholderIndividual</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.CreateShareholderIndividualAssociations(Common.Models.Shareholder.ShareholderIndividualAssociationsCreateRequest)">
            <summary>
            Create Association between Individuals and the current merchant.
            </summary>
            <param name="request"></param>
            <returns>Created ok</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.PatchShareholderIndividual(Common.Models.Shareholder.ShareholderIndividualPatchRequest)">
            <summary>
            Updates the shareholder individual's details.
            </summary>
            <param name="request"></param>
            <returns>Edited ShareholderIndividual</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.ShareholderController.GetShareholderIndividualEditableStatus(System.Guid)">
            <summary>
            Gets the editable status of a shareholder individual.
            </summary>
            <param name="shareholderIndividualId"></param>
            <returns>If the individual is a principal owner or returned by WATHQ.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.SubordinateMerchantController.FindAssociatedAndAvailableSubordinateMerchants(System.Guid,Common.Models.Merchant.SubordinateMerchant.SubordinateMerchantSearchRequestDto)">
            <summary>
            Retrieves the list of associated and available subordinate businesses for a specific merchant
            </summary>
            /// <param name="merchantId">The id of the merchant for which we retrieve the subordinates.</param>
            <param name="request">The parameters for the subordinate merchant search request.</param>
            <response code="200">Returns the list of subordinate businesses</response>
        </member>
        <member name="M:BackofficeApi.Controllers.SubordinateMerchantController.FindAssociatedSubordinateMerchants(System.Guid,Common.Models.Merchant.SubordinateMerchant.SubordinateMerchantSearchRequestDto)">
            <summary>
            Retrieves the list of only associated subordinate businesses for a specific merchant
            </summary>
            /// <param name="merchantId">The id of the merchant for which we retrieve the subordinates.</param>
            <param name="request">The parameters for the subordinate merchant search request.</param>
            <response code="200">Returns the list of subordinate businesses</response>
        </member>
        <member name="M:BackofficeApi.Controllers.SubordinateMerchantController.FindAssociatedParent(System.Guid)">
            <summary>
            Retrieves only the parent associated to a merchant
            </summary>
            /// <param name="merchantId">The id of the merchant for which we retrieve the parent.</param>
            <response code="200">Returns the associated parent for a subordinate merchant</response>
        </member>
        <member name="M:BackofficeApi.Controllers.SubordinateMerchantController.CreateBusinessHierarchy(System.Guid,System.Collections.Generic.List{System.Guid})">
            <summary>
            Creates a business hierarchy between the parent merchant and the subordinate merchants
            </summary>
            <param name="parentMerchantId">The id of the parent merchant.</param>
            <param name="subordinateMerchantIds">The list containing the ids of the subordinate business</param>
            <response code="204">Returns No Contend if the business hierarchy was created successfully</response>
        </member>
        <member name="M:BackofficeApi.Controllers.SubordinateMerchantController.DeleteBusinessHierarchy(System.Guid,System.Guid)">
            <summary>
            Deletes a business hierarchy between the parent merchant and the subordinate merchant
            </summary>
            <param name="parentMerchantId">The id of the parent merchant.</param>
            <param name="subordinateMerchantId">The id of the subordinate business</param>
            <response code="204">Returns No Contend if the business hierarchy was deleted successfully</response>
        </member>
        <member name="M:BackofficeApi.Controllers.TaskCommentController.CreateTaskComment(System.Guid,Common.Models.TaskComments.TaskCommentCreateRequest)">
            <summary>
            Create new comment for specific task.
            </summary>
            <param name="taskId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.TaskCommentController.GetCommentByTaskId(System.Guid)">
            <summary>
            Get comments list by taskId.
            </summary>
            <param name="taskId"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.TaskCommentController.GetCommentById(System.Guid)">
            <summary>
            Get comment by Id.
            </summary>
            <param name="commentId"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.TaskController.UpdateAsync(System.Guid,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{Common.Models.Tasks.TaskUpdateRequest})">
            <summary>
            Update a specific task by Id.
            </summary>
            <param name="taskId"></param>
            <param name="patchDocument"></param>
            <returns></returns>
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.GetUsers(System.Guid)">
            <summary>
            Get users for merchant id
            </summary>
            <response code="204">Returns array with user ids</response>
            <response code="400">If there is a problem</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.ActivateUser(System.Guid)">
            <summary>
            Activate user based on user id.
            </summary>
            <response code="204">Returns no content if the user has been activated</response>
            <response code="400">Returns the error</response> 
            <response code="401">If the user is unauthorized.</response> 
            <response code="400">If the user does not have the permission to activate other users.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.DisableUser(System.Guid)">
            <summary>
            Disable user based on user id.
            </summary>
            <response code="204">Returns no content if the user has been disabled</response>
            <response code="400">Returns the error</response> 
            <response code="401">If the user is unauthorized.</response> 
            <response code="400">If the user does not have the permission to disable other users.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.SearchUsers(Common.Models.User.UserSearchParameters)">
            <summary>
            Search users based on criteria
            </summary>
            <response code="200">Returns users according to search criteria.</response>
            <response code="400">Returns the error.</response> 
            <response code="401">If the user is unauthorized.</response> 
            <response code="400">If the user does not have the permission to list other users.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.UpdateUsers(System.Guid,Microsoft.AspNetCore.JsonPatch.JsonPatchDocument{Common.Models.User.UpdateBackOfficeUserRequest})">
            <summary>
            Update back office user data based on userId.
            </summary>
            <response code="204">Returns no content if the user has been updated.</response>
            <response code="400">Returns the error.</response> 
            <response code="401">If the user is unauthorized.</response> 
            <response code="400">If the user does not have the permission to update other users.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.GetUserById(System.Guid)">
            <summary>
            Get user based on userId
            </summary>
            <response code="200">Returns the user.</response>
            <response code="400">Returns the error.</response> 
            <response code="401">If the user is unauthorized.</response> 
            <response code="403">If the user does not have the permission to get user.</response> 
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.FindRoles(Common.Models.User.UserRoleSearchFilter)">
            <summary>
            Finds all Roles based on search criteria.
            </summary>
            <returns>An array of roles objects matching the search criteria.</returns>
        </member>
        <member name="M:BackofficeApi.Controllers.UserController.ExportBackofficeUsers(Common.Models.User.UserAdvancedSearchFilter)">
            <summary>
            Export Users list based on search criteria
            </summary>
            <response code="200">Returns the users export</response>
            <response code="400">Returns the error</response> 
            <response code="401">If the request is unauthorized</response> 
            <response code="403">If if the user does not have the correct role</response> 
        </member>
    </members>
</doc>
