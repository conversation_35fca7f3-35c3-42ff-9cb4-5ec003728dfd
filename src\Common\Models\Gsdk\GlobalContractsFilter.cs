﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Gsdk
{
    public class GlobalContractsFilter
    {
        public List<string> TransactionTypes { get; set; } = new List<string>() { "PURCHASE" };

        public List<Guid> InProviderAccountCategoryIds { get; set; } = new List<Guid>();

        public Guid? MccGroupId { get; set; }
    }
}