﻿using AutoMapper;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Merchant;
using Common.Models.NexusBridge;
using Common.Models.Search;
using Common.Models.Shareholder;
using Common.Options;
using Common.Services;
using Elastic.Apm.Api;
using FluentAssertions;
using Geidea.Messages.Gsdk;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using static Common.Constants;

namespace Services.Tests
{
    public class NexusBridgeServiceTests
    {
        private Mock<INexusBridgeClient> nexusClientMock = new Mock<INexusBridgeClient>();
        private Mock<IReferenceService> refMock = new Mock<IReferenceService>();

        private NexusBridgeService services = null!;
        private Mock<ILogger<NexusBridgeService>> loggerMock = new Mock<ILogger<NexusBridgeService>>();
        private Mock<IOptions<UrlSettings>> urlOptionsMock = new Mock<IOptions<UrlSettings>>();
        private Mock<HttpMessageHandler> mockHttpMessageHandler = new Mock<HttpMessageHandler>();
        private const string TestBaseUrl = "http://testbaseurl.com";
        private const string NexusBridgeServiceEndpoint = "/api/v1/merchant/create";

        [SetUp]
        public void SetUp()
        {

            mockHttpMessageHandler = new Mock<HttpMessageHandler>(MockBehavior.Strict);

            urlOptionsMock.Setup(x => x.Value).Returns(new UrlSettings { NexusBridgeApiBaseUrl = TestBaseUrl });

            services = new NexusBridgeService(nexusClientMock.Object, loggerMock.Object, refMock.Object);
        }

        [Test]
        public Task CreateMerchant_SuccessfullyCreatesMerchant()
        {
            // Arrange
            var merchantId = Guid.NewGuid();
            var contact = ArrangeBasicContact();
            Merchant merchant;
            List<MerchantExternalProduct> extProducts;
            List<Common.Models.NexusBridge.MerchantAddress> addresses;
            List<MerchantBankAccountResponse> bankAccounts;
            List<MerchantTerminal> terminal;

            MockMerchant(merchantId, out merchant, out extProducts, out addresses, out bankAccounts, out terminal);

            urlOptionsMock.Setup(opt => opt.Value)
                .Returns(new UrlSettings
                {
                    NexusBridgeApiBaseUrl = "http://example.com/api",
                    MerchantServiceBaseUrl = "http://example.com/merchant",
                    ProductServiceBaseUrl = "http://example.com/product"
                });

            var service = new NexusBridgeService(
                nexusClientMock.Object, loggerMock.Object, refMock.Object

            );
            List<Common.Models.NexusBridge.MerchantCommissionConfig> commissionConfig = new List<Common.Models.NexusBridge.MerchantCommissionConfig>()
            {
                new Common.Models.NexusBridge.MerchantCommissionConfig()
                {
                    Id = new Guid(),
                MerchantId = merchantId,
                ProductCode = "1",
                CommissionType = "1",
                Value = 0,
                IsInterchange ="true",
                TransactionType = "1",
                VatPercentage =1,
               
                }

            };
            Common.Models.NexusBridge.MerchantAccountConfig config = new Common.Models.NexusBridge.MerchantAccountConfig
            {
                Id = Guid.NewGuid(),
                MerchantId = Guid.NewGuid(),
                SettlementCurrency = "USD",
                PayoutSchedule = "Weekly",
                PayoutDay = 15,
                PayoutCapAmount = 5000.00m,
                PayoutMinAmount = 100.00m,
                AcceptedPaymentMethods = "Credit Card, Debit Card",
                TransactionCurrency = "EUR",
                DCCProvider = "Provider A",
                OrderSetupFeePaymentMode = "Credit Card",
                OrderSetupFeePaymentReference = "REF123",
                OrderSecurityDepositPaymentMode = "Bank Transfer",
                OrderSecurityDepositPaymentReference = "DEP456",
                SettlementTimeFrame = 7,
                PerTransactionLimit = 1000,
                PerTransactionRefundLimit = 500,
                TransactionType = "Online Payment",
            };
            var prodaccount = new ProductsOnAccount()
            {
                Name = "Test",
                MID = "1",
                TID = "1",
                Family = "2",
                Make = "2",
                Model = "c",
                ProductStatus = "new",
                SerialNumber = "123"
            };
            var expectedNMerchant = new NBMerchant
            {
                MerchantId = merchantId,
                MerchantStatus = "Active",
                Tag = "TestMerchant",
                MerchantType = "New",
                Counterparty = "UAE",
                ApplicationId = "1",
                CreatedBy = "1",
                DeletedFlag = false,
                CreatedDate = DateTime.Now,
                LeadId = new Guid(),
                UpdatedBy = "1",
                UpdatedDate = DateTime.Now,
                PersonOfInterests = ArrangeBasicContactNB(),
                ExternalProducts = extProducts,
                CommissionTypes = commissionConfig,
                AccountConfig = config,
                MerchantDetails = It.IsAny<MerchantDetails>(),
                Addresses = addresses,
                BankAccounts = ArrangeBasicBankAccount(),
                MerchantTerminals = new List<MerchantTerminal>()
                {
                    new MerchantTerminal()
                    {
                        Brand ="new",
                        Model ="a",
                        TerminalId ="1",
                        TerminalStatus = "pending",
                        SerialNumber = "12",

                    }
                }
            };
            var order = new Common.Models.Checkout.OrderResponse()
            {
                Note = "12",
                DeletedFlag = true,
                Discount = 1,
                CheckoutDate = DateTime.Now,
                CreatedDate = DateTime.Now,
                MonthlyDiscount = 1,
                VatPercent = 1,
            };
            nexusClientMock.Setup(client => client.ConnectToNexusBridge(expectedNMerchant, false)).ReturnsAsync("sent");
            var requestLogContent = new StringContent(JsonConvert.SerializeObject(expectedNMerchant), Encoding.UTF8, "application/json");

            // Act
            Func<Task> act = async () => await service!.CreateMerchant(merchant, contact, merchant, order, false);

            // Assert
            Assert.NotNull(act);
            return Task.CompletedTask;
        }

        [Test]
        public async Task ConnectToNexusTerminal_SuccessfulResponse_ReturnsJsonResult()
        {
            // Arrange
            var product = new NBProduct 
            {
                MerchantId ="1201000",
                MerchantTerminals = new MerchantTerminal()
                {
                    TerminalId = null,
                    TerminalStatus = "",
                    Brand = "",
                    Model ="",
                    SerialNumber ="",
                    MerchantId="120100"
                }
            };
            var expectedJson = JsonConvert.SerializeObject(product);
            var responseMessage = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(expectedJson, Encoding.UTF8, "application/json")
            };



            nexusClientMock
              .Setup(x => x.ConnectToNexusTerminal(product))
              .ReturnsAsync(expectedJson);

            var result = await nexusClientMock.Object.ConnectToNexusTerminal(product);

            // Assert
            Assert.AreEqual(expectedJson, result);
        }

        [Test]
        public Task CreateMerchant_SuccessfullyCreatesOrder()
        {
            // Arrange
            var merchantId = Guid.NewGuid();
            var contact = ArrangeBasicContact();
            Merchant merchant;
            List<MerchantExternalProduct> extProducts;
            List<Common.Models.NexusBridge.MerchantAddress> addresses;
            List<MerchantBankAccountResponse> bankAccounts;
            List<MerchantTerminal> terminal;

            MockMerchant(merchantId, out merchant, out extProducts, out addresses, out bankAccounts, out terminal);

            urlOptionsMock.Setup(opt => opt.Value)
                .Returns(new UrlSettings
                {
                    NexusBridgeApiBaseUrl = "http://example.com/api",
                    MerchantServiceBaseUrl = "http://example.com/merchant",
                    ProductServiceBaseUrl = "http://example.com/product"
                });

            var service = new NexusBridgeService(
                nexusClientMock.Object, loggerMock.Object, refMock.Object

            );
            List<Common.Models.NexusBridge.MerchantCommissionConfig> commissionConfig = new List<Common.Models.NexusBridge.MerchantCommissionConfig>()
            {
                new Common.Models.NexusBridge.MerchantCommissionConfig()
                {
                    Id = new Guid(),
                MerchantId = merchantId,
                ProductCode = "1",
                CommissionType = "1",
                Value = 0,
                IsInterchange ="true",
                TransactionType = "1",
                VatPercentage =1,

                }

            };
            Common.Models.NexusBridge.MerchantAccountConfig config = new Common.Models.NexusBridge.MerchantAccountConfig
            {
                Id = Guid.NewGuid(),
                MerchantId = Guid.NewGuid(),
                SettlementCurrency = "USD",
                PayoutSchedule = "Weekly",
                PayoutDay = 15,
                PayoutCapAmount = 5000.00m,
                PayoutMinAmount = 100.00m,
                AcceptedPaymentMethods = "Credit Card, Debit Card",
                TransactionCurrency = "EUR",
                DCCProvider = "Provider A",
                OrderSetupFeePaymentMode = "Credit Card",
                OrderSetupFeePaymentReference = "REF123",
                OrderSecurityDepositPaymentMode = "Bank Transfer",
                OrderSecurityDepositPaymentReference = "DEP456",
                SettlementTimeFrame = 7,
                PerTransactionLimit = 1000,
                PerTransactionRefundLimit = 500,
                TransactionType = "Online Payment",
            };
            var prodaccount = new ProductsOnAccount()
            {
                Name = "Test",
                MID = "1",
                TID = "1",
                Family = "2",
                Make = "2",
                Model = "c",
                ProductStatus = "new",
                SerialNumber = "123"
            };
            var expectedNMerchant = new NBMerchant
            {
                MerchantId = merchantId,
                MerchantStatus = "Active",
                Tag = "TestMerchant",
                MerchantType = "New",
                Counterparty = "UAE",
                ApplicationId = "1",
                CreatedBy = "1",
                DeletedFlag = false,
                CreatedDate = DateTime.Now,
                LeadId = new Guid(),
                UpdatedBy = "1",
                UpdatedDate = DateTime.Now,
                PersonOfInterests = ArrangeBasicContactNB(),
                ExternalProducts = extProducts,
                CommissionTypes = commissionConfig,
                AccountConfig = config,
                MerchantDetails = It.IsAny<MerchantDetails>(),
                Addresses = addresses,
                BankAccounts = ArrangeBasicBankAccount(),
                MerchantTerminals = new List<MerchantTerminal>()
                {
                    new MerchantTerminal()
                    {
                        Brand ="new",
                        Model ="a",
                        TerminalId ="1",
                        TerminalStatus = "pending",
                        SerialNumber = "12",

                    }
                }
            };
            var order = new Common.Models.Checkout.OrderResponse()
            {
                Note = "12",
                DeletedFlag = true,
                Discount = 1,
                CheckoutDate = DateTime.Now,
                CreatedDate = DateTime.Now,
                MonthlyDiscount = 1,
                VatPercent = 1,
            };
            nexusClientMock.Setup(client => client.ConnectToNexusBridge(expectedNMerchant, false)).ReturnsAsync("sent");
            var requestLogContent = new StringContent(JsonConvert.SerializeObject(expectedNMerchant), Encoding.UTF8, "application/json");

            // Act
            Func<Task> act = async () => await service!.CreateMerchant(merchant, contact, merchant, order, true);

            // Assert
            Assert.NotNull(act);
            return Task.CompletedTask;
        }

        private void MockMerchant(Guid merchantId, out Merchant merchant, out List<MerchantExternalProduct> extProducts, out List<Common.Models.NexusBridge.MerchantAddress> addresses, out List<MerchantBankAccountResponse> bankAccounts, out List<MerchantTerminal> terminal)
        {
            merchant = ArrangeBasicMerchant();
            var shareholders = ArrangeBasicContactNB();
            var bank = ArrangeBasicBankAccount();
            terminal = new List<MerchantTerminal>()
            { new MerchantTerminal()
            {
                Brand ="NBN",
                TerminalStatus ="a",
                MerchantId =merchant.MerchantId.ToString(),
                Model ="model",
                TerminalId = "1"
            }
            };

            extProducts = new List<MerchantExternalProduct>
            {
                new MerchantExternalProduct
                {
                    ProductCode = "Prod001",
                    ProductCategory = "Category",
                    ProductType = "1",
                }
            };
            addresses = new List<Common.Models.NexusBridge.MerchantAddress>
            {
                new Common.Models.NexusBridge.MerchantAddress
                {
                    Street = "Street",
                    City = "City",
                    Country = "Country",
                    District = "District",
                    Coordinates = "Coordinates"
                }
            };

            bankAccounts = bank;
            nexusClientMock.Setup(client => client.GetMerchantExternalProducts())
                .ReturnsAsync(extProducts);
            nexusClientMock.Setup(client => client.GetMerchantAddress(merchantId))
                .ReturnsAsync(addresses);
            nexusClientMock.Setup(client => client.GetBankAccountDetails(merchantId))
                .ReturnsAsync(bankAccounts);
            nexusClientMock.Setup(client => client.GetTerminalDetails(merchantId.ToString()))
               .ReturnsAsync(terminal);
            var catalogues = new[]
           {
                new Catalogue { CatalogueName = "Cities", Key = "City1", Value = "City One" },
                new Catalogue { CatalogueName = "Bank", Key = "City1", Value = "City One" },
                new Catalogue { CatalogueName = "Governorate", Key = "City1", Value = "City One" },


            };
            refMock.Setup(x => x.GetCataloguesAsync(new string[] { Catalogues.ReferralChannelRestrictions }, "EN")).ReturnsAsync(catalogues);

        }
        [Test]
        public async Task CreateMerchant_ValidData_CallsConnectToNexusBridge()
        {
            // Arrange

            var merchant = new Merchant
            {
                MerchantId = Guid.NewGuid(),
                MerchantStatus = "Active",
                Tag = "Retail",
                MerchantDetails = new MerchantDetails
                {
                    BusinessType = "Retail",
                    LegalName = "Sample Merchant",
                    TradingName = "Sample Trading",
                    BusinessEmail = "niyu",
                    RegistrationNumber = "123456",
                    FoundationDate = DateTime.UtcNow,
                    AnnualTurnover = 1000000m,
                    PayoutBank = "Example Bank",
                    PayoutMinimumCap = 10,
                    PayoutTransferFeeAmount = 5,
                    TLIssueDate = DateTime.Now,
                    TLExpiryDate = DateTime.Now,
                    Iban = "IBAN123",
                    AccountNumber = "ACC123456",
                    BeneficiaryFullName = "John Doe",
                    Mid = "***********",
                    MerchantSegment = "POS",
                    AlternativeStoreName = "new",
                    StoreName = "new",
                    AlternativeMerchantName = "new",
                    AcquiringLedger = "bank",
                    MerchantName = "legalName",
                    DoingBusinessAsName = "new",
                },
                CommissionTypes = new List<Common.Models.Checkout.MerchantCommissionConfig>()
                {
                    new Common.Models.Checkout.MerchantCommissionConfig()
                    {
                         Id = new Guid(),
                        MerchantId = new Guid(),
                        ProductCode = "1",
                        CommissionType = "1",
                        Value = 0,
                    }
                },
                AccountConfig = new Common.Models.Checkout.MerchantAccountConfig()
                {
                    TransactionType ="1",
                    DCCProvider ="1",
                    PayoutDay =0,
                    SettlementCurrency ="22",
                    PayoutCapAmount =0,
                },
            };
            var catalogues = new[]
            {
                new Catalogue { CatalogueName = "Cities", Key = "City1", Value = "City One" }
            };
            var shareholders = new List<MerchantShareholderIndividual>
            {
                new MerchantShareholderIndividual
                {
                    FirstName = "John",
                    LastName = "Doe",
                    DOB = DateTime.Parse("1980-01-01"),
                    PhoneNumber = "*********",
                    Address = new ShareholderIndividualAddress
                    {
                        Country = "Country",
                        City = "City",
                        Area = "Area",
                        Governorate = catalogues?.FirstOrDefault(x=>x.CatalogueName == "Cities" && x.Key == "AD")?.Value,
                        AddressInfo = "Address",
                        Email = "<EMAIL>",
                        PhoneNumber = "123",
                        
                    }
                }
            };

            var merchantExternalProducts = new List<MerchantExternalProduct>
            {
                new MerchantExternalProduct
                {
                    ProductCategory = "Category1",
                    ProductCode = "Code1",
                    ProductType = "Type1"
                },
                new MerchantExternalProduct
                {
                    ProductCategory = "Category2",
                    ProductCode = "Code2",
                    ProductType = "Type2"
                }
            };

            var merchantAddresses = new List<Common.Models.NexusBridge.MerchantAddress>
            {
                new Common.Models.NexusBridge.MerchantAddress
                {
                    Street = "123 Example St",
                    City = "Sample City",
                    Country = "Sample Country",
                    District = "Sample District",
                    Coordinates = "12.34, 56.78",
                    MerchantId = Guid.NewGuid(),
                    AddressId = Guid.NewGuid(),
                    Governorate = "Sample Governorate",
                    Area = "Sample Area",
                    POBox = "12345",
                    Email = "<EMAIL>",
                    Url = "http://example.com",
                    AddressType = "Type1",
                    AddressPin = "1234",
                    IsDefaultAddress = true,
                    ValidFrom = DateTime.UtcNow.AddMonths(-1),
                    ValidTo = DateTime.UtcNow.AddMonths(1),
                    OfficeLocationType = "Office",
                    IsBranchSelected = false,
                    Zip = "12345",
                    Purpose = "Business"
                }
            };

            var bankAccounts = new List<MerchantBankAccountResponse>
            {
                new MerchantBankAccountResponse
                {
                CountryCode = "US",
                AccountHolderName = "John Doe",
                RefBankId = 1,
                BankCheckId = Guid.NewGuid(),
                City = "Sample City",
                IBAN = "US******************************",
                Swift = "BANKUS33",
                AccountName = "John Doe Business Account",
                DDReference = "DD123",
                ValidFrom = DateTime.UtcNow.AddYears(-1),
                ValidTo = DateTime.UtcNow.AddYears(1),
                BankAccountNumber = "**********",
                Balance = "100",
                Currency = "USD",
                AccountType = "Business",
                MerchantId = Guid.NewGuid(),
                BankName = "Sample Bank",
                MerchantBankAccountId = Guid.NewGuid()
                }
            };

            var terminals = new List<MerchantTerminal>
            {
                new MerchantTerminal
                {
                    MerchantId = "Merchant1",
                    Brand = "Brand1",
                    Model = "Model1",
                    TerminalStatus = "Active",
                    TerminalId = "Terminal1"
                }
            };
            var order = new Common.Models.Checkout.OrderResponse()
            {
                Note = "12",
                DeletedFlag = true,
                Discount = 1,
                CheckoutDate = DateTime.Now,
                CreatedDate = DateTime.Now,
                MonthlyDiscount = 1,
                VatPercent = 1,
            };

            // Mocking the external calls
            nexusClientMock.Setup(x => x.GetMerchantExternalProducts())
                           .ReturnsAsync(merchantExternalProducts);
            nexusClientMock.Setup(x => x.GetMerchantAddress(merchant.MerchantId))
                           .ReturnsAsync(merchantAddresses);
            nexusClientMock.Setup(x => x.GetBankAccountDetails(merchant.MerchantId))
                           .ReturnsAsync(bankAccounts);
            nexusClientMock.Setup(x => x.GetTerminalDetails(merchant.MerchantDetails.Mid))
                           .ReturnsAsync(terminals);

            // Act
            await services.CreateMerchant(merchant, shareholders, merchant, order, false);

            // Assert
            nexusClientMock.Verify(x => x.ConnectToNexusBridge(It.IsAny<NBMerchant>(), false), Times.Once);
        }

        [Test]
        public void ReturnCatalogue_ShouldReturnOriginalCatalogueName_WhenAcquiringLedgerIsNull()
        {
            // Arrange
            var acquiringLedger = "new";
            var catalogueName = "Cities";
            var catalogues = new[]
            {
                new Catalogue { CatalogueName = "Cities", Key = "City1", Value = "City One" }
            };

            // Act
            var result = NexusBridgeService.ReturnCatalogue(acquiringLedger, catalogues, catalogueName);

            // Assert
            Assert.AreEqual("Cities", result);
        }

        [Test]
        public void ConcatAcquirerWithCatalogueName_ShouldReturnExpectedString()
        {
            // Arrange
            var acquirer = "Acquirer";
            var catalogueName = "CatalogueName";

            // Act
            var result = NexusBridgeService.ConcatAcquirerWithCatalogueName(acquirer, catalogueName);

            // Assert
            Assert.AreEqual("ACQUIRER_CatalogueName", result);
        }

        [Test]
        public Task CreateMerchant_ShouldCallCorrectEndpoint_WithCorrectPayload()
        {
            var merchant = ArrangeBasicMerchant();
            var contact = ArrangeBasicContact();
            var merchantId = Guid.NewGuid();
            List<MerchantExternalProduct> extProducts;
            List<Common.Models.NexusBridge.MerchantAddress> addresses;
            List<MerchantBankAccountResponse> bankAccounts;
            List<MerchantTerminal> terminal;

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success response")
            };

            mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage);

            MockMerchant(merchantId, out merchant, out extProducts, out addresses, out bankAccounts, out terminal);
            var expectedUri = new Uri($"{TestBaseUrl}{NexusBridgeServiceEndpoint}");
            var expectedContent = new StringContent(JsonConvert.SerializeObject(merchant), Encoding.UTF8, "application/json");
            var order = new Common.Models.Checkout.OrderResponse()
            {
                Note = "12",
                DeletedFlag = true,
                Discount = 1,
                CheckoutDate = DateTime.Now,
                CreatedDate = DateTime.Now,
                MonthlyDiscount = 1,
                VatPercent = 1,
            };
            Func<Task> act = async () => await services!.CreateMerchant(merchant, contact, merchant, order, false);

            Assert.NotNull(act);
            return Task.CompletedTask;
        }
        private static List<MerchantBankAccountResponse> ArrangeBasicBankAccount()
        {
            // Arrange
            Guid merchantId = Guid.NewGuid();
            Guid merchantBankAccountId = Guid.NewGuid();
            string countryCode = "US";
            string accountHolderName = "John Doe";
            DateTime createdDate = DateTime.UtcNow;

            Common.Models.NexusBridge.BankAccount bankAccount = new Common.Models.NexusBridge.BankAccount
            {
                CountryCode = countryCode,
                AccountHolderName = accountHolderName,
                ValidFrom = createdDate,
                AccountType = "Savings",
            };

            IReadOnlyCollection<BankAccountMerchant> merchants = new List<BankAccountMerchant>
            {
                new BankAccountMerchant()
                {
                     MerchantId = merchantId,
                     MerchantType = "Retail"
                }
            };

            List<MerchantBankAccountResponse> merchantBankAccountResponse = new List<MerchantBankAccountResponse>
            {
                new MerchantBankAccountResponse()
                {
                    MerchantId = merchantId,
                    MerchantBankAccountId = merchantBankAccountId,
                CreatedBy = "Admin",
                CreatedDate = createdDate,
                Merchants = merchants,
                AccountHolderName= accountHolderName,
                AccountName = accountHolderName,
                AccountType = "Saving",
                Swift ="an",
                BankAccountNumber ="*********",
                Balance ="1000.00",
                BankCheckId =  new Guid(),
                DDReference = "100002",
                DeletedFlag = false,
                UpdatedDate = createdDate,
                City = "new City",
                CountryCode = countryCode,
                Currency = "New curr",
                IBAN ="********",
                RefBankId =1,
                UpdatedBy = "new",
                ValidFrom =DateTime.Now,
                ValidTo =DateTime.Now,
                BankName = "BankName",
                }
            };
            return merchantBankAccountResponse;
        }
        private static List<MerchantShareholderIndividual> ArrangeBasicContact()
        {
            var individuals = new List<MerchantShareholderIndividual>
            {
                 new MerchantShareholderIndividual
                {
                PersonOfInterestId = Guid.NewGuid(),
                NationalId = "**********",
                Nationality = "CountryName",
                FirstName = "John",
                LastName = "Doe",
                FirstNameAr = "NN",
                LastNameAr = "N",
                IdExpiryDate = DateTime.Today.AddYears(5),
                DOB = new DateTime(1985, 7, 15),
                PhonePrefix = "+1",
                PhoneNumber = "*********",
                PassportNo = "*********",
                PassportExpirationDate = DateTime.Today.AddYears(10),
                KYCCheck = "Passed",
                PEP = false,
                Address = new ShareholderIndividualAddress
                {
                    Area = "123 Main St",
                    City = "CityName",
                    Country = "CountryName",
                    Governorate ="Name",
                    AddressInfo ="New address",
                    Email ="<EMAIL>",
                    PhoneNumber ="12345678",
                },
                 }
            };
            return individuals;
        }
        private static List<Common.Models.NexusBridge.MerchantPersonOfInterest> ArrangeBasicContactNB()
        {
            PersonOfInterestAddress address = new PersonOfInterestAddress
            {
                Country = "Country",
                City = "City",
                Area = "Area",
                Governorate = "Governorate",
                Email = "<EMAIL>",
                AddressId = new Guid(),
                AddressPin = "123",
                District = "123",
                IsDefaultAddress = true,
                AddressType = "current",
                CityKey = 1,
                Coordinates = "new",
                IsBranchSelected = true,
                MerchantId = new Guid(),
                OfficeLocationType = "123",
                PersonOfIntrestId = new Guid(),
                PhoneNumber = "123",
                POBox = "12",
                Purpose = "new",
                Street = "street",
                Url = "new url",
                ValidFrom = DateTime.Now,
                ValidTo = DateTime.Now,
                Zip = "12",
            };


            // Act
            var merchantShareholderIndividual = new List<Common.Models.NexusBridge.MerchantPersonOfInterest>
            {
                new Common.Models.NexusBridge.MerchantPersonOfInterest()
                {
            MerchantPersonOfInterestId = new Guid(),
            NationalId = "**********",
            InterestEntityType = "Entity Type",
            Nationality = "Nationality",
            Title = "Title",
            FirstNameAr = "FirstNameAr",
            LastNameAr = "LastNameAr",
            IdIssueDate = DateTime.Now,
            IdExpiryDate = DateTime.Now,
            OrganizationRole = "Organization Role",
            IsPrincipal = true,
            OwnershipPercentage = 50.5m,
            DeletedFlag = false,
            ValidFrom = DateTime.Now,
            ValidTo = DateTime.Now,
            LeadId = new Guid(),
            Gender = "Male",
            PEP = true,
            AdverseMedia = false,
            WathqRelationId = 1,
            GsdkUserId = "GsdkUserId",
            PassportNo = "AB123456",
            PassportIssueDate = DateTime.Now,
            PassportExpirationDate = DateTime.Now,
            RoleId = new Guid(),
            PhonePrefix = "+1",
            KYCCheck = "Passed",
            Address = address,
            FirstName = "John",
            LastName = "Doe",
            MobileNumber = "**********",
            DateOfBirth = "1990-01-01",
            Website = "https://example.com",
            CreatedBy = "Admin",
            UpdatedBy = "SuperAdmin",
            MerchantId =new Guid(),
            }

            };

            return merchantShareholderIndividual;
        }

        private static Merchant ArrangeBasicMerchant()
        {
            return new Merchant
            {
                MerchantId = Guid.NewGuid(),
                Counterparty = "Test Counterparty",
                Tag = "Test Tag",
                MerchantDetails = new MerchantDetails
                {
                    MerchantDetailsId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    BusinessEmail = "email",
                    AccountNumber = "**********",
                    MIDMerchantReference = "12",
                    BusinessType = "Test Business Type",
                    OutletType = "Test Outlet Type",
                    MCC = "1234",
                    BusinessDomain = "Test Domain",
                    LegalName = "Test Legal Name",
                    FoundationDate = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = "Test Creator",
                    RiskLevel = "low"
                },
                MerchantStatus = "Active",
                AccountConfig = new Common.Models.Checkout.MerchantAccountConfig
                {
                    Id = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    AcceptedPaymentMethods = "Credit Card",
                    TransactionCurrency = "USD",
                    SettlementCurrency = "USD",
                    PayoutSchedule = "Daily",
                    PayoutDay = 1,
                    PayoutCapAmount = 10000m,
                    DCCProvider = "Test DCC Provider",
                    OrderSetupFeePaymentMode = "Credit",
                    SettlementTimeFrame = 7,
                    PerTransactionLimit = 5000,
                    PerTransactionRefundLimit = 3000,
                    TransactionType = "Purchase"
                },
                CommissionTypes = new List<Common.Models.Checkout.MerchantCommissionConfig>
                {
                    new Common.Models.Checkout.MerchantCommissionConfig
                    {
                        Id = Guid.NewGuid(),
                        MerchantId = Guid.NewGuid(),
                        ProductCode = "Test Product",
                        CommissionType = "Percentage",
                        Value = 2.5m,
                    }
                }
            };
        }
    }
}
