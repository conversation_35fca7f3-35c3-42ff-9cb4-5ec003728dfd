﻿using Common;
using Common.Models.Gsdk;
using Common.Models.Merchant;
using Common.Services;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Microsoft.Extensions.Logging;
using Moq;
using NSubstitute;
using NUnit.Framework;
using Services;
using System;
using System.Threading.Tasks;
using System.Collections;
using System.Collections.Generic;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class GsdkServiceTests
    {
        private readonly ILogger<GsdkService> logger = Substitute.For<ILogger<GsdkService>>();
        private readonly Mock<IGsdkClient> gsdkClient = new Mock<IGsdkClient>();
        private readonly Mock<IGsdkHelperService> gsdkHelperService = new Mock<IGsdkHelperService>();
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();
        private readonly Mock<IMerchantClient> merchantClient = new Mock<IMerchantClient>();

        private GsdkService gsdkService = null!;

        [Test]
        public async Task ValidateTmscContractsForProducts_ExistContractAssignedAlready_ReturnTrue()
        {

            referenceService
                .Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GsdkEgyptCatalogues));

            merchantClient
               .Setup(x => x.GetMerchantMmsContract(It.IsAny<SearchContractMappingFilter>()))
               .Returns(Task.FromResult(new List<MerchantExternalContractMappingResponse>() { new MerchantExternalContractMappingResponse() { Id = Guid.NewGuid() } }));

            gsdkClient
               .Setup(x => x.FindGlobalContracts(It.IsAny<GlobalContractsFilter>(), It.IsAny<string>(), It.IsAny<string>()))
               .Returns(Task.FromResult(TestsHelper.GetGlobalContractsWithDefaultContract(Constants.GsdkContractPaymentWay.Terminal, Constants.OutProviderAccounts.EgyptNBEBank)));

            CreateGsdkServiceInstance();

            var isVaild = await gsdkService.ValidateTmscContractsForProducts(TestsHelper.GetDirectProductInstanceConfiguredEgypt(Constants.ProductType.Terminal)
                  , TestsHelper.GetMerchant(Guid.NewGuid(), Constants.CounterParty.Egypt, Constants.MmsLedgers.EgyptNBEBank));

            Assert.That(isVaild, Is.True);
        }

        [Test]
        public async Task ValidateTmscContractsForProducts_NotExistContractAndHasAbilityToAssignAssignContract_ReturnTrue()
        {

            referenceService
                .Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GsdkEgyptCatalogues));

            merchantClient
               .Setup(x => x.GetMerchantMmsContract(It.IsAny<SearchContractMappingFilter>()))
               .Returns(Task.FromResult<List<MerchantExternalContractMappingResponse>>(null!));

            gsdkClient
               .Setup(x => x.FindGlobalContracts(It.IsAny<GlobalContractsFilter>(), It.IsAny<string>(), It.IsAny<string>()))
               .Returns(Task.FromResult(TestsHelper.GetGlobalContractsWithDefaultContract(Constants.GsdkContractPaymentWay.Terminal, Constants.OutProviderAccounts.EgyptNBEBank)));

            referenceService
               .Setup(x => x.GetCataloguesAsync(new string[] { Constants.Catalogues.GsdkOutAccountProviderToAcquirer }, It.IsAny<string>()))
               .Returns(Task.FromResult(new Common.Models.Catalogue[] {
                    new Common.Models.Catalogue {
                    Key = Constants.MmsLedgers.EgyptNBEBank,
                    Value = Constants.OutProviderAccounts.EgyptNBEBank
                    }
               }));

            CreateGsdkServiceInstance();

            var isVaild = await gsdkService.ValidateTmscContractsForProducts(TestsHelper.GetDirectProductInstanceConfiguredEgypt(Constants.ProductType.Terminal)
                  , TestsHelper.GetMerchant(Guid.NewGuid(), Constants.CounterParty.Egypt, Constants.MmsLedgers.EgyptNBEBank));

            Assert.That(isVaild, Is.True);
        }

        [Test]
        public async Task ValidateTmscContractsForProducts_NotExistContractAndHasNoAbilityToAssignAssignContract_ReturnFalse()
        {

            referenceService
                .Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GsdkEgyptCatalogues));

            merchantClient
               .Setup(x => x.GetMerchantMmsContract(It.IsAny<SearchContractMappingFilter>()))
               .Returns(Task.FromResult<List<MerchantExternalContractMappingResponse>>(null!));

            gsdkClient
               .Setup(x => x.FindGlobalContracts(It.IsAny<GlobalContractsFilter>(), It.IsAny<string>(), It.IsAny<string>()))
               .Returns(Task.FromResult(TestsHelper.GetGlobalContractsWithDefaultContract(Constants.GsdkContractPaymentWay.Terminal, Constants.OutProviderAccounts.EgyptMisrBank)));

            CreateGsdkServiceInstance();

            var isVaild = await gsdkService.ValidateTmscContractsForProducts(TestsHelper.GetDirectProductInstanceConfiguredEgypt(Constants.ProductType.Terminal)
                  , TestsHelper.GetMerchant(Guid.NewGuid(), Constants.CounterParty.Egypt, Constants.MmsLedgers.EgyptNBEBank));

            Assert.That(isVaild, Is.False);
        }

        private void CreateGsdkServiceInstance()
        {
            gsdkService = new GsdkService(logger, gsdkClient.Object, gsdkHelperService.Object, referenceService.Object, merchantClient.Object);
        }
    }
}
