﻿using FluentValidation;
using System;

namespace Common.Validators
{
    public class EgyptNationalIdValidator : AbstractValidator<string?>
    {
        public EgyptNationalIdValidator()
        {
            RuleFor(m => m)
                      .Length(14)
                      .WithErrorCode(Errors.NationalIdLength.Code);

            RuleFor(m => m)
                .Matches(@"^[\d]+$")
                .WithErrorCode(Errors.NationalIdNumber.Code)
                .DependentRules(() =>
                {
                    Transform(from: x => GetBirthDate(x), to: value => DateTime.TryParse(value, out DateTime val) ? (DateTime?)val : null)
                        .InclusiveBetween(new DateTime(1900, 1, 1), new DateTime(2099, 12, 31))
                        .WithErrorCode(Errors.Lead_InvalidEgyptNationalId.Code)
                        .When(m => m != null && m.Length > 7);
                });
        }

        public static string GetBirthDate(string? nationalId)
        {
            var century = nationalId![0].ToString();
            var yearOfBirth = century switch
            {
                "2" => $"19{nationalId.Substring(1, 2)}",
                "3" => $"20{nationalId.Substring(1, 2)}",
                _ => string.Empty
            };
            var monthOfBirth = nationalId.Substring(3, 2);
            var dayOfBirth = nationalId.Substring(5, 2);
            var dateOfBirth = $"{monthOfBirth}/{dayOfBirth}/{yearOfBirth}";

            return dateOfBirth;
        }
    }
}
