﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Chain;

public class Chain
{
    public int Id { get; set; }
    public string? ChainId { get; set; }
    public string? ChainName { get; set; }
    public string? Segment { get; set; }
    public string? RelationshipManager { get; set; }
    public ChainAddress? ChainAddress { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
}

public class ChainAddress
{
    public Guid AddressId { get; set; }
    public string? BuildingNumber { get; set; }
    public string? BuildingName { get; set; }
    public string? AddressLine1 { get; set; }
    public string? AddressLine2 { get; set; }
    public string? City { get; set; } = string.Empty;
    public string? Country { get; set; } = string.Empty;
    public string? Zip { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}

public class ChainMerchant : Common.Models.Merchant.Merchant
{
    public List<Common.Models.Merchant.Merchant> Accounts { get; set; } = new List<Common.Models.Merchant.Merchant>();
}