﻿using Common.Models.Comment;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Merchant
{
    public class PersonChecks
    {
        public DateTime CheckDate { get; set; }
        public string? CheckId { get; set; }

        public int CheckProvider { get; set; }
        public int CheckScore { get; set; }
        public string CheckStatus { get; set; } = string.Empty;
        public string? CheckType { get; set; }
        public List<CommentResponse> Comments { get; set; } = new List<CommentResponse>();
        public string? ParentEntityId { get; set; }
        public string? MerchantShareHolderCompanyId { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }

    }
}
