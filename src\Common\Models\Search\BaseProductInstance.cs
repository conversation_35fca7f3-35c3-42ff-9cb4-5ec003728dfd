﻿using Common.Models.Product;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Common.Models.Search
{
    public class BaseProductInstance
    {
        private object? metadata;

        public Guid ProductInstanceId { get; set; }
        public string? ProductCode { get; set; }
        public string? ProductType { get; set; }
        public string? ProductDescription { get; set; }
        public string? MID { get; set; }

        public List<ProductInstanceChildren>? Children { get; set; }

        public string? Data { get; set; }

        public object? Metadata
        {
            get
            {
                if (metadata == null && Data != null &&
                    (ProductType == Constants.ProductType.Mpos || ProductType == Constants.ProductType.Terminal))
                {
                    metadata = JsonConvert.DeserializeObject(Data.ToString() ?? string.Empty, typeof(TerminalData)) as TerminalData;
                }
                if (metadata == null && Data != null && ProductType == Constants.ProductType.Gway)
                {
                    metadata = JsonConvert.DeserializeObject(Data.ToString() ?? string.Empty, typeof(GatewayData)) as GatewayData;
                }
                return metadata;
            }
        }

        public ProductShortResponse Product { get; set; } = null!;
    }
}
