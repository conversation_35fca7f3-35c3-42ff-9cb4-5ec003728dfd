﻿using System;
using System.Linq;
using System.Threading.Tasks;
using BackofficeApi.Extensions;
using Common.Models.Shareholder;
using Common.Services;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace BackofficeApi.Controllers;

[Authorize]
[Route("api/v1/[controller]")]
[ApiController]
public class ShareholderController : ControllerBase
{
    private readonly Authorized authorized;
    private readonly IShareholderService shareholderService;
    private readonly ICounterpartyProvider counterpartyProvider;

    public ShareholderController(Authorized authorized,
        IShareholderService shareholderService,
        ICounterpartyProvider counterpartyProvider)
    {
        this.authorized = authorized;
        this.shareholderService = shareholderService;
        this.counterpartyProvider = counterpartyProvider;
    }
    /// <summary>
    /// Gets all Shareholder Companies by MerchantId.
    /// </summary>
    /// <param name="merchantId"></param>
    /// <returns>All Shareholder Companies that are associated on the Merchant.</returns>
    [HttpGet("shareholdercompanies/{merchantId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetShareholderCompanies(Guid merchantId)
    {
        if (!await authorized.To.View.ShareholderCompany())
        {
            return Forbid();
        }

        return Ok(await shareholderService.GetShareholderCompaniesAsync(new ShareholderCompaniesRequest
        {
            MerchantId = merchantId
        }));
    }

    /// <summary>
    /// Search for Shareholder Companies by keyword ( CompanyName and Company License(CR) ).
    /// </summary>
    /// <param name="request"></param>
    /// <returns>All Shareholder Companies that match the keyword.</returns>
    [HttpPost("shareholdercompanies/search")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> SearchShareholderCompanies([FromBody] ShareholderCompanySearchRequest request)
    {
        if (!await authorized.To.View.ShareholderCompany())
        {
            return Forbid();
        }

        return Ok(await shareholderService.SearchShareholderCompaniesAsync(new ShareholderCompaniesRequest
        {
            Keyword = request.Keyword
        }));
    }

    /// <summary>
    /// Create ShareholderCompany and associates it with the current merchant.
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Created ShareholderCompany</returns>
    [HttpPost("shareholdercompanies/create")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreateShareholderCompany([FromBody] ShareholderCompanyCreateRequest request)
    {
        if (!await authorized.To.Create.ShareholderCompany())
        {
            return Forbid();
        }

        SetCreateShareholderCompanyDefaultRequest(request);

        return Ok(await shareholderService.CreateShareholderCompanyAsync(request));
    }

    /// <summary>
    /// Patch ShareholderCompany.
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Edited ShareholderCompany</returns>
    [HttpPatch("shareholdercompanies")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> PatchShareholderCompany([FromBody] ShareholderCompanyPatchRequest request)
    {
        var properties = request.JsonPatchDocument.Operations?.Select(op => op.path.ToLowerInvariant());
        if (!await authorized.To.Patch.ShareholderCompany(properties?.ToArray()))
        {
            return Forbid();
        }

        return Ok(await shareholderService.PatchShareholderCompanyAsync(request));
    }


    [HttpGet("individuals/{merchantId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetShareholderIndividuals(Guid merchantId)
    {
        if (!await authorized.To.View.ShareholderIndividual())
        {
            return Forbid();
        }

        return Ok(await shareholderService.GetMerchantIndividualsAsync(merchantId));
    }

    /// <summary>
    /// Search for Shareholder individuals by keyword ( first name / last name / nationalId / passport number ).
    /// </summary>
    /// <param name="request"></param>
    /// <returns>All Shareholder individuals that match the keyword.</returns>
    [HttpPost("individuals/search")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> SearchShareholderIndividuals([FromBody] ShareholderIndividualsSearchRequest request)
    {
        if (!await authorized.To.View.ShareholderIndividual())
        {
            return Forbid();
        }

        return Ok(await shareholderService.SearchShareholderIndividualsAsync(request));
    }

    /// <summary>
    /// Create Association between ShareholderCompany and the current merchant.
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Created ShareholderCompany</returns>
    [HttpPost("shareholdercompanies/create-association")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreateShareholderCompanyMerchantAssociation(
        [FromBody] ShareholderCompanyMerchantAssociationRequest request)
    {
        if (!await authorized.To.Create.ShareholderCompany())
        {
            return Forbid();
        }

        await shareholderService.CreateShareholderCompanyMerchantAssociationAsync(request);

        return Ok();
    }

    [HttpPost("individual")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]

    public async Task<IActionResult> CreateShareholderIndividual([FromBody] ShareholderIndividualCreateRequest request)
    {
        if (!await authorized.To.Create.ShareholderIndividual())
        {
            return Forbid();
        }
        SetCreateShareholderCompanyDefaultRequest(request);
        await shareholderService.CreateShareholderIndividualAsync(request);
        return Ok();
    }

    /// <summary>
    /// Create Association between Individuals and the current merchant.
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Created ok</returns>
    [HttpPost("individual/create-associations")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreateShareholderIndividualAssociations(
        [FromBody] ShareholderIndividualAssociationsCreateRequest request)
    {
        if (!await authorized.To.Create.ShareholderIndividual())
        {
            return Forbid();
        }

        await shareholderService.CreateShareholderIndividualAssociations(request);

        return Ok();
    }

    /// <summary>
    /// Updates the shareholder individual's details.
    /// </summary>
    /// <param name="request"></param>
    /// <returns>Edited ShareholderIndividual</returns>
    [HttpPatch("individual/update")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> PatchShareholderIndividual([FromBody] ShareholderIndividualPatchRequest request)
    {
        if (!await authorized.To.Patch.ShareholderIndividual(request.PersonOfInterestId))
        {
            return Forbid();
        }

        await shareholderService.PatchShareholderIndividualAsync(request);
        return Ok();
    }

    /// <summary>
    /// Gets the editable status of a shareholder individual.
    /// </summary>
    /// <param name="shareholderIndividualId"></param>
    /// <returns>If the individual is a principal owner or returned by WATHQ.</returns>
    [HttpGet("individual/{shareholderIndividualId:guid}/isEditable")]
    public async Task<IActionResult> GetShareholderIndividualEditableStatus(Guid shareholderIndividualId)
    {
        if (!await authorized.To.View.ShareholderIndividual())
        {
            return Forbid();
        }

        return Ok(await shareholderService.GetShareholderIndividualEditableStatusAsync(shareholderIndividualId));
    }

    private void SetCreateShareholderCompanyDefaultRequest(BaseShareholderModel request)
    {
        request.CreatedBy = this.GetUserId().ToString();
        request.Counterparty = counterpartyProvider.GetCode();
    }
}