﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Common.Models.Checkout
{
    public class OrderItem
    {
        public Guid Id { get; set; }
        public Guid OrderId { get; set; }

        [JsonIgnore]
        [ForeignKey("OrderId")]
        public OrderResponse Order { get; set; } = null!;

        public string? ProductType { get; set; }

        public string? CreatedBy { get; set; } = null!;

        public string? UpdatedBy { get; set; }

        public DateTime? CreatedDateUtc { get; set; }

        public DateTime? UpdatedDateUtc { get; set; }

        public bool DeletedFlag { get; set; } = false;

        public Guid? AddressId { get; set; }

        public string? EposTicketId { get; set; }

        public Guid ProductId { get; set; }

        public List<Guid> ProductInstanceIds { get; set; } = new List<Guid>();
    }
}
