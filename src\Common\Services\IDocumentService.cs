﻿using Common.Models;
using Common.Models.Document;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IDocumentService
    {
        Task<HttpContent> DownloadDocumentAsync(Guid documentId);
        Task<DocumentMetadata> GetDocumentMetadataAsync(Guid documentId);
        Task<DocumentWithContent[]> GetDocumentWithContentAsync(DocumentSearchCriteria searchCriteria);
        Task<DocumentWithContent> DownloadZippedDocuments(Guid merchantId);
        Task<DocumentWithContent> GetDocumentByMerchantId(Guid merchantId);

        Task<DocumentMetadata> CreateDocumentAsync(DocumentRequest documentRequest);
        Task DeleteDocumentsForLeadAsync(Guid leadId);
    }
}
