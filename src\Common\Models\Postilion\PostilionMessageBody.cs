﻿using System;

namespace Common.Models.Postilion
{
    public class PostilionMessageBody
    {
        public Guid? ProductInstanceId { get; set; }
        public string? MidMerchantReference { get; set; } = string.Empty;
        public string? LegalName { get; set; } = string.Empty;
        public string? City { get; set; } = string.Empty;
        public string? CountryCode { get; set; } = string.Empty;
        public string? FullTID { get; set; } = string.Empty;
        public string? ProviderBank { get; set; } = string.Empty;
        public string? AccountNumber { get; set; } = string.Empty;
        public string? ChannelType { get; set; } = string.Empty;
        public string? ConnectionType { get; set; } = string.Empty;
        public string? MPGSMID { get; set; } = string.Empty;
        public string? MPGSKEY { get; set; } = string.Empty;
    }
}
