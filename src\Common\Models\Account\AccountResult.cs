﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Account
{
    public class AccountResult
    {
        public string? Mid { get; set; }
        public string? BusinessName { get; set; }
        public string? BusinessNameAr { get; set; }
        public string? BusinessId { get; set; }
        public string? ChannelType { get; set; }
        public string? MerchantStatus { get; set; }
        public string? AccountStatus { get; set; }
        public Guid? MerchantId { get; set; }
        public Guid? StoreId { get; set; }
        public DateTime? CreatedDate { get; set; }
    }
}
