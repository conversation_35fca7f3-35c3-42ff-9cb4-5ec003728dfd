﻿using Common;
using Common.Models.Checkout;
using Common.Models.Checks;
using Common.Models.Match;
using Common.Models.Merchant;
using Common.Options;
using Common.Services;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static Common.Constants;

namespace Services
{
    public class DueDiligenceService : IDueDiligenceService
    {
        private readonly HttpClient client;
        private readonly ILogger<DueDiligenceService> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly IOptions<WorldCheckFeatureManagement> worldCheckFeatureManagement;
        private readonly IOptions<FinscanCheckFeatureToggle> finscanCheckFeatureToggle;
        private readonly IOptions<MatchCheckFeatureToggle> matchCheckFeatureToggle;
        private readonly IOptions<SASCheckFeatureToggle> sasCheckFeatureToggle;
        private readonly IOptions<RiskScoringFeatureToggle> riskScoringFeatureToggle;
        private readonly ICounterpartyProvider counterpartyProvider;

        public DueDiligenceService(HttpClient client, ILogger<DueDiligenceService> logger, IOptions<UrlSettings> urlSettingsOptions, IOptions<WorldCheckFeatureManagement> worldCheckFeatureManagement, ICounterpartyProvider counterpartyProvider
            , IOptions<FinscanCheckFeatureToggle> finscanCheckFeatureToggle
            , IOptions<MatchCheckFeatureToggle> matchCheckFeatureToggle
            , IOptions<SASCheckFeatureToggle> sasCheckFeatureToggle
            , IOptions<RiskScoringFeatureToggle> riskScoringFeatureToggle)
        {
            this.client = client;
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.Value;
            this.worldCheckFeatureManagement = worldCheckFeatureManagement;
            this.finscanCheckFeatureToggle = finscanCheckFeatureToggle;
            this.matchCheckFeatureToggle = matchCheckFeatureToggle;
            this.sasCheckFeatureToggle = sasCheckFeatureToggle;
            this.riskScoringFeatureToggle = riskScoringFeatureToggle;
            this.counterpartyProvider = counterpartyProvider;
        }

        private string DueDiligenceServiceUrl => $"{urlSettingsOptions.DueDiligenceServiceBaseUrlNS}/api/v1/match";
        private string DueDiligenceServiceBaseUrl => $"{urlSettingsOptions.DueDiligenceServiceBaseUrlNS}";
        private readonly string DueDiligenceWorldCheckOneEndpoint = "api/v1/WorldCheckOne";
        private readonly string DueDiligenceFinscanCheckOneEndpoint = "api/v1/Finscan";
        private readonly string DueDiligenceMatchCheckEndPoint = "api/v1/Match";

        public async Task<MatchResponse> RetrieveMerchantMatchReport(Guid documentId)
        {
            using (logger.BeginScope("DueDiligenceService({dueDiligenceServiceUrl})", DueDiligenceServiceUrl))
            {
                logger.LogInformation("Retrieving data about merchant check status for document with id : {documentId}", documentId);

                var response = await client.GetAsync($"{DueDiligenceServiceUrl}/{documentId}");
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error calling DueDiligenceService to retrieve match response for document with id : {documentId}", documentId);

                    throw new PassthroughException(response);
                }

                var matchResponse = Json.Deserialize<MatchResponse>(responseBody, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return matchResponse;
            }
        }

        public async Task SendWorldCheckOneRequest(Guid merchantId)
        {
            if (worldCheckFeatureManagement.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
            {
                var requestUri = $"{DueDiligenceServiceBaseUrl}/{DueDiligenceWorldCheckOneEndpoint}/Business/Create/{merchantId}";
                using (logger.BeginScope("SendScreeningRequest({requestUri})", requestUri))
                {
                    try
                    {
                        var requestBody = new StringContent(JsonConvert.SerializeObject(merchantId), Encoding.UTF8, "application/json");
                        var response = await client.PostAsync(requestUri, requestBody);
                        var jsonResult = await response.Content.ReadAsStringAsync();

                        if (!response.IsSuccessStatusCode)
                        {
                            logger.LogCritical("Error when calling DueDiligence for WorldCheckOne screening. Error was {response.StatusCode} {@jsonResult}", response.StatusCode, Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonResult)));

                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Error occured while sending request to DueDilligence service: {0}", ex);
                    }
                }
            }
        }

        public async Task SendFinscanCheckRequest(Guid merchantId)
        {
            if (!finscanCheckFeatureToggle.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
                return;
            if (finscanCheckFeatureToggle.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
            {
                var requestUri = $"{DueDiligenceServiceBaseUrl}/{DueDiligenceFinscanCheckOneEndpoint}/MerchantBuisness/{merchantId}";
                using (logger.BeginScope("SendScreeningRequest({requestUri})", requestUri))
                {
                    try
                    {
                        var requestBody = new StringContent(JsonConvert.SerializeObject(merchantId), Encoding.UTF8, "application/json");
                        var response = await client.PostAsync(requestUri, requestBody);
                        var jsonResult = await response.Content.ReadAsStringAsync();

                        if (!response.IsSuccessStatusCode)
                        {
                            logger.LogCritical("Error when calling DueDiligence for FinscanCheck screening. Error was {response.StatusCode} {@jsonResult}", response.StatusCode, Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonResult)));

                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Error occured while sending request to DueDilligence service: {0}", ex);
                    }
                }
            }
            return;

        }
        public async Task SendMatchCheckRequest(MatchRequest merchantRequest)
        {
            if (!matchCheckFeatureToggle.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
                return;
            if (matchCheckFeatureToggle.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
            {
                var requestUri = $"{DueDiligenceServiceBaseUrl}/{DueDiligenceMatchCheckEndPoint}";
                using (logger.BeginScope("SendScreeningRequest({requestUri})", requestUri))
                {
                    try
                    {
                        var requestBody = new StringContent(JsonConvert.SerializeObject(merchantRequest), Encoding.UTF8, "application/json");
                        var response = await client.PostAsync(requestUri, requestBody);
                        var jsonResult = await response.Content.ReadAsStringAsync();

                        if (!response.IsSuccessStatusCode)
                        {
                            logger.LogCritical("Error when calling DueDiligence for FinscanCheck screening. Error was {response.StatusCode} {@jsonResult}", response.StatusCode, Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonResult)));

                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error occured while sending request to DueDilligence service");
                    }
                }
            }
            return;

        }

        public async Task SendRiskScoringCheckRequest(Guid merchantId)
        {
            await Task.CompletedTask;
        }

        public async Task TriggerChecks(Guid merchantId)
        {
            if (worldCheckFeatureManagement.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
            {
                await SendWorldCheckOneRequest(merchantId);
            }

            if (finscanCheckFeatureToggle.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
            {
                await SendFinscanCheckRequest(merchantId);
            }

            if (matchCheckFeatureToggle.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()) &&
                matchCheckFeatureToggle.Value.EnableMatchCheck)
            {
                MatchRequest matchRequest = new()
                {
                    MerchantId = merchantId,
                };

                await SendMatchCheckRequest(matchRequest);
            }

            if (riskScoringFeatureToggle.Value.AllowedCounterparties.Contains(counterpartyProvider.GetCode()))
            {
                await SendRiskScoringCheckRequest(merchantId);
            }
        }
    }
}