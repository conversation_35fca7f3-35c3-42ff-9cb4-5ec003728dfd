﻿using Common.Models.TaskComments;
using Common.Options;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Common.Services
{
    public class TaskCommentService : ITaskCommentService
    {
        private readonly HttpClient client;

        private readonly ILogger<TaskCommentService> logger;

        private readonly UrlSettings urlSettingsOptions;

        private string TaskServiceUrl => $"{urlSettingsOptions.MerchantServiceBaseUrl}/api/v1/merchant/task";

        public TaskCommentService(HttpClient client, IOptions<UrlSettings> urlSettingsOptions, ILogger<TaskCommentService> logger)
        {
            this.client = client;
            this.urlSettingsOptions = urlSettingsOptions.Value;
            this.logger = logger;
        }

        public async Task<TaskCommentResponse> CreateCommentAsync(Guid taskId, TaskCommentCreateRequest request)
        {
            using (logger.BeginScope("TaskService({taskServiceUrl})", TaskServiceUrl))
            {
                logger.LogInformation("Calling task comment service to create task comment for task with id : {taskId}.", taskId);

                var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
                var response = await client.PostAsync($"{TaskServiceUrl}/{taskId}/comment", body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling task service to create task comment for task with id : {taskId}. Error was {StatusCode} {@responseBody}",
                        taskId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var taskComment = Json.Deserialize<TaskCommentResponse>(responseBody,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                return taskComment;

            }
        }

        public async Task<TaskCommentResponse> GetCommentByIdAsync(Guid commentId)
        {
            using (logger.BeginScope("TaskService({taskServiceUrl})", TaskServiceUrl))
            {
                logger.LogInformation("Calling task comment service to get comment by Id. Id : '{commentId}'", commentId);
                var response = await client.GetAsync($"{TaskServiceUrl}/comment/{commentId}");
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling task service to get comment by Id. Id : '{commentId}'. Error was {StatusCode} {@responseBody}",
                        commentId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var taskComment = Json.Deserialize<TaskCommentResponse>(responseBody,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                return taskComment;
            }
        }

        public async Task<List<TaskCommentResponse>> GetCommentByTaskIdAsync(Guid taskId)
        {
            using (logger.BeginScope("TaskService({taskServiceUrl})", TaskServiceUrl))
            {
                logger.LogInformation("Calling task comment service to get comments by taskId. taskId : '{taskId}'", taskId);
                var response = await client.GetAsync($"{TaskServiceUrl}/{taskId}/comment");
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling task service to get comments by taskId. taskId: '{taskId}'. Error was {StatusCode} {@responseBody}",
                        taskId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var taskComments = Json.Deserialize<List<TaskCommentResponse>>(responseBody,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                return taskComments;
            }
        }
    }
}
