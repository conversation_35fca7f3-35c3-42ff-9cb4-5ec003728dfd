﻿using Common.Models.Checkout;
using CsvHelper.Configuration;

namespace BackofficeApi.Formatters.ClassMappers
{
    public class EgyptOrderExportResponseMap : ClassMap<EgyptOrderExportResponse>
    {
        public EgyptOrderExportResponseMap()
        {
            Map(m => m.BusinessName).Name("Business Name");
            Map(m => m.OrderNumber).Name("Order Number");
            Map(m => m.OrderStatus).Name("Order Status");
            Map(m => m.MerchantStatus).Name("Merchant Status");
            Map(m => m.MemberId).Name("Member Id");
            Map(m => m.Phone).Name("Phone Number");
            Map(m => m.FirstName).Name("First Name");
            Map(m => m.LastName).Name("Last Name");
            Map(m => m.NationalId).Name("National ID");
            Map(m => m.Email).Name("Email");
            Map(m => m.AccountHolderName).Name("Bank Account Name");
            Map(m => m.BankAccountNumber).Name("Account Number");
            Map(m => m.IBAN).Name("IBAN");
            Map(m => m.BankName).Name("Bank Name");
            Map(m => m.Quantity).Name("Quantity");
            Map(m => m.BusinessDomain).Name("Business Domain");
            Map(m => m.MCC).Name("MCC");
            Map(m => m.MerchantType).Name("Merchant Type");
            Map(m => m.Governorate).Name("Governorate");
            Map(m => m.BusinessAddress).Name("Business Address");
            Map(m => m.CommercialRegistration).Name("Commercial Registration Number");
            Map(m => m.SalesPersonFirstName).Name("Sales Person First Name");
            Map(m => m.SalesPersonLastName).Name("Sales Person Last Name");
            Map(m => m.DeliveryMethod).Name("Delivery Method");
            Map(m => m.DeliveryDays).Name("Delivery days");
            Map(m => m.ProofOfDelivery).Name("Proof of Delivery").TypeConverter<CustomBooleanConverter>();
            Map(m => m.BillPayments).Name("Bill payments registration");
            Map(m => m.Acquirer).Name("Acquirer");
            Map(m => m.StoreCity).Name("City");
        }
    }
}
