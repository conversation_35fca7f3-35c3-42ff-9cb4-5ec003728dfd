﻿using AutoMapper;
using Common.Models;
using Common.Models.Account;
using Common.Models.Chain;
using Common.Models.Lead;
using Common.Models.Merchant;
using Common.Options;
using Common.Services;
using Common.Validators;
using Common.Validators.Chain;
using FluentValidation;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Geidea.Utils.ReferenceData;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Net.Http;
using System.Security.Principal;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using static Common.Constants;
using Constants = Common.Constants;

namespace Services;

public class ChainService : IChainService
{
    private readonly ILogger<ChainService> logger;
    private readonly UrlSettings urlSettingsOptions;
    private readonly HttpClient client;
    private readonly Common.Services.IReferenceService referenceService;
    private string MerchantServiceBaseUrl => $"{urlSettingsOptions.MerchantServiceBaseUrlNS}/api/v1";
    private string SearchServiceBaseUrl => $"{urlSettingsOptions.SearchServiceBaseUrlNS}/api/v1";

    public ChainService(
        ILogger<ChainService> logger,
        IOptionsMonitor<UrlSettings> urlSettingsOptions,
        Common.Services.IReferenceService referenceService,
        HttpClient client)
    {
        this.logger = logger;
        this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
        this.client = client;
        this.referenceService = referenceService;
    }

    public async Task<ChainSearchResponse<ChainResult>> FindAsync(ChainSearchFilters filters, CancellationToken cancellationToken = default)
    {
        var searchServiceUrl = $"{SearchServiceBaseUrl}/Chain/advancedSearch";

        using (logger.BeginScope("AdvancedSearchAsync({@searchServiceUrl})", searchServiceUrl))
        {
            logger.LogInformation("Calling search service to search for chains");

            var body = new StringContent(JsonConvert.SerializeObject(filters), Encoding.UTF8, "application/json");
            var response = await client.PostAsync(searchServiceUrl, body, cancellationToken);
            var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling search service to search for chains. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var accounts = Json.Deserialize<ChainSearchResponse<ChainResult>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            var mappedResponse = Map(accounts);
            return mappedResponse;
        }

    }

    private ChainSearchResponse<ChainResult> Map(ChainSearchResponse<ChainResult> coreResponse)
    {
        var result = new ChainSearchResponse<ChainResult>()
        {
            ReturnedRecordCount = coreResponse.ReturnedRecordCount,
            TotalRecordCount = coreResponse.TotalRecordCount
        };

        var records = coreResponse.Records
            .Select(m =>
            {
                var apiResult = m;
                return apiResult;
            });

        result.Records = records.OfType<ChainResult>().ToList();
        return result;
    }

    public async Task<Chain> CreateChain(ChainCreateRequest request)
    {
        await ValidateChainCreateRequest(request);
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/Chain";

        using (logger.BeginScope("CreateChain({@merchantServiceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service to create a chain");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
            var response = await client.PostAsync(merchantServiceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to create a chain. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var chainResult = Json.Deserialize<Chain>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return chainResult;
        }
    }

    public async Task<Chain> UpdateChain(ChainUpdateRequest request)
    {
        await ValidateChainUpdateRequest(request);
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/Chain";

        using (logger.BeginScope("UpdateChain({@merchantServiceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service to update a chain");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
            var response = await client.PutAsync(merchantServiceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to update a chain. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var chainResult = Json.Deserialize<Chain>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return chainResult;
        }
    }

    public async Task<Chain> GetChain(string chainId)
    {
        string serviceUrl = $"{MerchantServiceBaseUrl}/Chain/{chainId}";

        using (logger.BeginScope("GetChain({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service, get chain.");

            var response = await client.GetAsync(serviceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service, get chain by chain id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<Chain>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }
    }

    public async Task DeleteChain(string chainId)
    {
        string requestUrl = $"{MerchantServiceBaseUrl}/Chain/{chainId}";

        using (logger.BeginScope("DeleteChain({@requestUrl})", requestUrl))
        {
            logger.LogInformation($"Calling merchant service in order to delete chain with id: '{chainId}'.");

            var response = await client.DeleteAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling merchant service, delete chain with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                    chainId, (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }
            logger.LogInformation($"Deleted chain with id '{chainId}'.");
        }
    }

    public async Task<List<Merchant>> AssociateChainMerchants(string chainId, List<ChainMerchantsLinkRequest> request)
    {
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/Chain/{chainId}/merchants";

        using (logger.BeginScope("AssociateChainMerchants({@merchantServiceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service to update merchants under a chain");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
            var response = await client.PostAsync(merchantServiceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to update merchants under a chain. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var chainResult = Json.Deserialize<List<Merchant>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return chainResult;
        }
    }

    public async Task<List<ChainMerchant>> GetChainMerchants(string chainId)
    {
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/Chain/{chainId}/merchants";

        using (logger.BeginScope("GetChainMerchants({@serviceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service, get chain merchants.");

            var response = await client.GetAsync(merchantServiceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service, get chain merchants by chain id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<List<ChainMerchant>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }
    }

    public async Task<List<ChainContact>> GetAllContactsOfChain(string chainId)
    {
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/ChainContacts/{chainId}";

        using (logger.BeginScope("GetAllContactsOfChain({@serviceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service, get chain contacts.");

            var response = await client.GetAsync(merchantServiceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service, get chain contacts by chain id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<List<ChainContact>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }
    }

    public async Task<ChainContact> GetContactById(Guid contactId)
    {
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/ChainContact/{contactId}";

        using (logger.BeginScope("GetContactById({@serviceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service, get chain contact.");

            var response = await client.GetAsync(merchantServiceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service, get chain contact by contact id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<ChainContact>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }
    }

    public async Task DeleteChainContact(Guid chainContactId)
    {
        string requestUrl = $"{MerchantServiceBaseUrl}/ChainContact/{chainContactId}";

        using (logger.BeginScope("DeleteChainContact({@requestUrl})", requestUrl))
        {
            logger.LogInformation($"Calling merchant service in order to delete chain contact with id: '{chainContactId}'.");

            var response = await client.DeleteAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling merchant service, delete chain contact with id '{chainContacId}'. Error was {StatusCode} {@responseBody}",
                    chainContactId, (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }
            logger.LogInformation($"Deleted chain contact with id '{chainContactId}'.");
        }
    }

    public async Task<List<ChainContact>> CreateChainContacts(ChainContactsCreateRequest request)
    {
        await ValidateChainContactsCreateRequest(request);
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/ChainContact";

        using (logger.BeginScope("CreateChainContacts({@merchantServiceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service to create a chain contacts");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
            var response = await client.PostAsync(merchantServiceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to create a chain contacts. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var chainResult = Json.Deserialize<List<ChainContact>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return chainResult;
        }
    }

    public async Task<List<ChainContact>> UpdateChainContacts(ChainContactsUpdateRequest request)
    {
        await ValidateChainContactsUpdateRequest(request);
        var merchantServiceUrl = $"{MerchantServiceBaseUrl}/ChainContact";

        using (logger.BeginScope("UpdateChainContacts({@merchantServiceUrl})", merchantServiceUrl))
        {
            logger.LogInformation("Calling merchant service to update a chain contacts");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
            var response = await client.PutAsync(merchantServiceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to update a chain contacts. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var chainResult = Json.Deserialize<List<ChainContact>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return chainResult;
        }
    }

    public async Task<List<ChainExport>> ExportChainsAsync(ChainSearchFilters searchCriteria)
    {
        var chains = await GetChainsBySearchCriteria(searchCriteria);

        var catalogues = await referenceService.GetCataloguesAsync(new string[] { Constants.Catalogues.Segment });

        var chainsExport = MapChainsForExport(chains.Records, catalogues);

        return chainsExport;
    }

    private async Task<ChainExportResponse<ChainExportResult>> GetChainsBySearchCriteria(ChainSearchFilters searchCriteria)
    {
        string serviceUrl = $"{SearchServiceBaseUrl}/Chain/export";
        var requestBody = new StringContent(JsonConvert.SerializeObject(searchCriteria), Encoding.UTF8, "application/json");

        using (logger.BeginScope("GetChainsBySearchCriteria({@searchServiceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling search service API to export all chains by search criteria");

            var response = await client.PostAsync(serviceUrl, requestBody);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {

                logger.LogCritical("Error when calling search service API to export chains. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var chains = Json.Deserialize<ChainExportResponse<ChainExportResult>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            var mappedResponse = MapChainExport(chains);
            return mappedResponse;
        }
    }

    private ChainExportResponse<ChainExportResult> MapChainExport(ChainExportResponse<ChainExportResult> coreResponse)
    {
        var result = new ChainExportResponse<ChainExportResult>()
        {
            ReturnedRecordCount = coreResponse.ReturnedRecordCount,
            TotalRecordCount = coreResponse.TotalRecordCount
        };

        var records = coreResponse.Records
            .Select(m =>
            {
                var apiResult = m;
                return apiResult;
            });

        result.Records = records.OfType<ChainExportResult>().ToList();
        return result;
    }

    private static List<ChainExport> MapChainsForExport(IList<ChainExportResult> chains, Catalogue[] catalogues)
    {
        var chainsExport = new List<ChainExport>();

        foreach (var chain in chains)
        {

            ChainExport chExport = new ChainExport();

            chExport.ChainId = chain.ChainId;
            chExport.ChainName = chain.ChainName;
            chExport.LinkedBusinesses = chain.LinkedBusinesses.HasValue ? chain.LinkedBusinesses.Value.ToString() : "";
            chExport.ContactPerson = chain.ContactPerson;
            chExport.Segment = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.Segment && x.Key == chain.Segment)?.Value;
            chExport.CreatedBy = chain.CreatedBy;
            chExport.CreatedDate = chain.CreatedDate.ToString() ?? string.Empty;

            chainsExport.Add(chExport);
        }

        return chainsExport;
    }

    public async Task<List<Catalogue>> GetReferenceDataAsync()
    {
        var catalogNames = new string[] { Catalogues.Segment, Catalogues.RelationToCompany, Catalogues.Designation };
        var refData = await referenceService.GetCataloguesAsync(catalogNames, "EN");
        var refDataList = refData.ToList();
        return refDataList;
    }

    private async Task ValidateChainCreateRequest(ChainCreateRequest chainCreateRequest)
    {
        var refData = await GetReferenceDataAsync();

        var refDataUtils = refData.Select(r => new ReferenceData { CatalogueName = r.CatalogueName, Key = r.Key, Value = r.Value }).ToList();

        var validationResult = new ChainCreateRequestValidator(refDataUtils).Validate(chainCreateRequest);

        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Chain create validation failed: {@errors}", errorDescription);

            throw new Geidea.Utils.Exceptions.ValidationException(validationResult);
        }
    }

    private async Task ValidateChainUpdateRequest(ChainUpdateRequest chainUpdateRequest)
    {
        var refData = await GetReferenceDataAsync();

        var refDataUtils = refData.Select(r => new ReferenceData { CatalogueName = r.CatalogueName, Key = r.Key, Value = r.Value }).ToList();

        var validationResult = new ChainUpdateRequestValidator(refDataUtils).Validate(chainUpdateRequest);

        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Chain update validation failed: {@errors}", errorDescription);

            throw new Geidea.Utils.Exceptions.ValidationException(validationResult);
        }
    }

    private async Task ValidateChainContactsCreateRequest(ChainContactsCreateRequest request)
    {
        var refData = await GetReferenceDataAsync();

        var refDataUtils = refData.Select(r => new ReferenceData { CatalogueName = r.CatalogueName, Key = r.Key, Value = r.Value }).ToList();

        var validationResult = new ChainContactsCreateRequestValidator(refDataUtils).Validate(request);

        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Chain contacts create validation failed: {@errors}", errorDescription);

            throw new Geidea.Utils.Exceptions.ValidationException(validationResult);
        }
    }

    private async Task ValidateChainContactsUpdateRequest(ChainContactsUpdateRequest request)
    {
        var refData = await GetReferenceDataAsync();

        var refDataUtils = refData.Select(r => new ReferenceData { CatalogueName = r.CatalogueName, Key = r.Key, Value = r.Value }).ToList();

        var validationResult = new ChainContactsUpdateRequestValidator(refDataUtils).Validate(request);

        if (!validationResult.IsValid)
        {
            var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
            logger.LogError("Chain contacts update validation failed: {@errors}", errorDescription);

            throw new Geidea.Utils.Exceptions.ValidationException(validationResult);
        }
    }
}
