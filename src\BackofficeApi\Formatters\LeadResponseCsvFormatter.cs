﻿using BackofficeApi.Formatters.ClassMappers;
using Common.Models.Lead;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BackofficeApi.Formatters
{
    public class LeadResponseCsvFormatter : TextOutputFormatter
    {
        public LeadResponseCsvFormatter()
        {
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/csv"));
            SupportedEncodings.Add(Encoding.UTF8);
        }

        protected override bool CanWriteType(Type? type)
        {
            if (typeof(IEnumerable<LeadExportResponse>).IsAssignableFrom(type))
            {
                return true;
            }
            return false;
        }

        public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
        {
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                InjectionOptions = InjectionOptions.Escape
            };
            using var streamWriter = context.WriterFactory(context.HttpContext.Response.Body, selectedEncoding);
            using var csv = new CsvWriter(streamWriter, config);
            csv.Context.RegisterClassMap<LeadExportResponseMap>();
            csv.WriteHeader(typeof(LeadExportResponse));
            await csv.NextRecordAsync();

            var leads = context.Object as IEnumerable<LeadExportResponse>;
            if (leads != null)
                leads.ToList().ForEach(async lead =>
                {
                    csv.WriteRecord(lead);
                    await csv.NextRecordAsync();
                });
            await streamWriter.FlushAsync();
        }
    }
}
