﻿using Common.Models.Lead;
using Common.Models.User;
using Common.Services;
using Common.Validators;
using Microsoft.AspNetCore.JsonPatch;
using System.Threading.Tasks;
using Common;
using Geidea.Utils.Validation;
using Microsoft.Extensions.Logging;

namespace Services
{
    public class SaleService : ISaleService
    {
        private readonly ILogger<SaleService> logger;
        private readonly IUserService userService;
        private readonly ILeadService leadService;
        private readonly IMerchantService merchantService;

        public SaleService(IUserService userService, ILeadService leadService, IMerchantService merchantService, ILogger<SaleService> logger)
        {
            this.userService = userService;
            this.leadService = leadService;
            this.merchantService = merchantService;
            this.logger = logger;
        }

        public async Task UpdateLeadSalesIdAsync(LeadSalesIdRequest leadSalesIdRequest)
        {
            var patch = new JsonPatchDocument<Lead>();
            patch.Add(x => x.SalesId, leadSalesIdRequest.SalesId);
            var lead = await leadService.PatchLeadByIdAsync(leadSalesIdRequest.LeadId, patch);

            if (lead.LeadStatus == Constants.LeadStatus.Converted)
                await merchantService.UpdateMerchantSalesIdByLead(leadSalesIdRequest.LeadId, leadSalesIdRequest.SalesId);
        }

        public async Task UpdateUserSalesIdAsync(UserSalesIdRequest userSalesIdRequest)
        {
            new ValidationHelpers().Validate(userSalesIdRequest, new UserSalesIdRequestValidator(), logger, "UserSalesIdRequest length validation failed!");

            var userSalesIdResponse = await userService.UpdateUserSalesIdAsync(userSalesIdRequest);

            if (!string.IsNullOrEmpty(userSalesIdResponse.InitialSalesId))
            {
                await leadService.UpdateSalesIdAsync(userSalesIdResponse.InitialSalesId, userSalesIdResponse.UpdatedSalesId);
                await merchantService.UpdateSalesIdAsync(userSalesIdResponse.InitialSalesId, userSalesIdResponse.UpdatedSalesId);
            }
        }
    }
}
