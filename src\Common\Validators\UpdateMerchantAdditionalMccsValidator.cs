﻿using Common.Models.Merchant;
using FluentValidation;
using System.Collections.Generic;

namespace Common.Validators
{
    public class UpdateMerchantAdditionalMccsValidator : AbstractValidator<IReadOnlyCollection<AdditionalMccUpdate>>
    {
        public UpdateMerchantAdditionalMccsValidator()
        {
            RuleForEach(x => x)
                .SetValidator(new UpdateMerchantAdditionalMccValidator());
        }
    }
}
