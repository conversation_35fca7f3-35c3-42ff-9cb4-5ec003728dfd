﻿using Common;
using Common.Models;
using Common.Models.Document;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services
{
    public class DocumentService : IDocumentService
    {
        private readonly HttpClient client;
        private readonly ILogger<DocumentService> logger;
        private readonly UrlSettings urlSettingsOptions;

        public DocumentService(ILogger<DocumentService> logger, IOptionsMonitor<UrlSettings> urlSettingsOptions, HttpClient client)
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.client = client;
        }

        private string DocumentServiceBaseUrl => $"{urlSettingsOptions.DocumentServiceBaseUrlNS}/api/v1";

        public async Task<DocumentMetadata> CreateDocumentAsync(DocumentRequest documentRequest)
        {
            string url = $"{DocumentServiceBaseUrl}/document";

            using (logger.BeginScope("CreateDocumentAsync({@url})", url))
            {
                logger.LogInformation($"Calling document service to create new document.");
                var response = await client.PostAsync(url, ConvertToMultipartFormData(documentRequest));
                var jsonResult = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling document service to create new document. Error was {response.StatusCode} {@jsonResult}");
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<DocumentMetadata>(jsonResult,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task DeleteDocumentsForLeadAsync(Guid leadId)
        {
            string url = $"{DocumentServiceBaseUrl}/document?LeadId={leadId}";

            using (logger.BeginScope("DeleteDocumentsForLeadAsync({@url})", url))
            {
                logger.LogInformation($"Calling document service to delete documents for lead with id {leadId}.");
                var response = await client.DeleteAsync(url);
                var jsonResult = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling document service todelete documents for lead. Error was {response.StatusCode} {@jsonResult}");
                    throw new PassthroughException(response);
                }
            }
        }

        public async Task<HttpContent> DownloadDocumentAsync(Guid documentId)
        {
            string serviceUrl = $"{DocumentServiceBaseUrl}/document/{documentId:N}";

            using (logger.BeginScope("GetDocumentAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling document service to retrieve document...");

                var response = await client.GetAsync(serviceUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    logger.LogCritical("Error when calling document service to retrieve document. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return response.Content;
            }
        }

        public async Task<DocumentMetadata> GetDocumentMetadataAsync(Guid documentId)
        {
            string serviceUrl = $"{DocumentServiceBaseUrl}/document/{documentId:N}/metadata";

            using (logger.BeginScope("GetDocumentMetadataAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling document service to retrieve document metadata...");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling document service to retrieve document metadata. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<DocumentMetadata>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<DocumentWithContent[]> GetDocumentWithContentAsync(DocumentSearchCriteria searchCriteria)
        {
            string serviceUrl = $"{DocumentServiceBaseUrl}/documents";

            using (logger.BeginScope("GetDocumentWithContentAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling document service to retrieve documents...");

                var content = new StringContent(JsonConvert.SerializeObject(searchCriteria), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(serviceUrl, content);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling document service to retrieve documents. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<DocumentWithContent[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<DocumentWithContent> DownloadZippedDocuments(Guid merchantId)
        {
            using (logger.BeginScope("GetZippedDocumentsForMerchant({merchantId})", merchantId))
            {
                var documents = (await GetDocumentWithContentAsync(new DocumentSearchCriteria { MerchantId = merchantId }))
                    .Where(d => d.DocumentType != Constants.DocumentType.GeneralContract)
                    .ToList();

                if (!documents.Any())
                {
                    logger.LogError("Documents for merchant with id {merchantId} not found.", merchantId);
                    throw new ServiceException(HttpStatusCode.NotFound, Errors.DocumentsNotFound).WithArguments(merchantId);
                }

                using var outStream = new MemoryStream();
                using (var archive = new ZipArchive(outStream, ZipArchiveMode.Create, false))
                {
                    foreach (var document in documents)
                    {
                        var fileBytes = document.Content;
                        var archiveEntry = archive.CreateEntry(document.FileName, CompressionLevel.Fastest);
                        await using var entryStream = archiveEntry.Open();
                        using var fileToCompressStream = new MemoryStream(fileBytes);
                        await fileToCompressStream.CopyToAsync(entryStream);
                    }
                }

                return new DocumentWithContent
                {
                    FileName = merchantId.ToString(),
                    Content = outStream.ToArray(),
                };
            }
        }

        public async Task<DocumentWithContent> GetDocumentByMerchantId(Guid merchantId)
        {
            using (logger.BeginScope("GetDocumentByMerchantId({merchantId})", merchantId))
            {
                var documents = (await GetDocumentWithContentAsync(new DocumentSearchCriteria { MerchantId = merchantId, DocumentType = Constants.Risk.Sas }))
                    .Where(d => d.DocumentType != Constants.DocumentType.GeneralContract)
                    .OrderByDescending(x => x.CreatedDate)
                    .ToList();

                if (!documents.Any())
                {
                    logger.LogError("Documents for merchant with id {merchantId} not found.", merchantId);
                    throw new ServiceException(HttpStatusCode.NotFound, Errors.DocumentsNotFound).WithArguments(merchantId);
                }

                return new DocumentWithContent
                {
                    FileName = merchantId.ToString(),
                    Id = documents.FirstOrDefault()!.Id,
                };
            }
        }

        private MultipartFormDataContent ConvertToMultipartFormData(DocumentRequest documentRequest)
        {
            byte[] data;
            var stream = documentRequest.File.OpenReadStream();

            using (var br = new BinaryReader(stream))
                data = br.ReadBytes((int)stream.Length);

            var bytes = new ByteArrayContent(data);

            return new MultipartFormDataContent
                {
                    { bytes, "file", documentRequest.File.FileName },

                    { new StringContent(documentRequest.OwnerUserId.ToString()), nameof(documentRequest.OwnerUserId) },
                    { new StringContent(documentRequest.MerchantId.ToString() ?? string.Empty), nameof(documentRequest.MerchantId) },
                    { new StringContent(documentRequest.PersonOfInterestId.ToString() ?? string.Empty), nameof(documentRequest.PersonOfInterestId) },
                    { new StringContent(documentRequest.LeadId.ToString() ?? string.Empty), nameof(documentRequest.LeadId) },
                    { new StringContent(documentRequest.DocumentType ?? string.Empty), nameof(documentRequest.DocumentType) },
                    { new StringContent(documentRequest.Language ?? string.Empty), nameof(documentRequest.Language) },
                    { new StringContent(documentRequest.ProviderId.ToString() ?? string.Empty), nameof(documentRequest.ProviderId.ToString) }
                };
        }
    }
}