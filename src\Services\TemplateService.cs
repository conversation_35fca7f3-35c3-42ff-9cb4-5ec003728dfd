﻿using Common;
using Common.Services;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using static Common.Constants.MatchTemplatesFilesSettings;

namespace Services
{
    public class TemplateService : ITemplateService
    {
        private readonly ILogger<TemplateService> logger;

        private readonly IWebHostEnvironment hostingEnvironment;
        public TemplateService(ILogger<TemplateService> logger, IWebHostEnvironment hostingEnvironment)
        {
            this.logger = logger;
            this.hostingEnvironment = hostingEnvironment;
        }

        public string GetReportTemplate() => GetFileContent(ReportTempalteFileName);

        public string GetTableHeaderTemplate() => GetFileContent(TableHeaderFileName);

        public string GetTableRowTemplate() => GetFileContent(TableRowFileName);

        private string GetFileContent(string fileName)
        {
            try
            {
                var path = $"{hostingEnvironment.ContentRootPath}/{Constants.MatchTemplatesFilesSettings.FolderPath}/{fileName}";

                logger.LogInformation("Reading data from the templates for merchant match report. FilePath : '{filePath}'", path);

                using StreamReader reader = new(path);

                string body = reader.ReadToEnd();

                reader.Close();
                reader.Dispose();

                return body;
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error retrieving footer template for merchant match.");
            }

            return string.Empty;
        }
    }
}