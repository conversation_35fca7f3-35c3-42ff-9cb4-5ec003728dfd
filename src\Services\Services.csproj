﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<ProjectGuid>{4d369fc0-ffad-49e5-a3fb-3cd381ba9d33}</ProjectGuid>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Common.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.0" />
		<PackageReference Include="Geidea.Messaging" Version="2.2.103" />
		<PackageReference Include="Geidea.Utils.ApplicationLanguage" Version="2.0.4" />
		<PackageReference Include="Geidea.Utils.Cleanup" Version="1.0.2" />
		<PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
		<PackageReference Include="Geidea.Utils.Json" Version="1.1.249" />
		<PackageReference Include="Geidea.Utils.Messaging" Version="1.0.32" />
		<PackageReference Include="Geidea.Utils.ReferenceData" Version="1.0.113" />
		<PackageReference Include="Geidea.Utils.Security" Version="2.0.137" />
		<PackageReference Include="Geidea.Utils.Validation" Version="2.1.114" />
		<PackageReference Include="Geidea.Utils.WebUtilities" Version="1.0.7" />
		<PackageReference Include="GeideaPaymentGateway.Utils.RabbitMQ" Version="1.2.14" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.9" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
		<PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="6.0.12" />
		<PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
		<PackageReference Include="mixpanel-csharp" Version="6.0.0" />
		<PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.4" />
	</ItemGroup>

</Project>
