﻿using Common.Models.Checkout;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;
using BackofficeApi.Extensions;
using Common.Enums;
using Common.Models.Comment;
using Geidea.Utils.Policies.Evaluation;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1")]
    public class OrderController : ControllerBase
    {
        private readonly ICheckoutService checkoutService;
        private readonly IOrderExportService orderExportService;
        private readonly IEgyptOrderExportService egyptOrderExportService;
        private readonly Authorized authorized;
        private readonly IEPosMessagingService ePosMessagingService;

        public OrderController(
            ICheckoutService checkoutService,
            IOrderExportService orderExportService,
            IEgyptOrderExportService egyptOrderExportService,
            Authorized authorized,
            IEPosMessagingService ePosMessagingService
            )
        {
            this.checkoutService = checkoutService;
            this.authorized = authorized;
            this.ePosMessagingService = ePosMessagingService;
            this.orderExportService = orderExportService;
            this.egyptOrderExportService = egyptOrderExportService;
        }

        /// <summary>
        /// Gets a specific order from databse based on a given Id.
        /// </summary>
        /// <param name="orderId"> The id based on which we are looking for the order</param>
        /// <response code="200">Returns the order</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="401">If the request in unauthorized</response> 
        /// <response code="403">If you are not the merchant for that order</response> 
        /// <response code="404">If there is no order with that id</response> 

        [Produces("application/json", "text/csv")]
        [HttpGet("order/{orderId}/export")]
        [ProducesResponseType(typeof(OrderExportResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExportOrderById(Guid orderId)
        {
            if (!await authorized.To.View.Order(orderId))
            {
                return Forbid();
            }

            var order = await orderExportService.ExportOrderByIdAsync(orderId);
            return Ok(order);
        }

        [Produces("application/json", "text/csv")]
        [HttpGet("order/{orderId}/export/egypt")]
        [ProducesResponseType(typeof(OrderExportResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExportEgyptOrderById(Guid orderId)
        {
            if (!await authorized.To.View.Order(orderId))
            {
                return Forbid();
            }

            var order = await egyptOrderExportService.ExportOrderByIdAsync(orderId);
            return Ok(order);
        }

        /// <summary>
        /// Updates an existing order based on id.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /order
        ///     [
        ///         { "op": "replace", "path": "TrackingNumber", "value": "123"},
        ///     	{ "op": "replace", "path": "AgreementId", "value": 59e5a908-60ef-457f-91f1-adeddd4781e2}
        ///     ]
        ///
        /// </remarks>
        /// <response code="204">Returns no content if the order has been updated</response>
        /// <response code="400"></response> 
        /// <response code="401">If the request in unauthorized</response> 
        /// <response code="403">If if the user does not have the same merchantId as the one in the order</response> 
        /// <response code="404">If there is no order with that id</response> 

        [HttpPatch("order/{orderId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateOrderDetails(Guid orderId, [FromBody] JsonPatchDocument<OrderUpdateRequest> updateOrderRequest)
        {
            var properties = updateOrderRequest.Operations?.Select(op => op.path);
            var newOrderStatus = updateOrderRequest.Operations
                ?.FirstOrDefault(op => op?.path?.ToLowerInvariant() == nameof(OrderUpdateRequest.OrderStatus).ToLowerInvariant())
                ?.value?.ToString();

            if (!await authorized.To.Patch.Order(orderId, properties?.ToArray(), newOrderStatus))
            {
                return Forbid();
            }

            await checkoutService.UpdateOrderAsync(orderId, updateOrderRequest, this.GetUserId());
            return NoContent();
        }

        /// <summary>
        /// Bulk upload orders to product registered
        /// </summary>
        /// <response code="200">Returns a list with all errors that occur</response>
        /// <response code="400">Returns the error.</response> 
        /// <response code="401">If the request in unauthorized.</response> 
        /// <response code="403">If the user does not have the correct role.</response> 
        [HttpPost("order/bulk")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> BulkUpdateOrdersToProductRegistered(string[] orders)
        {
            if (!await authorized.To.Upload.OrderBulk())
            {
                return Forbid();
            }

            var result = await checkoutService.UpdateOrdersToProductRegisteredAsync(orders.ToList(), this.GetUserId());
            return Ok(result);
        }

        /// <summary>
        /// Soft delete for all orders based on an array of OrderIds.
        /// </summary>
        /// <response code="204">Returns no content if all the orders have been deleted.</response>
        /// <response code="400">Returns the error.</response> 
        /// <response code="401">If the request in unauthorized.</response> 
        /// <response code="403">If the user does not have the correct role.</response> 
        /// <response code="404">If the order with a certain orderId does not exists.</response> 
        [Produces("application/json")]
        [HttpDelete("order")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteOrders([FromBody] OrderDeleteRequest orderDeleteRequest)
        {
            if (orderDeleteRequest == null || orderDeleteRequest.OrderId == null || orderDeleteRequest.OrderId.Length == 0)
            {
                throw new ServiceException(System.Net.HttpStatusCode.BadRequest);
            }

            foreach (var o in orderDeleteRequest.OrderId)
            {
                if (!await authorized.To.Delete.Order(o))
                {
                    return Forbid();
                }
            }

            await checkoutService.DeleteOrderAsync(orderDeleteRequest);

            return NoContent();
        }

        /// <summary>
        /// Export orders based on search criteria
        /// </summary>
        /// <response code="200">Returns the orders export</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="401">If the request in unauthorized</response> 
        /// <response code="403">If if the user does not have the correct role</response> 

        [Produces("application/json", "text/csv")]
        [HttpPost("orders/export")]
        [ProducesResponseType(typeof(OrderExportResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ExportOrders([FromBody] OrderSearchCriteria orderSearchCriteria)
        {
            if (!await authorized.To.Export.Order(orderSearchCriteria.SalesId))
            {
                return Forbid();
            }

            var orders = await orderExportService.ExportOrdersAsync(orderSearchCriteria);
            return Ok(orders);
        }

        [Produces("application/json", "text/csv")]
        [HttpPost("orders/export/egypt")]
        [ProducesResponseType(typeof(EgyptOrderExportResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ExportEgyptOrders([FromBody] OrderSearchCriteria orderSearchCriteria)
        {
            if (!await authorized.To.Export.Order(orderSearchCriteria.SalesId))
            {
                return Forbid();
            }

            var orders = await egyptOrderExportService.ExportOrdersAsync(orderSearchCriteria);
            return Ok(orders);
        }

        /// <summary>
        /// Gets a specific order item from databse based on a given Id.
        /// </summary>
        /// <response code="200">Returns the order item</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="401">If the request in unauthorized</response> 
        /// <response code="403">If if the user does not have the correct role</response> 
        /// <response code="404">If there is no order item with that id</response> 

        [Produces("application/json")]
        [HttpGet("orderItem")]
        [ProducesResponseType(typeof(OrderItemResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetOrderItemById([FromQuery] Guid[] orderItemsId)
        {
            foreach (var o in orderItemsId)
            {
                if (!await authorized.To.View.Order(o))
                {
                    return Forbid();
                }
            }

            OrderItemResponse[] orderItems = await checkoutService.GetOrderItemsByIdAsync(orderItemsId);
            return Ok(orderItems);
        }

        /// <summary>
        /// Updates an existing order item based on id.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PATCH /orderitem
        ///     [
        ///         { "op": "replace", "path": "TrackingNumber", "value": "123"},
        ///     	{ "op": "replace", "path": "AgreementId", "value": 59e5a908-60ef-457f-91f1-adeddd4781e2}
        ///     ]
        ///
        /// </remarks>
        /// <response code="204">Returns no content if the order item has been updated</response>
        /// <response code="400"></response> 
        /// <response code="404">If there is no order item with that id</response> 

        [HttpPatch("orderItem/{orderItemId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateOrderItemDetails(Guid orderItemId, [FromBody] JsonPatchDocument<OrderItemUpdateRequest> updateOrderItemRequest)
        {
            var properties = updateOrderItemRequest.Operations?.Select(op => op.path);
            if (properties != null && properties.Any())
            {
                return Forbid();
            }

            var orderItems = await checkoutService.GetOrderItemsByIdAsync(new Guid[] { orderItemId });

            if (orderItems == null || orderItems.Length == 0)
            {
                return NotFound();
            }

            await checkoutService.UpdateOrderItemAsync(orderItemId, updateOrderItemRequest);
            return NoContent();
        }

        /// <summary>
        /// Creeate a new comment for a specific order.
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="commentCreateRequest"></param>
        /// <response code="201">Returns the newly created comment </response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no order with that id</response> 

        [Produces("application/json")]
        [HttpPost("order/{orderId}/comment")]
        [ProducesResponseType(typeof(OrderCommentResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CreateCommentAction(Guid orderId, [FromBody] CommentCreateRequest commentCreateRequest)
        {
            if (!await authorized.To.Create.OrderComment())
            {
                return Forbid();
            }

            var newComment = await checkoutService.CreateCommentAsync(orderId, commentCreateRequest);
            return CreatedAtAction(nameof(GetCommentById),
                new { commentId = newComment.CommentId },
                newComment);
        }

        /// <summary>
        /// Gets all comments for an order based on orderId.
        /// </summary>
        /// <param name="orderId"> Order id</param>
        /// <response code="200">Returns a list of comments.</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no order with that id.</response> 

        [Produces("application/json")]
        [HttpGet("order/{orderId}/comment")]
        [ProducesResponseType(typeof(OrderCommentResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCommentByOrderIdAction(Guid orderId)
        {
            if (!await authorized.To.View.Order(orderId))
            {
                return Forbid();
            }

            var comments = await checkoutService.GetCommentByOrderIdAsync(orderId);
            return Ok(comments);
        }

        /// <summary>
        /// Gets a specific comment based on comment id.
        /// </summary>
        /// <param name="commentId"> The id based on which we are looking for the comment</param>
        /// <response code="200">Returns the comment</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no comment with that id</response> 

        [Produces("application/json")]
        [HttpGet("orders/comment/{commentId}")]
        [ProducesResponseType(typeof(OrderCommentResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCommentById(Guid commentId)
        {
            if (!await authorized.To.View.OrderComment(commentId))
            {
                return Forbid();
            }

            var comment = await checkoutService.GetCommentByIdAsync(commentId);

            return Ok(comment);
        }

        /// <summary>
        /// Update comment based on comment id.
        /// </summary>
        /// <param name="commentId"> CommentId</param>
        /// <param name="commentUpdateRequest"></param>
        /// <response code="200">Returns the updated comment</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no comment with given id</response> 

        [Produces("application/json")]
        [HttpPut("orders/comment/{commentId}")]
        [ProducesResponseType(typeof(OrderCommentResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateCommentAction(Guid commentId, [FromBody] CommentUpdateRequest commentUpdateRequest)
        {
            if (!await authorized.To.Update.OrderComment(commentId))
            {
                return Forbid();
            }

            var updatedComment = await checkoutService.UpdateCommentAsync(commentId, commentUpdateRequest);
            return Ok(updatedComment);

        }

        /// <summary>
        /// Soft delete for comments based on comment id.
        /// </summary>
        /// <param name="commentId"> Comment id</param>
        /// <response code="204"></response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no comment with given id</response> 

        [Produces("application/json")]
        [HttpDelete("orders/comment/{commentId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteCommentAction(Guid commentId)
        {
            if (!await authorized.To.Delete.OrderComment(commentId))
            {
                return Forbid();
            }

            await checkoutService.DeleteCommentAsync(commentId);
            return NoContent();
        }

        /// <summary>
        /// Gets status history for an order based on orderId.
        /// </summary>
        /// <param name="orderId"> Order id</param>
        /// <response code="200">Returns a list of statuses.</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no order with that id.</response> 

        [Produces("application/json")]
        [HttpGet("order/{orderId}/status/history")]
        [ProducesResponseType(typeof(OrderCommentResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetOrderStatusHistory(Guid orderId)
        {
            if (!await authorized.To.View.OrderStatusHistory())
            {
                return Forbid();
            }

            var orderStatuses = await checkoutService.GetOrdersStatusHistoryAsync(orderId);
            return Ok(orderStatuses);
        }


        /// <summary>
        /// Creates an EPos ticket for the order
        /// </summary>
        /// <param name="orderId"></param>
        /// <response code="204"></response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no order with that id</response> 

        [Produces("application/json")]
        [HttpPost("order/{orderId}/eposticket/create")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CreateOrderEPosTicket(Guid orderId)
        {
            if (!await authorized.To.Create.EposTicket())
            {
                return Forbid();
            }

            await ePosMessagingService.CreateOrderEPosTicketAsync(orderId, true);
            return NoContent();
        }

        /// <summary>
        /// Sends ePos software type update messages for eligible terminals of the order.
        /// </summary>
        /// <param name="orderId"></param>
        /// <response code="204"></response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no order with that id</response> 

        [Produces("application/json")]
        [HttpPost("order/{orderId}/ePosSwTypeUpdate")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateEPosSwType(Guid orderId)
        {
            if (!await authorized.To.Create.EposTicket())
            {
                return Forbid();
            }

            await ePosMessagingService.SendEPosSwTypeUpdate(orderId);

            return NoContent();
        }

        /// <summary>
        /// Gets the available ePos action for an order based on its terminals state.
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpGet("order/{orderId}/ePosActions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetEPosAvailableAction(Guid orderId)
        {
            if (!await authorized.To.Create.EposTicket())
            {
                return Forbid();
            }

            var ePosAction = await ePosMessagingService.GetEPosActions(orderId);

            return Ok(new
            {
                url = ePosAction switch
                {
                    EPosAction.CreateEPosTicket => Url.Action(nameof(CreateOrderEPosTicket), new { orderId }),
                    EPosAction.UpdateEPosSwType => Url.Action(nameof(UpdateEPosSwType), new { orderId }),
                    EPosAction.NoAction => string.Empty,
                    _ => string.Empty
                }
            });
        }
    }
}