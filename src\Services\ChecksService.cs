﻿using Common;
using Common.Models;
using Common.Models.Checks;
using Common.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services
{
    public class ChecksService : IChecksService
    {
        private readonly IDocumentService documentService;
        private readonly ILogger<ChecksService> logger;

        public ChecksService(IDocumentService documentService, ILogger<ChecksService> logger)
        {
            this.documentService = documentService;
            this.logger = logger;
        }

        public async Task<List<FullInfoCheck>> GetMerchantFullInfoChecks(Guid[] merchantIds)
        {
            return await GetMerchantChecks<FullInfoCheck>(merchantIds, Constants.Checks.FullInfoCheck);
        }

        public async Task<List<IdentityCheck>> GetMerchantIdentityChecks(Guid[] merchantIds)
        {
            return await GetMerchantChecks<IdentityCheck>(merchantIds, Constants.Checks.IdentityCheck);
        }

        private async Task<List<T>> GetMerchantChecks<T>(Guid[] merchantIds, string documentType) where T : IMerchantCheck
        {
            var documents = await documentService.GetDocumentWithContentAsync(new DocumentSearchCriteria { MerchantIds = merchantIds, DocumentType = documentType });
            var checks = new ConcurrentBag<T>();

            Parallel.ForEach(documents, document =>
            {
                try
                {
                    var stringContent = System.Text.Encoding.UTF8.GetString(document.Content);
                    if (!stringContent.TrimStart().StartsWith("{"))
                        return;

                    var check = JsonSerializer.Deserialize<T>(stringContent,
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    check!.MerchantId = document.MerchantId.GetValueOrDefault();
                    checks.Add(check);
                }
                catch (Exception ex)
                {
                    logger.LogInformation(ex, "Couldn't deserialize check.");
                }
            });

            return checks.ToList();
        }
    }
}