﻿using System;

namespace Common.Models.Merchant
{
    public class MerchantAddress
    {
        public Guid MerchantId { get; set; }
        public Guid AddressId { get; set; }
        public string? Country { get; set; }
        public string? City { get; set; }
        public int? CityKey { get; set; }
        public string? Street { get; set; }
        public string? Zip { get; set; }
        public string? Email { get; set; }
        public string? Url { get; set; }
        public string? AddressType { get; set; }
        public bool IsDefaultAddress { get; set; }
        public string? Purpose { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }
    }
}
