﻿using Common.Models.User;
using CsvHelper.Configuration;
using System.Data;

namespace BackofficeApi.Formatters.ClassMappers
{
    public class UserExportResponseMap : ClassMap<UserExportModel>
    {
        public UserExportResponseMap()
        {
            Map(u => u.FirstName).Name("First Name");
            Map(u => u.LastName).Name("Last Name");
            Map(u => u.SalesId).Name("SalesID");
            Map(u => u.Email).Name("Email");
            Map(u => u.PhoneNumber).Name("Phone Number");
            Map(u => u.Designation).Name("Designation");
            Map(u => u.Designation).Name("Phone number");
            Map(u => u.Team).Name("Team");
            Map(u => u.AccessRole).Name("Access Name");
            Map(u => u.ReportingManager).Name("Reporting Manager");
            Map(u => u.Counterparty).Name("Counterparty");
            Map(u => u.Status).Name("Status");
        }
    }
}
