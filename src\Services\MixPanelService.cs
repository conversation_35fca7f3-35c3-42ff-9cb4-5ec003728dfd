﻿using Common.Models.MixPanel;
using Common.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Mixpanel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services
{
    public class MixPanelService : IMixPanelService
    {
        private readonly ILogger<MixPanelService> logger;
        private readonly IOptions<MixPanelConfiguration> mixPanelConfiguration;

        public MixPanelService(ILogger<MixPanelService> logger, IOptions<MixPanelConfiguration> mixPanelConfiguration)
        {
            this.logger = logger;
            this.mixPanelConfiguration = mixPanelConfiguration;
        }

        public async Task SendMerchantStatusEvent(MixPanelEventRequest eventRequest)
        {
            logger.LogInformation("Start Sending Event with merchantStatus changed to Mixpanel with a new status '{merchantStatus}' and '{memberId}'", eventRequest.MerchantStatus, eventRequest.MemberId);

            var mc = new MixpanelClient(mixPanelConfiguration.Value.ApplicationToken);
            var isSucceeded = await mc.TrackAsync(mixPanelConfiguration.Value.MerchantStatusEvent, eventRequest.MemberId, new
            {
                Time = DateTime.UtcNow,
                merchant_status = eventRequest.MerchantStatus

            });
            if (isSucceeded)
            {
                logger.LogInformation("Sending  merchantStatus Event to Mixpanel Successfully   with a new status '{merchantStatus}' and '{memberId}'", eventRequest.MerchantStatus, eventRequest.MemberId);
            }
            else
            {
                logger.LogError("Error at Sending  merchantStatus Event to Mixpanel with a new status '{merchantStatus}' and '{memberId}'", eventRequest.MerchantStatus, eventRequest.MemberId);

            }
        }
    }
}
