﻿using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public abstract class PrincipalBase
{
    [Display(Order = 5, Name = "Phone Number")]
    public string PhoneNumber { get; set; } = string.Empty;

    [Display(Order = 6, Name = "Alternative Phone Number")]
    public string AltPhoneNumber { get; set; } = string.Empty;

    [Display(Order = 7, Name = "National Id")]
    public string NationalId { get; set; } = string.Empty;
}