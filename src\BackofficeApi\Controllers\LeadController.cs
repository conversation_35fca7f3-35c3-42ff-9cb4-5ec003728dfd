﻿using Common.Models.Lead;
using Common.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;
using BackofficeApi.Extensions;
using Geidea.Utils.Policies.Evaluation;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    public class LeadController : ControllerBase
    {
        private readonly ILeadService leadService;
        private readonly Authorized authorized;
        private readonly IHttpContextAccessor context;

        public LeadController(ILeadService leadService, Authorized authorized, IHttpContextAccessor context)
        {
            this.leadService = leadService;
            this.authorized = authorized;
            this.context = context;
        }

        /// <summary>
        /// Returns a list of leads.
        /// </summary>
        /// <param name="searchParameters">The search parameters used to filter the list of leads.</param>
        /// <response code="200">Returns the lead list.</response>        
        /// <response code="400"></response> 
        /// <response code="401">If the request in unauthorized</response> 
        /// <response code="403">If there user is not a backoffice admin</response> 

        [HttpPost("advancedSearch")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SearchLeads([FromBody] LeadSearchParameters searchParameters)
        {
            if (!await authorized.To.List.Lead(searchParameters.SalesPartnerId, searchParameters.SalesId))
            {
                return Forbid();
            }

            var response = await leadService.SearchLeadsAsync(searchParameters);
            return Ok(response);
        }

        /// <summary>
        /// Updates an existing lead based on id.
        /// </summary>
        /// <param name="leadId">The id of the lead to be updated.</param>
        /// <param name="leadPatch">The json document patch.</param>
        /// <response code="204">Returns no content if the lead has been updated</response>
        /// <response code="400"></response> 
        /// <response code="401">If the request in unauthorized</response> 
        /// <response code="403">If there user is not a backoffice admin</response> 
        /// <response code="404">If there is no lead with that id</response> 

        [HttpPatch("{leadId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateLead(Guid leadId, JsonPatchDocument<Lead> leadPatch)
        {
            var properties = leadPatch.Operations?.Select(op => op.path);
            if (!await authorized.To.Patch.Lead(leadId, properties?.ToArray()))
            {
                return Forbid();
            }

            await leadService.UpdateLeadAsync(leadId, leadPatch);
            return NoContent();
        }

        /// <summary>
        /// Export leads based on search criteria
        /// </summary>
        /// <response code="200">Returns the leads export</response>
        /// <response code="400">Returns the error</response> 
        /// <response code="401">If the request is unauthorized</response> 
        /// <response code="403">If if the user does not have the correct role</response> 

        [Produces("application/json", "text/csv")]
        [HttpPost("export")]
        [ProducesResponseType(typeof(LeadExportResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ExportLeads([FromBody] LeadExportParameters exportParameters)
        {
            if (!await authorized.To.Export.Lead(exportParameters.SalesPartnerId, exportParameters.SalesId))
            {
                return Forbid();
            }

            var result = await leadService.ExportLeadsAsync(exportParameters);
            return Ok(result);
        }

        /// <summary>
        /// Create new lead.
        /// </summary>
        /// <response code="201">Returns the new lead.</response>
        /// <response code="400">Returns the error.</response> 
        /// <response code="401">If the request in unauthorized.</response> 
        /// <response code="403">If if the user does not have the correct role.</response> 

        [HttpPost]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreateLead([FromForm] LeadCreateRequest leadCreateRequest)
        {
            if (!await authorized.To.Create.Lead(leadCreateRequest.SalesPartnerId))
            {
                return Forbid();
            }

            return Ok(await leadService.CreateLeadAsync(leadCreateRequest, this.GetUserId()));
        }
    }
}