﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Geidea.Utils.HttpClientExtensions;
using LeadUpdate.Models;
using LeadUpdate.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;

namespace LeadUpdate
{
    public static class Program
    {
        public static async Task Main(string[] args)
        {
            var services = new ServiceCollection();
            ConfigureServices(services, args);
            await using ServiceProvider serviceProvider = services.BuildServiceProvider();
            var updateService = serviceProvider.GetService<LeadUpdateService>();

            if (updateService != null)
            {
                await updateService.UpdateLeadsAsync(int.Parse(args[3]), int.Parse(args[4]), Enum.Parse<LeadUpdateType>(args[5]), args[7]);
            }
        }

        private static void ConfigureServices(ServiceCollection services, string[] args)
        {
            var serilogLogger = new LoggerConfiguration()
                                .WriteTo.File("LeadUpdate.log")
                                .CreateLogger();

            services.AddLogging(builder =>
            {
                builder.SetMinimumLevel(LogLevel.Information);
                builder.AddSerilog(logger: serilogLogger, dispose: true);
            });

            var configuration = new ConfigurationBuilder().AddInMemoryCollection(new[] { new KeyValuePair<string, string>("Polly:TimeoutInSeconds", args[6]) }).Build();

            services.AddTransient<LeadUpdateService>();
            services.AddPollyHttpClient<ILeadService, LeadService>(configuration);
            services.AddPollyHttpClient<IDocumentService, DocumentService>(configuration);

            services.Configure<UrlSettings>(opt =>
            {
                opt.SearchServiceBaseUrl = args[0];
                opt.DocumentServiceBaseUrl = args[1];
                opt.LeadServiceBaseUrl = args[2];
            });
        }
    }
}
