﻿using FluentValidation.TestHelper;

namespace Common.Tests;

public class ShareholderCompanyIndividualLinkValidatorTests
{
    [Test]
    public void Validate_InvalidShareholderCompanyId_ShouldFail()
    {
        var result = new ShareholderCompanyIndividualLinkValidator().TestValidate(new ShareholderCompanyIndividualLink
        {
            ShareHolderCompanyId = Guid.Empty
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderCompanyIndividualLink.ShareHolderCompanyId))
            .WithErrorMessage(Errors.InvalidShareholderCompanyIndividualLinkCompanyId.Message)
            .WithErrorCode(Errors.InvalidShareholderCompanyIndividualLinkCompanyId.Code);
    }

    [Test]
    public void Validate_NullRole_ShouldFail()
    {
        var result = new ShareholderCompanyIndividualLinkValidator().TestValidate(new ShareholderCompanyIndividualLink());

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderCompanyIndividualLink.OrganizationRole))
            .WithErrorMessage(Errors.InvalidShareholderCompanyIndividualLinkRole.Message)
            .WithErrorCode(Errors.InvalidShareholderCompanyIndividualLinkRole.Code);
    }

    [TestCase("")]
    [TestCase("  ")]
    public void Validate_InvalidCompanyOrganizationRole_ShouldFail(string role)
    {
        var result = new ShareholderCompanyIndividualLinkValidator().TestValidate(new ShareholderCompanyIndividualLink
        {
            OrganizationRole = role
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderCompanyIndividualLink.OrganizationRole))
            .WithErrorMessage(Errors.InvalidShareholderCompanyIndividualLinkRole.Message)
            .WithErrorCode(Errors.InvalidShareholderCompanyIndividualLinkRole.Code);
    }

    [Test]
    public void Validate_ValidEntry_IsValid()
    {
        var result = new ShareholderCompanyIndividualLinkValidator().TestValidate(new ShareholderCompanyIndividualLink
        {
            OrganizationRole = "role",
            ShareHolderCompanyId = Guid.NewGuid()
        });

        Assert.That(result.IsValid, Is.True);
    }
}
