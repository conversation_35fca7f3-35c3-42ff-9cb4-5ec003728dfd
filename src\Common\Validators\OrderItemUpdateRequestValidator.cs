﻿using Common.Models.Checkout;
using FluentValidation;

namespace Common.Validators
{
    public class OrderItemUpdateRequestValidator : AbstractValidator<OrderItemUpdateRequest>
    {
        public OrderItemUpdateRequestValidator()
        {
            RuleFor(x => x.ProductType)
                .Must(x => x!.Length <= 64)
                .When(x => x.ProductType != null)
                .WithErrorCode(Errors.ProductTypeLengthValidation.Code)
                .WithMessage(Errors.ProductTypeLengthValidation.Message);

            RuleFor(x => x.EposTicketId)
                .Must(x => x!.Length <= 16)
                .When(x => x.EposTicketId != null)
                .WithErrorCode(Errors.EposTicketIdLengthValidation.Code)
                .WithMessage(Errors.EposTicketIdLengthValidation.Message);
        }
    }
}
