﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models
{

    public class CompanyDetails
    {
        public string? BusinessID { get; set; }
        public string? CompanyLegalType { get; set; }
        public OfficeAddress? OfficeAddress { get; set; }
        public string? MCC { get; set; }
        public decimal? HighestSingleTransaction { get; set; }
        public decimal? MaxMonthlyTransaction { get; set; }
        public List<TransactionDetails>? TransactionDetails { get; set; }
        public string? OfficeLocationType { get; set; }
        public List<Shareholders>? Shareholders { get; set; }
        public string? MerchantStatus { get; set; }
        public string? Counterparty { get; set; }
    }

    public class OfficeAddress
    {
        public string? Governorate { get; set; }
        public string? City { get; set; }
        public string? Area { get; set; }
    }

    public class Shareholders
    {
        public Guid? PersonId { get; set; }
        public string? OrganizationRole { get; set; }
        public decimal? OwnershipPercentage { get; set; }
        public string? Nationality { get; set; }
        public bool? Pep { get; set; }
    }

    public class TransactionDetails
    {
        public string? AccountId { get; set; }
        public List<string>? TransactionTypes { get; set; }
        public string? TransactionMode { get; set; }
    }


}
