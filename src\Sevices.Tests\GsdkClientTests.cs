﻿using Common;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using NUnit.Framework;
using Services;
using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class GsdkClientTests
    {
        private readonly IOptionsMonitor<GsdkSettings> gsdkSettings = Substitute.For<IOptionsMonitor<GsdkSettings>>();
        private readonly ILogger<GsdkClient> logger = Substitute.For<ILogger<GsdkClient>>();
        private readonly ICounterpartyProvider counterpartyProvider = Substitute.For<ICounterpartyProvider>();
        private readonly IKeycloakClient keycloakClient = Substitute.For<IKeycloakClient>();
        private GsdkClient gsdkClient = null!;

        private static string token = "xq23dddd456789b3cp4wy5693c85ty7w398vc643cv63w";
        private static string userId = Guid.NewGuid().ToString();
        private static string organizationId = Guid.NewGuid().ToString();

        [SetUp]
        public void Setup()
        {
            keycloakClient.GetKeycloakToken().Returns(Task.FromResult(token));

            counterpartyProvider.ClearReceivedCalls();
            keycloakClient.ClearReceivedCalls();
        }

        public GsdkClientTests()
        {
            gsdkSettings.CurrentValue.Returns(TestsHelper.GsdkSettings);
        }

        public void CreateGsdkClientInstance(HttpClient client)
        {
            gsdkClient = new GsdkClient(logger, client, keycloakClient, gsdkSettings, counterpartyProvider);
        }

        [TestCase(Constants.CounterParty.Egypt,Constants.MmsLedgers.EgyptDefault)]
        [TestCase(Constants.CounterParty.Saudi, Constants.MmsLedgers.SaudiDefault)]
        [TestCase(Constants.CounterParty.Saudi, Constants.MmsLedgers.SaudiSABB)]
        public async Task FindGLobalContract_HttpOk_ShouldReturnContracts(string counterParty, string ledger)
        {
            counterpartyProvider.GetCode().Returns(counterParty);

            CreateGsdkClientInstance(TestsHelper.CreateHttpClient(System.Net.HttpStatusCode.OK,
                JsonSerializer.Serialize(TestsHelper.GetGlobalContracts(Constants.GsdkContractPaymentWay.Terminal,Constants.OutProviderAccounts.EgyptMisrBank))));

            var result = await gsdkClient.FindGlobalContracts(new Common.Models.Gsdk.GlobalContractsFilter() { }, ledger);

            Assert.NotNull(result);
            Assert.That(result.Count, Is.GreaterThan(0));
        }

        [TestCase(Constants.CounterParty.Egypt, Constants.MmsLedgers.EgyptDefault)]
        public void FindGLobalContract_HttpBadRequest_ShouldThrowPassthroughException(string counterParty, string ledger)
        {
            counterpartyProvider.GetCode().Returns(counterParty);

            CreateGsdkClientInstance(TestsHelper.CreateHttpClient(System.Net.HttpStatusCode.BadRequest,
                JsonSerializer.Serialize(TestsHelper.GetGlobalContracts(Constants.GsdkContractPaymentWay.Terminal, Constants.OutProviderAccounts.EgyptMisrBank))));

            gsdkClient.Invoking(x => x.FindGlobalContracts(new Common.Models.Gsdk.GlobalContractsFilter() { }, ledger))
                .Should().ThrowAsync<PassthroughException>();
        }
    }
}
