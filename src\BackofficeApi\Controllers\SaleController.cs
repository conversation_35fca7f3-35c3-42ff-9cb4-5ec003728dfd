﻿using Common.Models.User;
using Common.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Geidea.Utils.Policies.Evaluation;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [Route("api/v1")]
    [ApiController]
    public class SaleController : ControllerBase
    {
        private readonly ISaleService saleService;
        private readonly Authorized authorized;

        public SaleController(ISaleService saleService, Authorized authorized)
        {
            this.saleService = saleService;
            this.authorized = authorized;
        }

        /// <summary>
        /// Update salesId for only one lead based on leadId.
        /// </summary>
        /// <param name="leadSalesIdRequest"></param>
        /// <response code="204">If the sales link is updated</response>
        /// <response code="403">If ther user is allowed to perform the action</response> 
        /// <response code="404">If ther user is not found</response> 
        /// <response code="500">If there user salesId is invalid</response> 
        [HttpPut("lead/salesId")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateLeadSalesId([FromBody] LeadSalesIdRequest leadSalesIdRequest)
        {
            if (!await authorized.To.Update.Sale())
                return Forbid();

            await saleService.UpdateLeadSalesIdAsync(leadSalesIdRequest);
            return NoContent();
        }


        /// <summary>
        /// Update salesId for user and for all leads created with users old salesId
        /// </summary>
        /// <param name="userSalesIdRequest"></param>
        /// <response code="204">If the sales id is updated</response>
        /// <response code="403">If ther user is allowed to perform the action</response> 
        /// <response code="404">If ther user is not found</response> 
        /// <response code="500">If there user salesId is invalid</response> 
        [HttpPut("user/salesId")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateUserSalesId([FromBody] UserSalesIdRequest userSalesIdRequest)
        {
            if (!await authorized.To.Update.Sale())
                return Forbid();

            await saleService.UpdateUserSalesIdAsync(userSalesIdRequest);
            return NoContent();
        }
    }
}
