﻿using System;
using System.Collections.Generic;
using Common.Models.Shareholder;
using FluentValidation;

namespace Common.Validators;

public class ShareholderCompanyCreateRequestValidator : AbstractValidator<ShareholderCompanyCreateRequest>
{
    private readonly List<string> acceptedKsaBusinessTypeList = new()
    {
        Constants.BusinessType.LegalEnterprise,
        Constants.BusinessType.Limited,
        Constants.BusinessType.MunicipalEntity,
        Constants.BusinessType.SoleTrader,
        Constants.BusinessType.Other
    };

    private readonly List<string> acceptedEgBusinessTypeList = new()
    {
        Constants.BusinessType.Limited,
        Constants.BusinessType.Other
    };

    private readonly List<string> acceptedUaeBusinessTypeList = new()
    {
        Constants.BusinessType.Limited,
        Constants.BusinessType.LLC,
        Constants.BusinessType.SoleEstablishment,
        Constants.BusinessType.PSC,
        Constants.BusinessType.JSC,
        Constants.BusinessType.Partnership,
        Constants.BusinessType.Branch,
    };

    public ShareholderCompanyCreateRequestValidator()
    {
        RuleFor(x => x.CompanyName)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x.Length <= 150)
            .WithErrorCode(Errors.ShareholderCompanyNameLength.Code)
            .WithMessage(Errors.ShareholderCompanyNameLength.Message);

        RuleFor(x => x.CompanyLicense)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x.Length <= 36)
            .WithErrorCode(Errors.InvalidShareholderCompanyLicenseLength.Code)
            .WithMessage(Errors.InvalidShareholderCompanyLicenseLength.Message);

        RuleFor(x => x.CompanyType)
            .Must(x => !string.IsNullOrWhiteSpace(x))
            .WithErrorCode(Errors.InvalidShareholderCompanyTypeLength.Code)
            .WithMessage(Errors.InvalidShareholderCompanyTypeLength.Message);

        RuleFor(x => x.LicenseExpiryDate)
            .Must(x => !x.Equals(DateTime.MinValue) && !x.Equals(DateTime.MaxValue))
            .When(x => x.LicenseExpiryDate != null)
            .WithErrorCode(Errors.InvalidShareholderCompanyLicenseExpiryDate.Code)
            .WithMessage(Errors.InvalidShareholderCompanyLicenseExpiryDate.Message);

        RuleFor(x => x.IssueDate)
            .Must(x => !x.Equals(DateTime.MinValue) && !x.Equals(DateTime.MaxValue))
            .When(x => x.IssueDate != null)
            .WithErrorCode(Errors.InvalidShareholderIssueDate.Code)
            .WithMessage(Errors.InvalidShareholderIssueDate.Message);

        RuleFor(x => x.MccCode)
            .Must(x => x!.Length <= 10)
            .When(x => !string.IsNullOrWhiteSpace(x.MccCode))
            .WithErrorCode(Errors.ShareholderCompanyMccLengthValidation.Code)
            .WithMessage(Errors.ShareholderCompanyMccLengthValidation.Message);

        RuleFor(x => x.OwnershipPercentage)
            .Must(x => x >= 0 && x <= 100)
            .When(x => x.OwnershipPercentage != null)
            .WithErrorCode(Errors.InvalidShareholderCompanyOwnershipPercentage.Code)
            .WithMessage(Errors.InvalidShareholderCompanyOwnershipPercentage.Message);

        RuleFor(x => x.MerchantId)
            .Must(x => x != Guid.Empty)
            .WithErrorCode(Errors.InvalidShareholderCompanyMerchantId.Code)
            .WithMessage(Errors.InvalidShareholderCompanyMerchantId.Message);

        RuleFor(x => x.PhoneNumber)
            .Must(x => !string.IsNullOrWhiteSpace(x))
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber))
            .WithErrorCode(Errors.PhoneLengthValidation.Code)
            .WithMessage(Errors.PhoneLengthValidation.Message)
            .Matches(@"^[0-9]{6,10}$")
            .WithErrorCode(Errors.PhoneValidation.Code)
            .WithMessage(Errors.PhoneValidation.Message);

        RuleFor(x => x.PhonePrefix)
            .Must(x => x != null)
            .When(x => !string.IsNullOrWhiteSpace(x.PhoneNumber))
            .WithErrorCode(Errors.CountryPrefixLengthValidation.Code)
            .WithMessage(Errors.CountryPrefixLengthValidation.Message)
            .Matches(@"(?:^)\+[0-9]{1,3}(?:$)")
            .WithErrorCode(Errors.InvalidCountryPrefix.Code)
            .WithMessage(Errors.InvalidCountryPrefix.Message);

        RuleFor(x => x.Country)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x.Length >= 2 && x.Length <= 3)
            .When(x => x.Country != null)
            .WithErrorCode(Errors.CountryLengthValidation.Code)
            .WithMessage(Errors.CountryLengthValidation.Message);

        RuleFor(x => x.Address)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x.Length <= 50)
            .When(x => !string.IsNullOrWhiteSpace(x.Address))
            .WithErrorCode(Errors.AddressLineLengthValidation.Code)
            .WithMessage(Errors.AddressLineLengthValidation.Message);

        When(x => x.Counterparty == Constants.CounterParty.Saudi, () =>
        {
            RuleFor(x => x.CompanyType)
                .Must(x => acceptedKsaBusinessTypeList.Contains(x))
                .WithErrorCode(Errors.InvalidShareholderCompanyType.Code)
                .WithMessage(Errors.InvalidShareholderCompanyType.Message);

            RuleFor(x => x.Governorate)
                .Must(x => string.IsNullOrWhiteSpace(x))
                .WithErrorCode(Errors.DisabledProperty.Code)
                .WithMessage(Errors.DisabledProperty.Message);

            RuleFor(x => x.Area)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Area != null)
                .WithErrorCode(Errors.AreaLengthValidation.Code)
                .WithMessage(Errors.AreaLengthValidation.Message);

            RuleFor(x => x.City)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.City != null)
                .WithErrorCode(Errors.CityLengthValidation.Code)
                .WithMessage(Errors.CityLengthValidation.Message);
        });

        When(x => x.Counterparty == Constants.CounterParty.Egypt, () =>
        {
            RuleFor(x => x.CompanyType)
                .Must(x => acceptedEgBusinessTypeList.Contains(x))
                .WithErrorCode(Errors.InvalidShareholderCompanyType.Code)
                .WithMessage(Errors.InvalidShareholderCompanyType.Message);

            RuleFor(x => x.Area)
                .Must(x => string.IsNullOrWhiteSpace(x))
                .WithErrorCode(Errors.DisabledProperty.Code)
                .WithMessage(Errors.DisabledProperty.Message);

            RuleFor(x => x.Governorate)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Governorate != null)
                .WithErrorCode(Errors.GovernorateLengthValidation.Code)
                .WithMessage(Errors.GovernorateLengthValidation.Message);

            RuleFor(x => x.City)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.City != null)
                .WithErrorCode(Errors.CityLengthValidation.Code)
                .WithMessage(Errors.CityLengthValidation.Message);
        });

        When(x => x.Counterparty == Constants.CounterParty.Uae, () =>
        {
            RuleFor(x => x.CompanyType)
                .Must(x => acceptedUaeBusinessTypeList.Contains(x))
                .WithErrorCode(Errors.InvalidShareholderCompanyType.Code)
                .WithMessage(Errors.InvalidShareholderCompanyType.Message);

            RuleFor(x => x.Area)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Area != null)
                .WithErrorCode(Errors.AreaLengthValidation.Code)
                .WithMessage(Errors.AreaLengthValidation.Message);

            RuleFor(x => x.Governorate)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Governorate != null)
                .WithErrorCode(Errors.GovernorateLengthValidation.Code)
                .WithMessage(Errors.GovernorateLengthValidation.Message);

            RuleFor(x => x.City)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.City != null)
                .WithErrorCode(Errors.CityLengthValidation.Code)
                .WithMessage(Errors.CityLengthValidation.Message);
        });

        When(x => x.Counterparty != Constants.CounterParty.Saudi 
                  && x.Counterparty != Constants.CounterParty.Egypt && x.Counterparty != Constants.CounterParty.Uae,
            () =>
            {
                RuleFor(x => x.CompanyType)
                    .Must(x => acceptedKsaBusinessTypeList.Contains(x))
                    .WithErrorCode(Errors.InvalidShareholderCompanyType.Code)
                    .WithMessage(Errors.InvalidShareholderCompanyType.Message);

                RuleFor(x => x.Governorate)
                    .Must(x => string.IsNullOrWhiteSpace(x))
                    .WithErrorCode(Errors.DisabledProperty.Code)
                    .WithMessage(Errors.DisabledProperty.Message);

                RuleFor(x => x.Area)
                    .Must(x => string.IsNullOrWhiteSpace(x))
                    .WithErrorCode(Errors.DisabledProperty.Code)
                    .WithMessage(Errors.DisabledProperty.Message);

                RuleFor(x => x.City)
                    .Must(x => string.IsNullOrWhiteSpace(x))
                    .WithErrorCode(Errors.DisabledProperty.Code)
                    .WithMessage(Errors.DisabledProperty.Message);
            });
    }
}