﻿using System;
using System.Diagnostics.CodeAnalysis;


namespace Common.Models.Messaging.ApexProvider
{
    [ExcludeFromCodeCoverage]
    public class ApexResponseEventArgs : BaseEventArgs
    {
        public ApexResponse? apexResponse { get; }
        public ApexResponseEventArgs(ApexResponse apexResponse, string? counterparty, Guid? correlationId) : base(counterparty, correlationId)
        {
            this.apexResponse = apexResponse ?? throw new ArgumentNullException(nameof(apexResponse), "Apex response cannot be null.");
        }
    }
}