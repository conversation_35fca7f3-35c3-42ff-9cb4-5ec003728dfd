﻿using Common.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;
using Geidea.Utils.Policies.Evaluation;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1")]
    public class DocumentController : ControllerBase
    {
        private const string FreelanceCheck = "FREELANCE_CHECK";
        private const string IdCheck = "ID_CHECK";
        private const string MatchCheck = "MATCH_CHECK";

        private static readonly string[] CompanyChecks =
        {
            "BASIC_INFO_CHECK", "REGISTRATION_CHECK", "OWNERS_CHECK", "MANAGERS_CHECK", "DOCUMENTS_CHECK", "ADDRESS_CHECK", "BANK_CHECK", "FINSCAN_CHECK", "SAS_CHECK", "FULL_INFO_CHECK", "IBAN_CHECK","WORLD_CHECK_ONE"
        };

        private readonly Authorized authorized;
        private readonly IDocumentService documentService;
        private readonly IPayloadTransformService payloadTransformService;
        private readonly IMatchReportBuilderService matchReportBuilderService;
        private readonly IDueDiligenceService dueDiligenceService;

        public DocumentController(
            IDocumentService documentService,
            IPayloadTransformService payloadTransformService,
            Authorized authorized,
            IMatchReportBuilderService matchReportBuilderService,
            IDueDiligenceService dueDiligenceService)
        {
            this.documentService = documentService;
            this.payloadTransformService = payloadTransformService;
            this.authorized = authorized;
            this.matchReportBuilderService = matchReportBuilderService;
            this.dueDiligenceService = dueDiligenceService;
        }

        /// <summary>
        /// Returns the check attributes
        /// </summary>
        /// <param name="documentId">The id of the document</param>
        /// <param name="checkType">The check type of the document</param>
        [HttpGet("document/{documentId}/CheckAttributes/{checkType}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCheckAttributes(Guid documentId, string checkType)
        {
            if (!await authorized.To.View.Document(documentId))
            {
                return Forbid();
            }

            if (!checkType.Equals(IdCheck, StringComparison.InvariantCultureIgnoreCase) &&
                !checkType.Equals(FreelanceCheck, StringComparison.InvariantCultureIgnoreCase) &&
                !checkType.Equals(MatchCheck, StringComparison.InvariantCultureIgnoreCase) &&
                !CompanyChecks.Any(s => s.Equals(checkType, StringComparison.InvariantCultureIgnoreCase)))
                return BadRequest($"Document check type '{checkType}' is not supported");

            var doc = await documentService.DownloadDocumentAsync(documentId);
            var file = await doc.ReadAsStringAsync();

            return Ok(payloadTransformService.GetJsonObjects(file));
        }

        /// <summary>
        /// Returns a document as a stream.
        /// </summary>
        /// <param name="documentId">The id of the document</param>
        /// <response code="200">The document's data as a byte stream</response>
        /// <response code="403">User does not have access to that document</response>
        /// <response code="404">No document was found with the specified id</response>
        [HttpGet]
        [Route("document/{documentId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetDocument(Guid documentId)
        {
            if (!await authorized.To.View.Document(documentId))
            {
                return Forbid();
            }

            var doc = await documentService.DownloadDocumentAsync(documentId);

            return File(
                await doc.ReadAsStreamAsync(),
                doc.Headers.ContentType?.ToString() ?? "application/octet-stream",
                doc.Headers.ContentDisposition?.FileName?.Trim(new[] { ' ', '\"' }));
        }

        /// <summary>
        /// Returns a zip file containing all the documents of the merchant, except the contracts.
        /// </summary>
        /// <param name="merchantId">The id of the merchant.</param>
        /// <response code="200">The document's data as a byte stream.</response>
        /// <response code="403">User does not have access to this resource.</response>
        /// <response code="404">No documents were found for the specified merchant id.</response>
        [HttpGet]
        [Route("documents/{merchantId:guid}")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetZippedDocuments(Guid merchantId)
        {
            if (!await authorized.To.View.Document(Guid.Empty) || await authorized.To.View.Contract(Guid.Empty))
            {
                return Forbid();
            }

            var doc = await documentService.DownloadZippedDocuments(merchantId);

            return File(
                doc.Content,
                "application/zip",
                doc.FileName?.Trim(new[] { ' ', '\"' }));
        }


        [HttpGet]
        [Route("document/{documentId:guid}/match")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetMerchantMatch(Guid documentId)
        {
            if (!await authorized.To.View.Document(documentId))
            {
                return Forbid();
            }

            var deserializedResponse = await dueDiligenceService.RetrieveMerchantMatchReport(documentId);

            if (deserializedResponse == null) return BadRequest();

            var document = await matchReportBuilderService.GenerateMatchReportAsync(deserializedResponse);

            return Ok(document);
        }
    }
}