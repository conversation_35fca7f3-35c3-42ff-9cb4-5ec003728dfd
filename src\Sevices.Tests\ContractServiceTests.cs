﻿using System;
using System.Linq;
using System.Net;
using Common.Options;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using Services;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class ContractServiceTests
    {
        private readonly Mock<ILogger<ContractService>> logger = new Mock<ILogger<ContractService>>();
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
        private ContractService contractService = null!;

        public ContractServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        }

        [Test]
        public void UpdateMerchantContractAsDeletedAsync_WhenNotFound_ShouldTrowException()
        {
            contractService = new ContractService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound)
              );

            contractService.Invoking(x => x.UpdateMerchantContractAsDeletedAsync(Guid.NewGuid()))
                .Should().NotThrowAsync<PassthroughException>();
        }


        [Test]
        public void UpdateMerchantContractAsDeletedAsync_ShouldNotThrowException()
        {
            contractService = new ContractService(
                                logger.Object,
                                urlSettingsOptions.Object,
                                 TestsHelper.CreateHttpClient(HttpStatusCode.OK)
                              );
            contractService.Invoking(x => x.UpdateMerchantContractAsDeletedAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

    }
}
