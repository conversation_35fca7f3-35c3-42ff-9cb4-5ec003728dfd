﻿using Geidea.PaymentGateway.ConfigServiceClient;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

namespace OrderExportOptimization
{
    public static class Program
    {
        public static async Task Main(string[] args)
        {
            var host = CreateHostBuilder(args).Build();

            await RunMerchantService(host.Services);
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("IsRunInEnvironments")))
                    {
                        webBuilder.UseConfigService();
                    }
                });
        private static async Task RunMerchantService(IServiceProvider serviceProvider)
        {
            var orderExportOptimization = serviceProvider.GetService<IOrderExportOptimizationService>();

            if (orderExportOptimization != null)
            {
                await orderExportOptimization.UpdateMerchantsDetails();
            }
        }
    }
}
