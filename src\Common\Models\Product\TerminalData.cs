﻿namespace Common.Models.Product
{
    using System;

    public class TerminalData
    {
        public string? ShortName_EN { get; set; }
        public string? ShortName_AR { get; set; }
        public DateTime? InstallDate { get; set; }
        public DateTime? ActiveDate { get; set; }
        public DateTime? TerminationDate { get; set; }
        public string? TerminalPassword { get; set; }
        public string? ProductStatus { get; set; }
        public string? TerminalSerialNumber { get; set; }
        public string? ActivationType { get; set; }
        public DateTime EODCutoff { get; set; }
        public string? Mcc { get; set; }
        public string? MIDMerchantReference { get; set; }
        public string? GSDKKey { get; set; }
        public decimal? DailyVolumeCap { get; set; }
        public string? ActivationCode { get; set; }
        public string? GPS_Latitude { get; set; }
        public string? GPS_Longitude { get; set; }
        public string? TradingCurrency { get; set; }
        public string? POSDataCode { get; set; }
        public string TId { get; set; } = null!;
        public string? FullTId { get; set; }
        public string? ProviderBank { get; set; }
        public string? AccountNo { get; set; }
        public string? ExternalBankTId { get; set; }
        public string? CountryPrefix { get; set; }
        public string? PhoneNumber { get; set; }
        public string? ChannelType { get; set; }
        public string? ConnectionType { get; set; }
        public string? MPGSMID { get; set; }
        public string? MPGSKEY { get; set; }
    }
}
