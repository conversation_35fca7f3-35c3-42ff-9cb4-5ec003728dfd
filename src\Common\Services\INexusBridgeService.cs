﻿using Common.Models.Checkout;
using Common.Models.Merchant;
using Common.Models.NexusBridge;
using Common.Models.Shareholder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface INexusBridgeService
    {
        Task CreateMerchant(Merchant merchant,List<MerchantShareholderIndividual> contact, Merchant store, OrderResponse order, bool orderFlag);
    }
}
