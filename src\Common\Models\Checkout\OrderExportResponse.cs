﻿using System;
using System.Collections.Generic;

namespace Common.Models.Checkout
{
    public class OrderExportResponse
    {
        public Guid OrderId { get; set; }
        public Guid UserId { get; set; }
        public Guid MerchantId { get; set; }
        public Guid StoreId { get; set; }
        public string? OrderNumber { get; set; }
        public string PaidUpfront { get; set; } = string.Empty;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? NationalName { get; set; }
        public string? FirstNameAr { get; set; }
        public string? LastNameAr { get; set; }
        public string? NationalId { get; set; }
        public string? CommercialRegistration { get; set; }
        public DateTime? LeadCreationDate { get; set; }
        public string? City { get; set; }
        public string? CityAR { get; set; }
        public string? Area { get; set; }
        public string? AreaAR { get; set; }
        public string? MerchantName { get; set; }
        public string? CityCR { get; set; }
        public string? AddressCR { get; set; }
        public string? IBAN { get; set; }
        public string? AccountHolderName { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? MCC { get; set; }
        public DateTime? CheckoutDate { get; set; }
        public string? OrderStatus { get; set; }
        public string? MerchantStatus { get; set; }
        public string? RegistrationNumber { get; set; }
        public string? BusinessType { get; set; }
        public string? UnifiedId { get; set; }
        public long? CrNumber { get; set; }
        public string? MunicipalLicenseNumber { get; set; }
        public string? ProjectName { get; set; }
        public string? ReferralChannel { get; set; }
        public string? BankCheckStatus { get; set; }
        public string? AcceptedPaymentMethods { get; set; }
        public string? MigrationRequest { get; set; }
        public string? SaudiOrderLedger { get; set; }
        public List<ProductExportResponse> ProductExport { get; set; } = new List<ProductExportResponse>();
        public List<OrderItemResponse> OrderItem { get; set; } = new List<OrderItemResponse>();
    }
}
