﻿using System;

namespace Common.Models.ProductInstance
{
    public class UpdateProductInstanceMetaRequest
    {
        public Guid? ProductInstanceId { get; set; }
        public string? TId { get; set; }
        public string? MIDMerchantReference { get; set; }
        public string? ProviderBank { get; set; }
        public string? LegalName { get; set; }
        public string? LegalNameAr { get; set; }
        public string? TradingCurrency { get; set; }
        public string? FullTId { get; set; }
        public string? Trsm { get; set; }
        public string? ConnectionType { get; set; }
        public string? ChannelType { get; set; }
        public string? MCC { get; set; }
    }
}
