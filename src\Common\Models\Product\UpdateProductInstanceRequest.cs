﻿namespace Common.Models.Product
{
    using System;

    public class UpdateProductInstanceRequest
    {
        public Guid? StoreId { get; set; }
        public DateTime? ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }
        public Guid? ParentId { get; set; }
        public object? Data { get; set; }
        public Guid? ParentConfigurationId { get; set; }
        public string? EPosTicketId { get; set; }
        public bool? EPosTicketCompleted { get; set; }
    }
}
