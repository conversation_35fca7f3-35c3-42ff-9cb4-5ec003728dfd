﻿using System;

namespace Common.Models.User
{
    public class UserAdvancedSearchResponse
    {
        public Guid Id { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? SalesId { get; set; }
        public string? Team { get; set; }
        public string? Designation { get; set; }
        public string? ReportingManager { get; set; }
        public string? Counterparty { get; set; }
        public string? GroupName { get; set; }
        public bool IsDisabled { get; set; }
    }
}
