﻿using Common.Models.Sales;
using Common.Models.User;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Geidea.Utils.Json;
using System.Text.Json;
using System.Linq;
using AutoMapper;
using Common.Models;

namespace Services
{
    public class UserService : IUserService
    {
        private readonly HttpClient httpClient;
        private readonly ILogger<UserService> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly ISearchService searchService;
        private readonly IMapper mapper;

        public UserService(ILogger<UserService> logger, HttpClient httpClient, IOptionsMonitor<UrlSettings> urlSettingsOptions, ISearchService searchService, IMapper mapper)
        {
            this.logger = logger;
            this.httpClient = httpClient;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.searchService = searchService;
            this.mapper = mapper;
        }

        private string UserServiceBaseUrl => $"{urlSettingsOptions.UserServiceBaseUrlNS}/api/v1";
        private readonly string PatchBackOfficeUserEndpoint = "/user/backOffice/";

        public async Task ActivateUserAsync(Guid userId)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/users/{userId}/activate";

            using (logger.BeginScope("ActivateUserAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to activate user.");

                var response = await httpClient.PostAsync(userServiceUrl, null!);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to activate user. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"User with id '{userId}' has been activated.");
            }
        }

        public async Task<UserExistsResponse> CheckUserExistsAsync(UserExistsRequest userExistsRequest)
        {
            string url = $"{UserServiceBaseUrl}/user/exists";

            using (logger.BeginScope("CheckUserExistsAsync({url})", url))
            {
                logger.LogInformation("Calling User Service to check if an user already exists with a certain email or phone number.");

                var requestBody = new StringContent(JsonConvert.SerializeObject(userExistsRequest), Encoding.Default, "application/json");
                var response = await httpClient.PostAsync(url, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling Users API to get user details. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                logger.LogInformation("Success calling Users API");

                return Json.Deserialize<UserExistsResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task DisableUserAsync(Guid userId)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/users/{userId}/disable";

            using (logger.BeginScope("DisableUserAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to disable user.");

                var response = await httpClient.PostAsync(userServiceUrl, null!);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to disable user. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"User with id '{userId}' has been disabled.");
            }
        }

        public async Task<User[]> GetAllUsersAsync()
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/users";

            using (logger.BeginScope("GetAllUsersAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to get all users.");

                var response = await httpClient.GetAsync(userServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to get all users. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<User[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<User> GetUserByIdAsync(Guid userId)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/users/{userId}";

            using (logger.BeginScope("GetUserByIdAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to get user by id.");

                var response = await httpClient.GetAsync(userServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to get user by id. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<User>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<UserViewModel> FindUserByIdAsync(Guid userId)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/user/{userId}";

            using (logger.BeginScope("FindUserByIdAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to get user by id.");

                var response = await httpClient.GetAsync(userServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to get user by id. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<UserViewModel>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<UserShortResponse[]> GetUsersAsync(Guid merchantId)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/merchant/{merchantId}/users";

            using (logger.BeginScope("GetUsersAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation($"Calling user service to get all users for merchant with id '{merchantId}'.");

                var response = await httpClient.GetAsync(userServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to get users for merchant with id '{merchantId}'. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<UserShortResponse[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<UserShortResponse[]> GetReportingManagersAsync()
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/user/reportingManagers";

            using (logger.BeginScope("GetReportingManagersAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation($"Calling user service to get reporting managers.");

                var response = await httpClient.GetAsync(userServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to get reporting managers. Error was {StatusCode} {@responseBody}",
                                      (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<UserShortResponse[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task PatchBackOfficeUserAsync(Guid userId, JsonPatchDocument<UpdateBackOfficeUserRequest> updateBackOfficeUserRequest)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}{PatchBackOfficeUserEndpoint}{userId}";
            var requestBody = new StringContent(JsonConvert.SerializeObject(updateBackOfficeUserRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("PatchBackOfficeUserAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to update backoffice user.");

                var response = await httpClient.PatchAsync(userServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to update backoffice user. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation("Backoffice User with id '{userId}' has been updated.", userId);
            }
        }

        public async Task RemoveRolesForMerchantAsync(Guid merchantId)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/merchant/{merchantId}/roles";

            using (logger.BeginScope("RemoveAllRolesforMerchantAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation($"Calling user service to remove all user roles for merchant with id '{merchantId}'.");

                var response = await httpClient.DeleteAsync(userServiceUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();

                    logger.LogCritical("Error when calling user service to remove all user roles for merchant with id '{merchantId}'. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"Removed all user roles for merchant with id '{merchantId}'.");
            }
        }

        public async Task<UserResponse[]> SearchUsersAsync(UserSearchParameters userSearchParameters)
        {
            string userServiceUrl = AddSearchQueryParams($"{UserServiceBaseUrl}/user/search", userSearchParameters);

            using (logger.BeginScope("SearchUsersAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to search users.");

                var response = await httpClient.GetAsync(userServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to search users. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }
                return Json.Deserialize<UserResponse[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<UserSalesIdResponse> UpdateUserSalesIdAsync(UserSalesIdRequest userSalesIdRequest)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/user/salesId";

            using (logger.BeginScope("UpdateUserSalesIdAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to change sale id for user.");

                var requestBody = new StringContent(JsonConvert.SerializeObject(userSalesIdRequest), Encoding.UTF8, "application/json");
                var response = await httpClient.PutAsync(userServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to change sale id for user. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<UserSalesIdResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task UpdateUserAsync(UserUpdateRequest userUpdateRequest)
        {
            string userServiceUrl = $"{UserServiceBaseUrl}/user/update";
            var requestBody = new StringContent(JsonConvert.SerializeObject(userUpdateRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateUserAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to update user.");

                var response = await httpClient.PutAsync(userServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to update user. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation("User with id '{userId}' has been updated.", userUpdateRequest.UserId);
            }
        }

        public async Task<UserExportModel[]> ExportBackofficeUsers(UserAdvancedSearchFilter exportParameters)
        {
            var result = await searchService.FindUsersAsync(exportParameters);

            if (result != null)
            {
                List<UserAdvancedSearchResponse> userAdvancedSearches = result.Records.ToList();
                var userExports = mapper.Map<List<UserExportModel>>(userAdvancedSearches);

                return userExports.ToArray();
            }
            return Array.Empty<UserExportModel>();

        }


        public async Task SetUserRoleToMerchant(Guid userId)
        {


            string userServiceUrl = $"{UserServiceBaseUrl}/users/{userId}/group/merchant";

            using (logger.BeginScope("UpdateUserRole({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation("Calling user service to update user role.");

                var response = await httpClient.PostAsync(userServiceUrl, null!);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogInformation("Error when calling user service to update user role");
                }

                logger.LogInformation($"User with id '{userId}' has been updated.");
            }
        }

        private string AddSearchQueryParams(string baseUrl, UserSearchParameters userSearchParameters)
        {
            var queryParams = new List<string>();

            if (userSearchParameters.UserId != null && userSearchParameters.UserId != Guid.Empty)
                queryParams.Add($"UserId={userSearchParameters.UserId}");

            if (userSearchParameters.MerchantId != null && userSearchParameters.MerchantId != Guid.Empty)
                queryParams.Add($"MerchantId={userSearchParameters.MerchantId}");

            if (userSearchParameters.LeadId != null && userSearchParameters.LeadId != Guid.Empty)
                queryParams.Add($"LeadId={userSearchParameters.LeadId}");

            if (!string.IsNullOrEmpty(userSearchParameters.SalesId))
                queryParams.Add($"SalesId={userSearchParameters.SalesId}");

            if (!string.IsNullOrEmpty(userSearchParameters.Email))
                queryParams.Add($"Email={userSearchParameters.Email}");

            if (!string.IsNullOrEmpty(userSearchParameters.PhoneNumber))
                queryParams.Add($"PhoneNumber={HttpUtility.UrlEncode(userSearchParameters.PhoneNumber)}");

            if (!string.IsNullOrEmpty(userSearchParameters.CountryPrefix))
                queryParams.Add($"CountryPrefix={HttpUtility.UrlEncode(userSearchParameters.CountryPrefix)}");

            if (userSearchParameters.Groups != null && userSearchParameters.Groups.Count != 0)
                foreach (var group in userSearchParameters.Groups)
                    queryParams.Add($"Groups={group}");

            if (queryParams.Count != 0)
                baseUrl = $"{baseUrl}?{string.Join("&", queryParams)}";

            return baseUrl;
        }

        public async Task<List<string>?> ApplyteamAndDesgnationFilters(Guid userId)
        {
            List<string>? salesId;

            string userServiceUrl = $"{urlSettingsOptions.UserServiceBaseUrl}/api/v1/user/reportingManagerLevels/{userId}";

            using (logger.BeginScope("ApplyTeamAndDesignationFilter({@SearchService})", userServiceUrl))
            {
                var response = await httpClient.GetAsync(userServiceUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();

                    logger.LogCritical("Error when calling user service API  with  user Id {@userId}. Error was {StatusCode} {@responseBody}",
                        userId.ToString(), (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var result = await response.Content.ReadAsStringAsync();

                salesId = JsonConvert.DeserializeObject<List<string>>(result);

            }

            return salesId;
        }
    }
}