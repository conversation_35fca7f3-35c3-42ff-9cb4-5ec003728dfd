﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class PrincipalMatch : PrincipalBase
{

    [Display(Name = "First Name")]
    public string FirstName => this.Name;

    [Display(Name = "Middle Initial")]
    public string MiddleInitial => this.Name;

    [Display(Name = "Last Name")]
    public string LastName => this.Name;

    [Display(Name = "Name")]
    public string Name { get; set; } = string.Empty;

    [Display(Name = "Address")]
    public string Address { get; set; } = string.Empty;

    [Display(Name = "Drivers License")]
    public string DriversLicense { get; set; }= string.Empty;

    public override string ToString()
    {
        return $"{Environment.NewLine}Name-{Name}," +
            $"{Environment.NewLine}Address-{Address}," +
            $"{Environment.NewLine}Drivers license-{DriversLicense}," +
            $"{Environment.NewLine}NationalId-{NationalId}," +
            $"{Environment.NewLine}Phone number-{PhoneNumber}," +
            $"{Environment.NewLine}Alternative phone number-{AltPhoneNumber}";
    }
}

