﻿using System;
using System.Collections.Generic;

namespace Common.Models.Merchant
{
    public class MerchantViewModel
    {
        public Guid MerchantId { get; set; }
        public Guid? LeadId { get; set; }
        public string? MerchantType { get; set; }
        public string? MerchantStatus { get; set; }
        public string? Tag { get; set; }
        public string? Counterparty { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool DeletedFlag { get; set; }
        public MerchantDetails MerchantDetails { get; set; } = new MerchantDetails();
        public IReadOnlyCollection<MerchantAddress> Addresses { get; set; } = new List<MerchantAddress>();
        public IReadOnlyCollection<MerchantBankAccount> BankAccounts { get; set; } = new List<MerchantBankAccount>();
        public ICollection<MerchantExternalIdentifier> ExternalIdentifiers { get; set; } = new List<MerchantExternalIdentifier>();
    }
}
