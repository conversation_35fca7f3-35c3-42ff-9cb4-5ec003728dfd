﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models.Merchant.SubordinateMerchant;
using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace BackofficeApi.Controllers;

[Authorize]
[ApiController]
[Route("api/v1")]
public class SubordinateMerchantController : ControllerBase
{
    private readonly ISubordinateMerchantService subordinateMerchantService;
    private readonly Authorized authorized;

    public SubordinateMerchantController(
        ISubordinateMerchantService subordinateMerchantService,
        Authorized authorized)
    {
        this.subordinateMerchantService = subordinateMerchantService;
        this.authorized = authorized;
    }

    /// <summary>
    /// Retrieves the list of associated and available subordinate businesses for a specific merchant
    /// </summary>
    /// /// <param name="merchantId">The id of the merchant for which we retrieve the subordinates.</param>
    /// <param name="request">The parameters for the subordinate merchant search request.</param>
    /// <response code="200">Returns the list of subordinate businesses</response>
    [HttpPost("{merchantId}/findAssociatedAndAvailable")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> FindAssociatedAndAvailableSubordinateMerchants(Guid merchantId, [FromBody] SubordinateMerchantSearchRequestDto request)
    {
        if (!await authorized.To.List.SubordinateMerchants(merchantId))
        {
            return Forbid();
        }

        var response = await subordinateMerchantService.FindAssociatedAndAvailableSubordinateMerchants(merchantId, request);
        return Ok(response);
    }

    /// <summary>
    /// Retrieves the list of only associated subordinate businesses for a specific merchant
    /// </summary>
    /// /// <param name="merchantId">The id of the merchant for which we retrieve the subordinates.</param>
    /// <param name="request">The parameters for the subordinate merchant search request.</param>
    /// <response code="200">Returns the list of subordinate businesses</response>
    [HttpPost("{merchantId}/findAssociatedChildren")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> FindAssociatedSubordinateMerchants(Guid merchantId, [FromBody] SubordinateMerchantSearchRequestDto request)
    {
        if (!await authorized.To.List.SubordinateMerchants(merchantId))
        {
            return Forbid();
        }

        var response = await subordinateMerchantService.FindAssociatedSubordinateMerchants(merchantId, request);
        return Ok(response);
    }

    /// <summary>
    /// Retrieves only the parent associated to a merchant
    /// </summary>
    /// /// <param name="merchantId">The id of the merchant for which we retrieve the parent.</param>
    /// <response code="200">Returns the associated parent for a subordinate merchant</response>
    [HttpGet("{merchantId}/findAssociatedParent")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> FindAssociatedParent(Guid merchantId)
    {
        if (!await authorized.To.List.SubordinateMerchants(merchantId))
        {
            return Forbid();
        }

        var response = await subordinateMerchantService.FindAssociatedParent(merchantId);
        return Ok(response);
    }

    /// <summary>
    /// Creates a business hierarchy between the parent merchant and the subordinate merchants
    /// </summary>
    /// <param name="parentMerchantId">The id of the parent merchant.</param>
    /// <param name="subordinateMerchantIds">The list containing the ids of the subordinate business</param>
    /// <response code="204">Returns No Contend if the business hierarchy was created successfully</response>
    [HttpPost("createBusinessHierarchy/{parentMerchantId}")]
    public async Task<IActionResult> CreateBusinessHierarchy(Guid parentMerchantId, [FromBody] List<Guid> subordinateMerchantIds)
    {
        if (!await authorized.To.Update.SubordinateMerchants(parentMerchantId))
        {
            return Forbid();
        }

        await subordinateMerchantService.CreateBusinessHierarchyAsync(parentMerchantId, subordinateMerchantIds);
        return NoContent();
    }

    /// <summary>
    /// Deletes a business hierarchy between the parent merchant and the subordinate merchant
    /// </summary>
    /// <param name="parentMerchantId">The id of the parent merchant.</param>
    /// <param name="subordinateMerchantId">The id of the subordinate business</param>
    /// <response code="204">Returns No Contend if the business hierarchy was deleted successfully</response>
    [HttpDelete("deleteBusinessHierarchy/{parentMerchantId}/{subordinateMerchantId}")]
    public async Task<IActionResult> DeleteBusinessHierarchy(Guid parentMerchantId, Guid subordinateMerchantId)
    {
        if (!await authorized.To.Update.SubordinateMerchants(parentMerchantId))
        {
            return Forbid();
        }

        await subordinateMerchantService.DeleteBusinessHierarchyAsync(parentMerchantId, subordinateMerchantId);
        return NoContent();
    }
}