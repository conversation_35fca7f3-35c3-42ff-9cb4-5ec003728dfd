﻿using FluentValidation.TestHelper;

namespace Common.Tests;

public class ShareholderIndividualAssociationsCreateRequestValidatorTests
{
    [Test]
    public void Validate_InvalidShareholderIndividualId_ShouldFail()
    {
        var result = new ShareholderIndividualAssociationsCreateRequestValidator().TestValidate(new ShareholderIndividualAssociationsCreateRequest
        {
            ShareholderIndividualId = Guid.Empty,
            Merchant = new MerchantIndividualLink()
        });

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveValidationErrorFor(nameof(ShareholderIndividualAssociationsCreateRequest.ShareholderIndividualId))
            .WithErrorMessage(Errors.InvalidIndividualShareholderId.Message)
            .WithErrorCode(Errors.InvalidIndividualShareholderId.Code);
    }

    [Test]
    public void Validate_MissingMerchantShareholderAndCompanies_ShouldFail()
    {
        var result = new ShareholderIndividualAssociationsCreateRequestValidator().TestValidate(new ShareholderIndividualAssociationsCreateRequest
        {
            ShareholderIndividualId = Guid.NewGuid(),
            Merchant = new MerchantIndividualLink
            {
                MerchantId = new Guid()
            }
        });

        Assert.That(result.IsValid, Is.False);
    }

    [Test]
    public void Validate_ValidInput_ShouldNotFail()
    {
        var result = new ShareholderIndividualAssociationsCreateRequestValidator().TestValidate(new ShareholderIndividualAssociationsCreateRequest
        {
            ShareholderIndividualId = Guid.NewGuid(),
            Merchant = new MerchantIndividualLink
            {
                MerchantId = Guid.NewGuid(),
                OwnershipPercentage = 20
            },
            ShareholderCompanies = new List<ShareholderCompanyIndividualLink> {
                new ShareholderCompanyIndividualLink {
                    OrganizationRole = "role",
                    ShareHolderCompanyId = Guid.NewGuid(),
                }
            }
        }
        );

        Assert.That(result.IsValid, Is.True);
    }
}
