﻿using Common.Models.Lead;
using FluentValidation;

namespace Common.Validators
{
    public class LeadProductValidator : AbstractValidator<LeadProduct>
    {
        public LeadProductValidator()
        {
            RuleFor(x => x.ProductCode)
                .Must(x => x != null && x.Length <= 64)
                .WithErrorCode(Errors.ProductCodeLengthValidation.Code)
                .WithMessage(Errors.ProductCodeLengthValidation.Message);
        }
    }
}
