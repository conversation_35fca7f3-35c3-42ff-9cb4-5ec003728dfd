﻿using System;

namespace Common.Models.Checkout
{
    public class MerchantAccountConfig
    {
        public Guid Id { get; set; }
        public Guid MerchantId { get; set; }
        public string? AcceptedPaymentMethods { get; set; }
        public string? TransactionCurrency { get; set; }
        public string? SettlementCurrency { get; set; }
        public string? PayoutSchedule { get; set; }
        public int PayoutDay { get; set; }
        public decimal PayoutCapAmount { get; set; }
        public string? DCCProvider { get; set; }
        public string? OrderSetupFeePaymentMode { get; set; }
        public string? OrderSetupFeePaymentReference { get; set; }
        public string? OrderSecurityDepositPaymentMode { get; set; }
        public string? OrderSecurityDepositPaymentReference { get; set; }
        public int? SettlementTimeFrame { get; set; }
        public long? PerTransactionLimit { get; set; }
        public long? PerTransactionRefundLimit { get; set; }
        public string? TransactionType { get; set; }
        public decimal PayoutMinAmount { get; set; }
        public string? AmexMID { get; set; }
        public string? TamaraMID { get; set; }

    }
}