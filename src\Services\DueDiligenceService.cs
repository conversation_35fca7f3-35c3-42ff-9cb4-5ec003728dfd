﻿using Common.Models.Match;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services
{
    public class DueDiligenceService : IDueDiligenceService
    {
        private readonly HttpClient client;
        private readonly ILogger<DueDiligenceService> logger;
        private readonly UrlSettings urlSettingsOptions;

        public DueDiligenceService(HttpClient client, ILogger<DueDiligenceService> logger, IOptions<UrlSettings> urlSettingsOptions)
        {
            this.client = client;
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.Value;
        }

        private string DueDiligenceServiceUrl => $"{urlSettingsOptions.DueDiligenceServiceBaseUrl}/api/v1/match";
        private string DueDiligenceServiceBaseUrl => $"{urlSettingsOptions.DueDiligenceServiceBaseUrl}";
        private readonly string DueDiligenceWorldCheckOneEndpoint = "api/v1/WorldCheckOne";

        public async Task<MatchResponse> RetrieveMerchantMatchReport(Guid documentId)
        {
            using(logger.BeginScope("DueDiligenceService({dueDiligenceServiceUrl})", DueDiligenceServiceUrl))
            {
                logger.LogInformation("Retrieving data about merchant check status for document with id : {documentId}", documentId);

                var response = await client.GetAsync($"{DueDiligenceServiceUrl}/{documentId}");
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error calling DueDiligenceService to retrieve match response for document with id : {documentId}", documentId);

                    throw new PassthroughException(response);
                }

                var matchResponse = Json.Deserialize<MatchResponse>(responseBody,new JsonSerializerOptions
                                    {
                                        PropertyNameCaseInsensitive = true
                                    });

                return matchResponse;
            }
        }

        public async Task SendWorldCheckOneRequest(Guid merchantId)
        {
            var requestUri = $"{DueDiligenceServiceBaseUrl}/{DueDiligenceWorldCheckOneEndpoint}/Business/Create/{merchantId}";
            using (logger.BeginScope("SendScreeningRequest({requestUri})",requestUri))
            {
                try
                {
                    var requestBody = new StringContent(JsonConvert.SerializeObject(merchantId), Encoding.UTF8, "application/json");
                    var response = await client.PostAsync(requestUri, requestBody);
                    var jsonResult = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        logger.LogCritical("Error when calling DueDiligence for WorldCheckOne screening. Error was {response.StatusCode} {@jsonResult}", response.StatusCode, Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonResult)));

                    }
                }
                catch (Exception ex)
                {
                    logger.LogCritical("Duedilligence api is not reachable while sending to WorldCheckOne . Exception {@exception}", ex);
                }
                


            }
            
        }
    }
}