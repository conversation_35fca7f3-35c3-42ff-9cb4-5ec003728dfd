﻿using Common.Models.Merchant;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BackofficeApi.Formatters
{
    public class MerchantResponseCsvFormatter : TextOutputFormatter
    {
        public MerchantResponseCsvFormatter()
        {
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/csv"));
            SupportedEncodings.Add(Encoding.UTF8);
        }

        protected override bool CanWriteType(Type? type)
        {
            if (typeof(MerchantExportResponse).IsAssignableFrom(type)
                || typeof(IEnumerable<MerchantExportResponse>).IsAssignableFrom(type))
            {
                return true;
            }
            return false;
        }

        public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
        {
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                InjectionOptions = InjectionOptions.Escape
            };
            using var streamWriter = context.WriterFactory(context.HttpContext.Response.Body, selectedEncoding);
            using var csv = new CsvWriter(streamWriter, config);
            csv.WriteHeader(typeof(MerchantExport));
            csv.WriteHeader(typeof(MerchantAddress));
            csv.WriteHeader(typeof(MerchantContact));
            csv.WriteHeader(typeof(MerchantExportStore));
            csv.WriteHeader(typeof(MerchantAddress));
            csv.WriteHeader(typeof(MerchantExternalIdentifier));
            csv.WriteHeader(typeof(MerchantExportPersonOfInterest));
            csv.WriteHeader(typeof(MerchantContact));
            csv.WriteHeader(typeof(MerchantBankAccount));
            await csv.NextRecordAsync();

            var merchants = context.Object as IEnumerable<MerchantExportResponse>;
            if (merchants != null)
                merchants.ToList().ForEach(async merchant => await WriteMerchant(csv, merchant));
            else
            {
                var singleMerchant = context.Object as MerchantExportResponse;
                if (singleMerchant != null)
                    await WriteMerchant(csv, singleMerchant);
            }
            await streamWriter.FlushAsync();
            await csv.DisposeAsync();
        }

        private async Task WriteMerchant(CsvWriter csv, MerchantExportResponse merchant)
        {
            csv.WriteRecord(merchant.Merchant);
            await csv.NextRecordAsync();

            foreach (var address in merchant.Merchant.Addresses)
            {
                AddPadding(PaddingPoint.Addresses, csv);
                csv.WriteRecord(address);
                await csv.NextRecordAsync();
            }

            foreach(var contact in merchant.Merchant.Contacts)
            {
                AddPadding(PaddingPoint.Contacts, csv);
                csv.WriteRecord(contact);
                await csv.NextRecordAsync();
            }

            foreach (var store in merchant.Merchant.MerchantStores)
            {
                AddPadding(PaddingPoint.Stores, csv);
                csv.WriteRecord(store);
                await csv.NextRecordAsync();

                foreach(var address in store.StoreAddresses)
                {
                    AddPadding(PaddingPoint.StoreAddresses, csv);
                    csv.WriteRecord(address);
                    await csv.NextRecordAsync();
                }
            }

            foreach(var identifier in merchant.Merchant.ExternalIdentifiers)
            {
                AddPadding(PaddingPoint.ExternalIdentifiers, csv);
                csv.WriteRecord(identifier);
                await csv.NextRecordAsync();
            }

            foreach (var poi in merchant.Merchant.PersonsOfInterest)
            {
                AddPadding(PaddingPoint.PersonsOfInterest, csv);
                csv.WriteRecord(poi);
                await csv.NextRecordAsync();

                foreach (var contact in poi.PersonsOfInterestContacts)
                {
                    AddPadding(PaddingPoint.POIContact, csv);
                    csv.WriteRecord(contact);
                    await csv.NextRecordAsync();
                }
            }

            foreach (var account in merchant.Merchant.BankAccounts)
            {
                AddPadding(PaddingPoint.BankAccounts, csv);
                csv.WriteRecord(account);
                await csv.NextRecordAsync();
            }
        }

        private void AddPadding(PaddingPoint paddingPoint, CsvWriter csv)
        {
            switch(paddingPoint)
            {
                case PaddingPoint.Addresses:
                    csv.WriteRecord<MerchantExport?>(null);
                    break;
                case PaddingPoint.Contacts:
                    csv.WriteRecord<MerchantExport?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    break;
                case PaddingPoint.Stores:
                    csv.WriteRecord<MerchantExport?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantContact?>(null);
                    break;
                case PaddingPoint.StoreAddresses:
                    csv.WriteRecord<MerchantExport?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantContact?>(null);
                    csv.WriteRecord<MerchantExportStore?>(null);
                    break;
                case PaddingPoint.ExternalIdentifiers:
                    csv.WriteRecord<MerchantExport?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantContact?>(null);
                    csv.WriteRecord<MerchantExportStore?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    break;
                case PaddingPoint.PersonsOfInterest:
                    csv.WriteRecord<MerchantExport?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantContact?>(null);
                    csv.WriteRecord<MerchantExportStore?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantExternalIdentifier?>(null);
                    break;
                case PaddingPoint.POIContact:
                    csv.WriteRecord<MerchantExport?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantContact?>(null);
                    csv.WriteRecord<MerchantExportStore?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantExternalIdentifier?>(null);
                    csv.WriteRecord<MerchantExportPersonOfInterest?>(null);
                    break;
                case PaddingPoint.BankAccounts:
                    csv.WriteRecord<MerchantExport?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantContact?>(null);
                    csv.WriteRecord<MerchantExportStore?>(null);
                    csv.WriteRecord<MerchantAddress?>(null);
                    csv.WriteRecord<MerchantExternalIdentifier?>(null);
                    csv.WriteRecord<MerchantExportPersonOfInterest?>(null);
                    csv.WriteRecord<MerchantContact?>(null);
                    break;
            }
        }
    }

    public enum PaddingPoint
    {
        Addresses,
        Contacts,
        Stores,
        StoreAddresses,
        ExternalIdentifiers,
        PersonsOfInterest,
        POIContact,
        BankAccounts
    }
}
