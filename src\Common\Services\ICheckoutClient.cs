﻿using Common.Models.Checkout;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models.Comment;
using Common.Models.Merchant;

namespace Common.Services
{
    public interface ICheckoutClient
    {
        Task<OrderResponse> GetOrderByIdAsync(Guid orderId);
        Task DeleteOrderAsync(OrderDeleteRequest orderDeleteRequest);
        Task<OrderResponse[]> GetAllOrdersAsync(bool includeDeleted);
        Task<OrderItemResponse[]> GetOrderItemsByIdAsync(Guid[] orderItemIds);
        Task DeleteOrderItemAsync(Guid[] orderItemIds);
        Task<OrderResponse[]> SearchOrderAsync(CoreOrderSearchCriteria? coreOrderSearchCriteria);
        Task UpdateOrderItemAsync(Guid orderItemId, JsonPatchDocument<OrderItemUpdateRequest> updateOrderItemRequest);
        Task<OrderCommentResponse> CreateCommentAsync(Guid orderId, CommentCreateRequest commentCreateRequest);
        Task<OrderCommentResponse[]> GetCommentByOrderIdAsync(Guid orderId);
        Task<OrderCommentResponse> GetCommentByIdAsync(Guid commentId);
        Task<OrderCommentResponse> UpdateCommentAsync(Guid commentId, CommentUpdateRequest commentUpdateRequest);
        Task DeleteCommentAsync(Guid commentId);
        Task<List<OrderStatusResponse>> GetOrdersStatusHistoryAsync(Guid orderId);
        Task<MerchantOrders> GetMerchantOrdersNotPassedProductRegisterd(Guid merchantId);
        Task<List<OrderItem>> GetOrderItemByOrderIdAsync(Guid orderId);
        Task<OrderResponse> GetByOrderNumberAsync(string orderNumber);
        Task UpdateOrderAsync(JsonPatchDocument<OrderUpdateRequest> updateOrderRequest, Guid orderId);
        Task<OrderConfigurationResponse> GetOrderConfigAsync(Guid orderId);
        Task<OrderConfigurationResponse> AddOrderConfigurationAsync(OrderConfigurationResponse orderConfiguration);
        Task<OrderConfigurationResponse> PatchOrderConfigAsync(Guid orderId, JsonPatchDocument<OrderConfigurationResponse> jsonPatchDocument);
    }
}
