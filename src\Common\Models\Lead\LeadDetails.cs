﻿using Geidea.Utils.ReferenceData;

namespace Common.Models.Lead
{
    public class LeadDetails
    {
        [ReferenceData("BUSINESS_TYPE")]
        public string? BusinessType { get; set; }
        public string? RegistrationNumber { get; set; }
        public string? AccountHolderName { get; set; }
        public string? IBAN { get; set; }
        public string? BankAccountNumber { get; set; }
        [ReferenceData("BANKS")]
        public int? RefBankId { get; set; }
        public string? MunicipalLicenseNumber { get; set; }
        public string? LegalId { get; set; }
        public string? Tag { get; set; }
        public int? MaxMonthlyTransaction { get; set; }
        public int? HighestSingleTransaction { get; set; }
    }
}
