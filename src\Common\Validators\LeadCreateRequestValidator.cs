﻿using Common.Models.Lead;
using FluentValidation;
using Geidea.Utils.ReferenceData;
using System.Collections.Generic;
using UtilsConstants = Geidea.Utils.Common.Constants;
using static Common.Constants;
using Common.Models;
using static Geidea.Utils.ReferenceData.Constants;
using System.Linq;
using System;
using Newtonsoft.Json.Linq;

namespace Common.Validators
{
    public class LeadCreateRequestValidator : AbstractValidator<LeadCreateRequest>
    {
        public LeadCreateRequestValidator(string counterparty, string acquirer, List<ReferenceData> refData)
        {
            RuleFor(x => x.LeadDetails).SetValidator(new LeadDetailsValidator(counterparty));

            RuleFor(x => x.LeadStatus)
                .Must(x => x!.Length <= 32)
                .WithErrorCode(Errors.LeadStatusLengthValidation.Code)
                .WithMessage(Errors.LeadStatusLengthValidation.Message);

            RuleFor(x => x.BusinessDomain)
                .Must(x => x!.Length <= 32)
                .When(x => x.BusinessDomain != null)
                .WithErrorCode(Errors.BusinessDomainLengthValidation.Code)
                .WithMessage(Errors.BusinessDomainLengthValidation.Message);

            RuleFor(x => x.CountryPrefix)
                .Must(x => x != null && x.Length <= 16)
                .WithErrorCode(Errors.CountryPrefixLengthValidation.Code)
                .WithMessage(Errors.CountryPrefixLengthValidation.Message);

            RuleFor(x => x.OwnerFirstName)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerFirstName != null)
                .WithErrorCode(Errors.FirstNameLengthValidation.Code)
                .WithMessage(Errors.FirstNameLengthValidation.Message);

            RuleFor(x => x.OwnerLastName)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerLastName != null)
                .WithErrorCode(Errors.LastNameLengthValidation.Code)
                .WithMessage(Errors.LastNameLengthValidation.Message);

            RuleFor(x => x.OwnerFirstNameAr)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerFirstNameAr != null)
                .WithErrorCode(Errors.OwnerFirstNameArLengthValidation.Code)
                .WithMessage(Errors.OwnerFirstNameArLengthValidation.Message);

            RuleFor(x => x.OwnerLastNameAr)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerLastNameAr != null)
                .WithErrorCode(Errors.OwnerLastNameArLengthValidation.Code)
                .WithMessage(Errors.OwnerLastNameArLengthValidation.Message);

            RuleFor(x => x.LegalName)
                .Must(x => x!.Length <= 64)
                .When(x => x.LegalName != null)
                .WithErrorCode(Errors.LegalNameLengthValidation.Code)
                .WithMessage(Errors.LegalNameLengthValidation.Message);

            RuleFor(x => x.LegalNameAr)
                .Must(x => x!.Length <= 64)
                .When(x => x.LegalNameAr != null)
                .WithErrorCode(Errors.LegalNameArLengthValidation.Code)
                .WithMessage(Errors.LegalNameArLengthValidation.Message);

            RuleFor(x => x.AddressLine)
                .Must(x => x!.Length <= 128)
                .When(x => x.AddressLine != null)
                .WithErrorCode(Errors.AddressLineLengthValidation.Code)
                .WithMessage(Errors.AddressLineLengthValidation.Message);

            RuleFor(x => x.City)
                .Must(x => x!.Length <= 32)
                .When(x => x.City != null)
                .WithErrorCode(Errors.CityLengthValidation.Code)
                .WithMessage(Errors.CityLengthValidation.Message);

            RuleFor(x => x.Country)
                .Must(x => x!.Length <= 32)
                .When(x => x.Country != null)
                .WithErrorCode(Errors.CountryLengthValidation.Code)
                .WithMessage(Errors.CountryLengthValidation.Message);

            RuleFor(x => x.UTM)
                .Must(x => x!.Length <= 1000)
                .When(x => x.UTM != null)
                .WithErrorCode(Errors.UtmLengthValidation.Code)
                .WithMessage(Errors.UtmLengthValidation.Message);

            RuleFor(x => x.SalesId)
                .Must(x => x!.Length <= 10)
                .When(x => x.SalesId != null)
                .WithErrorCode(Errors.SalesIdLengthValidation.Code)
                .WithMessage(Errors.SalesIdLengthValidation.Message);

            RuleFor(x => x.SalesPartnerId)
                .Must(x => x!.Length <= 32)
                .When(x => x.SalesPartnerId != null)
                .WithErrorCode(Errors.SalesPartnerIdLengthValidation.Code)
                .WithMessage(Errors.SalesPartnerIdLengthValidation.Message);

            RuleFor(x => x.Nationality)
                .Must(x => x!.Length <= 64)
                .When(x => x.Nationality != null)
                .WithErrorCode(Errors.NationalityLengthValidation.Code)
                .WithMessage(Errors.NationalityLengthValidation.Message);

            RuleFor(x => x.Gender)
                .Must(x => x!.Length <= 16)
                .When(x => x.Gender != null)
                .WithErrorCode(Errors.GenderLengthValidation.Code)
                .WithMessage(Errors.GenderLengthValidation.Message);

            RuleFor(x => x.ReferralChannel)
                .Must(x => x!.Length <= 128)
                .When(x => x.ReferralChannel != null)
                .WithErrorCode(Errors.ReferralChannelLengthValidation.Code)
                .WithMessage(Errors.ReferralChannelLengthValidation.Message);

            RuleFor(x => x.Governorate)
                .Must(x => x!.Length <= 64)
                .When(x => x.Governorate != null)
                .WithErrorCode(Errors.GovernorateLengthValidation.Code)
                .WithMessage(Errors.GovernorateLengthValidation.Message);

            RuleFor(x => x.CRMLeadId)
                .Must(x => x!.Length <= 64)
                .When(x => x.CRMLeadId != null)
                .WithErrorCode(Errors.CrmLeadIdLengthValidation.Code)
                .WithMessage(Errors.CrmLeadIdLengthValidation.Message);

            RuleFor(x => x.Area)
                .Must(x => x!.Length <= 64)
                .When(x => x.Area != null)
                .WithErrorCode(Errors.AreaLengthValidation.Code)
                .WithMessage(Errors.AreaLengthValidation.Message);

            RuleFor(x => x.TahakomTransactionId)
                .Must(x => x!.Length <= 64)
                .When(x => x.TahakomTransactionId != null)
                .WithErrorCode(Errors.TahakomTransactionIdLengthValidation.Code)
                .WithMessage(Errors.TahakomTransactionIdLengthValidation.Message);

            RuleFor(x => x.PhoneNumber).SetValidator(new PhoneValidator());

            RuleFor(x => x.City)
                  .ChildRules(x =>
                  {
                      RuleFor(l => l)
                           .Must(l => IsCityValid(l, acquirer, refData))
                           .When(l => !string.IsNullOrEmpty(l.City))
                           .OverridePropertyName(nameof(LeadCreateRequest.City))
                           .WithErrorCode(Errors.InvalidCity.Code)
                           .WithMessage(Errors.InvalidCity.Message);
                  });

            RuleFor(x => x.Governorate)
                 .ChildRules(x =>
                 {
                     RuleFor(l => l)
                          .Must(l => IsGovernorateValid(l, acquirer, refData))
                          .When(l => !string.IsNullOrEmpty(l.Governorate))
                          .OverridePropertyName(nameof(LeadCreateRequest.Governorate))
                          .WithErrorCode(Errors.InvalidGovernorate.Code)
                          .WithMessage(Errors.InvalidGovernorate.Message);
                 });

            RuleFor(x => x.BusinessDomain)
                .ChildRules(x =>
                {
                    RuleFor(l => l)
                         .Must(l => IsBusinessDomainValid(l, acquirer, refData))
                         .When(l => !string.IsNullOrEmpty(l.BusinessDomain))
                         .OverridePropertyName(nameof(LeadCreateRequest.BusinessDomain))
                         .WithErrorCode(Errors.InvalidBusinessDomain.Code)
                         .WithMessage(Errors.InvalidBusinessDomain.Message);
                });

            When(x => x.OwnerEmail != null, () =>
            {
                RuleFor(x => x.OwnerEmail!).SetValidator(new EmailValidator());
            });

            When(x => counterparty == UtilsConstants.CounterpartySaudi || counterparty == UtilsConstants.CounterpartyUae, () =>
            {
                RuleFor(x => x.NationalId)
                    .Must(x => x!.Length <= 32)
                    .When(x => x.NationalId != null)
                    .WithErrorCode(Errors.NationalIdLengthValidation.Code)
                    .WithMessage(Errors.NationalIdLengthValidation.Message);
            });

            When(x => counterparty == UtilsConstants.CounterpartyEgypt, () =>
            {
                RuleFor(x => x.NationalId)
                        .SetValidator(new EgyptNationalIdValidator())
                        .When(x => x.NationalId != null);
            });

            When(x => counterparty == UtilsConstants.CounterpartyEgypt, () =>
             {
                 RuleFor(l => l)
                  .ChildRules(x =>
                  {
                      RuleFor(l => l)
                           .Must(l => CityIsFromGovernorate(l, acquirer, refData))
                           .When(l => !string.IsNullOrEmpty(l.City) && !string.IsNullOrEmpty(l.Governorate))
                           .OverridePropertyName(nameof(LeadCreateRequest.City))
                           .WithErrorCode(Errors.CityNotFromGovernorate.Code)
                           .WithMessage(Errors.CityNotFromGovernorate.Message);
                  });


             });


        }

        private static bool CityIsFromGovernorate(LeadCreateRequest lead, string acquirer, List<ReferenceData> refData)
        {
            if (!AcquirerHelper.IsValidAcquirer(acquirer, refData))
                return true;

            string citiesCatalougeName = Constants.Catalogues.Cities;
            string govenoratesCatalougeName = Constants.Catalogues.Governorates;
            string cityToGovernorateCatalougeName = Constants.Catalogues.CitiesToGovernarotes;

            if (refData.Any(r => r.CatalogueName.StartsWith(acquirer, StringComparison.OrdinalIgnoreCase)))
            {
                citiesCatalougeName = AcquirerHelper.ConcatAcquirerWithCatalogueName(acquirer, citiesCatalougeName);
                govenoratesCatalougeName = AcquirerHelper.ConcatAcquirerWithCatalogueName(acquirer, govenoratesCatalougeName);
                cityToGovernorateCatalougeName = AcquirerHelper.ConcatAcquirerWithCatalogueName(acquirer, cityToGovernorateCatalougeName);
            }

            var citiesKeys = refData.Where(r =>
            r.CatalogueName == citiesCatalougeName &&
            string.Equals(r.Key, lead.City, StringComparison.OrdinalIgnoreCase))
                .Select(r => r.Key)
                .ToList();

            var governorateKey = refData.Where(r =>
            r.CatalogueName == govenoratesCatalougeName &&
            string.Equals(r.Key, lead.Governorate, StringComparison.OrdinalIgnoreCase))
                .Select(r => r.Key)
                .FirstOrDefault();

            return refData.Exists(r =>
            r.CatalogueName == cityToGovernorateCatalougeName &&
            string.Equals(r.Value, governorateKey, StringComparison.OrdinalIgnoreCase) &&
            citiesKeys.Exists(c => c == r.Key));
        }

        private static bool IsCityValid(LeadCreateRequest lead, string acquirer, List<ReferenceData> refData)
        {
            string citiesCatalougeName = Constants.Catalogues.Cities;
            if (!string.IsNullOrEmpty(acquirer) && refData.Any(r => r.CatalogueName.StartsWith(acquirer, StringComparison.OrdinalIgnoreCase)))
            {
                citiesCatalougeName = AcquirerHelper.ConcatAcquirerWithCatalogueName(acquirer, citiesCatalougeName);
            }
            return  refData.Exists(r =>string.Equals(r.CatalogueName, citiesCatalougeName, StringComparison.OrdinalIgnoreCase) && string.Equals(r.Key, lead.City, StringComparison.OrdinalIgnoreCase));
        }

        private static bool IsGovernorateValid(LeadCreateRequest lead, string acquirer, List<ReferenceData> refData)
        {
            string governorateCatalougeName = Constants.Catalogues.Governorates;
            if (!string.IsNullOrEmpty(acquirer) && refData.Any(r => r.CatalogueName.StartsWith(acquirer, StringComparison.OrdinalIgnoreCase)))
            {
                governorateCatalougeName = AcquirerHelper.ConcatAcquirerWithCatalogueName(acquirer, governorateCatalougeName);
            }
            return refData.Exists(r => string.Equals(r.CatalogueName, governorateCatalougeName, StringComparison.OrdinalIgnoreCase) && string.Equals(r.Key, lead.Governorate, StringComparison.OrdinalIgnoreCase));
        }

        private static bool IsBusinessDomainValid(LeadCreateRequest lead, string acquirer, List<ReferenceData> refData)
        {
            string businessDomainCatalougeName = Constants.Catalogues.BusinessDomain;
            if (!string.IsNullOrEmpty(acquirer) && refData.Any(r => r.CatalogueName.StartsWith(acquirer, StringComparison.OrdinalIgnoreCase)))
            {
                businessDomainCatalougeName = AcquirerHelper.ConcatAcquirerWithCatalogueName(acquirer, businessDomainCatalougeName);
            }
            return refData.Exists(r => string.Equals(r.CatalogueName, businessDomainCatalougeName, StringComparison.OrdinalIgnoreCase) && string.Equals(r.Key, lead.BusinessDomain, StringComparison.OrdinalIgnoreCase));
        }
    }
}