﻿using System.Security.Claims;
using System.Text.Json;
using Geidea.Utils.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Geidea.BackofficePortal.Backoffice.Api.Tests.Extensions
{
    public static class HttpContextHelper
    {
        public static void CreateHttpContext(this ControllerBase controller, string role)
        {
            controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = new ClaimsPrincipal(new[]
                    {
                        new ClaimsIdentity(new[]
                        {
                            new Claim(ClaimTypes.Role, JsonSerializer.Serialize(new RoleAssignment {Name = role}))
                        })
                    })
                }
            };
        }
    }
}
