﻿using Common.Models.Tasks;
using Common.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Common.Models;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    public class TaskController : ControllerBase
    {
        private readonly ITaskService taskService;
        private readonly ISearchService searchService;

        public TaskController(ITaskService taskService, ISearchService searchService)
        {
            this.taskService = taskService;
            this.searchService = searchService;
        }

        /// <summary>
        /// Update a specific task by Id.
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="patchDocument"></param>
        /// <returns></returns>
        [HttpPatch("{taskId:guid}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateAsync(Guid taskId, [FromBody]JsonPatchDocument<TaskUpdateRequest> patchDocument)
        {
            await taskService.UpdateTaskAsync(taskId, patchDocument);
            return NoContent();
        }

        [HttpPost("advancedSearch")]
        [ProducesResponseType(typeof(SearchResponse<TaskResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SearchAsync([FromBody] TaskSearchRequest searchRequest)
        {
            return Ok(await searchService.TaskAdvancedSearch(searchRequest));
        }
    }
}
