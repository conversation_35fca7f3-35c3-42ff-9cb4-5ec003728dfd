﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers
{
    public class HttpMessageHandlerMock : HttpMessageHandler
    {
        private readonly HttpStatusCode statusCode;
        private readonly Queue<string> response = new Queue<string>();
        private readonly List<HttpRequestHeaders> sentHeadersCollection = new List<HttpRequestHeaders>();

        public HttpMessageHandlerMock(HttpStatusCode statusCode, params string[] response)
        {
            this.statusCode = statusCode;
            response.ToList().ForEach(x => this.response.Enqueue(x));
        }

        public HttpRequestMessage RequestMessage { get; private set; } = null!;

        public bool HasRequestWithHeader(string headerName, string headerValue)
        {
            return sentHeadersCollection.Any(c => c.Contains(headerName) && c.GetValues(headerName).Single().Contains(headerValue));
        }

        public async Task<TRequest> GetDeserializedRequestBody<TRequest>()
        {
            if (RequestMessage == null)
                throw new InvalidOperationException("This client has not sent a request");

            var serializerOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new JsonStringEnumConverter() }
            };

            var requestBody = await RequestMessage.Content!.ReadAsStringAsync();
            var deserializedRequest = JsonSerializer.Deserialize<TRequest>(requestBody, serializerOptions);

#pragma warning disable 8603
            return deserializedRequest;
#pragma warning restore 8603
        }

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            if (request.Headers != null)
                sentHeadersCollection.Add(request.Headers);

            RequestMessage = request;
            return Task.FromResult(new HttpResponseMessage
            {
                StatusCode = statusCode,
                Content = new StringContent(response.Dequeue())
            });
        }
    }
}