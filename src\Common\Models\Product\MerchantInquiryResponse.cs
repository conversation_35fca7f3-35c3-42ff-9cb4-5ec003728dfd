﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Product
{
    public class MerchantInquiryResponse
    {
        public List<LinkedTerminal> linkedTerminals { get; set; } = new List<LinkedTerminal>();

    }

    public class LinkedTerminal
    {
        public string terminalId { get; set; } = string.Empty;
        public string serialNumber { get; set; } = string.Empty;
        public string terminalStatus { get; set; } = string.Empty;
        public string brand { get; set; } = string.Empty;
        public string model { get; set; } = string.Empty;
    }

}
