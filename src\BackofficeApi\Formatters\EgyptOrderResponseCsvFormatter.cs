﻿using BackofficeApi.Formatters.ClassMappers;
using Common.Models.Checkout;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CsvHelper.TypeConversion;

namespace BackofficeApi
{
    public class EgyptOrderResponseCsvFormatter : TextOutputFormatter
    {
        public EgyptOrderResponseCsvFormatter()
        {
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/csv"));
            SupportedEncodings.Add(Encoding.UTF8);
        }

        protected override bool CanWriteType(Type? type)
        {
            if (typeof(EgyptOrderExportResponse).IsAssignableFrom(type)
                || typeof(IEnumerable<EgyptOrderExportResponse>).IsAssignableFrom(type))
            {
                return true;
            }
            return false;
        }

        public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
        {
            try
            {
                var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                {
                    InjectionOptions = InjectionOptions.Escape
                };
                using (var streamWriter = context.WriterFactory(context.HttpContext.Response.Body, selectedEncoding))
                using (var csv = new CsvWriter(streamWriter, config))
                {
                    csv.Context.RegisterClassMap<EgyptOrderExportResponseMap>();
                    csv.Context.RegisterClassMap<EgyptProductExportResponseMap>();
                    csv.WriteHeader(typeof(EgyptOrderExportResponse));
                    csv.WriteHeader(typeof(EgyptProductExportResponse));
                    await csv.NextRecordAsync();

                    var orders = context.Object as IEnumerable<EgyptOrderExportResponse>;
                    if (orders != null)
                    {
                        foreach (var order in orders)
                        {
                            await WriteOrder(csv, order);
                        }
                    }
                    else
                    {
                        var singleOrder = context.Object as EgyptOrderExportResponse;
                        if (singleOrder != null)
                            await WriteOrder(csv, singleOrder);
                    }
                    await streamWriter.FlushAsync();
                }
            }
            catch (Exception ex)
            {
                var httpContext = context.HttpContext;
                var serviceProvider = httpContext.RequestServices;
                var logger = serviceProvider.GetRequiredService<ILogger<EgyptOrderResponseCsvFormatter>>();

                logger.LogError(ex, $"Error occured with the following message.");
            }
        }

        private static async Task WriteOrder(CsvWriter csv, EgyptOrderExportResponse order)
        {
            if (!order.ProductExport.Any() && order.ProductExport.Count <= 0)
            {
                csv.WriteRecord(order);
                csv.WriteRecord(new EgyptProductExportResponse());
                await csv.NextRecordAsync();
                return;
            }

            foreach (var orderItem in order.ProductExport)
            {
                csv.WriteRecord(order);
                csv.WriteRecord(orderItem);
                await csv.NextRecordAsync();
            }
        }
    }

    public class CustomBooleanConverter : DefaultTypeConverter
    {
        public override string ConvertToString(object? value, IWriterRow row, MemberMapData memberMapData)
        {
            if (value is not bool boolValue)
            {
                return string.Empty;
            }

            return boolValue ? "Yes" : "No";
        }
    }
}
