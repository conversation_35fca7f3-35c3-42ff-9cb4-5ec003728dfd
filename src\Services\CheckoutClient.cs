﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Common.Models.Checkout;
using Common.Models.Comment;
using Common.Models.Merchant;
using Common.Options;
using Common.Services;
using Common.Validators;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Geidea.Utils.Validation;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Services;

public class CheckoutClient : ICheckoutClient
{
    private readonly HttpClient client;
    private readonly ILogger<CheckoutClient> logger;
    private readonly IOptionsMonitor<UrlSettings> urlSettingsOptions;
    private string CheckoutServiceBaseUrl => $"{urlSettingsOptions.CurrentValue.CheckoutServiceBaseUrlNS}/api/v1";

    public CheckoutClient(HttpClient client, ILogger<CheckoutClient> logger, IOptionsMonitor<UrlSettings> urlSettingsOptions)
    {
        this.client = client;
        this.logger = logger;
        this.urlSettingsOptions = urlSettingsOptions;
    }
    public async Task<OrderResponse> GetOrderByIdAsync(Guid orderId)
    {
        string checkoutServiceUrl = $"{CheckoutServiceBaseUrl}/order/{orderId}";

        using (logger.BeginScope("GetOrderByIdAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation("Calling checkout API to get the order with id: '{orderId}'.", orderId);

            var response = await client.GetAsync(checkoutServiceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout API get order with id '{orderId}'. Error was {StatusCode} {@responseBody}",
                    orderId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            OrderResponse order = Json.Deserialize<OrderResponse>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            logger.LogInformation("Received order with id '{orderId}'.", order.OrderId);

            return order;
        }
    }

    public async Task<OrderCommentResponse> CreateCommentAsync(Guid orderId, CommentCreateRequest commentCreateRequest)
    {
        string requestUrl = $"{CheckoutServiceBaseUrl}/order/{orderId}/comment";
        var requestBody = new StringContent(JsonConvert.SerializeObject(commentCreateRequest), Encoding.UTF8, "application/json");

        using (logger.BeginScope("CreateCommentAsync({@requestUrl})", requestUrl))
        {
            logger.LogInformation("Calling checkout service, create new comment.");

            var response = await client.PostAsync(requestUrl, requestBody);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service, create new comment. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            logger.LogInformation("New comment created.");

            OrderCommentResponse orderComment = Json.Deserialize<OrderCommentResponse>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            return orderComment;
        }
    }

    public async Task DeleteCommentAsync(Guid commentId)
    {
        string requestUrl = $"{CheckoutServiceBaseUrl}/comment/{commentId}";

        using (logger.BeginScope("DeleteCommentAsync({@requestUrl})", requestUrl))
        {
            logger.LogInformation("Calling checkout service in order to delete comment with id: '{commentId}'.", commentId);

            var response = await client.DeleteAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Error when calling checkout service, delete comment with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                    commentId, (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }
            logger.LogInformation("Deleted comment with id '{commentId}'.", commentId);
        }
    }

    public async Task DeleteOrderAsync(OrderDeleteRequest orderDeleteRequest)
    {
        StringBuilder checkoutServiceUrl = new StringBuilder($"{CheckoutServiceBaseUrl}/order?");

        foreach (Guid orderId in orderDeleteRequest.OrderId)
        {
            checkoutServiceUrl.Append("&orderIds=").Append(orderId);
        }

        using (logger.BeginScope("DeleteOrdersAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation("Calling checkout API to delete orders.");

            var response = await client.DeleteAsync(checkoutServiceUrl.ToString());

            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();

                logger.LogCritical("Error when calling checkout API delete orders. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            logger.LogInformation("Deleted orders.");
        }
    }

    public async Task DeleteOrderItemAsync(Guid[] orderItemIds)
    {
        StringBuilder checkoutServiceUrl = new StringBuilder($"{CheckoutServiceBaseUrl}/orderItem?");

        foreach (Guid orderItemId in orderItemIds)
        {
            checkoutServiceUrl.Append("&orderItemIds=").Append(orderItemId);
        }

        using (logger.BeginScope("DeleteOrderItemsAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation("Calling checkout API to delete order items.");

            var response = await client.DeleteAsync(checkoutServiceUrl.ToString());

            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();

                logger.LogCritical("Error when calling checkout API delete order itemss. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            logger.LogInformation("Deleted order items.");
        }
    }

    public async Task<OrderResponse[]> GetAllOrdersAsync(bool includeDeleted)
    {
        string baseUrl = $"{CheckoutServiceBaseUrl}/orders/all";
        var param = new Dictionary<string, string>() { { "includeDeleted", includeDeleted.ToString() } };

        var checkoutServiceUrl = new Uri(QueryHelpers.AddQueryString(baseUrl, param!));

        using (logger.BeginScope("GetAllOrderAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation($"Calling checkout API to get all orders.");

            var response = await client.GetAsync(checkoutServiceUrl);

            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();

                logger.LogCritical("Error when calling checkout API to get all orders. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            OrderResponse[] order = await response.Content.ReadAsAsync<OrderResponse[]>();
            logger.LogInformation("Received all orders.");

            return order;
        }
    }

    public async Task<OrderCommentResponse> GetCommentByIdAsync(Guid commentId)
    {
        string requestUrl = $"{CheckoutServiceBaseUrl}/comment/{commentId}";

        using (logger.BeginScope("GetCommentByIdAsync({@requestUrl})", requestUrl))
        {
            logger.LogInformation("Calling checkout service, get comment with id: '{commentId}'.", commentId);

            var response = await client.GetAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service, get comment with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                    commentId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            OrderCommentResponse orderComment = Json.Deserialize<OrderCommentResponse>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            return orderComment;
        }
    }

    public async Task<OrderCommentResponse[]> GetCommentByOrderIdAsync(Guid orderId)
    {
        string requestUrl = $"{CheckoutServiceBaseUrl}/order/{orderId}/comment";

        using (logger.BeginScope("GetCommentByOrderIdAsync({@requestUrl})", requestUrl))
        {
            logger.LogInformation("Calling checkout service, get comments for order with id: '{orderId}'.", orderId);

            var response = await client.GetAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service, get comments for order with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                    orderId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            OrderCommentResponse[] comments = Json.Deserialize<OrderCommentResponse[]>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            return comments;
        }
    }

    public async Task<OrderResponse> GetByOrderNumberAsync(string orderNumber)
    {
        string checkoutServiceUrl = $"{CheckoutServiceBaseUrl}/order/ordernumber/{orderNumber}";

        using (logger.BeginScope("GetOrderByOrderNumberAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation("Calling checkout API to get the order with number: {orderNumber}.", orderNumber);

            var response = await client.GetAsync(checkoutServiceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout API get order with number '{orderNumber}'. Error was {StatusCode} {@responseBody}",
                    orderNumber, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            return Json.Deserialize<OrderResponse>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }
    }

    public async Task<List<OrderItem>> GetOrderItemByOrderIdAsync(Guid orderId)
    {
        string requestUrl = $"{CheckoutServiceBaseUrl}/order/{orderId}/orderItems";

        using (logger.BeginScope("GetOrderItemByOrderIdAsync({@requestUrl})", requestUrl))
        {
            logger.LogInformation("Calling checkout service, get order status history with order id: '{orderId}'.", orderId);

            var response = await client.GetAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service, get order item by order id '{orderId}'. Error was {statusCode} {@responseBody}", orderId, response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            return Json.Deserialize<List<OrderItem>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
        }
    }

    public async Task<OrderItemResponse[]> GetOrderItemsByIdAsync(Guid[] orderItemIds)
    {
        StringBuilder checkoutServiceUrl = new StringBuilder($"{CheckoutServiceBaseUrl}/orderItem?");

        foreach (Guid orderItemId in orderItemIds)
        {
            checkoutServiceUrl.Append("&orderItemId=").Append(orderItemId);
        }

        using (logger.BeginScope("GetOrderByIdAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation("Calling checkout API to get the orderItems.");

            var response = await client.GetAsync(checkoutServiceUrl.ToString());

            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();

                logger.LogCritical("Error when calling checkout API get order items. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            OrderItemResponse[] orderItem = await response.Content.ReadAsAsync<OrderItemResponse[]>();
            logger.LogInformation($"Received order items.");

            return orderItem;
        }
    }

    public async Task<List<OrderStatusResponse>> GetOrdersStatusHistoryAsync(Guid orderId)
    {
        string requestUrl = $"{CheckoutServiceBaseUrl}/order/{orderId}/status/history";

        using (logger.BeginScope("GetOrdersStatusHistoryAsync({@requestUrl})", requestUrl))
        {
            logger.LogInformation("Calling checkout service, get order status history with order id: '{orderId}'.", orderId);

            var response = await client.GetAsync(requestUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service, get order status history with order id '{orderId}'. Error was {statusCode} {@responseBody}", orderId, response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            var orderStatusResponses = Json.Deserialize<List<OrderStatusResponse>>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return orderStatusResponses;
        }
    }

    public async Task<MerchantOrders> GetMerchantOrdersNotPassedProductRegisterd(Guid merchantId)
    {
        string serviceUrl = $"{CheckoutServiceBaseUrl}/orders/{merchantId}/not-passed-product-registered";

        using (logger.BeginScope("GetMerchantOrdersNotPassedProductRegisterd({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling checkout service");

            var response = await client.GetAsync(serviceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service, get Get Merchant Orders NotPassed ProductRegisterd by merchant with Id '{merchantId}'. Error was {StatusCode} {@responseBody}", merchantId, (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var merchantOrderResponse = Json.Deserialize<MerchantOrders>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return merchantOrderResponse;
        }
    }

    public async Task<OrderResponse[]> SearchOrderAsync(CoreOrderSearchCriteria? coreOrderSearchCriteria)
    {
        if (coreOrderSearchCriteria == null)
        {
            return Array.Empty<OrderResponse>();
        }

        var url = $"{CheckoutServiceBaseUrl}/order/advancedSearch";
        var requestBody = new StringContent(JsonConvert.SerializeObject(coreOrderSearchCriteria), Encoding.UTF8, "application/json");

        using (logger.BeginScope("SearchOrdersAdvancedAsync({@url})", url))
        {
            logger.LogInformation("Calling checkout service to search orders advanced'.");

            var response = await client.PostAsync(url, requestBody);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service to search orders advanced. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var orderSearchResponse = Json.Deserialize<OrderSearchResponse>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            logger.LogInformation("Found '{returnedOrdersCount}' order records.", orderSearchResponse.ReturnedRecordCount);

            return orderSearchResponse.Records;
        }
    }

    public async Task<OrderCommentResponse> UpdateCommentAsync(Guid commentId, CommentUpdateRequest commentUpdateRequest)
    {
        string requestUrl = $"{CheckoutServiceBaseUrl}/comment/{commentId}";
        var requestBody = new StringContent(JsonConvert.SerializeObject(commentUpdateRequest), Encoding.UTF8, "application/json");

        using (logger.BeginScope("UpdateCommentAsync({@requestUrl})", requestUrl))
        {
            logger.LogInformation("Calling checkout service, update comment with id: '{commentId}'.", commentId);

            var response = await client.PutAsync(requestUrl, requestBody);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service, update comment with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                    commentId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            OrderCommentResponse updatedOrderComment = Json.Deserialize<OrderCommentResponse>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            return updatedOrderComment;
        }
    }

    public async Task UpdateOrderAsync(JsonPatchDocument<OrderUpdateRequest> updateOrderRequest, Guid orderId)
    {
        string checkoutServiceUrl = $"{CheckoutServiceBaseUrl}/order/{orderId}";
        var body = new StringContent(JsonConvert.SerializeObject(updateOrderRequest), Encoding.UTF8, "application/json");

        using (logger.BeginScope("UpdateOrderAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation("Calling checkout API to update order with id '{orderId}'.", orderId);

            var response = await client.PatchAsync(checkoutServiceUrl, body);

            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();

                logger.LogCritical("Error when calling checkout API update order with id '{orderId}'. Error was {StatusCode} {@responseBody}",
                    orderId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            logger.LogInformation("Updated order with id '{orderId}'.", orderId);
        }
    }

    public async Task UpdateOrderItemAsync(Guid orderItemId, JsonPatchDocument<OrderItemUpdateRequest> updateOrderItemRequest)
    {
        string checkoutServiceUrl = $"{CheckoutServiceBaseUrl}/orderItem/{orderItemId}";
        var body = new StringContent(JsonConvert.SerializeObject(updateOrderItemRequest), Encoding.UTF8, "application/json");

        using (logger.BeginScope("UpdateOrderItemAsync({@checkoutServiceUrl})", checkoutServiceUrl))
        {
            logger.LogInformation("Calling checkout API to update order item with id '{orderItemId}'.", orderItemId);

            var response = await client.PatchAsync(checkoutServiceUrl, body);

            if (!response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();

                logger.LogCritical("Error when calling checkout API update order item with id '{orderItemId}'. Error was {StatusCode} {@responseBody}",
                    orderItemId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            logger.LogInformation("Updated order item with id '{orderItemId}'.", orderItemId);
        }
    }
    public async Task<OrderConfigurationResponse> GetOrderConfigAsync(Guid orderId)
    {
        var url = $"{CheckoutServiceBaseUrl}/order/configuration/{orderId}";

        using (logger.BeginScope("GetOrderByIdAsync({@url})", url))
        {
            logger.LogInformation("Calling checkout service to get order with id {OrderId}", orderId);

            var response = await client.GetAsync(url);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service to get order. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            var order = Json.Deserialize<OrderConfigurationResponse>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            return order;
        }
    }
    public async Task<OrderConfigurationResponse> AddOrderConfigurationAsync(OrderConfigurationResponse orderConfiguration)
    {
        var url = $"{CheckoutServiceBaseUrl}/order/configuration";
        var requestBody = new StringContent(JsonConvert.SerializeObject(orderConfiguration), Encoding.UTF8, "application/json");

        using (logger.BeginScope("AddOrderConfigurationAsync({@url})", url))
        {
            logger.LogInformation($"Calling checkout service to add Order Configuration");

            var response = await client.PostAsync(url, requestBody);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling checkout service add order configurations to order" +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var orderConfig = Json.Deserialize<OrderConfigurationResponse>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            return orderConfig;
        }
    }
    public async Task<OrderConfigurationResponse> PatchOrderConfigAsync(Guid orderId, JsonPatchDocument<OrderConfigurationResponse> jsonPatchDocument)
    {
        var url = $"{CheckoutServiceBaseUrl}/order/configuration/?orderId={orderId}";
        var requestBody = new StringContent(JsonConvert.SerializeObject(jsonPatchDocument), Encoding.UTF8, "application/json");

        using (logger.BeginScope("PatchOrderConfigAsync({@url})", url))
        {
            logger.LogInformation("Calling checkout service to update order configuration with id '{orderId}'", orderId);

            var response = await client.PatchAsync(url, requestBody);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical(
                    "Error when calling checkout service update order configuration with id '{orderId}'. Error was {StatusCode} {@responseBody}",
                    orderId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            logger.LogInformation("Updated order configuration with id '{orderId}'.", orderId);
            return Json.Deserialize<OrderConfigurationResponse>(responseBody);
        }
    }
}