﻿using Common.Models.Shareholder;
using FluentValidation;
using System;

namespace Common.Validators;

public class MerchantIndividualLinkValidator : AbstractValidator<MerchantIndividualLink>
{
    public MerchantIndividualLinkValidator()
    {
        RuleFor(x => x.MerchantId)
            .Must(x => x != Guid.Empty)
            .WithErrorCode(Errors.InvalidMerchantIndividualLinkMerchant.Code)
            .WithMessage(Errors.InvalidMerchantIndividualLinkMerchant.Message);

        RuleFor(x => x.OwnershipPercentage)
            .Must(x => x >= 0 && x <= 100)
            .WithErrorCode(Errors.InvalidMerchantIndividualLinkOwnershipPercentage.Code)
            .WithMessage(Errors.InvalidMerchantIndividualLinkOwnershipPercentage.Message);
    }
}
