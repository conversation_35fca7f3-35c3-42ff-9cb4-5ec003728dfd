﻿using System;

namespace Common.Models.Merchant.SubordinateMerchant;

public class SubordinateMerchantSearchFilters
{
    public int Skip { get; set; } = 0;
    public int Take { get; set; } = int.MaxValue;
    public string? OrderBy { get; set; }
    public string? Sort { get; set; }

    public string? Keyword { get; set; }
    public string[]? SearchIn { get; set; }

    public Guid? MerchantId { get; set; }
    public Guid? ParentMerchantId { get; set; }
    public string? MerchantTag { get; set; }
    public string? ParentMerchantTag { get; set; }
    public bool IsAssociatedToParentMerchant { get; set; }
}