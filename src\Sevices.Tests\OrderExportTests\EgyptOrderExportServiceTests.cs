﻿using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Search;
using Common.Services;
using FluentAssertions;
using Moq;
using NUnit.Framework;
using Services.OrderExport;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests.OrderExportTests
{
    public class EgyptOrderExportServiceTests
    {
        private readonly EgyptOrderExportService orderExportService;
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();
        private readonly Mock<ISearchService> searchService = new Mock<ISearchService>();

        public EgyptOrderExportServiceTests()
        {
            orderExportService = new EgyptOrderExportService(referenceService.Object, searchService.Object);
        }

        private readonly Catalogue[] catalogueResponse = new Catalogue[] 
        {
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Banks,
                Key = "7",
                Value = "ALEXBANK"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.BusinessDomain,
                Key = "GASTRONOMY",
                Value = "Gastronomy"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Governorates,
                Key = "1",
                Value = "Cairo"
            },
            new Catalogue
            {
                CatalogueName = Constants.Catalogues.MerchantTag,
                Key = "RETAIL",
                Value = "Retail"
            },
            new Catalogue
            {
                CatalogueName = Constants.Catalogues.MerchantCategoryCode,
                Key = "5814",
                Value = "Fast Food Restaurants"
            },
            new Catalogue
            {
                CatalogueName = Constants.Catalogues.Cities,
                Key = "1",
                Value = "Mansoura"
            },
            new Catalogue
            {
                CatalogueName = Constants.Catalogues.AcquiringLedger,
                Key = "DEFAULT_BANK",
                Value = "Default"
            }
        };

        [Test]
        public async Task ExportByIdTest()
        {
            var orderId = Guid.NewGuid();
            var orders = new List<OrdersExport>() { 
                new OrdersExport() 
                {
                    OrderId = orderId,
                    BankAccounts = new List<BankAccount>(){new BankAccount()}
                }
            };
            
            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            
            var orderExport = await orderExportService.ExportOrderByIdAsync(orderId);

            orderExport.Should().NotBeNull();
        }

        [Test]
        public async Task ExportAllTest()
        {
            var orderId = Guid.NewGuid();
            var orderItemId = Guid.NewGuid();
            var productInstanceId = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = orderId,
                    CounterParty = "GEIDEA_EGYPT",
                    LegalName = "Business Name",
                    OrderNumber = "EX_123123",
                    OrderStatus="SUBMITTED",
                    MerchantStatus="VERIFIED",
                    MemberId = "22********",
                    PhoneNumber = "+************",
                    FirstName = "John",
                    LastName = "Doe",
                    NationalId = "**********",
                    Email = "<EMAIL>",
                    AddressLine1 = "Address",
                    RegistrationNumber = "***********",
                    MCC = "5814",
                    BusinessDomain = "GASTRONOMY",
                    MerchantType = "RETAIL",
                    Governorate = "1",
                    SalesPersonFirstName = "Ash",
                    AcquiringLedger ="DEFAULT_BANK",
                    SalesPersonLastName = "Keaton",
                    City = "1",
                    BankAccounts = new List<BankAccount>()
                    { 
                        new BankAccount()
                        {
                            AccountHolderName = "John Doe",
                            BankAccountNumber = "******************",
                            Iban = "IBAN",
                            RefBankId = 7
                        }
                    },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItemId,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType = "M_POS",
                                    ProductCode = "SUNMI_P2",
                                    OrderItemId = orderItemId,
                                    ProductInstanceId = productInstanceId,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"456978\"," +
                                            "\"FullTId\": \"FullTId\",\"ProviderBank\": \"DEFAULT_BANK\"," +
                                            "\"TId\": \"********\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstanceId
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
            ordersExport[0].BusinessName.Should().Be("Business Name");
            ordersExport[0].OrderNumber.Should().Be("EX_123123");
            ordersExport[0].OrderStatus.Should().Be("SUBMITTED");
            ordersExport[0].MerchantStatus.Should().Be("VERIFIED");
            ordersExport[0].MemberId.Should().Be("22********");
            ordersExport[0].Phone.Should().Be("+************");
            ordersExport[0].FirstName.Should().Be("John");
            ordersExport[0].LastName.Should().Be("Doe");
            ordersExport[0].NationalId.Should().Be("**********");
            ordersExport[0].Email.Should().Be("<EMAIL>");
            ordersExport[0].BusinessAddress.Should().Be("Address");
            ordersExport[0].CommercialRegistration.Should().Be("***********");
            ordersExport[0].MCC.Should().Be("5814");
            ordersExport[0].MerchantType.Should().Be("Retail");
            ordersExport[0].Governorate.Should().Be("Cairo");
            ordersExport[0].SalesPersonFirstName.Should().Be("Ash");
            ordersExport[0].SalesPersonLastName.Should().Be("Keaton");
            ordersExport[0].AccountHolderName.Should().Be("John Doe");
            ordersExport[0].BankAccountNumber.Should().Be("******************");
            ordersExport[0].IBAN.Should().Be("IBAN");
            ordersExport[0].BankName.Should().Be("ALEXBANK");
            ordersExport[0].Quantity.Should().Be(1);
            ordersExport[0].ProductExport[0].ProductCode.Should().Be("SUNMI_P2");
            ordersExport[0].ProductExport[0].TID.Should().Be("********");
            ordersExport[0].ProductExport[0].MID.Should().Be("456978");
            ordersExport[0].City.Should().Be("Mansoura");
            ordersExport[0].Acquirer.Should().Be("Default");
        }

        [Test]
        public async Task ExportAllTest_WithNoProductInstance()
        {
            var orderId = Guid.NewGuid();
            var orderItemId = Guid.NewGuid();
            var productInstanceId = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = orderId,
                    CounterParty = "GEIDEA_EGYPT",
                    LegalName = "Business Name",
                    OrderNumber = "EX_123123",
                    MemberId = "22********",
                    PhoneNumber = "+************",
                    FirstName = "John",
                    LastName = "Doe",
                    NationalId = "**********",
                    Email = "<EMAIL>",
                    AddressLine1 = "Address",
                    RegistrationNumber = "***********",
                    MCC = "5814",
                    BusinessDomain = "GASTRONOMY",
                    MerchantType = "S",
                    Governorate = "1",
                    BankAccounts = new List<BankAccount>()
                    {
                        new BankAccount()
                        {
                            AccountHolderName = "John Doe",
                            BankAccountNumber = "******************",
                            Iban = "IBAN",
                            RefBankId = 7
                        }
                    },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItemId,
                            ProductCode = "GO_SMART"
                        }
                    }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
            ordersExport[0].Quantity.Should().Be(1);
        }

        [Test]
        public async Task ExportAllTest_WithSupportedProductType()
        {
            var orderId = Guid.NewGuid();
            var orderItemId = Guid.NewGuid();
            var productInstanceId = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = orderId,
                    CounterParty = "GEIDEA_EGYPT",
                    LegalName = "Business Name",
                    OrderNumber = "EX_123123",
                    MemberId = "22********",
                    PhoneNumber = "+************",
                    FirstName = "John",
                    LastName = "Doe",
                    NationalId = "**********",
                    Email = "<EMAIL>",
                    AddressLine1 = "Address",
                    RegistrationNumber = "***********",
                    MCC = "5814",
                    BusinessDomain = "GASTRONOMY",
                    MerchantType = "S",
                    Governorate = "1",
                    BankAccounts = new List<BankAccount>()
                    {
                        new BankAccount()
                        {
                            AccountHolderName = "John Doe",
                            BankAccountNumber = "******************",
                            Iban = "IBAN",
                            RefBankId = 7
                        }
                    },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItemId,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType = "GWAY",
                                    ProductCode = "PAYMENT_GATEWAY",
                                    OrderItemId = orderItemId,
                                    ProductInstanceId = productInstanceId,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"456978\"," +
                                            "\"FullTId\": \"FullTId\",\"ProviderBank\": \"DEFAULT_BANK\"," +
                                            "\"TId\": \"********\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstanceId
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
            ordersExport[0].Quantity.Should().Be(1);
        }

        [Test]
        public async Task ExportAllTest_WithProductInstanceChildren()
        {
            var orderId = Guid.NewGuid();
            var orderItemId = Guid.NewGuid();
            var productInstanceId = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = orderId,
                    CounterParty = "GEIDEA_EGYPT",
                    LegalName = "Business Name",
                    OrderNumber = "EX_123123",
                    MemberId = "22********",
                    PhoneNumber = "+************",
                    FirstName = "John",
                    LastName = "Doe",
                    NationalId = "**********",
                    Email = "<EMAIL>",
                    AddressLine1 = "Address",
                    RegistrationNumber = "***********",
                    MCC = "5814",
                    BusinessDomain = "GASTRONOMY",
                    MerchantType = "S",
                    Governorate = "1",
                    BankAccounts = new List<BankAccount>()
                    {
                        new BankAccount()
                        {
                            AccountHolderName = "John Doe",
                            BankAccountNumber = "******************",
                            Iban = "IBAN",
                            RefBankId = 7
                        }
                    },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItemId,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType = "M_POS",
                                    ProductCode = "SUNMI_P2",
                                    OrderItemId = orderItemId,
                                    ProductInstanceId = productInstanceId,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"456978\"," +
                                            "\"FullTId\": \"FullTId\",\"ProviderBank\": \"DEFAULT_BANK\"," +
                                            "\"TId\": \"********\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstanceId,
                                            ProductType = "SCHEME",
                                            ProductCode = "VISA"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
            ordersExport[0].Quantity.Should().Be(1);
        }

        [Test]
        public async Task ExportAllTest_WithTerminTIDSearch()
        {
            var orderId = Guid.NewGuid();
            var orderItemId = Guid.NewGuid();
            var productInstanceId = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = orderId,
                    CounterParty = "GEIDEA_EGYPT",
                    LegalName = "Business Name",
                    OrderNumber = "EX_123123",
                    MemberId = "22********",
                    PhoneNumber = "+************",
                    FirstName = "John",
                    LastName = "Doe",
                    NationalId = "**********",
                    Email = "<EMAIL>",
                    AddressLine1 = "Address",
                    RegistrationNumber = "***********",
                    MCC = "5814",
                    BusinessDomain = "GASTRONOMY",
                    MerchantType = "S",
                    Governorate = "1",
                    BankAccounts = new List<BankAccount>()
                    {
                        new BankAccount()
                        {
                            AccountHolderName = "John Doe",
                            BankAccountNumber = "******************",
                            Iban = "IBAN",
                            RefBankId = 7
                        }
                    },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItemId,
                            ProductCode = "GO_SMART"
                        }
                    }
                }
            };

            var orderSearchCriteria = new OrderSearchCriteria { Keyword = "4515", SearchIn = new List<string> { Constants.OrderSearchProperties.TerminalTID } };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(orderSearchCriteria)).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(orderSearchCriteria);

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
            ordersExport[0].Quantity.Should().Be(1);
        }


        [Test]
        public async Task ExportAllTest_WithStoreCitySearch()
        {
            var orderId = Guid.NewGuid();
            var orderItemId = Guid.NewGuid();
            var productInstanceId = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = orderId,
                    CounterParty = "GEIDEA_EGYPT",
                    LegalName = "Business Name",
                    OrderNumber = "EX_123123",
                    MemberId = "22********",
                    PhoneNumber = "+************",
                    FirstName = "John",
                    LastName = "Doe",
                    NationalId = "**********",
                    Email = "<EMAIL>",
                    AddressLine1 = "Address",
                    RegistrationNumber = "***********",
                    MCC = "5814",
                    BusinessDomain = "GASTRONOMY",
                    MerchantType = "S",
                    Governorate = "1",
                    StoreCity = "1",
                    BankAccounts = new List<BankAccount>()
                    {
                        new BankAccount()
                        {
                            AccountHolderName = "John Doe",
                            BankAccountNumber = "******************",
                            Iban = "IBAN",
                            RefBankId = 7
                        }
                    },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItemId,
                            ProductCode = "GO_SMART"
                        }
                    }
                }
            };

            var orderSearchCriteria = new OrderSearchCriteria { Keyword = "1", SearchIn = new List<string> { Constants.OrderSearchProperties.StoreCity } };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(orderSearchCriteria)).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(orderSearchCriteria);

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
            ordersExport[0].Quantity.Should().Be(1);
        }
    }
}
