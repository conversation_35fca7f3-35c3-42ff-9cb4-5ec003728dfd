﻿namespace Common.Tests.InputModels;

public static class ShareholderIndividualCreateRequestInput
{
    public static readonly object[] InvalidDOB =
{
       new object[] {
          new ShareholderIndividualCreateRequest()
       },
        new object[] {
          new ShareholderIndividualCreateRequest { DOB = DateTime.MinValue }
       },
        new object[] {
          new ShareholderIndividualCreateRequest { DOB = DateTime.MaxValue }
       }
    };

    public static readonly object[] InvalidAddress =
{
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Saudi,
              Address = new ShareholderIndividualAddress
              {
                  Governorate = "not empty guv on saudi"
              }
          },
          "Address.Governorate",
          Errors.DisabledProperty
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Saudi,
              Address = new ShareholderIndividualAddress
              {
                  Area = " "
              }
          },
          "Address.Area",
          Errors.AreaLengthValidation
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Saudi,
              Address = new ShareholderIndividualAddress
              {
                  Area = string.Concat(Enumerable.Repeat("xyz", 200))
}
          },
          "Address.Area",
          Errors.AreaLengthValidation
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Saudi,
              Address = new ShareholderIndividualAddress
              {
                  City = " "
              }
          },
          "Address.City",
          Errors.CityLengthValidation
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Saudi,
              Address = new ShareholderIndividualAddress
              {
                  City = string.Concat(Enumerable.Repeat("xyz", 200))
                }
          },
          "Address.City",
          Errors.CityLengthValidation
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Egypt,
              Address = new ShareholderIndividualAddress
              {
                  Area = "not empty Area on saudi"
              }
          },
          "Address.Area",
          Errors.DisabledProperty
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Egypt,
              Address = new ShareholderIndividualAddress
              {
                  Governorate = " "
              }
          },
          "Address.Governorate",
          Errors.GovernorateLengthValidation
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Egypt,
              Address = new ShareholderIndividualAddress
              {
                  Governorate = string.Concat(Enumerable.Repeat("xyz", 200))
               }
          },
          "Address.Governorate",
          Errors.GovernorateLengthValidation
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Egypt,
              Address = new ShareholderIndividualAddress
              {
                  City = " "
              }
          },
          "Address.City",
          Errors.CityLengthValidation
       },
       new object[] {
          new ShareholderIndividualCreateRequest
          {
              Counterparty = Constants.CounterParty.Egypt,
              Address = new ShareholderIndividualAddress
              {
                  City = string.Concat(Enumerable.Repeat("xyz", 200))
              }
          },
          "Address.City",
          Errors.CityLengthValidation
       }
    };
}
