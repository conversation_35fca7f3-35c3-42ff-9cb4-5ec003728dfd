﻿using Common.Models.Account;
using Common.Models;
using Common.Services;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Common.Options;
using Constants = Common.Constants;
using Common.Models.Chain;
using System.Text.Json;
using FluentAssertions;
using Common.Models.Comment;
using Common.Models.Merchant;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests;

public class ChainServiceTests
{
    private readonly Mock<ILogger<ChainService>> logger = new Mock<ILogger<ChainService>>();
    private ChainService chainService = null!;
    private readonly Mock<ISearchService> searchService = new Mock<ISearchService>();
    private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
    private static readonly DateTime createdDate = DateTime.UtcNow;
    private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();

    public ChainServiceTests()
    {
        urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
    }


    private readonly Catalogue[] catalogueResponse = new Catalogue[]
    {
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Segment,
                Key = "TEST",
                Value = "Test"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.RelationToCompany,
                Key = "TEST",
                Value = "test"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Designation,
                Key = "TEST",
                Value = "test"
            }

    };

    private readonly ChainSearchFilters chainSearchFilters = new ChainSearchFilters()
    {
        ChainId = "C1000000002",
        ChainName = "Chain name",
        DateInterval = new DateInterval()
        {
            FromDate = DateTime.UtcNow,
            ToDate = DateTime.UtcNow
        }
    };

    private static readonly ChainResult chainResult = new ChainResult()
    {
        Id = 1,
        ChainId = "C1000000002",
        ChainName = "Chain name",
        Segment = "test",
        ContactPerson = "test id",
        LinkedBusinesses = 2,
        CreatedBy = "test",
        CreatedDate = createdDate
    };

    private static readonly ChainExportResult chainExport = new ChainExportResult()
    {
        ChainId = "C1000000002",
        ChainName = "Chain name",
        Segment = "test",
        ContactPerson = "test id",
        LinkedBusinesses = 2,
        CreatedBy = "test",
        CreatedDate = createdDate
    };

    private ChainCreateRequest chainCreateRequest = new ChainCreateRequest()
    {
        ChainName = "test",
        Segment = "TEST",
        RelationshipManager = "TEST",
        ChainAddress = new ChainAddressRequest()
        {
            BuildingName = "test",
            BuildingNumber = "test",
            City = "test",
            Country = "test",
            Zip = "test",
            AddressLine1 = "test",
            AddressLine2 = "test"
        }
    };

    private ChainUpdateRequest chainUpdateRequest = new ChainUpdateRequest()
    {
        ChainId = "test",
        ChainName = "test",
        Segment = "TEST",
        RelationshipManager = "TEST",
        ChainAddress = new ChainAddressRequest()
        {
            AddressId = new Guid(),
            BuildingName = "test",
            BuildingNumber = "test",
            City = "test",
            Country = "test",
            Zip = "test",
            AddressLine1 = "test",
            AddressLine2 = "test"
        }
    };

    private readonly Chain chainResponse = new Chain()
    {
        Id = 1,
        ChainId = "test",
        ChainName = "test",
        Segment = "TEST",
        RelationshipManager = "TEST",
        ChainAddress = new ChainAddress()
        {
            AddressId = new Guid(),
            BuildingName = "test",
            BuildingNumber = "test",
            City = "test",
            Country = "test",
            Zip = "test",
            AddressLine1 = "test",
            AddressLine2 = "test",
            CreatedBy = "test",
            UpdatedBy = "test",
        }
    };

    private readonly ChainSearchResponse<ChainResult> chainSearchResponse = new ChainSearchResponse<ChainResult>()
    {
        Records = new List<ChainResult>()
            {
               chainResult
            },
        ReturnedRecordCount = 1,
        TotalRecordCount = 1
    };

    private readonly ChainExportResponse<ChainExportResult> chainExportResponse = new ChainExportResponse<ChainExportResult>()
    {
        Records = new List<ChainExportResult>()
        {
            chainExport
        },
        ReturnedRecordCount = 1,
        TotalRecordCount = 1
    };

    private readonly List<ChainMerchantsLinkRequest> merchantsLinkRequest = new List<ChainMerchantsLinkRequest>()
    {
        new ChainMerchantsLinkRequest()
        {
            MerchantId = new Guid(),
            IsLinked = true,
        }
    };

    private List<Merchant> merchantsLinkResponse = new List<Merchant>()
    {
        new Merchant()
        {
            MerchantId = new Guid(),
            MerchantDetails = new MerchantDetails()
            {
                MerchantId = new Guid(),
                LinkedChainId = "test",
                LinkedChainName = "test",
            }
        }
    };

    private ChainContactsCreateRequest chainContactsCreateRequest = new ChainContactsCreateRequest()
    {
        ChainId = "test",
        ChainContacts = new List<ChainContactModel>
        {
            new ChainContactModel()
            {
                Email = "<EMAIL>",
                CountryPrefix = "test",
                ContactName = "test",
                IsSpoc = true,
                PhoneNumber = "test",
                RoleDesignation = "TEST",
            }
        }
    };

    private ChainContactsUpdateRequest chainContactsUpdateRequest = new ChainContactsUpdateRequest()
    {
        ChainId = "test",
        ChainContacts = new List<ChainContactModel>
        {
            new ChainContactModel()
            {
                ChainContactId = Guid.NewGuid(),
                Email = "<EMAIL>",
                CountryPrefix = "test",
                ContactName = "test",
                IsSpoc = true,
                PhoneNumber = "test",
                RoleDesignation = "TEST",
            }
        }
    };

    private List<ChainContact> chainContactResponse = new List<ChainContact>() {
        new ChainContact() {
            ChainContactId = Guid.NewGuid(),
             ChainIdentityId = 1,
                Email = "test",
                CountryPrefix = "test",
                ContactName = "test",
                IsSpoc = true,
                PhoneNumber = "test",
                RoleDesignation = "TEST",
                CreatedBy = "test",
                UpdatedBy = "test"
        }
    };

    private ChainService GetChainService(HttpClient httpClient)
    {
        return new ChainService(
            logger.Object,
            urlSettingsOptions.Object,
            referenceService.Object,
            httpClient);
    }

    [Test]
    public async Task ExportChains()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainExportResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
        var result = await chainService.ExportChainsAsync(chainSearchFilters);

        result.Count.Should().Be(1);
        result.Should().BeEquivalentTo(MapChainsForExport(chainExportResponse.Records, catalogueResponse));
    }

    private static List<ChainExport> MapChainsForExport(IList<ChainExportResult> chains, Catalogue[] catalogues)
    {
        var chainsExport = new List<ChainExport>();

        foreach (var chain in chains)
        {

            ChainExport chExport = new ChainExport();

            chExport.ChainId = chain.ChainId;
            chExport.ChainName = chain.ChainName;
            chExport.LinkedBusinesses = chain.LinkedBusinesses.HasValue ? chain.LinkedBusinesses.Value.ToString() : "";
            chExport.ContactPerson = chain.ContactPerson;
            chExport.Segment = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.Segment && x.Key == chain.Segment)?.Value;
            chExport.CreatedBy = chain.CreatedBy;
            chExport.CreatedDate = chain.CreatedDate.ToString() ?? string.Empty;

            chainsExport.Add(chExport);
        }

        return chainsExport;
    }


    [Test]
    public async Task FindAsync()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainSearchResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
        var result = await chainService.FindAsync(chainSearchFilters);

        result.Records.Count.Should().Be(1);
        result.Records[0].Should().BeEquivalentTo(chainResult);
    }

    [Test]
    public void FindAsync_PassthroughError()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
        chainService.Invoking(x => x.FindAsync(chainSearchFilters))
            .Should().ThrowAsync<PassthroughException>()
                    .Where(x => x.StatusCode == HttpStatusCode.NotFound);
    }

    [Test]
    public async Task CreatChain()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
        var result = await chainService.CreateChain(chainCreateRequest);

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(chainResponse);
    }

    [Test]
    public async Task UpdateChain()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        var result = await chainService.UpdateChain(chainUpdateRequest);

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(chainResponse);
    }

    [Test]
    public async Task DeleteChain()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.NoContent));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        await chainService.DeleteChain("test");
    }

    [Test]
    public async Task UpdateChainMerchants()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantsLinkResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        var result = await chainService.AssociateChainMerchants("test", merchantsLinkRequest);

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(merchantsLinkResponse);
    }

    [Test]
    public async Task GetChainMerchants()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchantsLinkResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        var result = await chainService.GetChainMerchants("test");

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(merchantsLinkResponse);
    }

    [Test]
    public async Task CreateChainContacts()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainContactResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        var result = await chainService.CreateChainContacts(chainContactsCreateRequest);

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(chainContactResponse);
    }

    [Test]
    public async Task UpdateChainContacts()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainContactResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        var result = await chainService.UpdateChainContacts(chainContactsUpdateRequest);

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(chainContactResponse);
    }

    [Test]
    public async Task GetChainContacts()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainContactResponse)));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        var result = await chainService.GetAllContactsOfChain("test");

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(chainContactResponse);
    }

    [Test]
    public async Task GetChainContact()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(chainContactResponse[0])));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        var result = await chainService.GetContactById(new Guid());

        Assert.NotNull(result);
        result.Should().BeEquivalentTo(chainContactResponse[0]);
    }

    [Test]
    public async Task DeleteChainContact()
    {
        chainService = GetChainService(TestsHelper.CreateHttpClient(HttpStatusCode.NoContent));
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));

        await chainService.DeleteChainContact(new Guid());
    }
}
