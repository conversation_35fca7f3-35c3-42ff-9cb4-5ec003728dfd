﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.NexusBridge
{
    public class Address
    {
        public Guid AddressId { get; set; }


        public int? CityKey { get; set; }
        public string? Governorate { get; set; }
        public string? Area { get; set; }
        public string? POBox { get; set; }
        public string? Email { get; set; }
        public string? Url { get; set; }
        public string? AddressType { get; set; }
        public bool IsDefaultAddress { get; set; }
        public string? Purpose { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }
        public string? OfficeLocationType { get; set; } = null;
        public string? AddressPin { get; set; } = null;
        public bool? IsBranchSelected { get; set; } = false;


        public string? Street { get; set; }
        public string? Zip { get; set; }
        public string? Country { get; set; }
        public string? City { get; set; }
        public string? District { get; set; }
        public string? Coordinates { get; set; }




    }

}
