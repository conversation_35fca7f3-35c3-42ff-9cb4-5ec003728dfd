﻿using Common.Models.Checkout;
using CsvHelper.Configuration;

namespace BackofficeApi.Formatters.ClassMappers
{
    public class ProductExportResponseMap : ClassMap<ProductExportResponse>
    {
        public ProductExportResponseMap()
        {
            Map(m => m.AccountNo).Name("Account Number");
            Map(m => m.BankName).Name("Bank Name");
            Map(m => m.MCCName).Name("MCC Name");
            Map(m => m.MID).Name("MID");
            Map(m => m.FullTID).Name("Full TID");
            Map(m => m.POSDataCode).Name("TRSM(POS Data Code)");
            Map(m => m.TID).Name("TID");
            Map(m => m.ProductCode).Name("Product Name");
            Map(m => m.CardSchema).Name("Card Schema");
            Map(m => m.MerchantName).Name("Merchant Name in English");
            Map(m => m.TerminalModel).Name("Terminal Model");
            Map(m => m.ProductId).Ignore();
        }
    }
}
