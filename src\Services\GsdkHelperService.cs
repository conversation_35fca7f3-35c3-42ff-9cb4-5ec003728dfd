﻿using Common;
using Common.Models;
using Common.Models.Product;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Services
{
    public class GsdkHelperService : IGsdkHelperService
    {
        private readonly ILogger<GsdkHelperService> logger;

        public GsdkHelperService(ILogger<GsdkHelperService> logger)
        {
            this.logger = logger;
        }

        public string GetLedgerKey(ProductInstance productInstance, Catalogue[] ledgerMappingCatalogues)
        {
            var ledgerKey = (productInstance.Metadata as TerminalData)?.ProviderBank ?? ledgerMappingCatalogues.FirstOrDefault(c =>
                c.CatalogueName == Constants.Catalogues.ProductTypeDefaultLedgerMapping &&
                c.Key == productInstance.Product.Type)?.Value;

            if (ledgerKey == null)
            {
                var productInstanceId = productInstance.ProductInstanceId;
                logger.LogError("GSDK: No provider bank found for product instance {@productInstanceId}", productInstanceId);
                throw new ServiceException(Errors.LedgerMappingNotFound);
            }

            return ledgerKey;
        }

        public string GetLedgerValue(Catalogue[] ledgerMappingCatalogues, ProductInstance firstProductInstanceChildren)
        {
            var key = GetLedgerKey(firstProductInstanceChildren, ledgerMappingCatalogues);

            var ledgerValue = ledgerMappingCatalogues.FirstOrDefault(c =>
                c.CatalogueName == Constants.Catalogues.AcquiringLedgerToGsdkMapping &&
                c.Key == key!)?.Value;

            if (ledgerValue == null)
            {
                logger.LogError("GSDK: No AcquiringLedgerToGsdkMapping value was found for ledger {@key}", key);
                throw new ServiceException(Errors.LedgerMappingNotFound);
            }
            return ledgerValue;
        }
    }
}
