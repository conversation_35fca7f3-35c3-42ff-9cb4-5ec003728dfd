﻿using System;
using System.Collections.Generic;

namespace Common.Models.Shareholder;

public class ShareholderIndividualCreateRequest : BaseShareholderModel
{
    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public DateTime? DOB { get; set; }

    public string? NationalId { get; set; }

    public DateTime? IdExpiryDate { get; set; }

    public string? Nationality { get; set; }

    public string? PassportNo { get; set; }

    public DateTime? PassportExpirationDate { get; set; }

    public string? KYCCheck { get; set; } = "PENDING";

    public string? PhonePrefix { get; set; }

    public string? PhoneNumber { get; set; }

    public bool? PEP { get; set; } = false;

    public ShareholderIndividualAddress? Address { get; set; }

    public MerchantIndividualLink? Merchant { get; set; } = null!;

    public List<ShareholderCompanyIndividualLink>? ShareholderCompanies { get; set; } = null!;
}
