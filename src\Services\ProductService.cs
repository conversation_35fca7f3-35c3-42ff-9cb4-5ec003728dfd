﻿using Geidea.Utils.Json;

namespace Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using System.Text.Json;
    using System.Threading.Tasks;
    using Common.Helpers;
    using Common.Models;
    using Common.Models.Gle;
    using Common.Models.Product;
    using Common.Models.ProductInstance;
    using Common.Models.TerminalDataSet;
    using Common.Options;
    using Common.Services;
    using Geidea.Utils.Exceptions;
    using Microsoft.AspNetCore.JsonPatch;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Newtonsoft.Json;

    public class ProductService : IProductService
    {
        private readonly HttpClient client;
        private readonly ILogger<ProductService> logger;
        private readonly UrlSettings urlSettingsOptions;

        public ProductService(
            ILogger<ProductService> logger,
            IOptionsMonitor<UrlSettings> urlSettingsOptions,
            HttpClient client)
        {
            this.logger = logger;
            this.client = client;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
        }

        private string ProductServiceBaseUrl => $"{urlSettingsOptions.ProductServiceBaseUrlNS}/api/v1";
        private const string TerminalDataSetEndpoint = "/TerminalDataSet";

        public async Task<List<Product>> FindProductsAsync(FindProductRequest findProductRequest)
        {
            string url = UrlBuilder.AddQueryParams($"{ProductServiceBaseUrl}/Product", findProductRequest);

            using (logger.BeginScope("FindProductsAsync({@url})", url))
            {
                logger.LogInformation($"Calling product service to find products.");

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling product service to find products. Error was {response.StatusCode} {@responseBody}.");

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<Product>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<Product> GetProductByIdAsync(Guid productId)
        {
            string productServiceUrl = $"{ProductServiceBaseUrl}/product/?Id={productId}";

            using (logger.BeginScope("GetProductById({@productServiceUrl})", productServiceUrl))
            {
                logger.LogInformation("Calling product API to get product by id");

                var response = await client.GetAsync(productServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling product API to get product by id. Error was {response.StatusCode} {responseBody}");
                    throw new PassthroughException(response);
                }
                var products = Json.Deserialize<List<Product>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                return products.FirstOrDefault()!;
            }
        }

        public async Task<List<ProductInstance>> GetProductInstances(List<Guid> productInstancesIds)
        {
            string productServiceUrl = $"{ProductServiceBaseUrl}/productInstance/search";
            var findProductInstancesRequest = new FindProductInstanceRequest
            {
                ProductInstanceId = productInstancesIds.ToArray()
            };
            var requestBody = new StringContent(JsonConvert.SerializeObject(findProductInstancesRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetProductInstances({@productServiceUrl})", productServiceUrl))
            {
                logger.LogInformation("Calling product API to get product instances by id");

                var response = await client.PostAsync(productServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling product API to get product instances by id. Error was {response.StatusCode} {responseBody}");
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<ProductInstance>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<ProductInstance[]> GetProductInstancesByIdsAsync(Guid[] ids)
        {
            string url = $"{ProductServiceBaseUrl}/ProductInstance/Ids";
            var body = new StringContent(JsonConvert.SerializeObject(new IdsRequest { Ids = ids }), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetProductInstancesByIdsAsync({@url})", url))
            {
                logger.LogInformation($"Calling product service to get multiple product instances.");

                var response = await client.PostAsync(url, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling product service to get multiple product instances. Error was {response.StatusCode} {@responseBody}");

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<ProductInstance[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<Product[]> GetProductsByIdsAsync(Guid[] ids)
        {
            string url = $"{ProductServiceBaseUrl}/Product/Ids";
            var body = new StringContent(JsonConvert.SerializeObject(new IdsRequest { Ids = ids }), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetProductsByIdsAsync({@url})", url))
            {
                logger.LogInformation($"Calling product service to get multiple products.");

                var response = await client.PostAsync(url, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling product service to get multiple products. Error was {response.StatusCode} {@responseBody}");

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<Product[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<Guid[]> GetRelatedProductsAsync(ProductCodesRequest productCodesRequest)
        {
            string url = $"{ProductServiceBaseUrl}/Product/related";
            var body = new StringContent(JsonConvert.SerializeObject(productCodesRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetRelatedProductsAsync({@url})", url))
            {
                logger.LogInformation($"Calling product service to get all products based on product codes.");

                var response = await client.PostAsync(url, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling product service to get all products based on codes. Error was {response.StatusCode} {@responseBody}");

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<Guid[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<ProductInstance> PatchAsync(Guid productInstanceId, JsonPatchDocument<UpdateProductInstanceRequest> patchDocument)
        {
            string url = $"{ProductServiceBaseUrl}/ProductInstance/{productInstanceId}";
            var body = new StringContent(JsonConvert.SerializeObject(patchDocument), Encoding.UTF8, "application/json");

            using (logger.BeginScope("PatchAsync({@url})", url))
            {
                logger.LogInformation($"Calling product service to update product instance with id '{productInstanceId}'.");

                var response = await client.PatchAsync(url, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling product service to update product instance with id '{productInstanceId}'. Error was {response.StatusCode} {@responseBody}");

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"Updated product instance with id '{productInstanceId}'.");
                return Json.Deserialize<ProductInstance>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<BillPaymentServiceAndBundleFlags> GetBpProductTypesInListOfProducts(IdsRequest ids)
        {
            var requestUri = $"{ProductServiceBaseUrl}/Product/hasBillPayment/ids";
            var body = new StringContent(JsonConvert.SerializeObject(ids), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetBpProductTypesInListOfProducts({@url})", requestUri))
            {
                logger.LogInformation("Calling ProductService to get what bill payment product types does a list products contains");

                var response = await client.PostAsync(requestUri, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling ProductService product service to get what bill payment product types does a list products contains. Error was {statusCode} {@responseBody}", response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<BillPaymentServiceAndBundleFlags>(responseBody);
            }
        }

        public async Task<List<TerminalDataSetResponse>> GenerateTIDAndMIDAndAddEditTerminalDataSets(TerminalDataSetRequest terminalDataSetsRequest)
        {
            var requestUri = $"{ProductServiceBaseUrl}/TerminalDataSet/updateTerminalDatasetsTIDAndMID";
            var body = new StringContent(JsonConvert.SerializeObject(terminalDataSetsRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GenerateTIDAndMIDAndAddEditTerminalDataSets({@url})", requestUri))
            {
                logger.LogInformation("Calling ProductService to generate TID and MID for '{acquiringLedger}' and order number '{orderNumber}' and store Id '{storeId}'", terminalDataSetsRequest?.AcquiringLedger, terminalDataSetsRequest?.OrderNumber, terminalDataSetsRequest?.StoreId);

                var response = await client.PostAsync(requestUri, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling GenerateTIDAndMIDAndAddEditTerminalDataSets to generate and add/update terminal datasets TID and MID for '{acquiringLedger}' and order number '{orderNumber}' and store Id '{storeId}'. Error was {statusCode} {responseBody}", terminalDataSetsRequest?.AcquiringLedger, terminalDataSetsRequest?.OrderNumber, terminalDataSetsRequest?.StoreId, response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<TerminalDataSetResponse>>(responseBody,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
            }
        }

        public async Task<List<Guid>> UpdateTerminalProductInstancesMeta(List<UpdateProductInstanceMetaRequest> updateProductInstancesMetaRequest)
        {
            var requestUri = $"{ProductServiceBaseUrl}/ProductInstance/updateTerminalProductInstancesMeta";
            var body = new StringContent(JsonConvert.SerializeObject(updateProductInstancesMetaRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateTerminalProductInstancesMeta({@url})", requestUri))
            {
                logger.LogInformation("Calling ProductService to update product instance meta for '{acquiringLedger}'", updateProductInstancesMetaRequest?.FirstOrDefault()?.ProviderBank);

                var response = await client.PatchAsync(requestUri, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling UpdateTerminalProductInstancesMeta to update product instance meta for '{acquiringLedger}'. Error was {statusCode} {responseBody}", updateProductInstancesMetaRequest?.FirstOrDefault()?.ProviderBank, response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<Guid>>(responseBody,
                        new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });
            }
        }

        public async Task<List<TerminalDataSetResponse>> UpdateOrderTerminalDataSetsMcc(TerminalDataRequestMcc terminalDataSetsRequest)
        {
            var requestUri = $"{ProductServiceBaseUrl}{TerminalDataSetEndpoint}/updateTerminalDataSetMcc";
            var body = new StringContent(JsonConvert.SerializeObject(terminalDataSetsRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("AddUpdateTerminalDataSetByMcc({@url})", requestUri))
            {
                logger.LogInformation($"Calling product service to add or update terminal data set with MCC.");

                var response = await client.PostAsync(requestUri, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling ProductService to add or update terminal data set with MCC. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Geidea.Utils.Json.Json.Deserialize<List<TerminalDataSetResponse>>(responseBody);
            }
        }

        public async Task<bool> IsMerchantRegisteredInGle(Guid merchantId)
        {
            var url = $"{ProductServiceBaseUrl}/Gle/merchant/isRegisteredInGle/{merchantId}";

            using (logger.BeginScope("IsMerchantRegisteredInGle({@url})", url))
            {
                logger.LogInformation("Calling product service for merchant with id '{merchantId}'.", merchantId);

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling product service for merchant with id {merchantId}. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation($"Received response.");

                return Json.Deserialize<bool>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<MerchantInquiryResponse> ValidateTerminalRegistration(string mId)
        {
            var url = $"{ProductServiceBaseUrl}/TerminalDataSet/getMerchantIdTerminalId/{mId}";

            using (logger.BeginScope("getMerchantIdTerminalId({@url})", url))
            {
                logger.LogInformation("Calling merchant inquiry service for merchant with mid '{mId}'.", mId);

                var response = await client.PostAsync(url, null!);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant inquiry service for merchant with mid {mId}. Error was {StatusCode} {@responseBody}",
                        mId, (int)response.StatusCode, responseBody);

                    return new MerchantInquiryResponse();
                }

                logger.LogInformation($"Received response.");

                return Geidea.Utils.Json.Json.Deserialize<MerchantInquiryResponse>(responseBody);

            }
        }
    }
}