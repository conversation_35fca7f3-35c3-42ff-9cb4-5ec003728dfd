﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Models.Merchant;
using Common.Models.Merchant.SubordinateMerchant;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;

namespace Services;

public class SubordinateMerchantService : ISubordinateMerchantService
{
    private readonly IMapper mapper;
    private readonly ILogger<ISubordinateMerchantService> logger;
    private readonly IMerchantClient merchantClient;
    private readonly ISearchService searchService;
    private readonly IProductService productService;

    public SubordinateMerchantService(
        IMapper mapper,
        ILogger<ISubordinateMerchantService> logger,
        IMerchantClient merchantClient,
        ISearchService searchService,
        IProductService productService)
    {
        this.mapper = mapper;
        this.logger = logger;
        this.productService = productService;
        this.merchantClient = merchantClient;
        this.searchService = searchService;
    }

    public async Task<SubordinateMerchantSearchResponse> FindAssociatedAndAvailableSubordinateMerchants(Guid merchantId, SubordinateMerchantSearchRequestDto request)
    {
        var merchant = await merchantClient.GetCoreMerchantWithHierarchy(merchantId);
        var merchantTag = GetTagToFilterSubordinates(merchant.Tag);

        var filters = mapper.Map<SubordinateMerchantSearchFilters>(request);
        filters.ParentMerchantId = merchantId;
        filters.MerchantTag = merchantTag;

        var merchants = await searchService.SearchSubordinateMerchants(filters);
        return merchants;
    }

    public async Task<SubordinateMerchantSearchResponse> FindAssociatedSubordinateMerchants(Guid merchantId, SubordinateMerchantSearchRequestDto request)
    {
        var merchant = await merchantClient.GetCoreMerchantWithHierarchy(merchantId);
        var merchantTag = GetTagToFilterSubordinates(merchant.Tag);

        var filters = mapper.Map<SubordinateMerchantSearchFilters>(request);
        filters.ParentMerchantId = merchantId;
        filters.MerchantTag = merchantTag;
        filters.IsAssociatedToParentMerchant = true;

        var merchants = await searchService.SearchSubordinateMerchants(filters);
        return merchants;
    }

    public async Task<SubordinateMerchant?> FindAssociatedParent(Guid merchantId)
    {
        var merchant = await merchantClient.GetCoreMerchantWithHierarchy(merchantId);
        var merchantTag = GetTagToFilterParent(merchant.Tag);
        if (merchantTag == string.Empty)
        {
            return null;
        }

        var parentMerchantId = merchant.Hierarchies
            .Where(h => h.MerchantId == merchantId && h.HierarchyType == Constants.HierarchyType.Business)
            .Select(h => h.ParentMerchantId)
            .SingleOrDefault();

        if (parentMerchantId == Guid.Empty)
        {
            return null;
        }

        var filters = new SubordinateMerchantSearchFilters()
        {
            MerchantId = parentMerchantId,
            MerchantTag = merchantTag
        };

        var parentMerchant = await searchService.SearchSubordinateMerchants(filters);
        return parentMerchant.Records.SingleOrDefault();
    }

    public async Task PerformValidationForUpdatingMerchantTag(Guid merchantId)
    {
        var merchant = await merchantClient.GetMerchantWithAllHierarchies(merchantId);

        // check merchant Status
        if (merchant.MerchantStatus == Constants.MerchantStatus.Verified)
        {
            logger.LogError("Request validation failed: {@errors}", Errors.UpdateMerchantTagErrorVerified);
            throw new ValidationException(Errors.UpdateMerchantTagErrorVerified);
        }

        await CheckGleRegistrationAndExistingHierarchies(merchant);
    }

    public async Task CreateBusinessHierarchyAsync(Guid parentMerchantId, IReadOnlyCollection<Guid> subordinateMerchantIds)
    {
        logger.LogInformation("Start validation for create business hierarchy request.");
        await PerformValidationForCreatingTheHierarchy(parentMerchantId, subordinateMerchantIds);

        logger.LogInformation("Create business hierarchy for validated request.");
        await merchantClient.CreateBusinessHierarchyAsync(parentMerchantId, subordinateMerchantIds);
    }

    public async Task DeleteBusinessHierarchyAsync(Guid parentMerchantId, Guid subordinateMerchantId)
    {
        logger.LogInformation("Start validation for delete business hierarchy request.");
        var subordinateSentToGle = await productService.IsMerchantRegisteredInGle(subordinateMerchantId);
        if (subordinateSentToGle)
        {
            logger.LogError("Request validation failed: {@errors}", Errors.UpdateMerchantTagErrorBillPaymentsSent);
            throw new ValidationException(Errors.UpdateMerchantTagErrorBillPaymentsSent);
        }

        logger.LogInformation("Delete business hierarchy for validated request.");
        await merchantClient.DeleteBusinessHierarchyAsync(parentMerchantId, subordinateMerchantId);
    }

    private async Task PerformValidationForCreatingTheHierarchy(Guid parentMerchantId, IReadOnlyCollection<Guid> subordinateMerchantIds)
    {
        var parentMerchantWithAllHierarchies = await merchantClient.GetMerchantWithAllHierarchies(parentMerchantId);

        logger.LogInformation("Validate parent business tag: {tag}, for merchant with id: {id}", parentMerchantWithAllHierarchies.Tag, parentMerchantId);
        var allowedSubordinateTag = parentMerchantWithAllHierarchies.Tag switch
        {
            Constants.MerchantTag.MasterBusiness => Constants.MerchantTag.SubBusiness,
            Constants.MerchantTag.Wholesaler => Constants.MerchantTag.SubWholesaler,
            _ => throw new ValidationException(Errors.InvalidParentBusinessTag)
        };

        logger.LogInformation("Validate existing business hierarchy for the parent.");
        if (parentMerchantWithAllHierarchies.Hierarchies.Any(h =>
                h.HierarchyType == Constants.HierarchyType.Business &&
                subordinateMerchantIds.Contains(h.MerchantId)))
        {
            logger.LogError("Request validation failed: {@errors}", Errors.SubordinateBusinessAlreadyLinkedToParent);
            throw new ValidationException(Errors.SubordinateBusinessAlreadyLinkedToParent);
        }

        logger.LogInformation("Validate subordinate businesses.");
        foreach (var subordinateMerchantId in subordinateMerchantIds)
        {
            var subordinateMerchant = await merchantClient.GetMerchantWithAllHierarchies(subordinateMerchantId);
            await CheckGleRegistrationAndExistingHierarchies(subordinateMerchant);

            logger.LogInformation("Validate subordinate business tag: {tag} for merchant with id: {id}.",
                subordinateMerchant.Tag, subordinateMerchant.MerchantId);
            if (subordinateMerchant.Tag!.Equals(allowedSubordinateTag, StringComparison.OrdinalIgnoreCase))
                continue;

            logger.LogError("Request validation failed: {@errors}", Errors.InvalidSubordinateBusinessTag);
            throw new ValidationException(Errors.InvalidSubordinateBusinessTag);
        }
    }

    private async Task CheckGleRegistrationAndExistingHierarchies(CoreMerchantWithHierarchy merchant)
    {
        // check if business was sent to GLE
        var sentToGle = await productService.IsMerchantRegisteredInGle(merchant.MerchantId);
        if (sentToGle)
        {
            logger.LogError("Request validation failed: {@errors}", Errors.UpdateMerchantTagErrorBillPaymentsSent);
            throw new ValidationException(Errors.UpdateMerchantTagErrorBillPaymentsSent);
        }

        // check if business is linked to another business
        var merchantHasBusinessTag = Constants.BusinessTagsOnly.Contains(merchant.Tag!, StringComparer.OrdinalIgnoreCase);
        var hasAnyBusinessAssociation = merchant.Hierarchies.Any(h => h.HierarchyType == Constants.HierarchyType.Business);
        if (merchantHasBusinessTag && hasAnyBusinessAssociation)
        {
            logger.LogError("Request validation failed: {@errors}", Errors.UpdateMerchantTagErrorActiveAssociation);
            throw new ValidationException(Errors.UpdateMerchantTagErrorActiveAssociation);
        }
    }

    private static string GetTagToFilterSubordinates(string? merchantTag)
    {
        if (merchantTag == Constants.MerchantTag.MasterBusiness)
        {
            return Constants.MerchantTag.SubBusiness;
        }

        if (merchantTag == Constants.MerchantTag.Wholesaler)
        {
            return Constants.MerchantTag.SubWholesaler;
        }

        throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidMerchantTagForSearch);
    }

    private static string GetTagToFilterParent(string? merchantTag)
    {
        if (merchantTag == Constants.MerchantTag.SubBusiness)
        {
            return Constants.MerchantTag.MasterBusiness;
        }

        if (merchantTag == Constants.MerchantTag.SubWholesaler)
        {
            return Constants.MerchantTag.Wholesaler;
        }

        return string.Empty;
    }
}