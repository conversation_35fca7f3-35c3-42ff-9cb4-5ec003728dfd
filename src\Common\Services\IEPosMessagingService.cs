﻿using Common.Enums;

namespace Common.Services
{
    using Common.Models.EPOS;
    using System;
    using System.Threading.Tasks;

    public interface IEPosMessagingService
    {
        Task<EPosTicketCreationResponse> CreateOrderEPosTicketAsync(Guid orderId, bool shouldTriggerError = false);
        Task<EPosAction> GetEPosActions(Guid orderId);
        Task SendEPosSwTypeUpdate(Guid orderId);
    }
}