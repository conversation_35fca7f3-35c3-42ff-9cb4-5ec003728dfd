﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Common;
using Common.Models;
using Common.Models.Gsdk;
using Common.Models.Merchant;
using Common.Models.Product;
using Common.Models.Shareholder;
using Common.Models.TerminalDataSet;
using Common.Models.User;
using Common.Options;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using NSubstitute;
using static Common.Constants;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers
{
    public static class TestsHelper
    {
        public static readonly UrlSettings UrlSettingsOptions = new UrlSettings
        {
            MerchantServiceBaseUrl = "http://fakeMerchant.com",
            GeideaFederationServiceBaseUrl = "http://fakeFederation.com",
            DocumentServiceBaseUrl = "http://fakeDocument.com",
            NotificationServiceBaseUrl = "http://fakeNotification.com",
            CheckoutServiceBaseUrl = "http://fakeCheckout.com",
            UserServiceBaseUrl = "http://fakeUser.com",
            ReferenceServiceBaseUrl = "http://fakeReference.com",
            LeadServiceBaseUrl = "http://fakeLead.com",
            ProductServiceBaseUrl = "http://fakeProduct.com",
            SearchServiceBaseUrl = "http://fakeSearch.com",
            DueDiligenceServiceBaseUrl = "http://fakeDueDiligence.com",
            EposOrderFederationApiBaseUrl = "http://fakeEpos.com",
            MerchantServiceBaseUrlNS = "http://fakeMerchant.com",
            DocumentServiceBaseUrlNS = "http://fakeDocument.com",
            NotificationServiceBaseUrlNS = "http://fakeNotification.com",
            CheckoutServiceBaseUrlNS = "http://fakeCheckout.com",
            UserServiceBaseUrlNS = "http://fakeUser.com",
            ReferenceServiceBaseUrlNS = "http://fakeReference.com",
            LeadServiceBaseUrlNS = "http://fakeLead.com",
            ProductServiceBaseUrlNS = "http://fakeProduct.com",
            SearchServiceBaseUrlNS = "http://fakeSearch.com",
            DueDiligenceServiceBaseUrlNS = "http://fakeDueDiligence.com"
        };
        public static readonly StatusChangeAuToEmailConfiguration StatusChangeAuToEmailConfiguration = new StatusChangeAuToEmailConfiguration()
        {
            EnableAutoEmail = true,
            SalesSupport = "test.com",
            MerchantEnablement = "test.com",
            ComplianceOfficer = "test.com",
            RiskOfficer = "test.com",
            SalesManager = "test.com",
            CPMerchantEnablementL2 = "test.com",
            CNPECOMEnablement = "test.com"
        };

        public static readonly ShareholderSearchConfiguration ShareholderSearchConfiguration = new ShareholderSearchConfiguration()
        {
            ShareholderCompanySearchDefaultTake = 20,
            ShareholderIndividualSearchDefaultTake = 20
        };

        public static bool HasErrorCode(ValidationException ex, string code)
        {
            var errors = (ex.ProblemDetails as ExtendedValidationProblemDetails)!.Errors;

            if (errors == null)
            {
                return false;
            }

            return errors.SelectMany(group => group.Value).Any(error => error.Type == code);
        }

        public static readonly ApplicationOptions AppOptions = new ApplicationOptions
        {
            DefaultContactReasonName = "DEFAULT",
            CorsAllowedOrigins = ""
        };

        public static readonly SearchGsdkMerchantsRequest SearchGsdkMerchantsRequest = new SearchGsdkMerchantsRequest
        {
            HasGsdkOrganizationId = false,
            VerifiedStatusFilter = true
        };

        public static readonly GsdkMerchant GsdkMerchant = new GsdkMerchant
        {
            MerchantId = Guid.NewGuid(),
            MemberId = "12345678",
            Email = "<EMAIL>",
            OrganizationId = Guid.NewGuid().ToString(),
            FirstName = "FirstName",
            LastName = "LastName",
            FirstNameAr = "FirstNameAr",
            LastNameAr = "LastNameAr",
            Phone = "888888888"
        };

        public static Merchant GetMerchant(Guid merchantId, string counterParty, string? acquiringLedger)
        {
            return new Merchant
            {
                MerchantId = merchantId,
                MerchantStatus = Constants.MerchantStatus.Verified,
                Counterparty = counterParty,
                MerchantDetails = new MerchantDetails
                {
                    MerchantId = merchantId,
                    AcquiringLedger = acquiringLedger
                }
            };
        }

        public static readonly MerchantExternalIdentifier MerchantExternalIdentifier = new MerchantExternalIdentifier()
        {
            Id = Guid.NewGuid(),
            ExternalSourceId = "ExternalSourceId",
            MerchantId = Guid.NewGuid(),
            IdentifierKey = "IdentifierKey",
            IdentifierType = "IdentifierType",
            IdentifierValue = "IdentifierValue"
        };

        public static HttpClient CreateHttpClient(HttpStatusCode statusCode, string content = "")
        {
            var handlerMock = new Mock<HttpMessageHandler>();
            var response = new HttpResponseMessage
            {
                StatusCode = statusCode,
                Content = new StringContent(content, Encoding.Default, "application/json")
            };

            handlerMock.Protected().Setup<Task<HttpResponseMessage>>(
                  "SendAsync",
                  ItExpr.IsAny<HttpRequestMessage>(),
                  ItExpr.IsAny<CancellationToken>())
               .ReturnsAsync(response);

            return new HttpClient(handlerMock.Object);
        }

        public static HttpClient CreateHttpClientWithSequenceResponse(List<Tuple<HttpStatusCode, string>> sequenceResponseList)
        {
            var handlerMock = new Mock<HttpMessageHandler>();

            var handlerPart = handlerMock
                .Protected()
                .SetupSequence<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                );

            foreach (var item in sequenceResponseList)
            {
                handlerPart.ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = item.Item1,
                    Content = new StringContent(item.Item2, Encoding.Default, "application/json")
                });
            }
            handlerMock.Verify();

            return new HttpClient(handlerMock.Object);
        }

        public static MerchantCheckOptions MerchantCheckOptions(bool doCheck)
        {

            return new MerchantCheckOptions
            {
                UniqueLicenseCheckEnabled = doCheck
            };
        }

        public static readonly TerminalData terminalDataProductConfiguredSaudi = new()
        {
            MIDMerchantReference = "123456",
            Mcc = "12345",
            POSDataCode = "12345",
            FullTId = "12435543345345",
        };

        public static readonly TerminalData terminalDataProductConfiguredEgypt = new()
        {
            MIDMerchantReference = "123456",
            Mcc = "12345",
            TId = "1243554",
        };

        public static readonly TerminalData terminalDataProductNotConfiguredSaudi = new()
        {
            MIDMerchantReference = "123456",
            Mcc = "12345",
            POSDataCode = "12345",
            FullTId = "",
        };

        public static readonly TerminalData terminalDataProductNotConfiguredEgypt = new()
        {
            MIDMerchantReference = "123456",
            Mcc = "12345",
            POSDataCode = "12345",
            TId = "",
        };

        public static readonly GatewayData gatewayDataProductConfigured = new()
        {
            IsTest = false,
            GsdkTid = "123456",
            GsdkMid = "122345"
        };

        public static readonly GatewayData gatewayDataProductNotConfigured = new()
        {
            IsTest = false,
            GsdkTid = "",
            GsdkMid = "122345"
        };

        public static readonly List<ProductInstance> productInstanceTerminalNotConfiguredSaudi = new()
        {
            new ProductInstance()
            {
                Children = new List<ProductInstance>()
                {
                    new ProductInstance
                    {
                        Data = JsonSerializer.Serialize(terminalDataProductConfiguredSaudi),
                        Product = new ProductShortResponse
                        {
                            Type = "TERMINAL",
                        },
                        DeletedFlag = false,
                    },
                    new ProductInstance
                    {
                        Data = JsonSerializer.Serialize(terminalDataProductNotConfiguredSaudi),
                        Product = new ProductShortResponse
                        {
                            Type = "TERMINAL",
                        },
                        DeletedFlag = false
                    }
                },
                DeletedFlag = false,
                Product = new ProductShortResponse
                {
                    Type = "BUNDLE",
                },

            }
        };


        public static readonly List<ProductInstance> productInstanceTerminalConfiguredSaudi = new()
        {
            new ProductInstance()
            {
                Children = new List<ProductInstance>()
                {
                    new ProductInstance
                    {
                        Data = JsonSerializer.Serialize(terminalDataProductConfiguredSaudi),
                        Product = new ProductShortResponse
                        {
                            Type = "TERMINAL",
                        },
                        DeletedFlag = false,
                    }
                },
                DeletedFlag = false,
                Product = new ProductShortResponse
                {
                    Type = "BUNDLE",
                }
            }
        };

        public static readonly List<ProductInstance> productInstanceTerminalConfiguredEgypt = new()
        {
            new ProductInstance()
            {
                Children = new List<ProductInstance>()
                {
                    new ProductInstance
                    {
                        ProductInstanceId =new Guid("********-0000-0000-0000-********0001"),
                        Data = JsonSerializer.Serialize(terminalDataProductConfiguredEgypt),
                        Product = new ProductShortResponse
                        {
                            Type = "TERMINAL",
                        },
                        DeletedFlag = false,
                    },
                    new ProductInstance
                    {
                        ProductInstanceId =new Guid("********-0000-0000-0000-********0002"),
                        Data = JsonSerializer.Serialize(terminalDataProductConfiguredEgypt),
                        Product = new ProductShortResponse
                        {
                            Type = "TERMINAL",
                        },
                        DeletedFlag = false,
                    }

                },
                DeletedFlag = false,
                Product = new ProductShortResponse
                {
                    Type = "BUNDLE",
                }
            }
        };

        public static List<ProductInstance> GetProductInstanceConfiguredEgypt(string type)
        {
            return new List<ProductInstance>
            {
                new ProductInstance()
                    {
                        Children = new List<ProductInstance>()
                        {
                            new ProductInstance
                            {
                                Data = JsonSerializer.Serialize(terminalDataProductConfiguredEgypt),
                                Product = new ProductShortResponse
                                {
                                    Type = type,
                                },
                                DeletedFlag = false,
                            }
                        },
                        DeletedFlag = false,
                        Product = new ProductShortResponse
                        {
                            Type = "BUNDLE",
                        }
                    }
            };
        }

        public static List<ProductInstance> GetDirectProductInstanceConfiguredEgypt(string type)
        {
            return new List<ProductInstance>
            {
                new ProductInstance()
                {
                    Data = JsonSerializer.Serialize(terminalDataProductConfiguredEgypt),
                    Product = new ProductShortResponse
                    {
                        Type = type,
                    },
                    DeletedFlag = false,
                }
            };
        }

        public static readonly List<ProductInstance> productInstanceTerminalNotConfiguredEgypt = new List<ProductInstance>()
        {
            new ProductInstance()
            {
                Children = new List<ProductInstance>()
                {
                    new ProductInstance
                    {
                       Data = JsonSerializer.Serialize(terminalDataProductConfiguredEgypt),
                       Product = new ProductShortResponse
                       {
                            Type = "TERMINAL",
                            Code = ""
                       },
                       DeletedFlag = false
                    },
                    new ProductInstance
                    {
                       Data = JsonSerializer.Serialize(terminalDataProductNotConfiguredEgypt),
                       Product = new ProductShortResponse
                       {
                            Type = "TERMINAL",
                            Code = ""
                       },
                       DeletedFlag = false
                    }
                },
                DeletedFlag = false,
                Product = new ProductShortResponse
                {
                    Type = "BUNDLE",
                },
            }
        };

        public static readonly List<ProductInstance> productInstanceGatewayNotConfigured = new List<ProductInstance>()
        {
            new ProductInstance()
            {
                Children = new List<ProductInstance>()
                {
                    new ProductInstance
                    {
                       Data = JsonSerializer.Serialize(gatewayDataProductConfigured),
                       Product = new ProductShortResponse
                       {
                            Type = "GWAY",
                            Code = "PAYMENT_GATEWAY"
                       },
                       DeletedFlag = false,
                    },
                    new ProductInstance
                    {
                       Data = JsonSerializer.Serialize(gatewayDataProductNotConfigured),
                       Product = new ProductShortResponse
                       {
                            Type = "GWAY",
                            Code = "PAYMENT_GATEWAY"
                       },
                       DeletedFlag = false,
                    }
                },
                DeletedFlag = false,
                Product = new ProductShortResponse
                {
                    Type = "BUNDLE",
                },
            }
        };

        public static readonly Merchant verifiedMerchant = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = Constants.MerchantStatus.Verified,
            MerchantDetails = new MerchantDetails
            {
                BusinessEmail = "<EMAIL>",
                LegalName = "testGeideaBusiness",
                ReferralChannel = ReferralChannel.UNASSIGNED
            }
        };

        public static readonly Merchant verifiedHSBCMerchant = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = MerchantStatus.Verified,
            MerchantDetails = new MerchantDetails
            {
                BusinessEmail = "<EMAIL>",
                LegalName = "testHSBCBusiness",
                ReferralChannel = ReferralChannel.HSBC
            }
        };

        public static readonly MerchantAcquiringLedgerInfo merchantAcquiringLedgerInfoWithDefaultStore = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStoreIds = new List<Guid> { new Guid("0075495E-B046-45CD-89E7-74F0A6D80CA3") },
            AcquiringLedger = "NBE_BANK",
            LegalName = "LegalName",
            LegalNameAr = "LegalNameAr",
            StoreName = "Default Store"
        };

        public static readonly MerchantAcquiringLedgerInfo merchantAcquiringLedgerInfoWithoutDefaultStore = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStoreIds = new List<Guid> { new Guid("0075495E-B046-45CD-89E7-74F0A6D80CA3") },
            AcquiringLedger = "NBE_BANK",
            LegalName = "LegalName",
            LegalNameAr = "LegalNameAr",
            StoreName = "My Store"
        };

        public static readonly List<TerminalDataSetResponse> terminalDataSetsResponse = new List<TerminalDataSetResponse>
        {
            new TerminalDataSetResponse {  ProductInstanceId = new Guid("********-0000-0000-0000-********0001")  },
            new TerminalDataSetResponse {  ProductInstanceId = new Guid("********-0000-0000-0000-********0002")  },
            new TerminalDataSetResponse {  ProductInstanceId = new Guid("********-0000-0000-0000-********0003")  }
        };

        public static readonly List<Guid> updatedTerminalDataSetResponse = new()
        {
            new Guid("********-0000-0000-0000-********0001"),
            new Guid("********-0000-0000-0000-********0002"),
            new Guid("********-0000-0000-0000-********0003")
        };

        public static readonly List<TerminalDataSetResponse> terminalDataSetResponse = new()
        {
            new TerminalDataSetResponse {ProductInstanceId = new Guid("********-0000-0000-0000-********0001") },
        };

        public static readonly Merchant notVerifiedMerchant = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = Constants.MerchantStatus.BoardingInProgress
        };

        public static readonly FreelancerTerminalTypeFeatureToggle freelancerTerminalTypeFeatureToggle = new FreelancerTerminalTypeFeatureToggle
        {
            EnableT300TerminalType = true
        };

        public static IOptions<FreelancerTerminalTypeFeatureToggle> GetFreelancerTerminalTypeFeatureToggle()
        {
            var option = Substitute.For<IOptions<FreelancerTerminalTypeFeatureToggle>>();
            option.Value.Returns(freelancerTerminalTypeFeatureToggle);
            return option;
        }

        public static CoreMerchantWithHierarchy GetCoreMerchantWithHierarchy(Guid? merchantId)
        {
            if (merchantId == null || merchantId == Guid.Empty)
            {
                merchantId = Guid.NewGuid();
            }

            return new CoreMerchantWithHierarchy
            {
                MerchantId = merchantId.Value,
                Tag = Constants.MerchantTag.MasterBusiness,
                MerchantType = "C",
                Counterparty = Constants.CounterParty.Egypt,
                LeadId = Guid.NewGuid(),
                MerchantStatus = Constants.MerchantStatus.Verified,
                Hierarchies = new List<MerchantHierarchy>()
                {
                    new MerchantHierarchy()
                    {
                        HierarchyId = Guid.NewGuid(),
                        HierarchyType = Constants.HierarchyType.Business,
                        MerchantId = Guid.NewGuid(),
                        ParentMerchantId = merchantId.Value,
                        TopLevelMerchantId = merchantId.Value
                    }
                }
            };
        }

        public static readonly Catalogue[] GsdkEgyptCatalogues = new Catalogue[] {
                    new Catalogue {
                        CatalogueName = Constants.Catalogues.AcquiringLedgerToGsdkMapping,
                        Key = Constants.MmsLedgers.EgyptDefault,
                        Value = Constants.GsdkLedgers.EgyptDefault
                    },
                    new Catalogue {
                        CatalogueName = Constants.Catalogues.AcquiringLedgerToGsdkMapping,
                        Key = Constants.MmsLedgers.EgyptNBEBank,
                        Value = Constants.GsdkLedgers.EgyptDefault},
                    new Catalogue {
                        CatalogueName = Constants.Catalogues.ProductTypeDefaultLedgerMapping,
                        Value = Constants.MmsLedgers.EgyptDefault,
                    }
                };

        public static List<GlobalContractDto> GetGlobalContractsWithDefaultContract(string paymentWay, string outproviderId)
        {
            return new List<GlobalContractDto>
                {
                    new GlobalContractDto{
                        InProviderAccountCategory = new GlobalContractInProviderAccountCategoryDto { Name = paymentWay},
                        ContractRules = new List<GlobalContractRuleDto> {
                            new GlobalContractRuleDto {
                                OutProviderAccount = new OutProviderAccountDto {
                                    Id = outproviderId
                                }
                            }
                        },
                        IsDefault = true
                    }
                };
        }

        public static GlobalContractResponseDto GetGlobalContracts(string paymentWay, string outproviderId)
        {
            return new GlobalContractResponseDto
            {
                Records = new List<GlobalContractDto>
                {
                    new GlobalContractDto{
                        InProviderAccountCategory = new GlobalContractInProviderAccountCategoryDto { Name = paymentWay},
                        ContractRules = new List<GlobalContractRuleDto> {
                            new GlobalContractRuleDto {
                                OutProviderAccount = new OutProviderAccountDto {
                                    Id = outproviderId
                                }
                            }
                        },
                        IsDefault = true
                    }
                }
            };
        }

        public static readonly GsdkSettings GsdkSettings = new GsdkSettings
        {
            IsTest = true,
            Test = new GsdkEnvironmentSettings
            {
                BaseUrl = "https://fake-integration.sdk.finance",
                EgyptBaseUrl = "https://fakeeg-integration.sdk.finance",
                Username = "username",
                Password = "password"
            }
        };

        public static readonly MerchantBusinessInformation[] validMerchantBusinessInformation = new MerchantBusinessInformation[]
        {
            new MerchantBusinessInformation(){
            LegalName = "business",
            LegalNameAr = "اسم شركة"
            }
        };

        public static readonly JsonPatchDocument<PatchMerchantRequest> patchMerchantRequest = new JsonPatchDocument<PatchMerchantRequest>();

        public static readonly Catalogue[] catalogueResponse = new Catalogue[]
        {
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.AcquiringLedger,
                Key = "NBE_BANK",
                Value = "NBE"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.AcquiringLedger,
                Key = "DEFAULT_BANK",
                Value = "MB"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Governorates,
                Key="1",
                Value="Cairo"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.NbeGovernarotes,
                Key="1",
                Value="Giza"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Cities,
                Key="190",
                Value="Misr al-Gadida"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.NbeCities,
                Key="6THOCT01A",
                Value="6th October"
            },
            new Catalogue()
            {
                CatalogueName=Constants.Catalogues.CitiesToGovernarotes,
                Key="190",
                Value="1"
            },
            new Catalogue()
            {
                CatalogueName=Constants.Catalogues.NbeCitiesToGovernarotes,
                Key="6THOCT01A",
                Value="1"
            },
            new Catalogue()
            {
                CatalogueName=Constants.Catalogues.BusinessDomain,
                Key="4722",
                Value="Tourism"
            },
            new Catalogue()
            {
                CatalogueName=Constants.Catalogues.NbeBusinessDomain,
                Key="1",
                Value="Charities"
            }
        };
        public static readonly List<Merchant> expectedStore = new()
        {
            new Merchant()
            {
                MerchantId = Guid.NewGuid(),
                MerchantStatus = Constants.MerchantStatus.BoardingInProgress,
                MerchantDetails = new MerchantDetails
                {
                    MerchantDetailsId = Guid.Empty,
                    MerchantId = Guid.Empty,
                    OutletType = "type",
                    MCC = "0742",
                    LegalName = "legal name",
                    Nickname = "nickname",
                    FoundationDate = DateTime.MinValue,
                    ReferralChannel = "UNASSIGNED",
                    Mid = "***************"
                },
            }

        };
        public static readonly Merchant merchantAccount = new Merchant
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = Constants.MerchantStatus.BoardingInProgress,
            MerchantDetails = new MerchantDetails
            {
                LegalName = "legal name",
                LegalNameAr = "legal name ar",
                Nickname = "nickname",
                OutletType = "type",
                ReferralChannel = "UNASSIGNED"
            }
        };
        public static readonly Merchant CPmerchantAccount = new Merchant
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = Constants.MerchantStatus.BoardingInProgress,
            MerchantDetails = new MerchantDetails
            {
                LegalName = "legal name",
                LegalNameAr = "legal name ar",
                Nickname = "nickname",
                OutletType = "type",
                ReferralChannel = "UNASSIGNED",
                ChannelType = "POS"
            }
        };
        public static readonly Merchant CNPmerchantAccount = new Merchant
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = Constants.MerchantStatus.BoardingInProgress,
            MerchantDetails = new MerchantDetails
            {
                LegalName = "legal name",
                LegalNameAr = "legal name ar",
                Nickname = "nickname",
                OutletType = "type",
                ReferralChannel = "UNASSIGNED",
                ChannelType = "PGW"
            }
        };
        public static readonly Merchant coreMerchantBoardingCompletedStatus = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = MerchantStatus.BoardingCompleted,
            MerchantDetails = new MerchantDetails
            {
                MerchantDetailsId = Guid.Empty,
                MerchantId = Guid.Empty,
                OutletType = "type",
                BusinessEmail = "<EMAIL>",
                LegalName = "testGeideaBusiness",
                MCC = "0742",
                Nickname = "nickname",
                FoundationDate = DateTime.MinValue,
                ReferralChannel = ReferralChannel.UNASSIGNED,
                MaxMonthlyTransaction = 250000,
                HighestSingleTransaction = 5000
            }

        };
        public static readonly Merchant coreMerchantComplianceApprovalStatus = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = MerchantStatus.ComplianceApproval,
            MerchantDetails = new MerchantDetails
            {
                MerchantDetailsId = Guid.Empty,
                MerchantId = Guid.Empty,
                OutletType = "type",
                BusinessEmail = "<EMAIL>",
                LegalName = "testGeideaBusiness",
                MCC = "0742",
                Nickname = "nickname",
                FoundationDate = DateTime.MinValue,
                ReferralChannel = ReferralChannel.UNASSIGNED
            }
        };
        public static readonly Merchant coreMerchantBoardingRiskApprovalStatus = new()
        {
            MerchantId = Guid.NewGuid(),
            MerchantStatus = MerchantStatus.RiskApproval,
            MerchantDetails = new MerchantDetails
            {
                MerchantDetailsId = Guid.Empty,
                MerchantId = Guid.Empty,
                OutletType = "type",
                BusinessEmail = "<EMAIL>",
                LegalName = "testGeideaBusiness",
                MCC = "0742",
                Nickname = "nickname",
                FoundationDate = DateTime.MinValue,
                ReferralChannel = ReferralChannel.UNASSIGNED
            }

        };
        public static readonly List<MerchantShareholderIndividual> merchantShareholderIndividual = new()
        {
            new MerchantShareholderIndividual
            {
                PersonOfInterestId = Guid.NewGuid(),
                NationalId = "784200108683909",
                Nationality = "AE",
                FirstName = "Shareholder",
                LastName = "AKSHAY",
                PhonePrefix = "+971",
                PhoneNumber = "81122142",
                PassportNo = null,
                PassportExpirationDate = null,
                KYCCheck = "APPROVED",
                PEP = true,

                Address = new ShareholderIndividualAddress
                {
                    Country = "AE",
                    City = "DU-01",
                    Area = "Address Line contact",
                    Governorate = "AE-DU",
                    AddressInfo = "Address Line contact",
                    Email = "<EMAIL>"
                },
                Relations = new List<IndividualRelation>()
                {
                    new IndividualRelation
                    {
                        Type = "DIRECT_SHAREHOLDER",
                        MerchantId = Guid.NewGuid(),
                        IsPrincipal = true,
                        OwnershipPercentage = 25,
                        OrganizationRole = "OWNER",

                    }
                }
            },
            new MerchantShareholderIndividual
            {
                PersonOfInterestId = Guid.NewGuid(),
                NationalId = "784200108683909",
                Nationality = "AE",
                FirstName = "Shareholder",
                LastName = "ARIKA",
                PhonePrefix = "+971",
                PhoneNumber = "81122142",
                PassportNo = null,
                PassportExpirationDate = null,
                KYCCheck = "PENDING",
                PEP = true,

                Address = new ShareholderIndividualAddress
                {
                    Country = "AE",
                    City = "DU-01",
                    Area = "Address Line contact",
                    Governorate = "AE-DU",
                    AddressInfo = "Address Line contact",
                    Email = "<EMAIL>"
                },
                Relations = new List<IndividualRelation>()
                {
                    new IndividualRelation
                    {
                        Type = "STAKEHOLDER",
                        MerchantId = Guid.NewGuid(),
                        IsPrincipal = true,
                        OwnershipPercentage = 25,
                        OrganizationRole = "OWNER",

                    }
                }

            }
        };
    };
};

