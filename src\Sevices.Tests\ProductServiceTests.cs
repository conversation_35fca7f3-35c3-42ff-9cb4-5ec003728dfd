﻿using Common.Models.Product;
using Common.Options;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

using NUnit.Framework;
using Services;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using System.Collections.Generic;
using System.Text.Json;
using Common.Models.Gle;
using Common.Models;
using Common.Services;
using Geidea.Utils.Counterparty.Providers;
using System.Net.Http;
using Common.Models.TerminalDataSet;
using Common.Models.ProductInstance;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class ProductServiceTests
    {
        private readonly Mock<ILogger<ProductService>> logger = new Mock<ILogger<ProductService>>();
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();

        public ProductServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        }

        [Test]
        public async Task GetInstancesByIdsTests()
        {
            var productInstances = new ProductInstance[]
            {
                new ProductInstance{ProductInstanceId=Guid.NewGuid(),
                    Product = new ProductShortResponse
                    {
                        Availability="Normal",
                        Description="Product description",
                        DisplayOrder=1,
                        DeletedFlag=false,
                        ValidFrom=DateTime.Now,
                        ValidTo=DateTime.MaxValue,
                        Prices = new List<PriceShortResponse>{ new PriceShortResponse {
                            ExemptFromVAT = false,
                            ChargeFrequency = "Monthly",
                            Currency = "SAR",
                            PerItemPrice = 1000,
                            DeletedFlag = false,
                            PriceId = Guid.NewGuid(),
                            ChargeType = "ChargeType",
                            Group = "Group",
                            MaxPrice = 10000,
                            PercentagePrice = 0,
                            Priority = 1,
                            RentalPeriod = 12,
                            Threshold = 100,
                            ThresholdType = "ThresholdType",
                            ValidFrom = DateTime.Now,
                            ValidTo = DateTime.UtcNow.AddDays(100)} }
                    },
                },
                new ProductInstance{ProductInstanceId=Guid.NewGuid() },
                new ProductInstance{ProductInstanceId=Guid.NewGuid() }
            };
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(productInstances)));

            var result = await productService.GetProductInstancesByIdsAsync(productInstances.Select(p => p.ProductInstanceId).ToArray());
            result.Length.Should().Be(3);
            result[0].Should().BeEquivalentTo(productInstances[0]);
            result[1].Should().BeEquivalentTo(productInstances[1]);
            result[2].Should().BeEquivalentTo(productInstances[2]);
        }

        [Test]
        public void GetInstancesByIdsTests_Error()
        {
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, ""));

            productService.Invoking(x => x.GetProductInstancesByIdsAsync(It.IsAny<Guid[]>())).Should().ThrowAsync<ServiceException>();
        }

        [Test]
        public async Task GetProductsByIdsTests()
        {
            var products = new Product[]
            {
                new Product{Id=Guid.NewGuid(), Type="TERMINAL" },
                new Product{Id=Guid.NewGuid(), Type="SCHEME" },
                new Product{Id=Guid.NewGuid(), Type="MINI_ECR" }
            };
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(products)));

            var result = await productService.GetProductsByIdsAsync(products.Select(p => p.Id).ToArray());
            result.Length.Should().Be(3);
            result[0].Should().BeEquivalentTo(products[0]);
            result[1].Should().BeEquivalentTo(products[1]);
            result[2].Should().BeEquivalentTo(products[2]);
        }

        [Test]
        public void GetProductsByIdsTests_Error()
        {
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, ""));

            productService.Invoking(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Should().ThrowAsync<ServiceException>();
        }

        [Test]
        public async Task GetRelatedProductsAsync_WhenCoreServiceReturnsProductIds()
        {
            var productCodesRequest = new ProductCodesRequest() { ProductCodes = new[] { "GEIDEA_GO" } };
            var productIds = new Guid[] { Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid() };

            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(productIds)));

            var result = await productService.GetRelatedProductsAsync(productCodesRequest);
            result.Length.Should().Be(3);
            result.Should().BeEquivalentTo(productIds);
        }

        [Test]
        public void GetRelatedProductsAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            var productCodesRequest = new ProductCodesRequest() { ProductCodes = new[] { "GEIDEA_GO" } };

            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, ""));

            productService.Invoking(x => x.GetRelatedProductsAsync(productCodesRequest)).Should().ThrowAsync<PassthroughException>();
        }


        [Test]
        public async Task FindProductsAsync_WhenCoreServiceReturnsProducts_ShouldReturnProducts()
        {
            var products = new Product[]
            {
                new Product{Id=Guid.NewGuid(), Type="TERMINAL" },
                new Product{Id=Guid.NewGuid(), Type="TERMINAL" },
                new Product{Id=Guid.NewGuid(), Type="TERMINAL" }
            };
            var findProductsRequest = new FindProductRequest() { Code = "TERMINAL", OnlyValid = true };

            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(products)));

            var result = await productService.FindProductsAsync(findProductsRequest);
            result.Count.Should().Be(3);
            result.Should().BeEquivalentTo(products);
        }

        [Test]
        public void FindProductsAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            var findProductsRequest = new FindProductRequest() { Id = Guid.NewGuid(), Availability = "Available", CategoryId = Guid.NewGuid(), Code = "TERMINAL", Type = "TERMINAL", OnlyValid = true, SalesChannel = "Shop", Flow = "Long" };

            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
               TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, ""));

            productService.Invoking(x => x.FindProductsAsync(findProductsRequest)).Should().ThrowAsync<PassthroughException>();
        }

        [Test]
        public async Task GetBpProductTypesInListOfProducts_ShouldReturnCorrect()
        {
            var expected = new BillPaymentServiceAndBundleFlags
            {
                HasBillPaymentBundle = true,
                HasBillPaymentService = false
            };


            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(expected));
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object, httpClient);
            var result = await productService.GetBpProductTypesInListOfProducts(new IdsRequest { Ids = new[] { Guid.NewGuid() } });
            result.Should().BeEquivalentTo(expected);
        }

        [Test]
        public async Task GetBpProductTypesInListOfProducts_ShouldThrowPassthroughException()
        {
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest);
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object, httpClient);
            await productService.Invoking(x => x.GetBpProductTypesInListOfProducts(new IdsRequest { Ids = new Guid[] { Guid.NewGuid() } }))
            .Should()
            .ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task GenerateTIDMIDAndAddEditNBETerminalDataSets_ShouldReturnSuccess()
        {
            var expectedResponse = new List<TerminalDataSetResponse>
            {
                new TerminalDataSetResponse { ProductInstanceId = new Guid() },
                new TerminalDataSetResponse { ProductInstanceId = new Guid() },
            };

            var productService = new ProductService(logger.Object, urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(expectedResponse)));

            var result = await productService.GenerateTIDAndMIDAndAddEditTerminalDataSets(new TerminalDataSetRequest());

            result.Should().BeEquivalentTo(expectedResponse);
        }

        [Test]
        public async Task GenerateTIDMIDAndAddEditNBETerminalDataSets_ShouldThrowException()
        {
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object, TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest));

            try
            {
                await productService.GenerateTIDAndMIDAndAddEditTerminalDataSets(new TerminalDataSetRequest());
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.AreEqual(ex.GetType(), typeof(PassthroughException));
            }
        }

        [Test]
        public async Task UpdateTerminalProductInstancesMeta_ShouldReturnSuccess()
        {
            List<Guid> expectedResponse = new List<Guid> { new Guid(), new Guid(), new Guid() };

            var productService = new ProductService(logger.Object, urlSettingsOptions.Object, TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(expectedResponse)));

            var result = await productService.UpdateTerminalProductInstancesMeta(new List<UpdateProductInstanceMetaRequest>());

            result.Count.Should().Be(3);
            result.Should().BeEquivalentTo(expectedResponse);
        }

        [Test]
        public async Task UpdateTerminalProductInstancesMeta_ShouldThrowException()
        {
            var productService = new ProductService(logger.Object, urlSettingsOptions.Object, TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest));

            try
            {
                await productService.UpdateTerminalProductInstancesMeta(new List<UpdateProductInstanceMetaRequest>());
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.AreEqual(ex.GetType(), typeof(PassthroughException));
            }
        }
    }
}
