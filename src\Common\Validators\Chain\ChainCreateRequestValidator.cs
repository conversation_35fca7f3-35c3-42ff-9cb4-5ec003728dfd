﻿using Common.Models.Chain;
using Common.Models.Lead;
using FluentValidation;
using Geidea.Utils.ReferenceData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators.Chain;

public class ChainCreateRequestValidator : AbstractValidator<ChainCreateRequest>
{
    public ChainCreateRequestValidator(List<ReferenceData> refData)
    {
        RuleFor(x => x.ChainName).NotNull().NotEmpty().MaximumLength(255);
        RuleFor(x => x.RelationshipManager)
                  .ChildRules(x =>
                  {
                      RuleFor(l => l)
                           .Must(l => IsValidRefData(l.RelationshipManager ?? "", Constants.Catalogues.RelationToCompany, refData))
                           .When(l => !string.IsNullOrEmpty(l.RelationshipManager))
                           .WithErrorCode(Errors.InvalidRelationshipManager.Code)
                           .WithMessage(Errors.InvalidRelationshipManager.Message);
                  });
        RuleFor(x => x.Segment)
                  .ChildRules(x =>
                  {
                      RuleFor(l => l)
                           .Must(l => IsValidRefData(l.Segment ?? "", Constants.Catalogues.Segment, refData))
                           .When(l => !string.IsNullOrEmpty(l.Segment))
                           .WithErrorCode(Errors.InvalidSegment.Code)
                           .WithMessage(Errors.InvalidSegment.Message);
                  });
        RuleFor(x => x.ChainAddress).SetValidator(new ChainAddressRequestValidator());
    }

    private static bool IsValidRefData(string currValue, string catalogueName, List<ReferenceData> refData)
    {
        return refData.Exists(r => string.Equals(r.CatalogueName, catalogueName, StringComparison.OrdinalIgnoreCase) && string.Equals(r.Key, currValue, StringComparison.OrdinalIgnoreCase));
    }
}
