﻿using Common.Models;
using FluentValidation;

namespace Common.Validators
{
    public class SendSmsInfoCheckRequestValidator : AbstractValidator<SendSmsInfoCheckRequest>
    {
        public SendSmsInfoCheckRequestValidator()
        {
            RuleFor(a => a.CheckType)
                .NotEmpty()
                .Must(x => x.Length <= 32)
                .WithErrorCode(Errors.CheckTypeLengthValidation.Code)
                .WithMessage(Errors.CheckTypeLengthValidation.Message);

            RuleFor(x => x.PhoneNumber).NotEmpty().SetValidator(new PhoneValidator());
#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
            When(x => x.Comments != null, () =>
            {
                RuleFor(x => x.Comments).SetValidator(new CommentValidator());
            });
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        }
    }
}
