﻿using AutoMapper;
using Common.Models.Checkout;
using Common.Models.Lead;
using Common.Models.Product;
using Common.Models.User;
using System;
using System.Collections.Generic;
using System.Linq;
using Common.Models.Merchant;
using Common.Models.Merchant.SubordinateMerchant;
using Common.Models.Shareholder;

namespace BackofficeApi
{
    public class AutoMapping : Profile
    {
        public AutoMapping()
        {
            CreateMap<OrderResponse, OrderExportResponse>()
                .ForMember(x => x.MerchantName, opts => opts.Ignore());

            CreateMap<MerchantSearchResult, MerchantApiResult>();

            CreateMap<Lead, LeadWithSalesIdResponse>();
            CreateMap<(Lead lead, UserResponse user, List<Product> products), LeadWithSalesIdResponse>()
                .IncludeMembers(s => s.lead)
                .ForMember(d => d.SalesFirstName, opt => opt.MapFrom(s => s.user.FirstName))
                .ForMember(d => d.SalesLastName, opt => opt.MapFrom(s => s.user.LastName))
                .ForMember(d => d.LeadProducts, opt => opt.MapFrom(s => MapProductCodes(s.lead.LeadProducts, s.products)));

            var coreSearchableColumns = new[] { "MerchantId", "OrderStatus", "ProjectName", "OrderNumber", "MerchantName", "CheckoutDate", "Total" };
            CreateMap<OrderSearchCriteria, CoreOrderSearchCriteria>()
                .ForMember(d => d.SearchIn, opt => opt.MapFrom(s => s.SearchIn.Where(x => coreSearchableColumns.Contains(x, StringComparer.InvariantCultureIgnoreCase))));

            CreateMap<LeadCreateRequest, LeadRequest>()
                .ForMember(d => d.LeadProducts, opt => opt.MapFrom(s => s.LeadProductIds));
            CreateMap<Lead, LeadWithDocumentsResponse>();
            CreateMap<Guid, LeadProductRequest>()
                .ForMember(s => s.ProductId, opt => opt.MapFrom(s => s));
            CreateMap<SubordinateMerchantSearchRequestDto, SubordinateMerchantSearchFilters>();
            CreateMap<ShareholderCompanyIndividualCore, MerchantShareholderIndividual>();
            CreateMap<MerchantIndividualCore, MerchantShareholderIndividual>();
            CreateMap<MerchantAccountConfig, OrderAccountConfig>()
            .ForMember(d => d.Id, opt => opt.Ignore())
            .ForMember(d => d.OrderId, opt => opt.Ignore());            
            CreateMap<MerchantCommissionConfig, OrderCommissionConfig>()
                .ForMember(d => d.Id, opt => opt.Ignore())
            .ForMember(d => d.OrderId, opt => opt.Ignore());
        }

        public List<LeadProduct> MapProductCodes(List<LeadProduct> leadProducts, List<Product> products)
        {
            return (from leadProduct in leadProducts
                   join product in products
                       on leadProduct.ProductId equals product.Id into nullableLeadProducts
                   from nullableLeadProduct in nullableLeadProducts.DefaultIfEmpty()
                   select new LeadProduct() { LeadProductId = leadProduct.LeadProductId, ProductId = leadProduct.ProductId, ProductCode = nullableLeadProduct?.Code }
                   ).ToList();
        }
    }
}
