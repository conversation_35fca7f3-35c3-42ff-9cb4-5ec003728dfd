﻿using System;
using System.Collections.Generic;

namespace Common.Models.Shareholder;

public class ShareholderCompanyResponse
{
    public Guid Id { get; set; }

    public string CompanyName { get; set; } = null!;

    public string CompanyType { get; set; } = null!;

    public string CompanyLicense { get; set; } = null!;

    public DateTime? IssueDate { get; set; }

    public DateTime? LicenseExpiryDate { get; set; }

    public string? MccCode { get; set; }

    public string? PhonePrefix { get; set; }

    public string? PhoneNumber { get; set; }

    public string? Counterparty { get; set; }

    public string CreatedBy { get; set; } = string.Empty;

    public DateTime CreatedDate { get; set; }

    public string? UpdatedBy { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public Guid MerchantId { get; set; }

    public ShareholderCompanyAddressResponse ShareholderCompanyAddress { get; set; } = new ShareholderCompanyAddressResponse();

    public IEnumerable<DocumentMetadata> FreelanceIds { get; set; } = new List<DocumentMetadata>();

    public IEnumerable<DocumentMetadata> CommercialRegistrations { get; set; } = new List<DocumentMetadata>();

    public IEnumerable<DocumentMetadata> LegalEnterpriseLicenses { get; set; } = new List<DocumentMetadata>();
}