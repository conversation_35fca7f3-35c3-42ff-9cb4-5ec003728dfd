﻿using Common.Models.Tasks;
using Common.Options;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Cleanup;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class TaskServiceTests
    {
        private readonly Mock<ILogger<TaskService>> logger = new Mock<ILogger<TaskService>>();
        private readonly Mock<IOptions<UrlSettings>> urlSettingsOptions = new Mock<IOptions<UrlSettings>>();
        private readonly CleanupService cleanupService = null!;
        private readonly Mock<ILogger<CleanupService>> loggerCleanup = new Mock<ILogger<CleanupService>>();

        public TaskServiceTests()
        {
            urlSettingsOptions.Setup(x => x.Value).Returns(TestsHelper.UrlSettingsOptions);
            cleanupService = new CleanupService(loggerCleanup.Object);
        }

        #region Arrangement
        private static TaskModel ArrangeBasicTask()
        {
            return new TaskModel()
            {
                Id = Guid.NewGuid(),
                MerchantCheckId = Guid.NewGuid(),
                MerchantId = Guid.NewGuid(),
                PersonCheckId = Guid.NewGuid(),
                PersonOfInterestId = Guid.NewGuid(),
                TaskNumber = 1,
                Type = nameof(TaskModel)
            };
        }

        private static JsonPatchDocument<TaskUpdateRequest> ArrangeBasicUpdatePayloadForStatus()
        {
            return new JsonPatchDocument<TaskUpdateRequest>(new List<Operation<TaskUpdateRequest>>()
            {
                new Operation<TaskUpdateRequest>("Replace","status",null,"OPEN")
            }, (new Mock<IContractResolver>()).Object);
        }

        #endregion

        [Test]
        public async Task TaskService_Update_should_call_correct_core_service_endpoint()
        {
            // Arrange
            var taskInstance = ArrangeBasicTask();

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(taskInstance)),
                })
                .Verifiable();

            var taskService = new TaskService(
                client: new HttpClient(handlerMock.Object),
                urlSettingsOptions: urlSettingsOptions.Object,
                logger: logger.Object);

            // Act
            var operation = await taskService.UpdateTaskAsync(taskInstance.Id, new JsonPatchDocument<TaskUpdateRequest>());

            // Assert
            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.MerchantServiceBaseUrl}/api/v1/merchant/task/{taskInstance.Id}");

            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Patch && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());
        }

        [Test]
        public async Task TaskService_Update_should_call_core_service_endpoint_with_correct_payload()
        {
            // Arrange
            var taskInstance = ArrangeBasicTask();

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(taskInstance)),
                })
                .Verifiable();

            var taskService = new TaskService(
                client: new HttpClient(handlerMock.Object),
                urlSettingsOptions: urlSettingsOptions.Object,
                logger: logger.Object);

            var patchRequest = ArrangeBasicUpdatePayloadForStatus();

            // Act
            var operation = await taskService.UpdateTaskAsync(taskInstance.Id, patchRequest);

            // Assert
            var body = new StringContent(JsonConvert.SerializeObject(patchRequest), Encoding.UTF8, "application/json");

            handlerMock
                .Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Patch
                    && req.Content != null
                    && req.Content.ReadAsStringAsync().Result == body.ReadAsStringAsync().Result),
                ItExpr.IsAny<CancellationToken>());
        }

        [Test]
        public async Task TaskService_Update_WhenUpdateToStatusDifferentFromOpen_ShouldRemoveResolution()
        {
            // Arrange
            var taskInstance = ArrangeBasicTask();

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(taskInstance)),
                })
                .Verifiable();

            var taskService = new TaskService(
                client: new HttpClient(handlerMock.Object),
                urlSettingsOptions: urlSettingsOptions.Object,
                logger: logger.Object);

            var patchRequest = ArrangeBasicUpdatePayloadForStatus();

            // Act
            await taskService.UpdateTaskAsync(taskInstance.Id, patchRequest);

            // Assert
            var resolutionRemoveOperation = patchRequest.Operations.SingleOrDefault(op =>
                op.path == nameof(TaskUpdateRequest.Resolution) && op.value == null &&
                op.OperationType == OperationType.Replace);
            
            Assert.IsNotNull(resolutionRemoveOperation);
        }
    }
}
