﻿using Common.Models;
using Common.Models.Product;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IGsdkHelperService
    {
        string GetLedgerKey(ProductInstance productInstance, Catalogue[] ledgerMappingCatalogues);
        string GetLedgerValue(Catalogue[] ledgerMappingCatalogues, ProductInstance firstProductInstanceChildren);

    }
}