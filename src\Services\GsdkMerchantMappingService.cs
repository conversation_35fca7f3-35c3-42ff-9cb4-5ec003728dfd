﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models.Gsdk;
using Common.Services;
using Microsoft.Extensions.Logging;

namespace Services
{
    public class GsdkMerchantMappingService : BaseGsdkService, IGsdkMerchantMappingService
    {
        private readonly IFederationService federationService;
        private readonly ILogger<GsdkMerchantMappingService> logger;

        public GsdkMerchantMappingService(
            IFederationService federationService,
            IMerchantService merchantService,
            ILogger<GsdkMerchantMappingService> logger) : base(merchantService)
        {
            this.federationService = federationService;
            this.logger = logger;
        }

        public async Task<IReadOnlyCollection<GsdkMappingResponse>> MapExistingMerchants()
        {
            logger.LogInformation("Start retrieving organizationIds for merchants from Gsdk");

            var notMappedMerchants = new List<GsdkMappingResponse>();

            var searchGsdkMerchantsRequest = new SearchGsdkMerchantsRequest { HasGsdkOrganizationId = false, VerifiedStatusFilter = true};
            var merchantsNotInGsdk = await MerchantService.SearchGsdkMerchants(searchGsdkMerchantsRequest);

            foreach (var merchant in merchantsNotInGsdk)
            {
                string? organizationId = null;

                if (!string.IsNullOrEmpty(merchant.Phone))
                    organizationId = await federationService.GetMerchantOrganizationIdByPhoneAsync(merchant.MerchantId, RemovePhonePlusSign(merchant.Phone));

                if (string.IsNullOrEmpty(organizationId) && !string.IsNullOrEmpty(merchant.Email))
                    organizationId = await federationService.GetMerchantOrganizationIdByEmailAsync(merchant.MerchantId, merchant.Email);

                if (string.IsNullOrEmpty(organizationId))
                {
                    AddNotMappedMerchant(notMappedMerchants, merchant);
                    continue;
                }

                var isSuccessful = await StoreOrganizationId(merchant.MerchantId, organizationId);

                if (!isSuccessful)
                    AddNotMappedMerchant(notMappedMerchants, merchant);
            }

            logger.LogInformation("Retrieving organizationIds for merchants from Gsdk completed");
            return notMappedMerchants;
        }

        private void AddNotMappedMerchant(ICollection<GsdkMappingResponse> notMappedMerchants, GsdkMerchant gsdkMerchant)
        {
            logger.LogError("Fail to map merchant with {@merchantId}", gsdkMerchant.MerchantId);

            notMappedMerchants.Add(new GsdkMappingResponse
            {
                MemberId = gsdkMerchant.MemberId,
                MerchantId = gsdkMerchant.MerchantId
            });
        }

        private static string RemovePhonePlusSign(string phone)
        {
            return phone.Replace("+", string.Empty);
        }
    }
}