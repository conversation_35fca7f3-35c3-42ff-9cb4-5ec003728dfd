﻿using Common.Models.Merchant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Account
{
    public class AccountSearchResponse<T> where T : AccountResult
    {
        public int TotalRecordCount { get; set; }

        public int ReturnedRecordCount { get; set; }

        public IList<T> Records { get; set; } = new List<T>();
    }
}
