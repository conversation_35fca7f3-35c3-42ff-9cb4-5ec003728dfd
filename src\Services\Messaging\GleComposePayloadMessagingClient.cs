﻿using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics.CodeAnalysis;
using Geidea.Utils.Messaging;
using Common.Models.Gle;

namespace Services.Messaging;


[ExcludeFromCodeCoverage]
public class GleComposePayloadMessagingClient : MessageClient
{
    public GleComposePayloadMessagingClient(IHttpContextAccessor contextAccessor,
        ILogger<MessageClient> logger,
        IOptionsMonitor<RabbitMqOptions> rabbitMqOptions)
        : base(contextAccessor, logger, rabbitMqOptions, new QueueSettings
        {
            MessageSender = "BackofficeAPI",
            ExchangeName = "GLE.User.Registration",
            QueueName = "GLE.User.Registration",
            RoutingKey = "GLE.User.Registration.ComposePayload",
            Durable = true
        })
    { }

    public virtual void SendGleComposePayloadMessage(GleComposePayloadRequest payload)
    {
        logger.LogInformation("Sending GLE compose payload message. Payload: {@payload}", payload);
        SendMessage(payload);
        logger.LogInformation("Sent GLE compose payload message.");
    }
}