﻿using System;

namespace LeadUpdate.Models
{
    public class TahakomResponse
    {
        public bool status { get; set; }
        public string transaction_id { get; set; } = null!;
        public UserInfo[] user_info { get; set; } = Array.Empty<UserInfo>();
    }

    public class UserInfo
    {
        public string? arabic_father_name { get; set; }
        public string? gender { get; set; }
        public string? id_version_no { get; set; }
        public string? id_expiry_date_gregorian { get; set; }
        public string? id_expiry_date_hijri { get; set; }
        public string? arabic_family_name { get; set; }
        public string? arabic_grand_father_name { get; set; }
        public string? userid { get; set; }
        public string? nationality_code { get; set; }
        public string? english_name { get; set; }
        public string? arabic_nationality { get; set; }
        public string? card_issue_date_gregorian { get; set; }
        public string? english_father_name { get; set; }
        public string? nationality { get; set; }
        public string? dob { get; set; }
        public string? arabic_name { get; set; }
        public string? english_first_name { get; set; }
        public string? card_issue_date_hijri { get; set; }
        public string? english_grand_father_name { get; set; }
        public string? english_family_name { get; set; }
        public string? issue_location_ar { get; set; }
        public string? dob_hijri { get; set; }
        public string? arabic_first_name { get; set; }
    }
}