﻿using System;
using System.Collections.Generic;

namespace Common.Models.User
{
    public class UserResponse
    {
        public Guid UserId { get; set; }
        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string? PhoneNumber { get; set; }
        public string? CountryPrefix { get; set; }
        public Guid? PersonOfInterestId { get; set; }
        public Guid? LeadId { get; set; }
        public string CreatedBy { get; set; } = null!;
        public DateTime CreatedDateUtc { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime? UpdatedDateUtc { get; set; }
        public List<string> Groups { get; set; } = new List<string>();
        public string? SalesId { get; set; }
        public string? SalesPartnerId { get; set; }
        public int? RefBankId { get; set; }
        public string? AcquiringLedger { get; set; }
        public bool IsDisabled { get; set; }
    }
}
