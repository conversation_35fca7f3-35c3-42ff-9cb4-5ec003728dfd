using BackofficeApi.Extensions;
using BackofficeApi.Filters;
using BackofficeApi.Formatters;
using BackofficeApi.OptionsWrappers;
using Common.Models.MixPanel;
using Common.Options;
using Common.Services;
using Common.Services.Validators;
using Elastic.Apm.NetCoreAll;
using Geidea.PaymentGateway.ConfigServiceClient;
using Geidea.Utils.ApplicationLanguage;
using Geidea.Utils.ApplicationLanguage.Providers;
using Geidea.Utils.Cleanup;
using Geidea.Utils.Common;
using Geidea.Utils.Counterparty;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.HeaderValidation;
using Geidea.Utils.HeaderValidation.Extensions;
using Geidea.Utils.HealthChecks;
using Geidea.Utils.HttpClientExtensions;
using Geidea.Utils.Logging;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Geidea.Utils.ReferenceData;
using Geidea.Utils.Security;
using Geidea.Utils.UserInfo;
using Geidea.Utils.Validation;
using Geidea.Utils.Versioning;
using Geidea.Utils.WebUtilities;
using GeideaPaymentGateway.Utils.RabbitMQ;
using GeideaPaymentGateway.Utils.RabbitMQ.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Microsoft.OpenApi.Models;
using Services;
using Services.Messaging;
using Services.Messaging.Consumers;
using Services.OrderExport;
using Services.Validation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using static GeideaPaymentGateway.Utils.CommonModels.Constants;
using Constants = Geidea.Utils.Common.Constants;
using MessageClient = Geidea.Utils.Messaging.MessageClient;

namespace BackofficeApi
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IOptionsMonitor<ApplicationOptions> appOptions, ILogger<Startup> logger)
        {
            app.UseHeaderPropagation();
#if !DEBUG
            app.UseAllElasticApm(Configuration);
#endif

            app.UseMiddleware<SerilogRequestLogger>();
            app.UseMiddleware<ExceptionHandler>();
            app.UseMiddleware<HeadersValidationMiddleware>();
            app.UseMiddleware<CounterpartyHandler>();
            app.UseMiddleware<ApplicationLanguageHandler>();

            Console.WriteLine("Environment: " + env.EnvironmentName);

            if (!env.IsProduction())
            {
                app.UseForwardedHeaders(new ForwardedHeadersOptions
                {
                    ForwardedHeaders = ForwardedHeaders.XForwardedProto
                });
                app.UseSwagger(c =>
                {
                    c.PreSerializeFilters.Add((swaggerDoc, httpReq) => swaggerDoc.Servers = new List<OpenApiServer> {
                        new OpenApiServer { Url = $"{httpReq.Scheme}://{httpReq.Host.Value}{(httpReq.Headers["GD-Service"].Count > 0 ? "/backoffice" : null)}"}
                         });
                });

                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("swagger/v1/swagger.json", "Backoffice API v1");
                    c.RoutePrefix = string.Empty;
                });
            }
            app.UseRouting();
            app.UseCors(a =>
            {
                a.AllowAnyHeader();
                a.AllowAnyMethod();
                a.WithExposedHeaders(CorrelationIdHeaderName);

                if (!string.IsNullOrWhiteSpace(appOptions.CurrentValue.CorsAllowedOrigins))
                {
                    var allowedOrigins = appOptions.CurrentValue.CorsAllowedOrigins
                        .Split(",", StringSplitOptions.RemoveEmptyEntries)
                        .Select(o => o.Trim())
                        .ToArray();

                    logger.LogInformation("CORS: Allowing origins specified in the 'CorsAllowedOrigins' configuration property: {@allowedOrigins}",
                        string.Join(',', allowedOrigins));
                    a.WithOrigins(allowedOrigins);
                }

            });

            app.UseVersionMiddleware();
            app.UseInternalConfigurationView();

            app.UseHealthChecks("/health", new HealthCheckOptions { AllowCachingResponses = false, ResponseWriter = HealthResponseWriter.WriteHealthCheckResponse });
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new PhysicalFileProvider($"{env.ContentRootPath}/{Common.Constants.MatchTemplatesFilesSettings.FolderPath}"),
                RequestPath = $"/{Common.Constants.MatchTemplatesFilesSettings.FolderPath}"
            });


#if !DEBUG
            app.StartMessageConsumption<PostilionMessageClient>(false);
            app.StartMessageConsumption();
#endif
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddFeatureManagement();
            services.AddStructuredLogging(Configuration);
            services.AddApplicationOptions(Configuration)
                .AddRabbitMqOptions(Configuration)
                .AddRabbitMqConnectionFactory();

            services.AddIdentityProviderConfiguration(Configuration);
            services.AddAuthorization();

            services.AddAutoMapper(typeof(Startup));
            services.AddOptions<RabbitMqConfig>().Bind(Configuration.GetSection("Default:RabbitMqConfig"));
            services.AddOptions<RabbitMqOptions>().Bind(Configuration.GetSection("Default:RabbitMqConfig"));
            services.AddOptions<GsdkSettings>().Bind(Configuration.GetSection("GsdkSettings"));
            services.AddOptions<MerchantCheckOptions>().Bind(Configuration.GetSection("MerchantCheckOptions"));
            services.AddOptions<FreelancerTerminalTypeFeatureToggle>().Bind(Configuration.GetSection("FreelancerTerminalTypeFeatureToggle"));
            services.AddOptions<MixPanelConfiguration>().Bind(Configuration.GetSection("MixPanelConfiguration"));
            services.AddOptions<StatusChangeAuToEmailConfiguration>().Bind(Configuration.GetSection("StatusChangeAuToEmailConfiguration"));
            services.AddOptions<WorldCheckFeatureManagement>().Bind(Configuration.GetSection("WorldCheckFeatureToggle"));
            services.AddOptions<FinscanCheckFeatureToggle>().Bind(Configuration.GetSection("FinscanTriggerCheck"));
            services.AddOptions<MatchCheckFeatureToggle>().Bind(Configuration.GetSection("MatchCheckFeatureToggle"));
            services.AddOptions<RiskScoringFeatureToggle>().Bind(Configuration.GetSection("RiskScoringFeatureToggle"));
            services.AddHealthCheckServicesAndOptions(Configuration);
            services.AddHealthChecks()
                .AddCommonHealthChecks()
                .AddRabbitMqHealthChecks(Configuration);

            // Add services
            services.AddPollyHttpClient<IChainService, ChainService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IMerchantService, MerchantService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IMerchantClient, MerchantClient>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IFederationService, FederationService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<INotificationService, NotificationService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IDocumentService, DocumentService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IDocumentClient, DocumentClient>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<ICheckoutClient, CheckoutClient>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IReferenceService, ReferenceService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<ILeadService, LeadService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IUserService, UserService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IProductService, ProductService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IReferenceDataClient, ReferenceDataClient>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IContractService, ContractService>(Configuration).AddHeaderPropagation();
            services.AddScoped<IPayloadTransformService, PayloadTransformService>();
            services.AddPollyHttpClient<ISearchService, SearchService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<ITaskService, TaskService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<ITaskCommentService, TaskCommentService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IDueDiligenceService, DueDiligenceService>(Configuration, false).AddHeaderPropagation();
            services.AddPollyHttpClient<IShareholderService, ShareholderService>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<IShareholderClient, ShareholderClient>(Configuration).AddHeaderPropagation();
            services.AddPollyHttpClient<INexusBridgeClient, NexusBridgeClient>(Configuration, false).AddHeaderPropagation();
            services.AddPollyHttpClient<IAccountService, AccountService>(Configuration).AddHeaderPropagation();


            services.AddTransient<IGsdkHelperService, GsdkHelperService>();
            services.AddTransient<IGsdkClient, GsdkClient>();
            services.AddTransient<IKeycloakClient, KeycloakClient>();
            services.AddTransient<IGsdkService, GsdkService>();
            services.AddTransient<IOrderExportService, OrderExportService>();
            services.AddTransient<IEgyptOrderExportService, EgyptOrderExportService>();
            services.AddTransient<IChecksService, ChecksService>();
            services.AddTransient<ISaleService, SaleService>();
            services.AddTransient<IEPosMessagingService, EPosMessagingService>();
            services.AddTransient<IPostilionService, PostilionService>();
            services.AddTransient<IGsdkMerchantMappingService, GsdkMerchantMappingService>();
            services.AddTransient<ICleanupService, CleanupService>();
            services.AddTransient<IActiveCampaignService, ActiveCampaignService>();
            services.AddTransient<IGSDKMerchantAdapterService, GSDKMerchantAdapterService>();
            services.AddTransient<IConfigureOptions<MvcNewtonsoftJsonOptions>, RoleBasedJsonOptionsWrapper>();
            services.AddTransient<ICheckoutService, CheckoutService>();
            services.AddSingleton<INexusBridgeService, NexusBridgeService>();
            services.AddSingleton<INexusBridgeLogService, NexusBridgeLogService>();
            services.AddTransient<ISubordinateMerchantService, SubordinateMerchantService>();
            services.AddTransient<IShareholderValidationService, ShareholderValidationService>();
            services.AddTransient<IMixPanelService, MixPanelService>();
            services.AddScoped<IApplicationLanguage, ApplicationLanguage>();
            services.AddScoped<ICounterpartyProvider, CounterpartyProvider>();
            services.AddScoped<ITemplateService, TemplateService>();
            services.AddScoped<IMatchReportBuilderService, MatchReportBuilderService>();
            services.AddScoped<ICounterpartyProvider, CounterpartyProvider>();
            services.AddScoped<ICorrelationIdProvider, CorrelationIdProvider>();
            services.AddSingleton<IApexResponseReceiver, ApexResponseReceiver>();
            services.AddSingleton<ApexResponseService>();

            services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddSingleton<EPosMessagingSoftwareTypeClient>();
            services.AddSingleton(s => new EPosMessagingClient(
                s.GetRequiredService<ILogger<MessageClient>>(),
                s.GetRequiredService<IHttpContextAccessor>(),
                new QueueSettings
                {
                    ExchangeName = "MMS.Order.Exchange",
                    MessageSender = "BackofficeAPI",
                    MessageType = "MMS.eposCreateTicketRequest",
                    QueueName = "MMS.Order.eposCreateTicket_v2",
                    RoutingKey = "MMS.Order.eposCreateTicket",
                    Durable = true
                },
                s.GetRequiredService<IOptionsMonitor<RabbitMqOptions>>()));

            services.AddSingleton(s => new ActiveCampaignMessagingClient(
                s.GetRequiredService<ILogger<MessageClient>>(),
                s.GetRequiredService<IHttpContextAccessor>(),
                new QueueSettings
                {
                    ExchangeName = "MMS.ActiveCampaign.Exchange",
                    MessageSender = "BackofficeAPI",
                    MessageType = "MMS.activeCampaignRequest",
                    QueueName = "MMS.ActiveCampaign.Request_v2",
                    RoutingKey = "MMS.ActiveCampaign.Request",
                    Durable = true
                },
                s.GetRequiredService<IOptionsMonitor<RabbitMqOptions>>()));

            services.AddSingleton(s => new PostilionMessageClient(
                s.GetRequiredService<IHttpContextAccessor>(),
                s.GetRequiredService<ILogger<MessageClient>>(),
                s.GetRequiredService<IOptionsMonitor<RabbitMqOptions>>(),
                new QueueSettings
                {
                    ExchangeName = "MMS.Postilion.Federation",
                    QueueName = "MMS.Postilion.Federation_v2",
                    MessageSender = "BackofficeAPI",
                    MessageType = "MMS.PostilionMessageClient",
                    RoutingKey = "MMS.Postilion.Federation",
                    Durable = true
                }));

            services.AddSingleton<MMSOrderUpdateMessagingClient>();
            services.AddSingleton<GleComposePayloadMessagingClient>();

            services.AddControllers().AddNewtonsoftJson()
                .AddMvcOptions(options =>
                {
                    options.OutputFormatters.Add(new OrderResponseCsvFormatter());
                    options.OutputFormatters.Add(new EgyptOrderResponseCsvFormatter());
                    options.OutputFormatters.Add(new MerchantResponseCsvFormatter());
                    options.OutputFormatters.Add(new LeadResponseCsvFormatter());
                    options.OutputFormatters.Add(new EgyptMerchantExportResponseCsvFormatter());
                    options.OutputFormatters.Add(new UserResponseCsvFormatter());
                    options.ModelValidatorProviders.Add(new InputModelSanitizationValidatorProvider());
                });

            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo
                {
                    Version = "v1",
                    Title = "Backoffice API",
                    Description = "Backoffice API"
                });

                options.IgnoreObsoleteActions();
                options.OperationFilter<CounterpartyHeaderFilter>();

                var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                options.IncludeXmlComments(xmlPath);

                var securityScheme = new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Id = "Bearer",
                        Type = ReferenceType.SecurityScheme
                    },
                    Description = "JWT Token",
                    In = ParameterLocation.Header,
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                };

                options.AddSecurityDefinition("Bearer", securityScheme);
                options.AddSecurityRequirement(new OpenApiSecurityRequirement {
                    { securityScheme, new List<string>() }
                });
            });

            services.AddAuthorizationPolicies(Configuration);
            services.AddIUserInfoService(Configuration);
            services.AddIReferenceDataClient(Configuration);
            services.AddHeaderWhitelistServiceCollection(Configuration);

            services.AddHeaderPropagation(o =>
            {
                o.Headers.Add(Constants.CounterpartyHeaderName);
                o.Headers.Add(Constants.CounterpartyHeaderName, _ => Constants.CounterpartyUae);
                o.Headers.Add("Authorization");
            });

            ValidationHelpers.InitValidation();
        }
    }
}