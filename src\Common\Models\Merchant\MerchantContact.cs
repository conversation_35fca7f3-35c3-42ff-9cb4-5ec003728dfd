﻿using System;

namespace Common.Models.Merchant
{
    public class MerchantContact
    {
        public Guid MerchantId { get; set; }

        public Guid MerchantContactId { get; set; }

        public string? Email { get; set; }

        public string? CountryPrefix { get; set; }

        public string? PhoneNumber { get; set; }

        public string? AlternativeCountryPrefix { get; set; }

        public string? AlternativePhoneNumber { get; set; }

        public int? TimeLength { get; set; }

        public string? Website { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime? ValidTo { get; set; }

        public string ContactReason { get; set; } = string.Empty;

        public string CreatedBy { get; set; } = string.Empty;

        public string? UpdatedBy { get; set; }
    }
}
