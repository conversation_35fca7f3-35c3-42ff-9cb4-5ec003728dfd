﻿using AutoMapper;
using BackofficeApi;
using Common;
using Common.Models;
using Common.Models.Document;
using Common.Models.Lead;
using Common.Models.User;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Cleanup;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.ApplicationLanguage.Providers;
using Microsoft.AspNetCore.JsonPatch.Operations;
using static Common.Constants;
using Common.Models.Product;
using System.Net.Http;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class LeadServiceTests
    {
        private readonly Mock<ILogger<LeadService>> logger = new Mock<ILogger<LeadService>>();
        private LeadService leadService = null!;
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
        private readonly Mock<IOptionsMonitor<MerchantCheckOptions>> merchantCheckOptions = new Mock<IOptionsMonitor<MerchantCheckOptions>>();
        private readonly Mock<IUserService> userService = new Mock<IUserService>();
        private readonly Mock<IProductService> productService = new Mock<IProductService>();
        private readonly Mapper mapper;
        private readonly Mock<ILogger<CleanupService>> loggerCleanup = new Mock<ILogger<CleanupService>>();
        private readonly CleanupService cleanupService = null!;
        private readonly Mock<IDocumentService> documentService = new Mock<IDocumentService>();
        private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
        private readonly Mock<IApplicationLanguage> applicationLanguage = new Mock<IApplicationLanguage>();
        private readonly Mock<IActiveCampaignService> activeCampaignService = new Mock<IActiveCampaignService>();
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();
        private readonly Mock<IMerchantService> merchantService = new Mock<IMerchantService>();

        public LeadServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
            merchantCheckOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.MerchantCheckOptions(false));
            var profile = new AutoMapping();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
            mapper = new Mapper(configuration);
            cleanupService = new CleanupService(loggerCleanup.Object);
        }

        private readonly Guid userId = Guid.NewGuid();

        private readonly Lead lead = new Lead()
        {
            LeadId = Guid.NewGuid(),
            SalesId = "GDV99999"
        };

        private readonly LeadCreationResponse leadCreation = new LeadCreationResponse
        {
            LeadId = Guid.NewGuid(),
            CreatedDate = DateTime.Now
        };

        private void CreateLeadService(HttpClient httpClient)
        {
            leadService = new LeadService(
               logger.Object,
               urlSettingsOptions.Object,
               httpClient,
               userService.Object,
               mapper,
               cleanupService,
               documentService.Object,
               counterpartyProvider.Object,
               applicationLanguage.Object,
               activeCampaignService.Object,
               productService.Object,
               referenceService.Object,
               merchantService.Object,
               merchantCheckOptions.Object);
        }

        [Test]
        public void UpdateLeadAsync()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            var patch = new JsonPatchDocument<Lead>();

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), patch)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateLeadAsync_PassthroughtError()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            var patch = new JsonPatchDocument<Lead>();
            patch.Add(x => x.LeadStatus, "New status");

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), patch))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }


        [Test]
        [TestCase("LeadDetails/IBAN", "krZjtTH2wX2SqMUJ7", "BA_Iban_Max24")]
        [TestCase("LeadDetails/IBAN", "nomRU1U3C3wIaktoH0G9gixFw9SXDfJ2h24353", "BA_Iban_Max24")]
        [TestCase("LeadDetails/IBAN", "**********************", "BA_Iban_Max24")]
        [TestCase("LeadDetails/IBAN", "111117771483466234584939", "BA_Iban_Max24")]
        [TestCase("LeadDetails/IBAN", "************************", "BA_Iban_Max24")]
        [TestCase("LeadDetails/IBAN", "NA777-483466234158493938", "BA_Iban_Max24")]
        [TestCase("LeadDetails/IBAN", "SA777 4834 6234158493938", "BA_Iban_Max24")]
        public async Task UpdateLeadAsync_ValidationException(string path,
            string value,
            string errorCode)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", path, null, value));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(Guid.NewGuid(), patch))
                .Should().ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                      && (TestsHelper.HasErrorCode(x, errorCode)));
        }

        [Test]
        public void SearchLeadsAsync()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new LeadsResponse { TotalRecordCount = 1, Records = new List<Lead> { lead } })));

            leadService.Invoking(x => x.SearchLeadsAsync(new LeadSearchParameters())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void SearchLeadsAsync_PassthroughtError()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            leadService.Invoking(x => x.SearchLeadsAsync(new LeadSearchParameters()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void GetLeadsCreationAsync()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new LeadCreationResponse[] { leadCreation })));

            leadService.Invoking(x => x.GetLeadCreationAsync(new IdsRequest())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void ExportLeadsAsync_WhenCoreServiceDoesNotThorwException_ShouldNotThrowException()
        {
            var leadExportParameters = new LeadExportParameters
            {
                CountryPrefix = "+966",
                Keyword = "1234",
                SearchIn = new[] { "NationalId" },
                LeadId = Guid.NewGuid(),
                NationalId = "1234567900",
                PhoneNumber = "52698522",
                ProductCodes = new List<string> { "GO_AIR" },
                SalesIds = new List<string> { "1234" },
                SalesPartnerId = "12324",
                DateInterval = new DateInterval { FromDate = new DateTime(), ToDate = new DateTime() },
                ReferralChannels = new List<string> { "asdf" },
                SalesPartnerIds = new List<string> { "2134" }
            };

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new List<LeadExportResponse> { new LeadExportResponse { LeadId = Guid.NewGuid(), LeadStatus = "NEW" } })));

            leadService.Invoking(x => x.ExportLeadsAsync(leadExportParameters)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void ExportLeadsAsync_WhenCoreSErviceThrowsException_ShouldThrowPassthroughtError()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            leadService.Invoking(x => x.ExportLeadsAsync(new LeadExportParameters()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void UpdateSalesIdAsync_WhenLeadserviceReturnsSuccess_ShouldNotThrowException()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            leadService.Invoking(x => x.UpdateSalesIdAsync("GDS00001", "GDS0002")).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateSalesIdAsync_WhenLeadServiceThrowsException_ShouldThrowPassthroughtError()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            leadService.Invoking(x => x.UpdateSalesIdAsync("GDS00001", "GDS0002"))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void CreateLeadAsync_WhenLeadserviceReturnsSuccess_ReturnCreatedLead()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            leadService.Invoking(x => x.CreateLeadAsync(new LeadCreateRequest
            {
                PhoneNumber = "+*********",
                CountryPrefix = "+966",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" }
            }, userId)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void CreateLeadAsync_WhenLeadServiceThrowsException_ShouldThrowPassthroughtError()
        {
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            leadService.Invoking(x => x.CreateLeadAsync(new LeadCreateRequest
            {
                PhoneNumber = "+*********",
                CountryPrefix = "+966",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" }
            }, userId))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void CreateLeadAsync_WhenThereIsAlreadyAnAccountWithPhone_ShouldThrowException()
        {
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = true, EmailIsUsed = false }));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            leadService.Invoking(x => x.CreateLeadAsync(new LeadCreateRequest { PhoneNumber = "*********", CountryPrefix = "+966" }, userId))
                .Should().ThrowAsync<ServiceException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.AccountAlreadyExists.Code);
        }

        [Test]
        public void CreateLeadAsync_WhenThereIsAlreadyAnAccountWithEmail_ShouldThrowException()
        {
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = true }));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            leadService.Invoking(x => x.CreateLeadAsync(new LeadCreateRequest { PhoneNumber = "*********", CountryPrefix = "+966", OwnerEmail = "<EMAIL>" }, userId))
                .Should().ThrowAsync<ServiceException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.AccountEmailAlreadyExists.Code);
        }

        [Test]
        public void CreateLeadAsync_WhenLeadHasSalesId_ShouldPatchLead()
        {
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            leadService.Invoking(x => x.CreateLeadAsync(
                new LeadCreateRequest
                {
                    PhoneNumber = "+*********",
                    CountryPrefix = "+966",
                    SalesId = "GDV99999",
                    LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" }
                }, userId))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void CreateLeadAsync_WhenLeadHasSalesPartnerId_ShouldPatchLead()
        {
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            leadService.Invoking(x => x.CreateLeadAsync(
                new LeadCreateRequest
                {
                    PhoneNumber = "+*********",
                    CountryPrefix = "+966",
                    SalesPartnerId = "GDV99999",
                    LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" }
                }, userId))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteLeadCallAsync_WhenCoreServiceThrowsError_ThrowsPassthroughException()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound, string.Empty));

            leadService.Invoking(x => x.DeleteLeadCallAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteLeadCallAsync_WhenCoreReturns_ShouldNotThrowException()
        {
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, string.Empty));

            leadService.Invoking(x => x.DeleteLeadCallAsync(Guid.NewGuid()))
                .Should().NotThrowAsync<Exception>();
        }

        //NationalId has less than 14 characters
        [TestCase(Constants.BusinessType.Limited, "5559993331", "1", null, Utils.Common.Constants.CounterpartyEgypt)]
        //NationalId has more than 14 characters
        [TestCase(Constants.BusinessType.Limited, "*********9", "111111111111111111", null, Utils.Common.Constants.CounterpartyEgypt)]
        [TestCase(Constants.BusinessType.Limited, "*********9", "111111111111111111111111111111111", null, Utils.Common.Constants.CounterpartyUae)]
        [TestCase(Constants.BusinessType.Limited, "*********9", "111111111111111111111111111111111", null, Utils.Common.Constants.CounterpartySaudi)]
        [Test]
        public void CreateLeadAsync_WhenInvalidNationalId_ShouldThrowValidationException(string businessType, string phoneNumber, string nationalId, string registrationNumber, string counterParty)
        {
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(counterParty);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            leadService.Invoking(x => x.CreateLeadAsync(
                new LeadCreateRequest
                {
                    PhoneNumber = phoneNumber,
                    CountryPrefix = "+20",
                    NationalId = nationalId,
                    LeadDetails = new LeadDetails
                    {
                        BusinessType = businessType,
                        RegistrationNumber = registrationNumber
                    }
                }, userId))
                .Should().ThrowAsync<ValidationException>();
        }

        [Test]
        public async Task CreateLeadAsyncForSaudi_WhenLeadHasDocuments_CallsDocumentService()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            applicationLanguage.Setup(x => x.GetLanguage()).Returns(Geidea.Utils.Common.Constants.ApplicationLanguageEnglish);

            IFormFile bankFile = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy  bank file")), 0, 0, "Bank", "bank.txt");

            var bankMetadata = new DocumentMetadata
            {
                DocumentType = Constants.DocumentType.BankStatement,
                Id = Guid.NewGuid(),
                StreamId = Guid.NewGuid(),
                OwnerUserId = userId,
                Uri = "uri",
                Version = 1,
                Name = "Bank",
                CreatedBy = userId.ToString()
            };
            var leadProductId = Guid.NewGuid();
            var leadResponse = new Lead { PhoneNumber = "*********", CountryPrefix = "+966", LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader } };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            documentService.Setup(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.File == bankFile))).Returns(Task.FromResult(bankMetadata));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "*********",
                CountryPrefix = "+966",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader },
                BankDocuments = new List<IFormFile> { bankFile },
                ReferralChannel = "test",
                LeadProductIds = new List<Guid> { leadProductId }
            };

            var result = await leadService.CreateLeadAsync(leadCreateRequest, userId);

            result.PhoneNumber.Should().Be(leadCreateRequest.PhoneNumber);
            result.CountryPrefix.Should().Be(leadCreateRequest.CountryPrefix);
            result.LeadDetails!.BusinessType.Should().Be(Constants.BusinessType.SoleTrader);
            result.LeadDocuments.Count.Should().Be(1);
            result.LeadDocuments[0].Should().BeEquivalentTo(bankMetadata);

            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.BankStatement && x.File == bankFile)), Times.Once);
            activeCampaignService.Invocations.Clear();
        }

        [Test]
        public void CreateLeadAsync_WhenCallReturnsError_CallbackMethodIsCalled()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            IFormFile bankFile = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy  bank file")), 0, 0, "Bank", "bank.txt");

            var leadResponse = new Lead { LeadId = Guid.NewGuid(), PhoneNumber = "*********", CountryPrefix = "+966", LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" } };
            var leadProductId = Guid.NewGuid();
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            documentService.Setup(x => x.CreateDocumentAsync(It.IsAny<DocumentRequest>())).ThrowsAsync(new Exception("ex"));
            documentService.Setup(x => x.DeleteDocumentsForLeadAsync(It.IsAny<Guid>())).Returns(Task.CompletedTask);
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "*********",
                CountryPrefix = "+966",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" },
                BankDocuments = new List<IFormFile> { bankFile },
                ReferralChannel = "test",
                LeadProductIds = new List<Guid> { leadProductId }
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, userId))
                .Should().ThrowAsync<ServiceException>().Where(x => x.StatusCode == HttpStatusCode.InternalServerError && x.ProblemDetails.Type == Errors.GenericError.Code);

            documentService.Verify(x => x.DeleteDocumentsForLeadAsync(It.Is<Guid>(x => x == leadResponse.LeadId)), Times.Once);
            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.BankStatement && x.File == bankFile)), Times.Once);
        }

        [Test]
        public async Task CreateLeadAsync_WhenLeadHasOnlyPhoneAndProduct_ShouldCreateLead()
        {
            activeCampaignService.Invocations.Clear();
            var productId = Guid.NewGuid();
            var leadResponse = new Lead { PhoneNumber = "*********", CountryPrefix = "+966", LeadProducts = new List<LeadProduct> { new LeadProduct { ProductId = productId, LeadProductId = Guid.NewGuid() } } };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "*********",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { productId },
                ReferralChannel = "test"
            };

            var result = await leadService.CreateLeadAsync(leadCreateRequest, userId);

            result.PhoneNumber.Should().Be(leadCreateRequest.PhoneNumber);
            result.CountryPrefix.Should().Be(leadCreateRequest.CountryPrefix);

            activeCampaignService.Verify(x => x.SendActiveCampaignRequest(It.Is<CreateActiveCampaignRequest>(
               x => x.LeadId == result.LeadId && x.UserId == null && x.MerchantId == null
               && x.Language == null && x.CounterParty == "GEIDEA_SAUDI"
               && x.OnboardingStatus == Utils.Common.Constants.ActiveCampaign.LeadCreated)), Times.Once);
        }

        [Test]
        public async Task CreateLeadAsync_WhenBundleIsGenesis_ShouldCreateLead()
        {
            var productId = Guid.NewGuid();
            var leadResponse = new Lead { PhoneNumber = "*********", CountryPrefix = "+966", LeadProducts = new List<LeadProduct> { new LeadProduct { ProductId = productId, LeadProductId = Guid.NewGuid() } } };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] { new Product() { Code = "GENESIS SMART", Type = ProductType.Bundle } }));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "*********",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { productId },
                ReferralChannel = "SABB"
            };

            var result = await leadService.CreateLeadAsync(leadCreateRequest, userId);

            result.PhoneNumber.Should().Be(leadCreateRequest.PhoneNumber);
            result.CountryPrefix.Should().Be(leadCreateRequest.CountryPrefix);
            activeCampaignService.Verify(x => x.SendActiveCampaignRequest(It.Is<CreateActiveCampaignRequest>(
               x => x.LeadId == result.LeadId && x.UserId == null && x.MerchantId == null
               && x.Language == null && x.CounterParty == "GEIDEA_SAUDI"
               && x.OnboardingStatus == Utils.Common.Constants.ActiveCampaign.LeadCreated)), Times.Once);
        }

        [Test]
        public async Task CreateLeadAsync_WhenBundleIsGenesisIdIsNull_ThrowServiceException()
        {
            var leadResponse = new Lead { PhoneNumber = "*********", CountryPrefix = "+966", LeadProducts = new List<LeadProduct> { new LeadProduct { LeadProductId = Guid.NewGuid() } } };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new Product[] { new Product() { Code = "test" } }));
            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<String[]>(), It.IsAny<string>())).Returns(Task.FromResult(new Catalogue[] { new Catalogue() { CatalogueName = Catalogues.ProductCodeToReferralChannel, Key = "GENESIS_SMART", Value = "SABB" } }));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "*********",
                CountryPrefix = "+966",
                ReferralChannel = "SABB"
            };

            await leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .ThrowAsync<ServiceException>().Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task CreateLeadAsyncForSaudi_WhenLeadHasGeneralContract_ShouldSaveGeneralContract()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            IFormFile generalContract = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy general contract file")), 0, 0, "GeneralContract", "generalContract.txt");

            var generalContractMetadata = new DocumentMetadata
            {
                DocumentType = Constants.DocumentType.GeneralContract,
                Id = Guid.NewGuid(),
                StreamId = Guid.NewGuid(),
                OwnerUserId = userId,
                Uri = "uri",
                Version = 1,
                Name = "GeneralContract",
                CreatedBy = userId.ToString()
            };
            var leadProductId = Guid.NewGuid();
            var leadResponse = new Lead { PhoneNumber = "*********", CountryPrefix = "+966" };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            documentService.Setup(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.File == generalContract))).Returns(Task.FromResult(generalContractMetadata));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "*********",
                CountryPrefix = "+966",
                GeneralContractDocuments = new List<IFormFile> { generalContract },
                ReferralChannel = "test",
                LeadProductIds = new List<Guid> { leadProductId }
            };

            var result = await leadService.CreateLeadAsync(leadCreateRequest, userId);

            result.PhoneNumber.Should().Be(leadCreateRequest.PhoneNumber);
            result.CountryPrefix.Should().Be(leadCreateRequest.CountryPrefix);
            result.LeadDocuments.Count.Should().Be(1);
            result.LeadDocuments[0].Should().BeEquivalentTo(generalContractMetadata);

            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.GeneralContract && x.File == generalContract)), Times.Once);
        }

        [Test]
        [TestCase(Utils.Common.Constants.CounterpartyEgypt)]
        public void CreateLeadAsync_LeadProductIdsRequestValidator_WhenLeadProductIdIsNotSpecificForCurrentCounterparty_ShouldThrowValidationException(string counterParty)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(counterParty);
            var productId = new Guid("6072212a-0570-420d-ae5f-4550e05aa7d8");

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+*********",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { productId },
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.ProductNotFound.Code));
        }

        [Test]
        [TestCase(Utils.Common.Constants.CounterpartySaudi)]
        public void CreateLeadAsync_LeadProductIdsRequestValidator_WhenLeadProductIdSpecificForCurrentCounterparty_ShouldNotThrowValidationException(string counterParty)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(counterParty);
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] { new Product() { Id = new Guid("6072212a-0570-420d-ae5f-4550e05aa7d8"), Code = "GENESIS_SMART", Type = ProductType.Bundle } }));
            var productId = new Guid("6072212a-0570-420d-ae5f-4550e05aa7d8");

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+123456777",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { productId },
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        public void CreateLeadAsync_ValidateProductIdsAsync_WhenLeadProductIdIsNotAllowed_ShouldThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] { new Product() { Id = new Guid("98026463-645b-46b8-a963-5ea8325ccf10"), Code = ForbiddenProductCodes.SpectraSp530, Type = ProductType.Terminal } }));
            var productId = new Guid("98026463-645b-46b8-a963-5ea8325ccf10");

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+123456777",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { productId },
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.ProductNotAllowed.Code));
        }

        [Test]
        public void CreateLeadAsync_ValidateProductIdsAsync_WhenLeadProductListIdIsNotAllowed_ShouldThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] {
                new Product() { Id = new Guid("98026463-645b-46b8-a963-5ea8325ccf10"), Code = ForbiddenProductCodes.SpectraSp530, Type = ProductType.Terminal },
                new Product(){ Id = new Guid("8c56b902-4c1c-443a-9168-355698ebc64a"), Code = ForbiddenProductCodes.Vx675, Type = ProductType.Terminal }
            }));

            var firstProductId = new Guid("98026463-645b-46b8-a963-5ea8325ccf10");
            var secondProductId = new Guid("8c56b902-4c1c-443a-9168-355698ebc64a");

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+123456777",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { firstProductId, secondProductId },
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.ProductNotAllowed.Code));
        }

        [Test]
        public void CreateLeadAsync_ValidateProductIdsAsync_WhenLeadProductListIdContainsAllowedAndNotAllowedProducts_ShouldThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] {
                new Product(){ Id = new Guid("969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff"), Code = "Test", Type = ProductType.Bundle },
                new Product() { Id = new Guid("98026463-645b-46b8-a963-5ea8325ccf10"), Code = ForbiddenProductCodes.SpectraSp530, Type = ProductType.Terminal }
            }));

            var allowedProductId = new Guid("969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff");
            var notAllowedProductId = new Guid("98026463-645b-46b8-a963-5ea8325ccf10");

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+123456777",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { allowedProductId, notAllowedProductId },
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.ProductNotAllowed.Code));
        }

        [Test]
        public void CreateLeadAsync_ValidateProductIdsAsync_WhenLeadProductIdIsAllowed_ShouldNotThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] { new Product() { Id = new Guid("969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff"), Code = "Test", Type = ProductType.Bundle } }));
            var allowedProductId = new Guid("969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff");

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+123456777",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { allowedProductId },
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        public void CreateLeadAsync_ValidateProductIdsAsync_WhenLeadProductListIdContainsAllowedProducts_ShouldNotThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] {
                new Product() { Id = new Guid("969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff"), Code = "Test1", Type = ProductType.Bundle },
                new Product() { Id = new Guid("909322c9-5a6c-4fb5-aaba-f0c4c1ce7dff"), Code = "Test2", Type = ProductType.Bundle }
            }));

            var firstAllowedProductId = new Guid("969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff");
            var secondAllowedProductId = new Guid("909322c9-5a6c-4fb5-aaba-f0c4c1ce7dff");

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+123456777",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { firstAllowedProductId },
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase(Utils.Common.Constants.CounterpartyEgypt)]
        [TestCase(Utils.Common.Constants.CounterpartyUae)]
        public async Task CreateLeadAsyncForEgypt_WhenLeadHasDocuments_CallsDocumentService(string counterParty)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(counterParty);

            IFormFile nationalIdFile = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy national id file")), 0, 0, "NationalId", "nationalId.txt");
            IFormFile commercialRegistrationFile = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy commercial registration file")), 0, 0, "CommercialRegistration", "commercialRegistration.txt");
            IFormFile shopPhotoFile = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy shop photo file")), 0, 0, "ShopPhoto", "shopPhoto.txt");

            var nationalIdMetadata = new DocumentMetadata
            {
                DocumentType = Constants.DocumentType.NationalId,
                Id = Guid.NewGuid(),
                StreamId = Guid.NewGuid(),
                OwnerUserId = userId,
                Uri = "uri",
                Version = 1,
                Name = "NationalId",
                CreatedBy = userId.ToString()
            };
            var commercialRegistrationMetadata = new DocumentMetadata
            {
                DocumentType = Constants.DocumentType.CommercialRegistration,
                Id = Guid.NewGuid(),
                StreamId = Guid.NewGuid(),
                OwnerUserId = userId,
                Uri = "uri",
                Version = 1,
                Name = "CommercialRegistration",
                CreatedBy = userId.ToString()
            };
            var shopPhotoMetadata = new DocumentMetadata
            {
                DocumentType = Constants.DocumentType.ShopPhoto,
                Id = Guid.NewGuid(),
                StreamId = Guid.NewGuid(),
                OwnerUserId = userId,
                Uri = "uri",
                Version = 1,
                Name = "ShopPhoto",
                CreatedBy = userId.ToString()
            };
            var leadProductId = Guid.NewGuid();
            var leadResponse = new Lead { PhoneNumber = "**********", CountryPrefix = "+20", LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader } };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            documentService.Setup(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.File == nationalIdFile))).Returns(Task.FromResult(nationalIdMetadata));
            documentService.Setup(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.File == commercialRegistrationFile))).Returns(Task.FromResult(commercialRegistrationMetadata));
            documentService.Setup(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.File == shopPhotoFile))).Returns(Task.FromResult(shopPhotoMetadata));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "**********",
                CountryPrefix = "+20",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader },
                NationalIdDocuments = new List<IFormFile> { nationalIdFile },
                CommercialRegistrationDocuments = new List<IFormFile> { commercialRegistrationFile },
                ShopPhotoDocuments = new List<IFormFile> { shopPhotoFile },
                ReferralChannel = "test",
                LeadProductIds = new List<Guid> { leadProductId }
            };

            var result = await leadService.CreateLeadAsync(leadCreateRequest, userId);

            result.PhoneNumber.Should().Be(leadCreateRequest.PhoneNumber);
            result.CountryPrefix.Should().Be(leadCreateRequest.CountryPrefix);
            result.LeadDetails!.BusinessType.Should().Be(Constants.BusinessType.SoleTrader);
            result.LeadDocuments.Count.Should().Be(3);
            result.LeadDocuments.Should().BeEquivalentTo(new List<DocumentMetadata> { nationalIdMetadata, commercialRegistrationMetadata, shopPhotoMetadata });

            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.NationalId && x.File == nationalIdFile)), Times.Once);
            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.CommercialRegistration && x.File == commercialRegistrationFile)), Times.Once);
            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.ShopPhoto && x.File == shopPhotoFile)), Times.Once);
        }

        [Test]
        [TestCase(Utils.Common.Constants.CounterpartyEgypt)]
        [TestCase(Utils.Common.Constants.CounterpartyUae)]
        public async Task CreateLeadAsyncForEgypt_WhenLeadHasAdditionalDocuments_CallsDocumentService(string counterparty)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);

            IFormFile additionalDocumentsFile = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy additional documents file")), 0, 0, "AdditionalDocuments", "additionalDocuments.txt");

            var additionalDocumentsMetadata = new DocumentMetadata
            {
                DocumentType = Constants.DocumentType.AdditionalDocuments,
                Id = Guid.NewGuid(),
                StreamId = Guid.NewGuid(),
                OwnerUserId = userId,
                Uri = "uri",
                Version = 1,
                Name = "AdditionalDocuments",
                CreatedBy = userId.ToString()
            };
            var leadProductId = Guid.NewGuid();
            var leadResponse = new Lead { PhoneNumber = "**********", CountryPrefix = "+20", LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader } };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            documentService.Setup(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.File == additionalDocumentsFile))).Returns(Task.FromResult(additionalDocumentsMetadata));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "**********",
                CountryPrefix = "+20",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader },
                AdditionalDocuments = new List<IFormFile> { additionalDocumentsFile },
                ReferralChannel = "test",
                LeadProductIds = new List<Guid> { leadProductId }
            };

            var result = await leadService.CreateLeadAsync(leadCreateRequest, userId);

            result.PhoneNumber.Should().Be(leadCreateRequest.PhoneNumber);
            result.CountryPrefix.Should().Be(leadCreateRequest.CountryPrefix);
            result.LeadDetails!.BusinessType.Should().Be(Constants.BusinessType.SoleTrader);
            result.LeadDocuments.Count.Should().Be(1);
            result.LeadDocuments[0].Should().BeEquivalentTo(additionalDocumentsMetadata);

            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.AdditionalDocuments && x.File == additionalDocumentsFile)), Times.Once);
        }

        [Test]
        [TestCase(Utils.Common.Constants.CounterpartyEgypt)]
        [TestCase(Utils.Common.Constants.CounterpartyUae)]
        public async Task CreateLeadAsyncForEgypt_WhenLeadHasBankDocuments_CallsDocumentService(string counterParty)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(counterParty);
            IFormFile bankFile = new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy bank file")), 0, 0, "Bank", "bank.txt");

            var bankMetadata = new DocumentMetadata
            {
                DocumentType = Constants.DocumentType.BankStatement,
                Id = Guid.NewGuid(),
                StreamId = Guid.NewGuid(),
                OwnerUserId = userId,
                Uri = "uri",
                Version = 1,
                Name = "Bank",
                CreatedBy = userId.ToString()
            };
            var leadProductId = Guid.NewGuid();
            var leadResponse = new Lead { PhoneNumber = "**********", CountryPrefix = "+20", LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader } };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            documentService.Setup(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.File == bankFile))).Returns(Task.FromResult(bankMetadata));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(leadResponse)));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "**********",
                CountryPrefix = "+20",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.SoleTrader },
                BankDocuments = new List<IFormFile> { bankFile },
                ReferralChannel = "test",
                LeadProductIds = new List<Guid> { leadProductId }
            };

            var result = await leadService.CreateLeadAsync(leadCreateRequest, userId);

            result.PhoneNumber.Should().Be(leadCreateRequest.PhoneNumber);
            result.CountryPrefix.Should().Be(leadCreateRequest.CountryPrefix);
            result.LeadDetails!.BusinessType.Should().Be(Constants.BusinessType.SoleTrader);
            result.LeadDocuments.Count.Should().Be(1);
            result.LeadDocuments[0].Should().BeEquivalentTo(bankMetadata);

            documentService.Verify(x => x.CreateDocumentAsync(It.Is<DocumentRequest>(x => x.DocumentType == Constants.DocumentType.BankStatement && x.File == bankFile)), Times.Once);
        }

        [Test]
        [TestCase("LeadStatus", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("BusinessDomain", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("PhoneNumber", "*****************")]
        [TestCase("CountryPrefix ", "*****************")]
        [TestCase("OwnerFirstName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerLastName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerFirstNameAr", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerLastNameAr", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerEmail", "<EMAIL>")]
        [TestCase("LegalName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("LegalNameAr", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("NationalId", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("AddressLine", "jxpvdkxiqdmwjionvwfrnvaegpogjoncsegbpbistbqmsinmpaldkknisuictmqpnmtzxatwjbpjtizeynfoxqyyamxikzhjeuyuqhxxdxefpacqbxrlckhheuupometh")]
        [TestCase("City", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("Governorate", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("Area", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("Country", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("UTM", "imcyrcemaungzsdrboqqyrjwfqguwbescmhfryhdfcntxmggblocerfefubaapxwdqevrtleicgtybrtrtziqyqeoygfnbyfvspcyahoctdbuhcshgpggcgbliozooshunoxecfpoyaasojylfjnvcswguvmigixdzqvylzgjteuyqhuukwfukfynshiahbmxvtscxcbqkbmwbxbuoqvzmbakojisfeevflubhuzyfbyfzpcvodjdpcnhzbkslwcvabyfxlcefqqbtxlbccyqyzpyejemnlqmzayvzzpffefjlrkdxaagdjsnfgeqkvdlodemanbraurcchpjmhvcpenilspmweggerkduabeettgxjlemzzyirciqxxjewvtwoksalfwrvxkblrixxmnigtcujrqlwgzvseutzjfdttedydqxculpzyaaaxyyacixzdveyulxxkyoeuzmevrjcuizlhiptascohxztartbgmbbrccjijbrgebfjqcfqmwovvejwirscjfbcefqvkmncgbxwfyuoyfhenwthkjrudzkrrcdbyeswoberrpzbpqsrrjrzafczqhjjwgumvhjmoydonvcezhaugdvypozzdyzaxkuivrluxxaacarjorytzljqwetpaiauplnimneytxrbpnceqeypcrpeaqlbflpzkwxzrykcpsxnlgurzuzkqcbhcvxuydhrunlakmpuenbqfuemgeznqkinvecnsdvpfyfpnvtswhhghlacgkqarylqiyqehcvyzmskngromiyvlwcijrsivzhbnrwylajgpyhquvvrksyhqnwqsthhlulaawcnbxbnriudytdjcdrifdsevmqtlbxdjqupofvusuwgrfgrxypbbatxehkcgutoxiiujbkagjedanujpztbyirxmrabzekeuedcnfwksysuauqnpkzfkwabmfycvdceabszmtwtdszolkhehjakohatjwubzfaog")]
        [TestCase("CRMLeadId", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("TahakomTransactionId", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("SalesId", "fvfzlndlmldrcfhhsoelavajlujteocaxoveyvlzgnirbdwkdipcjmsoejcuhjgidutbtthqxoixryizrqgowtpwfzouiuqispmnaczhapwuczctvfrnzlcyssqfoevnsnnwjhvgxpctgbfzzkffgpzbuuhypvtqshqlcttbtnzbtvkffuibalfqyxpqrbifajhblhtrtyyzxvdjweunpvulqpmconvitforuxwzzeeqguwtbdktqpexvtyfbrfyn")]
        [TestCase("SalesPartnerId", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("Nationality", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("Gender", "ehzuyzhmbawylwlfr")]
        [TestCase("ReferralChannel", "ehzuyzhmbawylwlfr")]
        public void CreateLeadAsyncSaudi_LeadCreateRequestValidator_ShouldThrowValidationException(string propertyName, string value)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest();
            Type leadCreateRequestType = leadCreateRequest.GetType();
            var propertyInfo = leadCreateRequestType.GetProperty(propertyName);
            if (propertyInfo != null)
            {
                propertyInfo.SetValue(leadCreateRequest, value);
            }

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.LeadStatusLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.BusinessDomainLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.PhoneLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.EmailLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CountryPrefixLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.FirstNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.LastNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.OwnerFirstNameArLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.OwnerLastNameArLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.LegalNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.LegalNameArLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.AddressLineLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CityLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CountryLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.UtmLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.SalesIdLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.NationalityLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.GenderLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.ReferralChannelLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.GovernorateLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CrmLeadIdLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.AreaLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.TahakomTransactionIdLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.NationalIdLengthValidation.Code)));
        }

        [Test]
        public void CreateLeadAsyncSaudi_LeadCreateRequestValidator_ShouldNotThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                LeadStatus = "LeadStatus",
                BusinessDomain = "BusinessDomain",
                PhoneNumber = "+12547854",
                CountryPrefix = "+966",
                OwnerFirstName = "OwnerFirstName",
                OwnerLastName = "OwnerLastName",
                OwnerFirstNameAr = "OwnerFirstNameAr",
                OwnerLastNameAr = "OwnerLastNameAr",
                OwnerEmail = "OwnerEmail",
                LegalName = "LegalName",
                LegalNameAr = "LegalNameAr",
                NationalId = "NationalId",
                AddressLine = "AddressLine",
                City = "City",
                Governorate = "Governorate",
                Area = "Area",
                Country = "Country",
                UTM = "UTM",
                CRMLeadId = "CRMLeadId",
                TahakomTransactionId = "TahakomTransactionId",
                SalesId = "SalesId",
                SalesPartnerId = "SalesPartnerId",
                Nationality = "Nationality",
                Gender = "Gender",
                ReferralChannel = "ReferralChannel",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" }
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .NotThrowAsync<ValidationException>();
        }

        [TestCase("wkhniezwtrwqubwopymuxseirzeqibsxa", "RegistrationNumber", "AccountHolderName", "IBAN", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "wkhniezwtrwqubwopymuxseirzeqibsxa", "AccountHolderName", "IBAN", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "RegistrationNumber", "nissiaptiqzrshranoxiteefbuzzyjutnpdphpvuotkjvtihewmfkwlcfgzmobrsjdxvdmxrbabtmitnctestkvggtofnilumeamnyaxugmghtfsrxmpuksdyzppffjvu", "IBAN", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "RegistrationNumber", "AccountHolderName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "RegistrationNumber", "AccountHolderName", "IBAN", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        public void CreateLeadAsyncSaudi_LeadDetailsRequestValidator_ShouldThrowValidationException(string businessType, string registrationNumber, string accountHolderName, string iban, string municipalLicenseNumber)
        {
            activeCampaignService.Invocations.Clear();
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadDetails leadDetails = new LeadDetails()
            {
                BusinessType = businessType,
                RegistrationNumber = registrationNumber,
                AccountHolderName = accountHolderName,
                IBAN = iban,
                MunicipalLicenseNumber = municipalLicenseNumber
            };

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                LeadDetails = leadDetails,
                CountryPrefix = "CountryPrefix"
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.BusinessTypeLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.RegistrationNumberLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.AccountHolderNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.IbanLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.BusinessTypeValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.MunicipalLicenseNumberLengthValidation.Code)));

            activeCampaignService.Verify(x => x.SendActiveCampaignRequest(It.IsAny<CreateActiveCampaignRequest>()), Times.Never);
        }

        [Test]
        public void CreateLeadAsyncSaudi_LeadDetailsRequestValidato_ShouldNotThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadDetails leadDetails = new LeadDetails
            {
                BusinessType = Constants.BusinessType.Limited,
                RegistrationNumber = "**********",
                AccountHolderName = "AccountHolderName",
                IBAN = "IBAN",
                MunicipalLicenseNumber = "MunicipalLicenseNumber"
            };

            LeadCreateRequest leadCreateRequest = new LeadCreateRequest()
            {
                PhoneNumber = "+*********",
                CountryPrefix = "+966",
                LeadDetails = leadDetails
            };

            leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, Guid.NewGuid())).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("LeadStatus", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("BusinessDomain", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("PhoneNumber", "*****************")]
        [TestCase("CountryPrefix", "*****************")]
        [TestCase("OwnerFirstName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerLastName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerFirstNameAr", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerLastNameAr", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("OwnerEmail", "<EMAIL>")]
        [TestCase("LegalName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("LegalNameAr", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("NationalId", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("AddressLine", "jxpvdkxiqdmwjionvwfrnvaegpogjoncsegbpbistbqmsinmpaldkknisuictmqpnmtzxatwjbpjtizeynfoxqyyamxikzhjeuyuqhxxdxefpacqbxrlckhheuupometh")]
        [TestCase("City", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("Country", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("UTM", "imcyrcemaungzsdrboqqyrjwfqguwbescmhfryhdfcntxmggblocerfefubaapxwdqevrtleicgtybrtrtziqyqeoygfnbyfvspcyahoctdbuhcshgpggcgbliozooshunoxecfpoyaasojylfjnvcswguvmigixdzqvylzgjteuyqhuukwfukfynshiahbmxvtscxcbqkbmwbxbuoqvzmbakojisfeevflubhuzyfbyfzpcvodjdpcnhzbkslwcvabyfxlcefqqbtxlbccyqyzpyejemnlqmzayvzzpffefjlrkdxaagdjsnfgeqkvdlodemanbraurcchpjmhvcpenilspmweggerkduabeettgxjlemzzyirciqxxjewvtwoksalfwrvxkblrixxmnigtcujrqlwgzvseutzjfdttedydqxculpzyaaaxyyacixzdveyulxxkyoeuzmevrjcuizlhiptascohxztartbgmbbrccjijbrgebfjqcfqmwovvejwirscjfbcefqvkmncgbxwfyuoyfhenwthkjrudzkrrcdbyeswoberrpzbpqsrrjrzafczqhjjwgumvhjmoydonvcezhaugdvypozzdyzaxkuivrluxxaacarjorytzljqwetpaiauplnimneytxrbpnceqeypcrpeaqlbflpzkwxzrykcpsxnlgurzuzkqcbhcvxuydhrunlakmpuenbqfuemgeznqkinvecnsdvpfyfpnvtswhhghlacgkqarylqiyqehcvyzmskngromiyvlwcijrsivzhbnrwylajgpyhquvvrksyhqnwqsthhlulaawcnbxbnriudytdjcdrifdsevmqtlbxdjqupofvusuwgrfgrxypbbatxehkcgutoxiiujbkagjedanujpztbyirxmrabzekeuedcnfwksysuauqnpkzfkwabmfycvdceabszmtwtdszolkhehjakohatjwubzfaog")]
        [TestCase("SalesId", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        [TestCase("Nationality", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz")]
        [TestCase("Gender", "ehzuyzhmbawylwlfr")]
        [TestCase("ReferralChannel", "ehzuyzhmbawylwlfr")]
        public void UpdateLeadAsync_LeadValidator_ShouldThrowValidationException(string path, string value)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            var leadPatch = new JsonPatchDocument<Lead>();

            leadPatch.Operations.Add(new Operation<Lead>("replace", path, null, value));

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), leadPatch)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.LeadStatusLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.BusinessDomainLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.PhoneLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.EmailLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CountryPrefixLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.FirstNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.LastNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.OwnerFirstNameArLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.OwnerLastNameArLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.LegalNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.LegalNameArLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.AddressLineLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CityLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CountryLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.UtmLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.SalesIdLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.NationalityLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.GenderLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.ReferralChannelLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.NationalIdLengthValidation.Code)));
        }

        [Test]
        public void UpdateLeadAsync_LeadValidator_ShouldNotThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            Lead leadRequest = new Lead()
            {
                LeadStatus = "LeadStatus",
                BusinessDomain = "BusinessDomain",
                PhoneNumber = "+12547854",
                CountryPrefix = "+5874",
                OwnerFirstName = "OwnerFirstName",
                OwnerLastName = "OwnerLastName",
                OwnerFirstNameAr = "OwnerFirstNameAr",
                OwnerLastNameAr = "OwnerLastNameAr",
                OwnerEmail = "OwnerEmail",
                LegalName = "LegalName",
                LegalNameAr = "LegalNameAr",
                NationalId = "NationalId",
                AddressLine = "AddressLine",
                City = "City",
                Country = "Country",
                UTM = "UTM",
                SalesId = "SalesId",
                Nationality = "Nationality",
                Gender = "Gender",
                ReferralChannel = "ReferralChannel",
            };

            var leadPatch = new JsonPatchDocument<Lead>();

            leadPatch.Replace(e => e.LeadStatus, leadRequest.LeadStatus);
            leadPatch.Replace(e => e.BusinessDomain, leadRequest.BusinessDomain);
            leadPatch.Replace(e => e.PhoneNumber, leadRequest.PhoneNumber);
            leadPatch.Replace(e => e.CountryPrefix, leadRequest.CountryPrefix);
            leadPatch.Replace(e => e.OwnerFirstName, leadRequest.OwnerFirstName);
            leadPatch.Replace(e => e.OwnerLastName, leadRequest.OwnerLastName);
            leadPatch.Replace(e => e.OwnerFirstNameAr, leadRequest.OwnerFirstNameAr);
            leadPatch.Replace(e => e.OwnerLastNameAr, leadRequest.OwnerLastNameAr);
            leadPatch.Replace(e => e.OwnerEmail, leadRequest.OwnerEmail);
            leadPatch.Replace(e => e.LegalName, leadRequest.LegalName);
            leadPatch.Replace(e => e.LegalNameAr, leadRequest.LegalNameAr);
            leadPatch.Replace(e => e.NationalId, leadRequest.NationalId);
            leadPatch.Replace(e => e.AddressLine, leadRequest.AddressLine);
            leadPatch.Replace(e => e.City, leadRequest.City);
            leadPatch.Replace(e => e.Country, leadRequest.Country);
            leadPatch.Replace(e => e.UTM, leadRequest.UTM);
            leadPatch.Replace(e => e.SalesId, leadRequest.SalesId);
            leadPatch.Replace(e => e.Nationality, leadRequest.Nationality);
            leadPatch.Replace(e => e.Gender, leadRequest.Gender);
            leadPatch.Replace(e => e.ReferralChannel, leadRequest.ReferralChannel);

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), leadPatch)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("wkhniezwwkhniezwtrwqubwopymuxseirzeqibsxatrwqubwopymuxseirzeqibsxa")]
        public void UpdateLeadAsync_LeadProductValidator_ShouldThrowValidationException(string productCode)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadProduct leadProduct = new LeadProduct()
            {
                ProductCode = productCode,
                ProductId = Guid.NewGuid()
            };

            List<LeadProduct> leadProductList = new List<LeadProduct>();
            leadProductList.Add(leadProduct);

            Lead leadRequest = new Lead()
            {
                LeadProducts = leadProductList
            };

            var leadPatch = new JsonPatchDocument<Lead>();

            leadPatch.Replace(e => e.LeadProducts, leadRequest.LeadProducts);

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), leadPatch)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.ProductCodeLengthValidation.Code));
        }

        [Test]
        public void UpdateLeadAsync_LeadProductValidator_ShouldNotThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadProduct leadProduct = new LeadProduct()
            {
                ProductCode = "ProductCode",
                ProductId = Guid.NewGuid()
            };

            List<LeadProduct> leadProductList = new List<LeadProduct>();
            leadProductList.Add(leadProduct);

            Lead leadRequest = new Lead()
            {
                LeadProducts = leadProductList
            };

            var leadPatch = new JsonPatchDocument<Lead>();

            leadPatch.Replace(e => e.LeadProducts, leadRequest.LeadProducts);

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), leadPatch)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("wkhniezwtrwqubwopymuxseirzeqibsxa", "RegistrationNumber", "AccountHolderName", "IBAN", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "wkhniezwtrwqubwopymuxseirzeqibsxa", "AccountHolderName", "IBAN", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "RegistrationNumber", "nissiaptiqzrshranoxiteefbuzzyjutnpdphpvuotkjvtihewmfkwlcfgzmobrsjdxvdmxrbabtmitnctestkvggtofnilumeamnyaxugmghtfsrxmpuksdyzppffjvu", "IBAN", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "RegistrationNumber", "AccountHolderName", "ztvqxjjxhqjuhthjpuegdsopzzfvttfwxljaydbjynlzuwhwyfhyqvslnqeduiptz", "MunicipalLicenseNumber")]
        [TestCase(Constants.BusinessType.Limited, "RegistrationNumber", "AccountHolderName", "IBAN", "wkhniezwtrwqubwopymuxseirzeqibsxa")]
        public void UpdateLeadAsync_LeadDetailsValidator_ShouldThrowValidationException(string businessType, string registrationNumber, string accountHolderName, string iban, string municipalLicenseNumber)
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadDetails leadDetails = new LeadDetails()
            {
                BusinessType = businessType,
                RegistrationNumber = registrationNumber,
                AccountHolderName = accountHolderName,
                IBAN = iban,
                MunicipalLicenseNumber = municipalLicenseNumber
            };

            Lead leadRequest = new Lead()
            {
                LeadDetails = leadDetails
            };

            var leadPatch = new JsonPatchDocument<Lead>();

            leadPatch.Replace(e => e.LeadDetails, leadRequest.LeadDetails);

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), leadPatch)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.BusinessTypeLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.RegistrationNumberLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.AccountHolderNameLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.IbanLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.BusinessTypeValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.MunicipalLicenseNumberLengthValidation.Code)));
        }

        [Test]
        public void UpdateLeadAsync_LeadDetailsValidator_ShouldNotThrowValidationException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            LeadDetails leadDetails = new LeadDetails
            {
                BusinessType = Constants.BusinessType.Limited,
                RegistrationNumber = "**********",
                AccountHolderName = "AccountHolderName",
                IBAN = "IBAN",
                MunicipalLicenseNumber = "MunicipalLicenseNumber"
            };

            Lead leadRequest = new Lead()
            {
                LeadDetails = leadDetails
            };

            var leadPatch = new JsonPatchDocument<Lead>();

            leadPatch.Replace(e => e.LeadProducts, leadRequest.LeadProducts);

            leadService.Invoking(x => x.UpdateLeadAsync(Guid.NewGuid(), leadPatch)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        public async Task UpdateLeadAsyncWithReferralChannel_IfRestrictedReferralChannel_ThrowsException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);

            var catalogue = new Catalogue()
            {
                CatalogueName = Constants.Catalogues.ReferralChannelRestrictions,
                Key = "SABB",
                Value = "SABB Genesis"
            };

            referenceService.Setup(x =>
                    x.GetCataloguesAsync(new[] { Constants.Catalogues.ReferralChannelRestrictions }, "en"))
                .Returns(Task.FromResult(new[] { catalogue }));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", "referralChannel", "", "SABB"));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(Guid.NewGuid(), patch))
                .Should().ThrowAsync<ServiceException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task UpdateLeadAsyncWithReferralChannel_IfLeadHasRestrictedReferralChannel_ThrowsException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);

            var catalogue = new Catalogue()
            {
                CatalogueName = Constants.Catalogues.ReferralChannelRestrictions,
                Key = "SABB",
                Value = "SABB Genesis"
            };

            referenceService.Setup(x =>
                    x.GetCataloguesAsync(new[] { Constants.Catalogues.ReferralChannelRestrictions }, "en"))
                .Returns(Task.FromResult(new[] { catalogue }));

            var leadId = Guid.NewGuid();
            var lead = new Lead()
            {
                LeadId = leadId,
                ReferralChannel = "SABB"
            };
            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", "referralChannel", "", "UNASSIGNED"));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(leadId, patch))
                .Should().ThrowAsync<ServiceException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task UpdateLeadAsyncWithReferralChannel_ShouldNotThrowException()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);

            var catalogue = new Catalogue()
            {
                CatalogueName = Constants.Catalogues.ReferralChannelRestrictions,
                Key = "SABB",
                Value = "SABB Genesis"
            };

            referenceService.Setup(x =>
                    x.GetCataloguesAsync(new[] { Constants.Catalogues.ReferralChannelRestrictions }, "en"))
                .Returns(Task.FromResult(new[] { catalogue }));

            var leadId = Guid.NewGuid();
            var lead = new Lead()
            {
                LeadId = leadId,
                ReferralChannel = "test"
            };

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", "referralChannel", "", "UNASSIGNED"));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(leadId, patch))
                .Should().NotThrowAsync<ServiceException>();
        }

        [Test]
        public async Task UpdateLeadAsync_ChangeBusinessInfo_IncompleteData()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);

            var catalogue = new Catalogue()
            {
                CatalogueName = Constants.Catalogues.ReferralChannelRestrictions,
                Key = "SABB_GENESIS",
                Value = "SABB Genesis"
            };

            referenceService.Setup(x =>
                    x.GetCataloguesAsync(new[] { Constants.Catalogues.ReferralChannelRestrictions }, "en"))
                .Returns(Task.FromResult(new[] { catalogue }));

            var leadId = Guid.NewGuid();
            var lead = new Lead()
            {
                LeadId = leadId,
                ReferralChannel = "SABB"
            };

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", "leadDetails/businessType", "", "LIMITED"));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(leadId, patch))
                .Should().NotThrowAsync<ServiceException>();
        }

        [Test]
        public async Task UpdateLeadAsync_ChangeBusinessInfo_Limited()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);

            var catalogue = new Catalogue()
            {
                CatalogueName = Catalogues.ReferralChannelRestrictions,
                Key = "SABB_GENESIS",
                Value = "SABB Genesis"
            };

            _ = referenceService.Setup(x =>
                      x.GetCataloguesAsync(new[] { Constants.Catalogues.ReferralChannelRestrictions }, "en"))
                .Returns(Task.FromResult(new[] { catalogue }));

            var leadId = Guid.NewGuid();
            var lead = new Lead()
            {
                LeadId = leadId,
                ReferralChannel = "SABB"
            };

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", "leadDetails/businessType", "", BusinessType.Limited));
            patch.Operations.Add(new Operation<Lead>("replace", "leadDetails/registrationNumber", "", "1234"));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(leadId, patch))
                .Should().NotThrowAsync<ServiceException>();
        }

        [Test]
        public async Task UpdateLeadAsync_ChangeBusinessInfo_MunicipalEntity()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);

            var catalogue = new Catalogue()
            {
                CatalogueName = Catalogues.ReferralChannelRestrictions,
                Key = "SABB_GENESIS",
                Value = "SABB Genesis"
            };

            referenceService.Setup(x =>
                    x.GetCataloguesAsync(new[] { Catalogues.ReferralChannelRestrictions }, "en"))
                .Returns(Task.FromResult(new[] { catalogue }));

            var leadId = Guid.NewGuid();
            var lead = new Lead()
            {
                LeadId = leadId,
                ReferralChannel = "SABB"
            };

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", "leadDetails/businessType", "", BusinessType.MunicipalEntity));
            patch.Operations.Add(new Operation<Lead>("replace", "leadDetails/municipalLicenseNumber", "", "1234"));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(leadId, patch))
                .Should().NotThrowAsync<ServiceException>();
        }

        [Test]
        public async Task UpdateLeadAsync_ChangeBusinessInfo_JustBusinessType()
        {
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Utils.Common.Constants.CounterpartySaudi);

            var catalogue = new Catalogue()
            {
                CatalogueName = Catalogues.ReferralChannelRestrictions,
                Key = "SABB_GENESIS",
                Value = "SABB Genesis"
            };

            referenceService.Setup(x =>
                    x.GetCataloguesAsync(new[] { Catalogues.ReferralChannelRestrictions }, "en"))
                .Returns(Task.FromResult(new[] { catalogue }));

            var leadId = Guid.NewGuid();
            var lead = new Lead()
            {
                LeadId = leadId,
                ReferralChannel = "SABB",
                LeadDetails =
                    new LeadDetails
                    {
                        MunicipalLicenseNumber = "123"
                    }
            };

            merchantService.Setup(x => x.ValidateLicenseIdIsUsedAsync(null, BusinessType.MunicipalEntity, null, "123", null)).Throws(new ServiceException(HttpStatusCode.BadRequest));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));

            var patch = new JsonPatchDocument<Lead>();
            patch.Operations.Add(new Operation<Lead>("replace", "leadDetails/businessType", "", BusinessType.MunicipalEntity));

            await leadService.Invoking(async (x) => await x.UpdateLeadAsync(leadId, patch))
                .Should().ThrowAsync<ServiceException>();
        }

        [Test]
        public async Task CreateLeadAsync_DuplicateLicense_ShouldThrowException()
        {
            var productId = Guid.NewGuid();

            merchantCheckOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.MerchantCheckOptions(true));

            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Duplicate license"));

            var leadCreateRequest = new LeadCreateRequest
            {
                PhoneNumber = "*********",
                CountryPrefix = "+966",
                LeadProductIds = new List<Guid> { productId },
                ReferralChannel = "SABB",
                LeadDetails = new LeadDetails
                {
                    BusinessType = "LIMITED",
                    RegistrationNumber = "0000000"
                }
            };

            await leadService.Invoking(x => x.CreateLeadAsync(leadCreateRequest, userId))
                .Should()
                .ThrowAsync<ServiceException>();
        }

        [Test]
        [TestCase("1", "190", Utils.Common.Constants.CounterpartyEgypt, "969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff", ProductCode.GoAir, "1")]
        [TestCase("1", "6THOCT01A", Utils.Common.Constants.CounterpartyEgypt, "969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff", ProductCode.GoAir, "4722")]
        public async Task CreateLeadAsyncEgypt_WhenCityNotBelongToGovernorate_ShouldThrowException(string governorate, string city, string countryPart, string productId, string productCode, string businessDomain)
        {
            var createLeadRequest = CreateLeadSetup(governorate, city, countryPart, productId, productCode, businessDomain);
            await leadService.Invoking(async (x) => await x.CreateLeadAsync(createLeadRequest, userId))
                  .Should().ThrowAsync<ValidationException>();

        }

        [Test]
        [TestCase("1", "6THOCT01A", Utils.Common.Constants.CounterpartyEgypt, "969322c9-5a6c-4fb5-aaba-f0c4c1ce7dff", ProductCode.GoAir, "1")]
        [TestCase("1", "190", Utils.Common.Constants.CounterpartyEgypt, "f4cea6d8-b646-4724-b172-b74955cd6082", ProductCode.GoSmart, "4722")]
        public async Task CreateLeadAsyncEgypt_WhenCityBelongToGovernorate_ShouldNotThrowException(string governorate, string city, string countryPart, string productId, string productCode, string businessDomain)
        {
            var createLeadRequest = CreateLeadSetup(governorate, city, countryPart, productId, productCode, businessDomain);
            await leadService.Invoking(async (x) => await x.CreateLeadAsync(createLeadRequest, userId))
                .Should().NotThrowAsync<ValidationException>();
        }

        private LeadCreateRequest CreateLeadSetup(string governorate, string city, string countryPart, string productId, string productCode, string businessDomain)
        {
            var createLeadRequest = new LeadCreateRequest
            {
                PhoneNumber = "+*********",
                CountryPrefix = "+02",
                SalesId = "GDV99999",
                LeadDetails = new LeadDetails { BusinessType = Constants.BusinessType.Limited, RegistrationNumber = "**********" },
                LeadProductIds = new List<Guid> { new Guid(productId) },
                Governorate = governorate,
                City = city,
                Country = countryPart,
                BusinessDomain = businessDomain
            };

            userService.Setup(x => x.CheckUserExistsAsync(It.IsAny<UserExistsRequest>())).Returns(Task.FromResult(new UserExistsResponse { PhoneIsUsed = false, EmailIsUsed = false }));
            productService.Setup(x => x.GetProductsByIdsAsync(It.IsAny<Guid[]>())).Returns(Task.FromResult(new[] { new Product() { Id = new Guid(productId), Code = productCode, Type = ProductType.Bundle } }));
            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<String[]>(), It.IsAny<string>())).Returns(Task.FromResult(TestsHelper.catalogueResponse));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);


            CreateLeadService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(lead)));
            return createLeadRequest;

        }
    }
}
