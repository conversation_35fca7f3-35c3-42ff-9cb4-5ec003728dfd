﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models
{
    public class RiskResponseModel
    {
        public ComplianceRiskModel? ComplianceRisk { get; set; }
        public AcquiringRiskModel? AcquiringRisk { get; set; }
    }

    public class ComplianceRiskModel
    {
        public int RiskScore { get; set; }
        public string? RiskLevel { get; set; }
        public string? DueDiligenceLevel { get; set; }
        public Compliancedescription? ComplianceRiskdescription { get; set; }

    }

    public class AcquiringRiskModel
    {
        public string BusinessID { get; set; } = string.Empty;
        public string AggregateRiskLevel { get; set; } = string.Empty;
        public AcquiringRiskKeyValue? OfficeType { get; set; }
        public AcquiringRiskKeyValue? MerchantLocation { get; set; }
        public AcquiringRiskKeyValue? ServiceDelivery { get; set; }
        public List<AcquiringRiskKeyValueList> TransactionDetails { get; set; } = new List<AcquiringRiskKeyValueList>();
        public string HighestSingleTransactionValue { get; set; } = string.Empty;
        public string MCCRisk { get; set; } = string.Empty;
    }

    public class AcquiringRiskKeyValue
    {
        public string? Value { get; set; }
        public string? Level { get; set; }
    }

    public class AcquiringRiskKeyValueList
    {
        public string AccountId { get; set; } = string.Empty;
        public TransactionTypeDetail? TransactionTypes { get; set; }
        public TransactionModeDetail? TransactionMode { get; set; }
    }

    public class TransactionTypeDetail
    {
        public List<string>? Value { get; set; }
        public string? Level { get; set; }
    }

    public class TransactionModeDetail
    {
        public string? Value { get; set; }
        public string? Level { get; set; }
    }
    public class RiskParameterDetail
    {
        public string? Value { get; set; }
        public int? Score { get; set; }
    }
    public class Compliancedescription
    {
        public RiskParameterDetail? CompanyLegalType { get; set; }
        public RiskParameterDetail? HighestSingleTransaction { get; set; }
        public RiskParameterDetail? MaxMonthlyTransaction { get; set; }
        public RiskParameterDetail? MCC { get; set; }
        public RiskParameterDetail? shareholdersnationality { get; set; }
        public RiskParameterDetail? shareholdersPEP { get; set; }
    }

}
