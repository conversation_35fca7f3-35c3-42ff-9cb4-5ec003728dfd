﻿using System;
using UtilsConstants = Geidea.Utils.Common.Constants;
using Geidea.Utils.ConditionalSerialization;

namespace Common.Models.Merchant
{
    public class MerchantResult
    {
        public Guid MerchantId { get; set; }
        public Guid? LeadId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? BusinessName { get; set; }
        public string? BusinessNameAr { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? BusinessDomain { get; set; }

        public decimal AnnualTurnover { get; set; }
        public string? Currency { get; set; }
        public string? OutletType { get; set; }
        public string? MerchantStatus { get; set; }
        public DateTime CreatedDate { get; set; }
        public int NumberOfStores { get; set; }
        public string? MemberId { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? Tag { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? ReferralChannel { get; set; }

        public string? NationalId { get; set; }
        public string? AcquirerReview { get; set; }
        public string? AcquiringLedger { get; set; }
        public int MerchantCommentsCount { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? SalesId { get; set; }

        public string? BusinessType { get; set; }
        public string? BusinessPhone { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? SalesFirstName { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? SalesLastName { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? SalesPartnerId { get; set; }

        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? BankCheckStatus { get; set; }
        public string? Mid { get; set; }
        public string? LinkedChainId { get; set; }
        public string? LinkedChainName { get; set; }
        public string? Street { get; set; }
        public string? Area { get; set; }
        public string? City { get; set; }
        public string? ZipCode { get; set; }
        public string? Country { get; set; }
    }
}
