﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class Merchant : MerchantBaseClass
{
    [Display(Name = "Concatinated Address", Order = 2)]
    public Address Address { get; set; } = new Address();

    [Display(Name = "Urls", Order = 9)]
    public string[] Url { get; set; } = Array.Empty<string>();

    [Display(Name = "Principals", Order = int.MaxValue)]
    public Principal[] Principal { get; set; } = Array.Empty<Principal>();

    [Display(Name = "Search Criteria", Order = int.MaxValue)]
    public SearchCriteria SearchCriteria { get; set; } = new SearchCriteria();

    [Display(Name = "Added on date", Order = int.MaxValue)]
    public string AddedOnDate { get; set; } = string.Empty;

    [Display(Name = "Termination Reason Code", Order = int.MaxValue)]
    public string TerminationReasonCode { get; set; } = string.Empty;

    [Display(Name = "Added by accuirer Id", Order = int.MaxValue)]
    public string AddedByAcquirerID { get; set; } = string.Empty;

    [Display(Name = "Url Group", Order = 10)]
    public UrlGroup[] UrlGroup { get; set; } = Array.Empty<UrlGroup>();

    [Display(Name = "Comments", Order = int.MaxValue)]
    public string Comments { get; set; } = string.Empty;

    [Display(Name = "Match Check Status", Order = int.MaxValue)]
    public string MerchantStatus { get; set; } = string.Empty;
}