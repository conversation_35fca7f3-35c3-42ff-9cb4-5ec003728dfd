﻿using System;

namespace Common.Models.Comment
{
    public class CommentResponse    
    {
        public Guid CommentId { get; set; }

        public string CommentText { get; set; } = null!;

        public bool DeletedFlag { get; set; } = false;

        public string CreatedBy { get; set; } = null!;

        public DateTime CreatedDate { get; set; }

        public string? UpdatedBy { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string AuthorName { get; set; } = string.Empty;

        public string AuthorRole { get; set; } = string.Empty;
    }
}
