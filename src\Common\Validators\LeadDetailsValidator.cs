﻿using System.Collections.Generic;
using Common.Models.Lead;
using FluentValidation;
namespace Common.Validators
{
    public class LeadDetailsValidator : AbstractValidator<LeadDetails>
    {
        public LeadDetailsValidator(string counterparty)
        {
            ClassLevelCascadeMode = CascadeMode.Stop;
            var businessTypes = new List<string> {
                Constants.BusinessType.Limited,
                Constants.BusinessType.SoleTrader,
                Constants.BusinessType.MunicipalEntity,
                Constants.BusinessType.LegalEnterprise
            };

            RuleFor(x => x.BusinessType)
                .Must(x => x!.Length <= 32)
                .When(x => x.BusinessType != null)
                .WithErrorCode(Errors.BusinessTypeLengthValidation.Code)
                .WithMessage(Errors.BusinessTypeLengthValidation.Message)
                .Must(x => businessTypes.Contains(x!))
                .When(x => x.BusinessType != null)
                .WithMessage("Please only use: " + string.Join(",", businessTypes))
                .WithErrorCode(Errors.BusinessTypeValidation.Code)
                .WithMessage(Errors.BusinessTypeValidation.Message);

            RuleFor(x => x.RegistrationNumber)
                .Must(x => x!.Length <= 32)
                .When(x => x.RegistrationNumber != null)
                .WithErrorCode(Errors.RegistrationNumberLengthValidation.Code)
                .WithMessage(Errors.RegistrationNumberLengthValidation.Message);

            RuleFor(x => x.AccountHolderName)
               .Must(x => x!.Length <= 128)
               .When(x => x.AccountHolderName != null)
               .WithErrorCode(Errors.AccountHolderNameLengthValidation.Code)
               .WithMessage(Errors.AccountHolderNameLengthValidation.Message);

            if (counterparty == Geidea.Utils.Common.Constants.CounterpartySaudi)
            {
                When(x => x.IBAN != null && x.IBAN != "", () =>
                {
                    RuleFor(x => x.IBAN)
                        .SetValidator(new IbanValidator());
                });

                When(x => x.BusinessType == "SOLE_TRADER", () =>
                {
                    RuleFor(x => x.RegistrationNumber)
                        .Matches("^(FL|fl)-[0-9]{0,27}?$|^[a-z0-9]{8,16}$")
                        .When(x => x.RegistrationNumber != null)
                        .WithErrorCode(Errors.RegistrationNumberFormatValidation.Code)
                        .WithMessage(Errors.RegistrationNumberFormatValidation.Message);
                });
                When(x => x.BusinessType != "SOLE_TRADER", () =>
                {
                    RuleFor(x => x.RegistrationNumber)
                        .Matches("^[a-zA-Z0-9]+$")
                        .When(x => x.RegistrationNumber != null)
                        .WithErrorCode(Errors.RegistrationNumberFormatValidation.Code)
                        .WithMessage(Errors.RegistrationNumberFormatValidation.Message);
                });
            }
            else
            {
                RuleFor(x => x.IBAN)
                    .Must(x => x!.Length <= 64)
                    .When(x => x.IBAN != null)
                    .WithErrorCode(Errors.IbanLengthValidation.Code)
                    .WithMessage(Errors.IbanLengthValidation.Message);
            }

            RuleFor(x => x.MunicipalLicenseNumber)
                .Must(x => x!.Length <= 32)
                .When(x => x.MunicipalLicenseNumber != null)
                .WithErrorCode(Errors.MunicipalLicenseNumberLengthValidation.Code)
                .WithMessage(Errors.MunicipalLicenseNumberLengthValidation.Message);

            RuleFor(m => m.LegalId)
                .Matches(@"^7\d{0,9}$")
                .When(m => !string.IsNullOrWhiteSpace(m.LegalId))
                .WithErrorCode(Errors.InvalidLegalId.Code)
                .WithMessage(Errors.InvalidLegalId.Message);

            RuleFor(x => x.BankAccountNumber).
                Null().
                When(_ => counterparty != Geidea.Utils.Common.Constants.CounterpartyEgypt).
                WithErrorCode(Errors.BankAccountNumber_InvalidCounterparty.Code).
                WithMessage(Errors.BankAccountNumber_InvalidCounterparty.Message).
                DependentRules(() =>
                {
                    RuleFor(x => x.BankAccountNumber).
                        Must(x => x!.Length <= 34).
                        When(x => x.BankAccountNumber != null).
                        WithErrorCode(Errors.BankAccountNumber_Max34.Code).
                        WithMessage(Errors.BankAccountNumber_Max34.Message).
                        DependentRules(() =>
                        {
                            RuleFor(x => x.BankAccountNumber).
                                Must(x => x!.Trim() != string.Empty).
                                When(x => x.BankAccountNumber != null).
                                WithErrorCode(Errors.BankAccountNumber_Invalid.Code).
                                WithMessage(Errors.BankAccountNumber_Invalid.Message);
                        });
                });

            RuleFor(x => x.RefBankId).
                Null().
                When(_ => counterparty != Geidea.Utils.Common.Constants.CounterpartyEgypt).
                WithErrorCode(Errors.RefBankId_InvalidCounterparty.Code).
                WithMessage(Errors.RefBankId_InvalidCounterparty.Message);
        }
    }
}
