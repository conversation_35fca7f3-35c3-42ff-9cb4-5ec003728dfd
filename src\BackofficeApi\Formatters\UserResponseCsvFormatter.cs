﻿using BackofficeApi.Formatters.ClassMappers;
using Common.Models.Lead;
using CsvHelper.Configuration;
using CsvHelper;
using Microsoft.AspNetCore.Mvc.Formatters;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading.Tasks;
using Common.Models.User;
using System.Linq;
using System.Reflection;
using System;
using Microsoft.Net.Http.Headers;

namespace BackofficeApi.Formatters
{
    public class UserResponseCsvFormatter : TextOutputFormatter
    {
        public UserResponseCsvFormatter()
        {
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/csv"));
            SupportedEncodings.Add(Encoding.UTF8);
        }

        protected override bool CanWriteType(Type? type)
        {
            if (typeof(IEnumerable<UserExportModel>).IsAssignableFrom(type))
            {
                return true;
            }
            return false;
        }

        public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
        {
            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                InjectionOptions = InjectionOptions.Escape
            };
            using var streamWriter = context.WriterFactory(context.HttpContext.Response.Body, selectedEncoding);
            using var csv = new CsvWriter(streamWriter, config);
            csv.Context.RegisterClassMap<UserExportResponseMap>();
            csv.WriteHeader(typeof(UserExportModel));
            await csv.NextRecordAsync();

            var users = context.Object as IEnumerable<UserExportModel>;
            if (users != null)
                users.ToList().ForEach(async lead =>
                {
                    csv.WriteRecord(lead);
                    await csv.NextRecordAsync();
                });
            await streamWriter.FlushAsync();
        }
    }
}
