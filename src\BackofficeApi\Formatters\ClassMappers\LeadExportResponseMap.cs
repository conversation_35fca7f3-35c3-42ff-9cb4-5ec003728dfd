﻿using Common.Models.Lead;
using CsvHelper.Configuration;

namespace BackofficeApi.Formatters.ClassMappers
{
    public class LeadExportResponseMap : ClassMap<LeadExportResponse>
    {
        public LeadExportResponseMap()
        {
            Map(m => m.LeadStatus).Name("Status");
            Map(m => m.OwnerFirstName).Name("First Name EN");
            Map(m => m.OwnerLastName).Name("Last Name EN");
            Map(m => m.OwnerFirstNameAr).Name("First Name AR");
            Map(m => m.OwnerLastNameAr).Name("Last Name AR");
            Map(m => m.OwnerEmail).Name("Email");
            Map(m => m.PhoneNumberWithPrefix).Name("Phone number");
            Map(m => m.UTM).Name("UTM");
            Map(m => m.SalesId).Name("Sales Id");
            Map(m => m.SalesFirstName).Name("Sales First Name");
            Map(m => m.SalesLastName).Name("Sales Last Name");
            Map(m => m.SalesPartnerId).Name("Sales Partner Id");
            Map(m => m.CreatedDate).Name("Created Date");
            Map(m => m.ReferralChannel).Name("Referral Channel");
            Map(m => m.Product).Name("Product");

            Map(m => m.LeadId).Ignore();
        }
    }
}
