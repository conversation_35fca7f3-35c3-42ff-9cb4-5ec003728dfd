﻿using System;

namespace Common.Models.User
{
    public class User
    {
        public Guid Id { get; set; }    
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? CountryPrefix { get; set; }
        public bool IsDisabled { get; set; }
        public DateTime? DisabledDate { get; set; }
        public string? DisabledReason { get; set; }
        public Guid? PersonOfInterestId { get; set; }
        public Guid ActivationKey { get; set; }
        public bool IsEmailConfirmed { get; set; }
        public Guid? LeadId { get; set; }
        public string? SalesId { get; set; }
        public int? RefBankId { get; set; }
        public string? AcquiringLedger { get; set; }
        public string? SalesPartnerId { get; set; }
    }
}