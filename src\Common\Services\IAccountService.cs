﻿using Common.Models.Account;
using Common.Models.Merchant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IAccountService
    {
        Task<AccountSearchResponse<AccountResult>> FindAsync(AccountSearchFilters filters, Guid userId, CancellationToken cancellationToken = default);
        Task<List<AccountExport>> ExportAccountsAsync(AccountSearchFilters searchCriteria);
    }
}
