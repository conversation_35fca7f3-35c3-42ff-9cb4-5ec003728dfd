﻿using Common.Models.Merchant;
using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using System.Threading;
using System;
using Common.Models.Chain;
using System.Collections.Generic;
using Elastic.Apm.Api;
using Common.Models.Account;
using Services;
using CsvHelper.Configuration;
using CsvHelper;
using System.Globalization;
using System.IO;
using System.Text;

namespace BackofficeApi.Controllers;

[Authorize]
[ApiController]
[Route("api/v1")]
public class ChainController : ControllerBase
{
    private readonly IChainService chainService;
    private readonly ILogger<MerchantController> logger;
    private readonly Authorized authorized;

    public ChainController(IChainService chainService, Authorized authorized, ILogger<MerchantController> logger)
    {
        this.chainService = chainService;
        this.authorized = authorized;
        this.logger = logger;
    }

    /// <summary>
    /// Finds all chains based on search criteria in the form of an OData query.
    /// </summary>
    /// <returns>An array of chains objects matching the search criteria.</returns>
    [HttpPost("chains/advancedSearch")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> Find([FromBody] ChainSearchFilters filters, CancellationToken cancellationToken = default)
    {
        if (!await authorized.To.View.ChainManagement())
        {
            return Forbid();
        }

        var response = await chainService.FindAsync(filters, cancellationToken);
        return Ok(response);
    }


    /// <summary>
    /// Export all chains based on search criteria in the form of an OData query.
    /// </summary>
    /// <returns>An array of chains objects matching the search criteria.</returns>
    [Produces("application/json", "text/csv")]
    [HttpPost("chains/export")]
    [ProducesResponseType(typeof(ChainExport[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> ExportChains([FromBody] ChainSearchFilters searchCriteria)
    {
        if (!await authorized.To.Export.ChainManagement())
        {
            return Forbid();
        }

        var csvBuilder = new StringBuilder();
        var accounts = await chainService.ExportChainsAsync(searchCriteria);
        var csvWriter = new CsvWriter(new StringWriter(csvBuilder), new CsvConfiguration(CultureInfo.InvariantCulture));
        csvWriter.WriteRecords(accounts);
        return Content(csvBuilder.ToString());
    }

    /// <summary>
    /// Create Chain.
    /// </summary>
    /// <returns>An Created Chain object.</returns>
    [HttpPost("chain")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CreateChain([FromBody] ChainCreateRequest request)
    {
        if (!await authorized.To.Create.ChainManagement())
        {
            return Forbid();
        }

        var response = await chainService.CreateChain(request);
        return Ok(response);
    }

    /// <summary>
    /// Update Chain.
    /// </summary>
    /// <returns>An Created Chain object.</returns>
    [HttpPut("chain")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateChain([FromBody] ChainUpdateRequest request)
    {
        if (!await authorized.To.Update.ChainDetails())
        {
            return Forbid();
        }

        var response = await chainService.UpdateChain(request);
        return Ok(response);
    }

    /// <summary>
    /// Get Chain.
    /// </summary>
    /// <returns>Chain object will return.</returns>
    [HttpGet("chain/{chainId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetChain(string chainId)
    {
        if (!await authorized.To.View.ChainDetails())
        {
            return Forbid();
        }

        var response = await chainService.GetChain(chainId);
        return Ok(response);
    }

    /// <summary>
    /// Delete Chain.
    /// </summary>
    /// <returns>Delete Chain.</returns>
    [HttpDelete("chain/{chainId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteChain(string chainId)
    {
        if (!await authorized.To.Update.ChainDetails())
        {
            return Forbid();
        }

        await chainService.DeleteChain(chainId);
        return NoContent();
    }

    /// <summary>
    /// Update chain merchants association.
    /// </summary>
    /// <returns>Merchants list associated with the chain will return.</returns>
    [HttpPost("chain/{chainId}/merchants")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateChainMerchants([FromRoute] string chainId, [FromBody] List<ChainMerchantsLinkRequest> request)
    {
        if (!await authorized.To.Update.ChainDetails())
        {
            return Forbid();
        }

        var associatedMerchants = await chainService.AssociateChainMerchants(chainId, request);
        return Ok(associatedMerchants);

    }

    /// <summary>
    /// Get merchants associated with the chain.
    /// </summary>
    /// <returns>Merchants list associated with the chain will return..</returns>
    [HttpGet("chain/{chainId}/merchants")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetChainMerchants([FromRoute] string chainId)
    {
        if (!await authorized.To.View.ChainManagement())
        {
            return Forbid();
        }

        var chainMerchantsData = await chainService.GetChainMerchants(chainId);
        return Ok(chainMerchantsData);
    }

    /// <summary>
    /// Get contacts associated with the chain.
    /// </summary>
    /// <returns>Chain Contacts list associated with the chain will return..</returns>
    [HttpGet("chain/{chainId}/contacts")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetChainContacts([FromRoute] string chainId)
    {
        if (!await authorized.To.View.ChainDetails())
        {
            return Forbid();
        }

        var chainContactsData = await chainService.GetAllContactsOfChain(chainId);
        return Ok(chainContactsData);
    }

    /// <summary>
    /// Get contact based on contactid.
    /// </summary>
    /// <returns>Chain Contact associated with the contactId will return..</returns>
    [HttpGet("chain/contact/{contactId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetChainContact([FromRoute] Guid contactId)
    {
        if (!await authorized.To.View.ChainDetails())
        {
            return Forbid();
        }
        var chainContactData = await chainService.GetContactById(contactId);
        return Ok(chainContactData);
    }

    /// <summary>
    /// Delete Chain Contact.
    /// </summary>
    /// <returns>Delete Chain Contact.</returns>
    [HttpDelete("chain/contact/{contactId}")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteChainContact(Guid contactId)
    {
        if (!await authorized.To.Update.ChainDetails())
        {
            return Forbid();
        }
        await chainService.DeleteChainContact(contactId);
        return NoContent();
    }

    /// <summary>
    /// Create Chain Contacts.
    /// </summary>
    /// <returns>Created Chain Contacts will return.</returns>
    [HttpPost("chain/contacts")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CreateChainContacts([FromBody] ChainContactsCreateRequest request)
    {
        if (!await authorized.To.Create.ChainManagement())
        {
            return Forbid();
        }

        var response = await chainService.CreateChainContacts(request);
        return Ok(response);
    }

    /// <summary>
    /// Update Chain Contacts.
    /// </summary>
    /// <returns>Created Chain Contacts will return.</returns>
    [HttpPut("chain/contacts")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> UpdateChainContacts([FromBody] ChainContactsUpdateRequest request)
    {
        if (!await authorized.To.Update.ChainDetails())
        {
            return Forbid();
        }

        var response = await chainService.UpdateChainContacts(request);
        return Ok(response);
    }
}