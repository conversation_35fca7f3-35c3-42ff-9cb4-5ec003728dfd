﻿using Common.Models;
using Common.Models.Match;
using Common.Services;
using FluentAssertions;
using Geidea.Utils.Cleanup;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using NUnit.Framework;
using Services;
using System;
using System.Threading.Tasks;
using static Common.Constants.MatchTemplatesFilesSettings;
using static Services.Extensions.ReflectionExtensions;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    internal class MatchReportBuilderServiceTests
    {
        private MatchReportBuilderService matchReportBuilderService;
        private Mock<ILogger<MatchReportBuilderService>> logger = new Mock<ILogger<MatchReportBuilderService>>();
        private readonly CleanupService cleanupService = null!;
        private readonly Mock<ILogger<CleanupService>> loggerCleanup = new Mock<ILogger<CleanupService>>();
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();

        public MatchReportBuilderServiceTests()
        {
            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(Array.Empty<Catalogue>()));

            Mock<ITemplateService> templateService = new Mock<ITemplateService>(MockBehavior.Strict);

            templateService.Setup(x => x.GetReportTemplate()).Returns("$$$Content$$$");
            templateService.Setup(x => x.GetTableRowTemplate()).Returns($"$$$PropertyName$$$ {Environment.NewLine} $$$PropertyValue$$$ {Environment.NewLine} $$$MatchValue$$$ {Environment.NewLine}");
            templateService.Setup(x => x.GetTableHeaderTemplate()).Returns($"$$$EntityName$$$ {Environment.NewLine}");

            matchReportBuilderService = new MatchReportBuilderService(templateService.Object, logger.Object, referenceService.Object);
            cleanupService = new CleanupService(loggerCleanup.Object);
        }

        private static MatchResponse GenerateMatchResponse()
        {
            var response = new MatchResponse()
            {
                TerminationInquiry = new TerminationInquiry()
                {
                    TransactionReferenceNumber = Guid.NewGuid().ToString(),
                    PageOffset = 1,
                    PossibleMerchantMatches = new PossibleMerchantMatch[]
                    {
                        new PossibleMerchantMatch()
                        {
                            TotalLength = 1,
                            TerminatedMerchant = new TerminatedMerchant[]
                            {
                                new TerminatedMerchant()
                                {
                                    Merchant = new Merchant()
                                    {
                                        MerchantStatus = "FAILED",
                                        AddedByAcquirerID = Guid.NewGuid().ToString(),
                                        AddedOnDate = DateTime.UtcNow.ToString(),
                                        Address = new Address()
                                        {
                                            City = "ExampleCity",
                                            Country = "ExampleCountry",
                                            CountrySubDivision = "ExampleCountrySubDivision",
                                            Line1 = "ExampleLine1",
                                            Line2 = "ExampleLine2",
                                            PostalCode = "ExamplePostalCode",
                                            Province = "ExampleProvince"
                                        },
                                        AltPhoneNumber = "ExamplePhoneNumber",
                                        Comments = "ExampleComments",
                                        CountrySubdivisionTaxId = "Example.CountrySubDivision",
                                        DoingBusinessAsName = "Jhon Doe",
                                        Name = "Jhon Doe",
                                        NationalTaxId = "ExampleNotionalTaxId",
                                        PhoneNumber = "test",
                                        Principal = new Principal[]
                                        {
                                            new Principal()
                                            {
                                                PhoneNumber = "ExamplePohone",
                                                AltPhoneNumber = "ExampleAltPhone",
                                                Address = new Address()
                                                {
                                                    City = "ExampleCity",
                                                    Country = "ExampleCountry",
                                                    CountrySubDivision = "ExampleCountrySubDivision",
                                                    Line1 = "ExampleLine1",
                                                    Line2 = "ExampleLine2",
                                                    PostalCode = "ExamplePostalCode",
                                                    Province = "ExampleProvince"
                                                },
                                                DriversLicense = new DriversLicense()
                                                {
                                                    Country = "ExampleCountry",
                                                    CountrySubDivision = "ExampleCountrySubDivision",
                                                    Number = "ExampleNumber"
                                                },
                                                FirstName = "Jhon",
                                                LastName = "Doe",
                                                MiddleInitial = "W",
                                                NationalId = "Exaple"
                                            }
                                        },
                                        ServiceProvDBA = "ExampleProvider",
                                        ServiceProvLegal = "ExampleProvider",
                                        TerminationReasonCode = "Some termination reason",
                                        Url = new string[1]
                                        {
                                            "www.example.com"
                                        },
                                        UrlGroup = new UrlGroup[1]
                                        {
                                            new UrlGroup()
                                            {
                                                CloseMatchUrls = new UrlClass()
                                                {
                                                    Url = new string[1]
                                                    {
                                                        "www.example2.com"
                                                    }
                                                },
                                                ExactMatchUrls =  new UrlClass()
                                                {
                                                    Url = new string[1]
                                                    {
                                                        "www.example.com"
                                                    }
                                                },
                                                NoMatchUrls =  new UrlClass()
                                                {
                                                    Url = new string[1]
                                                    {
                                                        "www.test3.com"
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    MerchantMatch = new MerchantMatch()
                                    {
                                        Address = "M00",
                                        AltPhoneNumber = "M00",
                                        CountrySubdivisionTaxId = "M00",
                                        DoingBusinessAsName = "M00",
                                        Name = "M00",
                                        NationalTaxId = "M00",
                                        PhoneNumber = "M00",
                                        PrincipalMatch = new PrincipalMatch[]
                                        {
                                            new PrincipalMatch()
                                            {
                                                PhoneNumber = "M00",
                                                Name="M00",
                                                AltPhoneNumber= "M00",
                                                Address = "M00",
                                                DriversLicense = "M00",
                                                NationalId = "M00"
                                            }
                                        },
                                        ServiceProvDBA = "M00",
                                        UrlMatch = new UrlMatch[1]
                                        {
                                            new UrlMatch()
                                            {
                                                Url = "M00"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    Ref = "test"
                }
            };

            return response;
        }

        [Test]
        public async Task Service_should_return_valid_report()
        {
            // Arrange
            var response = GenerateMatchResponse();

            // Act
            var report = await matchReportBuilderService.GenerateMatchReportAsync(response);

            // Assert
            report.Should().NotBeEquivalentTo(string.Empty);
        }

        [Test]
        public async Task Service_should_return_report_with_boht_inquiry_and_terminated_merchants()
        {
            // Arrange
            var response = GenerateMatchResponse();

            // Act
            var report = await matchReportBuilderService.GenerateMatchReportAsync(response);

            // Assert
            report.Should().NotBeEquivalentTo(string.Empty);
            report.Should().Contain(GetPropertyDisplayName<PossibleMerchantMatch>(prop => prop.TerminatedMerchant));
        }
    }
}
