﻿using Common.Models.Checkout;
using Common.Models.Merchant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.NexusBridge
{
    public class NBMerchant
    {
        public Guid MerchantId { get; set; }
        public Guid? LeadId { get; set; }
        public string? MerchantType { get; set; }
        public string? MerchantStatus { get; set; }
        //[ReferenceData("MERCHANT_TAG")]
        public string? Tag { get; set; }
        public string? Counterparty { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool DeletedFlag { get; set; }
        public string? ApplicationId { get; set; } = string.Empty;


        public MerchantDetails MerchantDetails { get; set; } = new MerchantDetails();
        public IReadOnlyCollection<MerchantTerminal> MerchantTerminals { get; set; } = new List<MerchantTerminal>();
        public IReadOnlyCollection<MerchantFee> MerchantFee { get; set; } = new List<MerchantFee>();

        public IReadOnlyCollection<MerchantAddress> Addresses { get; set; } = new List<MerchantAddress>();

        public IReadOnlyCollection<MerchantPersonOfInterest> PersonOfInterests { get; set; } = new List<MerchantPersonOfInterest>();
        public IReadOnlyCollection<MerchantBankAccountResponse> BankAccounts { get; set; } = new List<MerchantBankAccountResponse>();
        public IReadOnlyCollection<MerchantCommissionConfig> CommissionTypes { get; set; } = new List<MerchantCommissionConfig>();
        public IReadOnlyCollection<MerchantExternalProduct> ExternalProducts { get; set; } = new List<MerchantExternalProduct>();
        public MerchantAccountConfig AccountConfig { get; set; } = new MerchantAccountConfig();
    }
}
