﻿using Common.Models;
using FluentValidation;

namespace Common.Validators
{
    public class SendCustomEmailRequestValidator : AbstractValidator<SendCustomEmailRequest>
    {
        public SendCustomEmailRequestValidator()
        {
            RuleFor(a => a.Body).NotEmpty();

            RuleFor(a => a.Recipients).NotNull().NotEmpty();

            RuleFor(a => a.Subject)
                .NotEmpty()
                .Must(x => x.Length <= 128)
                .WithErrorCode(Errors.SubjectLengthValidation.Code)
                .WithMessage(Errors.SubjectLengthValidation.Message);
        }
    }
}
