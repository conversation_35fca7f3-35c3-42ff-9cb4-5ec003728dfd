﻿using Common.Models;
using Common.Options;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

using NUnit.Framework;
using Services;
using System.Net;
using System.Threading.Tasks;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using System.Text.Json;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class ReferenceServiceTests
    {
        private readonly Mock<ILogger<ReferenceService>> logger = new Mock<ILogger<ReferenceService>>();
        private ReferenceService referenceService = null!;
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();

        private readonly string[] catalogueNames = new string[] { "ADDRESSES" };
        private readonly string language = "AR";
        private readonly Catalogue catalogue = new Catalogue()
        {
            CatalogueName = "ADDRESSES",
            Value = "Value",
            Key = "Key"
        }; 
        
        public ReferenceServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        }

        [Test]
        public async Task GetCataloguesAsync()
        {
            referenceService = new ReferenceService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(new Catalogue[] { catalogue })));

            var result = await referenceService.GetCataloguesAsync(catalogueNames, language);

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(catalogue);
        }

        [Test]
        public void GetCataloguesAsync_PassthroughtError()
        {
            referenceService = new ReferenceService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            referenceService.Invoking(x => x.GetCataloguesAsync(catalogueNames))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }
    }
}
