﻿using System;
using System.Security.Claims;
using Geidea.Utils.Common;
using Geidea.Utils.Security.Claims;
using Microsoft.AspNetCore.Mvc;

namespace BackofficeApi.Extensions
{
    public static class ControllerBaseExtensions
    {
        public static string GetUsername(this ControllerBase controller)
        {
            return controller.User.FindFirstValue(Constants.UserNameClaim)!;
        }

        public static Guid GetUserId(this ControllerBase controller)
        {
            return ClaimsPrincipalExtensions.GetUserIdFromClaims(controller.User.Claims);
        }
    }
}
