﻿using System.Threading.Tasks;
using Common.Models;

namespace Common.Services
{
    public interface INotificationService
    {
        Task SendEmailInfoCheckAsync(SendEmailInfoCheckRequest request);
        Task SendSmsInfoCheckAsync(SendSmsInfoCheckRequest request);
        Task SendCustomEmailAsync(SendCustomEmailRequest request);
        Task SendCustomSmsAsync(SendCustomSmsRequest request);
        Task SendWelcomeEmialForCPMerchantAccount(SendWelcomeEmialRequest request);
        Task SendWelcomeEmialForCNPMerchantAccount(SendWelcomeEmialRequest request);
    }
}
