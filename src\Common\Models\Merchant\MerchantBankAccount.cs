﻿using System;

namespace Common.Models.Merchant
{
    public class MerchantBankAccount
    {
        public Guid MerchantBankAccountId { get; set; }

        public Guid MerchantId { get; set; }

        public string? CountryCode { get; set; }

        public string? AccountHolderName { get; set; }

        public string? City { get; set; }

        public string? IBAN { get; set; }

        public string? Swift { get; set; }

        public string? AccountName { get; set; }

        public string? DDReference { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime? ValidTo { get; set; }
    }
}
