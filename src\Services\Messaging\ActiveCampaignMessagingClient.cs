﻿using System.Diagnostics.CodeAnalysis;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Services.Messaging
{
    [ExcludeFromCodeCoverage]
    public class ActiveCampaignMessagingClient : MessageClient
    {
        public ActiveCampaignMessagingClient(
            ILogger<MessageClient> logger,
            IHttpContextAccessor contextAccessor,
            QueueSettings queue,
            IOptionsMonitor<RabbitMqOptions> rabbitMqOptions)
            : base(contextAccessor, logger, rabbitMqOptions, queue)
        {
        }

        public virtual void SendMessageToActiveCampaign<T>(T payload) where T : class, new()
        {
            SendMessage(payload);
        }
    }
}
