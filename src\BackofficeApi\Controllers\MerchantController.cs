﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Common;
using Common.Models;
using Common.Models.Comment;
using Common.Models.Merchant;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1")]
    public class MerchantController : ControllerBase
    {
        private readonly IMerchantService merchantService;
        private readonly ILogger<MerchantController> logger;
        private readonly Authorized authorized;

        public MerchantController(
            IMerchantService merchantService,
            Authorized authorized,
            ILogger<MerchantController> logger)
        {
            this.merchantService = merchantService;
            this.authorized = authorized;
            this.logger = logger;
        }

        /// <summary>
        /// Finds all merchants based o search criteria in the form of an OData query.
        /// </summary>
        /// <returns>An array of merchant objects matching the search criteria.</returns>
        [HttpPost("merchant/advancedSearch")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> Find([FromBody] MerchantSearchFilters filters, CancellationToken cancellationToken = default)
        {
            if (!await authorized.To.List.Merchant(filters.SalesPartnerId, filters.SalesId, filters.RefBankId, filters.AcquiringLedger, filters.MerchantStatus))
            {
                return Forbid();
            }

            logger.LogInformation("Initiating merchant search");

            try
            {
                var response = await merchantService.FindAsync(filters, cancellationToken);
                return Ok(response);
            }
            catch (Exception ex)
            {
                logger.LogCritical("Merchant search failed. Error was {}", ex.ToString());
                throw;
            }
            finally
            {
                logger.LogInformation("Finished merchant search");
            }
        }

        /// <summary>
        /// Returns a contact's details by the person of interest id
        /// </summary>
        /// <returns>An object representing the contact's details as presented by the merchant service</returns>
        [HttpGet("merchant/contact/{personOfInterestId}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetContactDetailsById(Guid personOfInterestId)
        {
            if (!await authorized.To.View.POI(personOfInterestId))
            {
                return Forbid();
            }

            return Ok(await merchantService.GetContactDetailsAsync(personOfInterestId));
        }

        /// <summary>
        /// Returns the default's contact details of a merchant by the merchant's id
        /// </summary>
        /// <param name="merchantId">The id of the merchant</param>
        /// <returns>A ContactDetails object</returns>
        [HttpGet("merchant/{merchantId}/contact")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetMerchantContactByMerchantId(Guid merchantId)
        {
            if (!await authorized.To.View.Merchant(merchantId))
            {
                return Forbid();
            }

            var contact = await merchantService.GetMerchantContactsAsync(merchantId);

            if (contact == null)
                return NotFound();

            return Ok(contact);
        }


        /// <summary>
        /// Delete merchants based on merchantId.
        /// </summary>
        /// <returns></returns>
        [HttpDelete("merchant")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeleteMerchant([FromBody] MerchantDeleteRequest merchantDeleteRequest)
        {
            if (!await authorized.To.Delete.Merchant())
            {
                return Forbid();
            }

            await merchantService.DeleteMerchantsAsync(merchantDeleteRequest);
            return NoContent();
        }

        [HttpPut("merchant/{merchantId}/additionalMccs")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateMerchantAdditionalMccs(Guid merchantId, [FromBody] IReadOnlyCollection<AdditionalMccUpdate> updateMerchantAdditionalMccs)
        {
            if (!await authorized.To.Update.MerchantMcc(merchantId))
            {
                return Forbid();
            }

            var response = await merchantService.UpdateMerchantAdditionalMccsAsync(merchantId, updateMerchantAdditionalMccs);
            return Ok(response);
        }


        /// <summary>
        /// Exports a merchant.
        /// </summary>
        /// <param name="merchantId">The ID of the merchant to export. Must be an integer greater than 0.</param>
        /// <returns></returns>
        [HttpGet("merchant/{merchantId}/export")]
        [Produces("application/json", "text/csv")]
        [Obsolete("Export merchant by id functionaltiy has been removed")]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public IActionResult ExportMerchant(Guid merchantId)
        {
            return Unauthorized();
        }

        [Produces("application/json", "text/csv")]
        [HttpPost("merchants/export/egypt")]
        [ProducesResponseType(typeof(EgyptMerchantExportResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> ExportEgyptMerchants([FromBody] MerchantSearchFilters searchCriteria)
        {
            if (!await authorized.To.Export.Merchant(searchCriteria.RefBankId, searchCriteria.AcquiringLedger))
            {
                return Forbid();
            }

            var merchants = await merchantService.ExportEgyptMerchantsAsync(searchCriteria);
            return Ok(merchants);
        }

        /// <summary>
        /// Exports all merchants.
        /// </summary>
        /// <returns></returns>
        [HttpGet("merchants/export")]
        [Produces("application/json", "text/csv")]
        [Obsolete("Export all merchants functionaltiy has been removed")]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public IActionResult ExportMerchants()
        {
            return Unauthorized();
        }

        /// <summary>
        /// Get merchant POI
        /// </summary>
        /// <param name="merchantId"></param>
        /// <returns></returns>
        [HttpGet("merchant/{merchantId}/poi")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPeopleByMerchantId(Guid merchantId)
        {
            if (!await authorized.To.View.Merchant(merchantId))
            {
                return Forbid();
            }

            var result = await merchantService.GetPeopleByMerchantIdAsync(merchantId);
            return Ok(result);
        }

        /// <summary>
        /// Get merchant status history
        /// </summary>
        /// <param name="merchantId"></param>
        /// <returns></returns>
        [HttpGet("merchant/{merchantId}/status/history")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetMerchantStatusHistory(Guid merchantId)
        {
            if (!await authorized.To.View.MerchantStatusHistory())
            {
                return Forbid();
            }

            var result = await merchantService.GetMerchantStatusHistoryAsync(merchantId);
            return Ok(result);
        }

        /// <summary>
        /// Updates the merchant.
        /// </summary>
        /// <response code="200">Returns the updated merchant.</response>
        /// <response code="401">If the request in unauthorized.</response> 
        /// <response code="403">If the user does not have the correct role.</response> 
        /// <response code="404">If the merchant with that id does not exists.</response>

        [HttpPatch("merchant/{merchantId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> PatchMerchant(Guid merchantId, [FromBody] JsonPatchDocument<PatchMerchantRequest> patchDocument)
        {
            var properties = patchDocument.Operations?.Select(op => op.path);
            var newMerchantStatus = patchDocument.Operations
                ?.FirstOrDefault(op => op?.path?.ToLowerInvariant() == nameof(PatchMerchantRequest.MerchantStatus).ToLowerInvariant())
                ?.value?.ToString();

            if (!await authorized.To.Patch.Merchant(merchantId, properties?.ToArray(), newMerchantStatus))
            {
                return Forbid();
            }

            var updateRequest = new PatchMerchantRequest();
            try
            {
                patchDocument.ApplyTo(updateRequest, ModelState);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Invalid merchant patch.");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatch);
            }

            TryValidateModel(updateRequest);

            var response = await merchantService.PatchMerchantAsync(merchantId, patchDocument);
            return Ok(response);
        }

        /// <summary>
        /// Create a new comment for a specific merchant.
        /// </summary>
        /// <param name="merchantId"></param>
        /// <param name="commentCreateRequest"></param>
        /// <response code="201">Returns the newly created comment </response>
        /// <response code="400">Returns the error</response> 
        /// <response code="404">If there is no merchant with that id</response> 
        [Produces("application/json")]
        [HttpPost("merchant/{merchantId}/comment")]
        [ProducesResponseType(typeof(MerchantCommentResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CreateMerchantCommentAction(Guid merchantId, [FromBody] CommentCreateRequest commentCreateRequest)
        {
            if (!await authorized.To.Create.MerchantComment())
            {
                return Forbid();
            }

            var newComment = await merchantService.CreateCommentAsync(merchantId, commentCreateRequest);
            return CreatedAtAction(nameof(GetMerchantsCommentById),
                new { commentId = newComment.CommentId },
                newComment);
        }

        /// <summary>
        /// Gets all comments for an merchant based on merchantId.
        /// </summary>
        /// <param name="merchantId"> Merchant id</param>
        [Produces("application/json")]
        [HttpGet("merchant/{merchantId}/comment")]
        [ProducesResponseType(typeof(MerchantCommentResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCommentByMerchantIdAction(Guid merchantId)
        {
            if (!await authorized.To.View.MerchantComment())
            {
                return Forbid();
            }

            var comments = await merchantService.GetCommentByMerchantIdAsync(merchantId);
            return Ok(comments);
        }

        /// <summary>
        /// Gets a specific comment based on comment id.
        /// </summary>
        /// <param name="commentId"> The id based on which we are looking for the comment</param>
        [Produces("application/json")]
        [HttpGet("merchants/comment/{commentId}")]
        [ProducesResponseType(typeof(MerchantCommentResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetMerchantsCommentById(Guid commentId)
        {
            if (!await authorized.To.View.MerchantComment())
            {
                return Forbid();
            }

            var comment = await merchantService.GetCommentByIdAsync(commentId);

            return Ok(comment);
        }

        /// <summary>
        /// Update comment based on comment id.
        /// </summary>
        /// <param name="commentId"> CommentId</param>
        /// <param name="commentUpdateRequest"></param>
        [Produces("application/json")]
        [HttpPut("merchants/comment/{commentId}")]
        [ProducesResponseType(typeof(MerchantCommentResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateMerchantsCommentAction(Guid commentId, [FromBody] CommentUpdateRequest commentUpdateRequest)
        {
            if (!await authorized.To.Update.MerchantComment(commentId))
            {
                return Forbid();
            }

            var updatedComment = await merchantService.UpdateCommentAsync(commentId, commentUpdateRequest);
            return Ok(updatedComment);
        }

        /// <summary>
        /// Soft delete for comments based on comment id.
        /// </summary>
        /// <param name="commentId"> Comment id</param>
        [Produces("application/json")]
        [HttpDelete("merchants/comment/{commentId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteMerchantsCommentAction(Guid commentId)
        {
            if (!await authorized.To.Delete.MerchantComment(commentId))
            {
                return Forbid();
            }

            await merchantService.DeleteCommentAsync(commentId);
            return NoContent();
        }

        [HttpPost("merchant/CalculateRisk")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CalculateRisk([FromBody] CompanyDetails companyDetails)
        {
            var (complainceRisk, aquringRisk) = await merchantService.CalculateRisk(companyDetails);
            var results = new
            {
                ComplianceRisk = complainceRisk,
                AcquiringRisk = aquringRisk
            };
            return Ok(results);
        }

        [HttpGet("Merchant/GetRiskCalculated/{businessId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetRiskCalculation(string businessId)
        {
            var result = await merchantService.GetRiskCalculation(businessId);
            return Ok(result);
        }
    }
}
