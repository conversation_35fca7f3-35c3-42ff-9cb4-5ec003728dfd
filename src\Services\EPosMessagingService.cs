﻿using Common;
using Common.Enums;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.EPOS;
using Common.Models.Merchant;
using Common.Models.Product;
using Common.Models.User;
using Common.Options;
using Common.Services;
using Geidea.Messages.Epos.Messages;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.CodeAnalysis;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Services.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants;
using ProductType = Common.Enums.ProductType;
using UtilsConstants = Geidea.Utils.Common.Constants;

namespace Services
{
    public class EPosMessagingService : IEPosMessagingService
    {
        private readonly ICheckoutClient checkoutClient;
        private readonly ICounterpartyProvider counterpartyProvider;
        private readonly EPosMessagingClient ePosMessagingClient;
        private readonly EPosMessagingSoftwareTypeClient ePosMessagingSoftwareTypeClient;
        private readonly ILogger<EPosMessagingService> logger;
        private readonly IMerchantClient merchantClient;
        private readonly IProductService productService;
        private readonly IReferenceService referenceService;
        private readonly IUserService userService;
        private readonly IOptions<FreelancerTerminalTypeFeatureToggle> freelancerTerminalTypeFeatureToggle;
        private readonly UrlSettings urlSettingsOptions;
        private readonly IHttpContextAccessor contextAccessor;

        private enum SoftwareType
        {
            WithoutBillPayment,
            WithBillPayment
        };

        public EPosMessagingService(
            EPosMessagingClient ePosMessagingClient,
            EPosMessagingSoftwareTypeClient ePosMessagingSoftwareTypeClient,
            IMerchantClient merchantClient,
            ICheckoutClient checkoutClient,
            IProductService productService,
            IReferenceService referenceService,
            IUserService userService,
            ICounterpartyProvider counterpartyProvider,
            IOptions<FreelancerTerminalTypeFeatureToggle> freelancerTerminalTypeFeatureToggle,
            ILogger<EPosMessagingService> logger,
            IOptionsMonitor<UrlSettings> urlSettingsOptions,
            IHttpContextAccessor contextAccessor)
        {
            this.ePosMessagingClient = ePosMessagingClient;
            this.ePosMessagingSoftwareTypeClient = ePosMessagingSoftwareTypeClient;
            this.merchantClient = merchantClient;
            this.checkoutClient = checkoutClient;
            this.productService = productService;
            this.referenceService = referenceService;
            this.userService = userService;
            this.counterpartyProvider = counterpartyProvider;
            this.freelancerTerminalTypeFeatureToggle = freelancerTerminalTypeFeatureToggle;
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.contextAccessor = contextAccessor;
        }

        private string EposOrderFederationBaseUrl => $"{urlSettingsOptions.EposOrderFederationApiBaseUrl}";
        private string EposOrderFederationEndpoint => "/api/v1/Epos/EposTicketCreation";
        public async Task<EPosAction> GetEPosActions(Guid orderId)
        {
            var order = await checkoutClient.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                logger.LogCritical("Error: {error} - {orderId}", Errors.OrderNotFound.Message, orderId);
                throw new ServiceException(System.Net.HttpStatusCode.NotFound, Errors.OrderNotFound).WithArguments(orderId);
            }
            var orderProductInstances = new List<ProductInstance>();
            await GetAllProductInstancesForOrder(order, orderProductInstances);

            if (OrderWasSentToEPos(orderProductInstances) > 0)
            {
                return EPosAction.CreateEPosTicket;
            }

            return TerminalsWithOutOfSyncSwType(orderProductInstances).Any()
                ? EPosAction.UpdateEPosSwType
                : EPosAction.NoAction;
        }

        public async Task<EPosTicketCreationResponse> CreateOrderEPosTicketAsync(Guid orderId, bool shouldTriggerError = false)
        {
            logger.LogInformation("Start create data for order {@orderId} to send to epos", orderId);

            EPosTicketCreationResponse response = new()
            {
                Success = false,
                Errors = new List<ErrorDetail>()
            };

            var order = await checkoutClient.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                logger.LogCritical(Errors.OrderNotFound.Message, orderId);
                throw new ServiceException(HttpStatusCode.NotFound, Errors.OrderNotFound).WithArguments(orderId);
            }

            if (order.OrderStatus == null || order.OrderStatus.ToUpper() != OrderStatus.ProductRegistered)
            {
                logger.LogCritical(Errors.OrderDoesNotHaveStatusRegistered.Message, orderId);
                throw new ServiceException(HttpStatusCode.InternalServerError, Errors.OrderDoesNotHaveStatusRegistered).WithArguments(orderId);
            }

            var merchantEPosInfo = await merchantClient.GetMerchantEPosInformationAsync(order.MerchantId);
            var salesEmail = await GetSalesEmail(merchantEPosInfo.SalesId);

            var allProductInstances = new List<ProductInstance>();
            await GetAllProductInstancesForOrder(order, allProductInstances);

            var productInstancesWithTerminals = OrderHasTerminals(allProductInstances);

            if (productInstancesWithTerminals == 0)
            {
                var errorMessage = $"Order with id {orderId} has no product instances from type terminal";
                logger.LogCritical(errorMessage);

                response.Errors.Add(new ErrorDetail
                {
                    Code = Errors.OrderHasNoTerminalProducts.Code,
                    Message = errorMessage
                });

                response.Success = response.Errors.Count == 0;
                return response;
            }

            var productInstancesNotSentToEpos = OrderWasSentToEPos(allProductInstances);
            if (productInstancesNotSentToEpos == 0)
            {
                var errorMessage = $"The order {orderId} was already send to ePos";
                logger.LogCritical(errorMessage);

                if (shouldTriggerError)
                {
                    throw new ServiceException(HttpStatusCode.InternalServerError, Errors.OrderAlreadySentToEPos).WithArguments(order.OrderId);
                }

                response.Errors.Add(new ErrorDetail
                {
                    Code = Errors.OrderAlreadySentToEPos.Code,
                    Message = errorMessage
                });

                response.Success = response.Errors.Count == 0;
                return response;
            }

            var catalogues = await referenceService.GetCataloguesAsync(new string[]
            {
                Catalogues.Areas, Catalogues.Governorates, Catalogues.NbeGovernarotes, Catalogues.AlxGovernarotes,
                Catalogues.AcquiringLedger, Catalogues.NbeCityToEPos, Catalogues.AlxCityToEPos , Catalogues.AcquiringLedgerToEpos
            });

            var counterparty = counterpartyProvider.GetCode();

            foreach (var productInstance in allProductInstances)
            {
                var errorDetails = await SendEposTicketRequest(counterparty, productInstance, order, merchantEPosInfo, salesEmail, catalogues, allProductInstances);

                if (errorDetails != null && errorDetails.Count > 0)
                {
                    response.Errors.AddRange(errorDetails);
                }
            }

            response.Success = response.Errors.Count == 0;
            return response;
        }

        public async Task SendEPosSwTypeUpdate(Guid orderId)
        {
            logger.LogInformation("Started the update in ePos for order {@orderId}.", orderId);

            var order = await checkoutClient.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                logger.LogCritical("Error: {error} - {orderId}", Errors.OrderNotFound.Message, orderId);
                throw new ServiceException(System.Net.HttpStatusCode.NotFound, Errors.OrderNotFound).WithArguments(orderId);
            }

            var orderProductInstances = new List<ProductInstance>();
            await GetAllProductInstancesForOrder(order, orderProductInstances);

            if (OrderWasSentToEPos(orderProductInstances) > 0)
            {
                logger.LogError("{errorMessage}. Order id: {orderId}.", Errors.OrderNotSentToEPos.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.OrderNotSentToEPos).WithArguments(order.OrderId);
            }

            var outOfSyncBpTerminals = TerminalsWithOutOfSyncSwType(orderProductInstances).ToList();

            if (!outOfSyncBpTerminals.Any())
            {
                logger.LogError("{errorMessage}. Order id: {orderId}.", Errors.OrderUpToDateInEPos.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.OrderUpToDateInEPos).WithArguments(order.OrderId);
            }

            if (outOfSyncBpTerminals.Exists(t => string.IsNullOrWhiteSpace((t.Metadata as TerminalData)?.TId)))
            {
                logger.LogError("{errorMessage}. Order id: {orderId}.", Errors.TerminalMissingTId.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.TerminalMissingTId).WithArguments(order.OrderId);
            }

            foreach (var bpTerminal in outOfSyncBpTerminals)
            {
                ePosMessagingSoftwareTypeClient.SendMessageToEPos(new UpdateTerminalSoftwareMessage()
                {
                    SoftwareType = (int)SoftwareType.WithBillPayment,
                    TerminalId = (bpTerminal.Metadata as TerminalData)?.TId!,
                    ProductInstanceId = bpTerminal.ProductInstanceId
                });
            }
        }

        private async Task<string> GetSalesEmail(string? salesId)
        {
            if (!string.IsNullOrEmpty(salesId))
            {
                var userSearchParameters = new UserSearchParameters
                {
                    SalesId = salesId
                };

                var salesInfo = await userService.SearchUsersAsync(userSearchParameters);

                if (salesInfo != null && salesInfo.Length == 1)
                {
                    return salesInfo[0].Email;
                }
            }

            return string.Empty;
        }

        private CreateEPosTicketPayload CreateEposTicketRequest(string counterparty, ProductInstance productInstance,
            OrderResponse order, MerchantEPosInformation merchantEPosInformation, string salesEmail,
            Catalogue[] catalogues, decimal? parentPrice, List<ProductInstance> orderProductInstances)
        {
            if (productInstance == null ||
                productInstance.Metadata == null ||
                productInstance.Product == null)
            {
                return null!;
            }

            var terminalData = productInstance.Metadata as TerminalData;

            var providerBank = catalogues.FirstOrDefault(x => x.CatalogueName == (counterparty == Constants.CounterParty.Saudi ? Constants.Catalogues.AcquiringLedgerToEpos : Constants.Catalogues.AcquiringLedger)
                                                         && x.Key == terminalData?.ProviderBank);

            var cityId = GetCityId(providerBank, catalogues, merchantEPosInformation?.CityId, counterparty);

            if (cityId == null)
            {
                return null!;
            }

            var terminalType = GetTerminalType(counterparty, merchantEPosInformation?.BusinessType, productInstance.Product.Code);
            var merchant = merchantClient.GetCoreMerchantAsync(order.MerchantId);

            var mId = merchant != null && merchant.Result != null && merchant.Result.MerchantDetails != null ? merchant.Result.MerchantDetails.Mid : string.Empty;
            var registrationNumber = merchant != null && merchant.Result != null && merchant.Result.MerchantDetails != null ? merchant.Result.MerchantDetails.RegistrationNumber : string.Empty;

            if (merchantEPosInformation == null)
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.MerchantEPosInformationIsNull);
            }

            var eposCreateTicketRequest = new CreateEPosTicketPayload
            {
                TerminalId = terminalData!.TId,
                RetailerName = ComputeRetailerName(terminalData, merchantEPosInformation),
                RetailerId = terminalData.MIDMerchantReference,
                StoreId = order.StoreId.ToString(),
                TRSM = terminalData.POSDataCode,
                RegType = GetRegType(terminalData!.ExternalBankTId),
                OldTerminal = terminalData!.ExternalBankTId ?? string.Empty,
                ProviderBank = GetProviderBankValue(providerBank, counterparty),
                Street = ComputeStreet(counterparty, providerBank, catalogues, merchantEPosInformation),
                Mobile = GetMobileNumberValue(terminalData, merchantEPosInformation, counterparty),
                CityId = cityId,
                TerminalType = terminalType,
                MCC = terminalData.Mcc,
                MposMode = SetMode(order.PaymentMethod),
                MposRefNo = order.OrderNumber,
                MposPrice = GetProductPrice(productInstance.Product) ?? parentPrice,
                MposSalesEmail = salesEmail,
                ProductInstanceId = productInstance.ProductInstanceId,
                SoftwareType = GetSoftwareType(productInstance, orderProductInstances),
                Counterparty = counterparty,
                Mid = productInstance.Mid!,
                CRnumber = !string.IsNullOrEmpty(registrationNumber) ? registrationNumber : "T789668",
                MerchantID = !string.IsNullOrEmpty(mId) ? mId : productInstance.Mid!,
                MerchantType = "1"
            };

            return eposCreateTicketRequest;
        }

        private static string GetMobileNumberValue(TerminalData terminalData, MerchantEPosInformation merchantEPosInformation, string counterparty)
        {
            string phoneNumber = string.Empty;

            if (counterparty == CounterParty.Uae)
            {
                if (string.IsNullOrEmpty(terminalData!.CountryPrefix) && string.IsNullOrEmpty(terminalData!.PhoneNumber))
                {
                    phoneNumber = merchantEPosInformation.PrincipalTelephone ?? string.Empty;
                }
                else
                {
                    phoneNumber = terminalData!.CountryPrefix + terminalData!.PhoneNumber;
                }
            }
            else if (counterparty == CounterParty.Saudi)
            {
                phoneNumber = "0" + (!string.IsNullOrEmpty(terminalData!.PhoneNumber) ? terminalData!.PhoneNumber : merchantEPosInformation.PrincipalTelephone!.Replace(CountryPrefix.Saudi, ""));
            }

            return phoneNumber;
        }

        private static int? GetCityId(Catalogue? providerBank, Catalogue[] catalogues, string? cityKey, string counterparty)
        {
            if (counterparty == UtilsConstants.CounterpartyEgypt && providerBank != null)
            {
                var providerCityValue = catalogues.FirstOrDefault(x =>
                x.CatalogueName == $"{providerBank.Key}_{Constants.CityToEPos}" &&
                string.Equals(x.Key, cityKey, StringComparison.OrdinalIgnoreCase));

                if (providerCityValue != null)
                    cityKey = providerCityValue.Value;
            }

            if (!Int32.TryParse(cityKey, out int cityId))
            {
                return null;
            }

            return cityId;
        }

        private static string GetProviderBankValue(Catalogue? providerBank, string counterparty)
        {
            if (providerBank != null)
            {
                if (counterparty == UtilsConstants.CounterpartyEgypt
                    && !string.IsNullOrEmpty(providerBank.Value))
                    return providerBank.Value.Substring(0, 2);
                else
                    return providerBank.Value;
            }

            return string.Empty;
        }

        private static int? GetSoftwareType(ProductInstance currentProductInstance, List<ProductInstance> orderProductInstances)
        {
            var hasBpSibling = HasBillPaymentSibling(currentProductInstance, orderProductInstances);

            return hasBpSibling ? (int)SoftwareType.WithBillPayment : (int)SoftwareType.WithoutBillPayment;
        }

        private static bool HasBillPaymentSibling(ProductInstance currentProductInstance, List<ProductInstance> orderProductInstances)
        {
            if (currentProductInstance.ParentId == null)
                return false;

            var parent =
                orderProductInstances.SingleOrDefault(pi => pi.ProductInstanceId == currentProductInstance.ParentId);

            return parent != null && parent.Children.Exists(child =>
                child.Product.Code == Constants.ProductCode.BillPayment &&
                child.ProductInstanceId != currentProductInstance.ProductInstanceId);
        }

        private static IEnumerable<ProductInstance> TerminalsWithOutOfSyncSwType(List<ProductInstance> orderProductInstances)
        {
            var terminalsWithOutOfSyncSwType = new List<ProductInstance>();
            foreach (var orderProductInstance in orderProductInstances)
            {
                var outOfSyncTerminals = GetTerminalsWithBillPayments(orderProductInstances, orderProductInstance)
                    .Where(t => !t.EPosBillPayments ?? true);
                terminalsWithOutOfSyncSwType.AddRange(outOfSyncTerminals);
            }

            return terminalsWithOutOfSyncSwType;
        }

        private static IEnumerable<ProductInstance> GetTerminalsWithBillPayments(List<ProductInstance> orderProductInstances, ProductInstance currentProductInstance)
        {
            var terminalsWithBillPayments = new List<ProductInstance>();

            if ((currentProductInstance.Product.Type == ProductType.M_POS.ToString() ||
                currentProductInstance.Product.Type == ProductType.TERMINAL.ToString()) &&
                HasBillPaymentSibling(currentProductInstance, orderProductInstances))
            {
                terminalsWithBillPayments.Add(currentProductInstance);
            }

            foreach (var child in currentProductInstance.Children)
            {
                terminalsWithBillPayments.AddRange(GetTerminalsWithBillPayments(orderProductInstances, child));
            }

            return terminalsWithBillPayments;
        }

        private string GetTerminalType(string counterparty, string? businessType, string productCode)
        {
            string[] freelancerProductsCodes = { "M_POS", "TERMINAL", "D135_READER", "MOBILE_POS_SP530", "SMARTPOS_A920", "P2", "A920", "SUNMI_P2", "PAX_A920" };

            if (counterparty == UtilsConstants.CounterpartySaudi &&
                freelancerTerminalTypeFeatureToggle.Value.EnableT300TerminalType &&
                businessType == Constants.BusinessType.SoleTrader &&
              freelancerProductsCodes.Contains(productCode))
            {
                return "T300_4G";
            }

            return productCode;
        }

        private static decimal? GetProductPrice(ProductShortResponse product)
        {
            var computableChargeTypes = new string[] { Constants.ChargeType.Reccurrring, Constants.ChargeType.SetupCharge, Constants.ChargeType.RetailPrice };
            var price = product.Prices.Where(p => computableChargeTypes.Contains(p.ChargeType)).Sum(x => x.PerItemPrice);

            if (price.HasValue && price != 0)
                return (decimal)price / 100;

            return null;
        }

        private static string GetRegType(string? externalBankTId)
        {
            if (!string.IsNullOrEmpty(externalBankTId))
            {
                return "M";
            }

            return "I";
        }

        private static string SetMode(string? paymentMethod)
        {
            switch (paymentMethod)
            {
                case "ON_DELIVERY": return "2";
                case "ONLINE": return "4";
                case "E_INV": return "5";
                case "NOT_REQUIRED": return "6";
                case "REQUEST": return "7";
                default:
                    break;
            }

            return string.Empty;
        }

        private static string ComputeRetailerName(TerminalData terminalData, MerchantEPosInformation? merchantEPosInformation)
        {
            if (!string.IsNullOrEmpty(terminalData!.ShortName_EN))
            {
                return terminalData!.ShortName_EN;
            }

            if (!string.IsNullOrEmpty(terminalData!.ShortName_AR))
            {
                return terminalData!.ShortName_AR;
            }

            return merchantEPosInformation!.BusinessName ?? merchantEPosInformation!.BusinessNameAr!;
        }

        private static string ComputeStreet(string counterparty, Catalogue? providerBank, Catalogue[] catalogues, MerchantEPosInformation? merchantEPosInformation)
        {
            var street = string.Empty;


            if (counterparty == UtilsConstants.CounterpartySaudi)
            {
                var areaName = merchantEPosInformation?.Area ?? string.Empty;
                var area = catalogues.FirstOrDefault(a => a.CatalogueName == Constants.Catalogues.Areas && a.Key == areaName);

                if (area != default)
                {
                    areaName = area.Value;
                }
                street = $"{areaName}";
            }
            else if (counterparty == UtilsConstants.CounterpartyEgypt)
            {
                var addressLine1 = merchantEPosInformation?.AddressLine1 ?? string.Empty;
                var governorateName = merchantEPosInformation?.Governorate ?? string.Empty;

                // if the provider bank was not provided, we would use the default governorate like the old behavior
                string govCatalogueName = providerBank != null ?
                    Geidea.Utils.ReferenceData.AcquirerHelper.GetCatalogueName(acquirer: providerBank.Key, defaultCatalogueName: Constants.Catalogues.Governorates,
                    catalogues.Select(c => new Geidea.Utils.ReferenceData.ReferenceData
                    {
                        CatalogueName = c.CatalogueName,
                        Key = c.Key,
                        Value = c.Value,
                    }).ToList()) : Constants.Catalogues.Governorates;

                var governorate = catalogues.FirstOrDefault(a => a.CatalogueName == govCatalogueName && a.Key == governorateName);

                if (governorate != default)
                {
                    governorateName = governorate.Value;
                }
                street = $"{governorateName} {addressLine1}";
            }
            return street;
        }

        private static bool EposCreateTicketRequestIsValid(CreateEPosTicketPayload payload)
        {
            return (payload != null &&
                payload.CityId != null &&
                !string.IsNullOrWhiteSpace(payload.MCC) &&
                !string.IsNullOrWhiteSpace(payload.MposMode) &&
                !string.IsNullOrWhiteSpace(payload.MposRefNo) &&
                !string.IsNullOrWhiteSpace(payload.RetailerId) &&
                !string.IsNullOrWhiteSpace(payload.TRSM) &&
                !string.IsNullOrWhiteSpace(payload.ProviderBank) &&
                !string.IsNullOrWhiteSpace(payload.RetailerName) &&
                !string.IsNullOrWhiteSpace(payload.TerminalId) &&
                !string.IsNullOrWhiteSpace(payload.TerminalType) &&
                !string.IsNullOrWhiteSpace(payload.StoreId));
        }

        private async Task GetAllProductInstancesForOrder(OrderResponse order, List<ProductInstance> allProductInstances)
        {
            if (order.OrderItem == null)
            {
                logger.LogCritical(Errors.NoOrderItems.Message, order.OrderId);
                throw new ServiceException(System.Net.HttpStatusCode.NotFound, Errors.NoOrderItems).WithArguments(order.OrderId);
            }

            foreach (var orderItem in order.OrderItem)
            {
                if (!orderItem.ProductInstanceIds.Any())
                {
                    logger.LogInformation($"Order item {orderItem.OrderItemId} does not have any product instance");
                    continue;
                }

                var productInstances = await productService.GetProductInstances(orderItem.ProductInstanceIds);

                if (productInstances.Count == 0)
                {
                    logger.LogInformation($"No product instances found for order item {orderItem.OrderItemId}");
                    continue;
                }
                allProductInstances.AddRange(productInstances);
            }
        }

        private async Task MarkProductInstanceAsSendToEpos(ProductInstance productInstance)
        {
            var patchProductInstace = new JsonPatchDocument<UpdateProductInstanceRequest>();
            patchProductInstace.Operations.Add(new Operation<UpdateProductInstanceRequest>
            {
                op = "replace",
                path = "/ePosTicketId",
                value = "0"
            });

            await productService.PatchAsync(productInstance.ProductInstanceId, patchProductInstace);
        }

        private int OrderWasSentToEPos(List<ProductInstance> productInstances)
        {
            var productInstancesNotSentToEpos = 0;
            foreach (var productInstance in productInstances)
            {
                productInstancesNotSentToEpos += ProductInstanceNotSentToEpos(productInstance);
            }
            return productInstancesNotSentToEpos;
        }

        private int ProductInstanceNotSentToEpos(ProductInstance productInstance)
        {
            if (productInstance == null)
            {
                return 0;
            }

            var productInstancesNotSentToEpos = 0;
            if ((productInstance.Product.Type == ProductType.M_POS.ToString() ||
                productInstance.Product.Type == ProductType.TERMINAL.ToString()) &&
                string.IsNullOrEmpty(productInstance.EPosTicketId))
            {
                productInstancesNotSentToEpos += 1;
            }

            foreach (var childProductInstance in productInstance.Children)
            {
                productInstancesNotSentToEpos += ProductInstanceNotSentToEpos(childProductInstance);
            }

            return productInstancesNotSentToEpos;
        }

        private async Task<List<ErrorDetail>> SendEposTicketRequest(string counterparty, ProductInstance productInstance,
             OrderResponse order, MerchantEPosInformation merchantEPosInformation, string salesEmail, Catalogue[] catalogues,
             List<ProductInstance> orderProductInstances, decimal? parentPrice = null)
        {
            var errors = new List<ErrorDetail>();

            if (productInstance == null || order == null || merchantEPosInformation == null)
            {
                var errorMessage = "SendEposTicketRequest: Invalid arguments";
                logger.LogInformation(errorMessage);

                errors.Add(new ErrorDetail
                {
                    Code = "InvalidArguments",
                    Message = errorMessage
                });
                return errors;
            }

            if (!string.IsNullOrEmpty(productInstance.EPosTicketId))
            {
                var message = $"Product instance {productInstance.ProductInstanceId} has already been sent to EPos";
                logger.LogInformation(message);

                errors.Add(new ErrorDetail
                {
                    Code = "InvalidArguments",
                    Message = message
                });
                return errors;
            }

            if (productInstance.Product.Type == ProductType.M_POS.ToString() || productInstance.Product.Type == ProductType.TERMINAL.ToString())
            {
                var eposCreateTicketRequest = CreateEposTicketRequest(counterparty, productInstance, order, merchantEPosInformation, salesEmail, catalogues, parentPrice, orderProductInstances);

                if (!EposCreateTicketRequestIsValid(eposCreateTicketRequest))
                {
                    logger.LogCritical(Errors.TerminalIsNotConfigured.Message, productInstance.ProductInstanceId);
                    throw new ServiceException(HttpStatusCode.InternalServerError, Errors.TerminalIsNotConfigured).WithArguments(productInstance.ProductInstanceId);
                }

                try
                {
                    await CreateEposTicket(eposCreateTicketRequest);
                    await MarkProductInstanceAsSendToEpos(productInstance);

                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error while creating Epos ticket request for product instance {ProductInstanceId}", productInstance.ProductInstanceId);
                    errors.Add(new ErrorDetail
                    {
                        Code = "EposTicketCreationError",
                        Message = $"Failed to create Epos ticket for product instance {productInstance.ProductInstanceId}: {ex.Message}"
                    });

                    throw new ServiceException(HttpStatusCode.InternalServerError, Errors.OrderFailedToSentToEPos);
                }

                return errors;
            }
            else
            {
                logger.LogInformation("Invalid Product Type {@Type}", productInstance.Product.Type);
            }

            var price = GetProductPrice(productInstance.Product);
            foreach (var childProductInstance in productInstance.Children)
            {
                var childErrors = await SendEposTicketRequest(counterparty, childProductInstance, order, merchantEPosInformation, salesEmail, catalogues, orderProductInstances, price);
                if (childErrors != null && childErrors.Count > 0)
                {
                    errors.AddRange(childErrors);
                }
            }

            return errors;
        }

        private int OrderHasTerminals(List<ProductInstance> productInstances)
        {
            var productInstancesWithTerminals = 0;
            foreach (var productInstance in productInstances)
            {
                productInstancesWithTerminals += ProductInstanceWithTerminal(productInstance);
            }
            return productInstancesWithTerminals;
        }

        private int ProductInstanceWithTerminal(ProductInstance productInstance)
        {
            if (productInstance == null)
            {
                return 0;
            }

            var productInstancesWithTerminal = 0;
            if ((productInstance.Product.Type == ProductType.M_POS.ToString() ||
                productInstance.Product.Type == ProductType.TERMINAL.ToString()))
            {
                productInstancesWithTerminal += 1;
            }

            foreach (var childProductInstance in productInstance.Children)
            {
                productInstancesWithTerminal += ProductInstanceWithTerminal(childProductInstance);
            }

            return productInstancesWithTerminal;
        }


        public async Task CreateEposTicket(CreateEPosTicketPayload createEPosTicketPayload)
        {
            string url = $"{EposOrderFederationBaseUrl}{EposOrderFederationEndpoint}";

            var payload = JsonConvert.SerializeObject(createEPosTicketPayload);
            var requestBody = new StringContent(payload, Encoding.UTF8, "application/json");

            try
            {
                using (logger.BeginScope("EpostTicketCreation({@url})", url))
                {
                    logger.LogInformation("Calling EposOrderFederationApi {url}", url);

                    var token = contextAccessor!.HttpContext!.Request.Headers["Authorization"];

                    logger.LogInformation("Calling EposOrderFederationApi Ticket Creation {@payload}", payload);

                    HttpClient httpClient = new();

                    if (httpClient.DefaultRequestHeaders.Authorization != null)
                    {
                        httpClient.DefaultRequestHeaders.Remove("Authorization");
                    }

                    httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token.ToString().Replace("Bearer ", ""));

                    var response = await httpClient.PostAsync(url, requestBody);
                    var responseBody = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        logger.LogCritical("Error when calling epos ticket creation service. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                        throw new PassthroughException(response);
                    }

                    logger.LogInformation("Epos ticket creation call returned with {@StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                }
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Error when calling epos ticket creation service");
                throw new ServiceException(HttpStatusCode.InternalServerError, Errors.OrderFailedToSentToEPos);
            }
        }
    }
}