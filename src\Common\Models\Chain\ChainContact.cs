﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Chain;

public class ChainContact
{
    public string? ChainId { get; set; }
    public int ChainIdentityId { get; set; }
    public Guid ChainContactId { get; set; }
    public string? ContactName { get; set; }
    public string? RoleDesignation { get; set; }
    public string? Email { get; set; }

    public string? CountryPrefix { get; set; }

    public string? PhoneNumber { get; set; }
    public bool IsSpoc { get; set; } = false;
    public string CreatedBy { get; set; } = string.Empty;
    public string? UpdatedBy { get; set; }
}
