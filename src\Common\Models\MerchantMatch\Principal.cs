﻿using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class Principal : PrincipalBase
{
    [Display(Order = 0, Name = "First Name")]
    public string FirstName { get; set; } = string.Empty;

    [Display(Order = 1, Name = "Middle Initial")]
    public string MiddleInitial { get; set; } = string.Empty;

    [Display(Order = 2, Name = "Last Name")]
    public string LastName { get; set; } = string.Empty;

    [Display(Order = 4, Name = "Address")]
    public Address Address { get; set; } 
        = new Address();

    [Display(Order = 3, Name = "Drivers License")]
    public DriversLicense DriversLicense { get; set; } = new DriversLicense();

}