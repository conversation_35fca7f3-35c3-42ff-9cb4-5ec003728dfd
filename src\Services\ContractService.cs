﻿using System.Threading.Tasks;
using Common.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using Geidea.Utils.Exceptions;
using Common.Options;
using System.Net.Http;

namespace Services
{
    public class ContractService : IContractService
    {
        private readonly ILogger<ContractService> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly HttpClient client;
        private string MerchantServiceBaseUrl => $"{urlSettingsOptions.MerchantServiceBaseUrlNS}";
        private readonly string ContractEndpoint = "/api/v1/Merchant/Contract";

        public ContractService(
            ILogger<ContractService> logger,
            IOptionsMonitor<UrlSettings> urlSettingsOptions,
            HttpClient client)
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.client = client;
        }
        public async Task UpdateMerchantContractAsDeletedAsync(Guid orderId)
        {
            string url = $"{MerchantServiceBaseUrl}{ContractEndpoint}/{orderId}";
            using (logger.BeginScope("UpdateMerchantContractAsDeletedAsync({orderId}, {url})", orderId, url))
            {
                logger.LogInformation("Calling merchant API to delete the contract for order with id @contractId {0}", orderId);

                var response = await client.DeleteAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to delete the contract for order. Error was Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                logger.LogInformation("The contract for order with id {orderId} was marked as deleted", orderId);
            }
        }
    }
}
