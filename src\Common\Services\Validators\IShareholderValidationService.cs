﻿using Common.Models.Shareholder;
using Microsoft.AspNetCore.JsonPatch;
using System.Threading.Tasks;

namespace Common.Services.Validators;

public interface IShareholderValidationService
{
    Task ValidateCreateIndividualRequest(ShareholderIndividualCreateRequest request);
    Task ValidateCreateCompanyRequest(ShareholderCompanyCreateRequest request);
    Task ValidateEditCompanyRequest(ShareholderCompanyPatchRequest request);
    Task ValidateShareholderIndividualAssociationsCreateRequest(ShareholderIndividualAssociationsCreateRequest request);
    void ValidateIndividualsPatchTemp(ShareholderIndividualPatchRequest request);
}
