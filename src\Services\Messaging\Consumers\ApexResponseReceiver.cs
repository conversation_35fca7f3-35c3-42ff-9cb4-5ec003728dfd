﻿using Common.Models.Messaging.ApexProvider;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client.Events;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Text.Json;
using static Common.Constants;

namespace Services.Messaging.Consumers
{
    [ExcludeFromCodeCoverage]
    public class ApexResponseReceiver : MessageClient, IApexResponseReceiver
    {
        public event EventHandler<ApexResponseEventArgs>? OnApexResponseReceived;
        public ApexResponseReceiver(IHttpContextAccessor httpContextAccessor,
                                    ILogger<MessageClient> logger,
                                    IOptionsMonitor<RabbitMqOptions> rabbitMqOptions) : base(httpContextAccessor, logger, rabbitMqOptions, new QueueSettings
                                    {
                                        ExchangeName = RabbitMqExchanges.ApexResponseExchange,
                                        QueueName = RabbitMqQueues.ApexResponseQueue,
                                        RoutingKey = RabbitMqRoutingKeys.ApexResponseRoutingKey,
                                        Durable = true
                                    })
        {
        }

        public override void OnMessageReciving(object? model, BasicDeliverEventArgs ea)
        {
            try
            {
                var body = ea.Body.ToArray();
                var message = Encoding.UTF8.GetString(body);

                var apexResponse = Json.Deserialize<GenericMessage<ApexResponse>>(message,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                RabbitMqHelper.AddCorrelationIdToLogger(apexResponse?.Header.CorrelationId);

                logger.LogInformation("Apex Response Message received on queue {@queueName}.", queue.QueueName);

                OnApexResponseReceived?.Invoke(this,
                    new ApexResponseEventArgs(apexResponse!.Data, apexResponse.Header.Counterparty, apexResponse.Header.CorrelationId));
            }

            catch (Exception ex) when (ex.InnerException is ServiceException)
            {
                logger.LogError("Processing Apex Response message failed with error {@ex}.", ex);
            }

            catch (Exception ex)
            {
                logger.LogError("Processing Apex Response message failed with unexpected error {@ex}.", ex);
            }


        }

        public override void Connect()
        {
            base.Connect();
            ReciveMessageFromQueue(OnMessageReciving);
        }
    }
}
