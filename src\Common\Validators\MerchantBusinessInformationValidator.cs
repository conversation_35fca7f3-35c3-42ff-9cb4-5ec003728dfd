﻿using System.Text.RegularExpressions;
using Common.Models.Merchant;
using FluentValidation;

namespace Common.Validators
{
    public class MerchantBusinessInformationValidator : AbstractValidator<MerchantBusinessInformation>
    {
        private static readonly Regex businessNameRegex = new Regex("^[~`!@#$%^&*()_+=[\\]\\\\{}|;':\",.\\/<>?a-zA-Z0-9 -]+$");
        private static readonly Regex businessNameArRegex = new Regex("^[~`!@#$%^&*()_+=[\\]\\\\{}|;':\",.\\/<>?\\u0621-\\u064A\\u0660-\\u0669 -]+$");
        public MerchantBusinessInformationValidator()
        {
            RuleFor(x => x.LegalName)
                        .Must(o => o != null && businessNameRegex.IsMatch(o))
                        .When(o => o != null)
                        .WithErrorCode(Errors.EnglishBusinessName.Code)
                        .WithMessage(Errors.EnglishBusinessName.Message);

            RuleFor(x => x.LegalNameAr)
                            .Must(o => o != null && businessNameArRegex.IsMatch(o))
                            .When(o => o != null)
                            .WithErrorCode(Errors.ArabicBusinessName.Code)
                            .WithMessage(Errors.ArabicBusinessName.Message);
        }
    }
}
