﻿using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Geidea.Utils.Exceptions;
using LeadUpdate.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace LeadUpdate.Services
{
    public class DocumentService : IDocumentService
    {
        private readonly ILogger<DocumentService> logger;
        private readonly UrlSettings urlOptions;
        private readonly HttpClient client;

        private string DocumentServiceBaseUrl => $"{urlOptions.DocumentServiceBaseUrlNS}/api/v1";

        public DocumentService(ILogger<DocumentService> logger, IOptions<UrlSettings> urlSettingsOptions, HttpClient client)
        {
            this.logger = logger;
            this.urlOptions = urlSettingsOptions.Value;
            this.client = client;
        }

        public async Task<DocumentWithContent[]> GetDocumentsAsync(DocumentSearchRequest searchRequest)
        {
            string serviceUrl = $"{DocumentServiceBaseUrl}/documents";

            using (logger.BeginScope("GetDocumentWithContentAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling document service to retrieve documents.");

                var content = new StringContent(JsonConvert.SerializeObject(searchRequest), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(serviceUrl, content);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling document service to retrieve documents. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var documents = JsonConvert.DeserializeObject<DocumentWithContent[]>(responseBody);
                logger.LogInformation($"Retrieved {documents.Length} documents.");
                foreach (var document in documents)
                {
                    logger.LogInformation($"Document id retrieved to update lead: {document.Id}.");
                }
                return documents;
            }
        }
    }
}