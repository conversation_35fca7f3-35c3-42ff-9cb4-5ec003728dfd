﻿using Common.Models;
using Common.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.Text;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class ChecksServiceTests
    {
        private readonly Mock<ILogger<ChecksService>> logger = new Mock<ILogger<ChecksService>>();
        private readonly Mock<IDocumentService> documentService = new Mock<IDocumentService>();
        private readonly ChecksService checksService;

        public ChecksServiceTests()
        {
            checksService = new ChecksService(documentService.Object, logger.Object);
        }

        [Test]
        public async Task GetFullInfoChecks()
        {
            var docs = new DocumentWithContent[]
            {
                new DocumentWithContent
                {
                    MerchantId = Guid.NewGuid(),
                    Content = Encoding.UTF8.GetBytes("{"+
                               "\"crName\":\"test name\","+
                               "\"parties\":["+
                                  "{"+
                                     "\"identity\":{"+
                                                    "\"id\":\"1010454206\","+
                                                    "\"type\":\"crno\""+
                                                   "}"+
                                    "}"+
                                   "],"+
                               "\"address\":{"+
                                            "\"general\":{"+
                                                "\"address\":\"test address 112\""+
                                  "}"+
                               "},"+
                               "\"location\":{"+
                                            "\"id\":\"1010\","+
                                            "\"name\":\"location name\""+
                                    "}"+
                               "}")
                },
                new DocumentWithContent
                {
                    MerchantId = Guid.NewGuid(),
                    Content = Encoding.UTF8.GetBytes("")
                }
            };
            documentService.Setup(x => x.GetDocumentWithContentAsync(It.IsAny<DocumentSearchCriteria>()))
                .Returns(Task.FromResult(docs));
            var checks = await checksService.GetMerchantFullInfoChecks(new Guid[0]);
            checks.Should().HaveCount(1);
            checks[0].CrName.Should().Be("test name");
            checks[0].Parties[0].Identity.Id.Should().Be("1010454206");
            checks[0].Address.General.Address.Should().Be("test address 112");
            checks[0].Location.Name.Should().Be("location name");
        }

        [Test]
        public async Task GetFullInfoChecks_Error()
        {
            var docs = new DocumentWithContent[]
            {
                new DocumentWithContent
                {
                    MerchantId = Guid.NewGuid(),
                    Content = Encoding.UTF8.GetBytes("{"+
                               "\"crName\":\"test name\","+
                               "\"parties\":[")
                }
            };
            documentService.Setup(x => x.GetDocumentWithContentAsync(It.IsAny<DocumentSearchCriteria>()))
                .Returns(Task.FromResult(docs));

            var checks = await checksService.GetMerchantFullInfoChecks(new Guid[0]);
            checks.Should().HaveCount(0);
        }

        [Test]
        public async Task GetIdentityChecks()
        {
            var docs = new DocumentWithContent[]
            {
                new DocumentWithContent
                {
                    MerchantId = Guid.NewGuid(),
                    Content = Encoding.UTF8.GetBytes("{"+
                               "\"user_info\":["+
                                  "{"+
                                     "\"arabic_name\":\"arabic name test\""+
                                    "}"+
                                   "]"+
                                    "}")
                },
                new DocumentWithContent
                {
                    MerchantId = Guid.NewGuid(),
                    Content = Encoding.UTF8.GetBytes("")
                }
            };
            documentService.Setup(x => x.GetDocumentWithContentAsync(It.IsAny<DocumentSearchCriteria>()))
                .Returns(Task.FromResult(docs));
            var checks = await checksService.GetMerchantIdentityChecks(new Guid[0]);
            checks.Should().HaveCount(1);
            checks[0].UserInfo[0].ArabicName.Should().Be("arabic name test");
        }
    }
}
