﻿using System;

namespace Common.Models
{
    public class MerchantPersonOfInterest
    {
        public Guid MerchantPersonOfInterestId { get; set; }
        public string NationalId { get; set; } = string.Empty;
        public string? InterestEntityType { get; set; }
        public string? Nationality { get; set; }
        public string? Title { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? FirstNameAr { get; set; }
        public string? LastNameAr { get; set; }
        public DateTimeOffset? DOB { get; set; }
        public DateTime? IdExpiryDate { get; set; }
        public Guid MerchantId { get; set; }
        public string OrganizationRole { get; set; } = string.Empty;
        public bool IsPrincipal { get; set; }
        public decimal OwnershipPercentage { get; set; }
        public bool DeletedFlag { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }
        public Guid? LeadId { get; set; }
        public string? Gender { get; set; }
    }
}
