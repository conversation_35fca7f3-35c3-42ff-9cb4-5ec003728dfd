﻿using System;
using System.Collections.Generic;

namespace Common.Models.Checkout
{
    public class OrderResponse
    {
        public Guid OrderId { get; set; }

        public Guid? AgreementId { get; set; }

        public Guid MerchantId { get; set; }

        public Guid StoreId { get; set; }

        public Guid UserId { get; set; }

        public Guid? AddressId { get; set; }

        public string OrderStatus { get; set; } = null!;

        public string? MerchantStatus { get; set; }

        public List<OrderItemResponse>? OrderItem { get; set; }

        public string? PaymentReference { get; set; }

        public string? TrackingNumber { get; set; }

        public string? TrackingUrl { get; set; }

        public string? Shipper { get; set; }

        public DateTime? ShippedDate { get; set; }

        public string? CouponCode { get; set; }

        public string? PaymentMethod { get; set; }

        public string? CompanyRegNo { get; set; }

        public string? SalesName { get; set; }

        public string? SubscriptionPlan { get; set; }

        public string? ProjectName { get; set; }

        public string? ReferralChannel { get; set; }

        public string? Note { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string? OrderNumber { get; set; }

        public int OrderCommentsCount { get; set; }

        public bool DeletedFlag { get; set; }

        public string MerchantName { get; set; } = null!;

        public DateTime? CheckoutDate { get; set; }

        public decimal? Subtotal { get; set; }

        public decimal? Discount { get; set; }

        public decimal? VatPercent { get; set; }

        public decimal? Vat { get; set; }

        public decimal? Total { get; set; }

        public decimal? MonthlySubtotal { get; set; }

        public decimal? MonthlyVat { get; set; }

        public decimal? MonthlyTotal { get; set; }

        public decimal? MonthlyDiscount { get; set; }

        public decimal? YearlySubtotal { get; set; }

        public decimal? YearlyVat { get; set; }

        public decimal? YearlyTotal { get; set; }

        public decimal? YearlyDiscount { get; set; }

        public string? Currency { get; set; }

        public string? BillPayments { get; set; }
    }
}
