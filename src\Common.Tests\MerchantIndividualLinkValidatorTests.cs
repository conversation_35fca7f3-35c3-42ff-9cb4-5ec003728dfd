﻿using FluentValidation.TestHelper;

namespace Common.Tests;

public class MerchantIndividualLinkValidatorTests
{
    [Test]
    public void MerchantIndividualLinkValidator_EmptyMerchant_IsInvalid()
    {
        var result = new MerchantIndividualLinkValidator().TestValidate(new MerchantIndividualLink()
        {
            OwnershipPercentage = 1
        });

        Assert.That(result.IsValid, Is.False);
    }

    [TestCase(-1.2)]
    [TestCase(100.001)]
    public void MerchantIndividualLinkValidator_InvalidOwnership_IsInvalid(decimal invalidOwnership)
    {
        var result = new MerchantIndividualLinkValidator().TestValidate(new MerchantIndividualLink
        {
            MerchantId = Guid.NewGuid(),
            OwnershipPercentage = invalidOwnership
        });

        Assert.That(result.IsValid, Is.False);
    }

    [Test]
    public void MerchantIndividualLinkValidator_InvalidOwnership_ReturnsOk()
    {
        var result = new MerchantIndividualLinkValidator().TestValidate(new MerchantIndividualLink
        {
            MerchantId = Guid.NewGuid(),
            OwnershipPercentage = 1.2M
        });

        Assert.That(result.IsValid, Is.True);
    }
}
