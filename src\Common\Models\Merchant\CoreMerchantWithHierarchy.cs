﻿using Geidea.Utils.ReferenceData;
using System;
using System.Collections.Generic;

namespace Common.Models.Merchant;

public class CoreMerchantWithHierarchy
{
    public Guid MerchantId { get; set; }

    public Guid? LeadId { get; set; }

    public string? MerchantType { get; set; }

    public string? Counterparty { get; set; }

    [ReferenceData("MERCHANT_TAG")]
    public string? Tag { get; set; }

    public string? MerchantStatus { get; set; }

    public List<MerchantHierarchy> Hierarchies { get; set; } = new List<MerchantHierarchy>();
}