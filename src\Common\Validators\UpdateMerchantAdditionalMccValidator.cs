﻿using Common.Models;
using Common.Models.Merchant;
using FluentValidation;

namespace Common.Validators
{
    public class UpdateMerchantAdditionalMccValidator : AbstractValidator<AdditionalMccUpdate>
    {
        public UpdateMerchantAdditionalMccValidator()
        {
            RuleFor(x => x.MCC)
                .Must(x => x != null && x.Length <= 4)
                .WithErrorCode(Errors.AdditionalMccLengthValidation.Code)
                .WithMessage(Errors.AdditionalMccLengthValidation.Message);
        }
    }
}
