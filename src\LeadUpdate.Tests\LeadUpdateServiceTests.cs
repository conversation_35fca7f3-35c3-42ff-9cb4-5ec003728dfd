﻿using System;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LeadUpdate.Models;
using LeadUpdate.Services;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;

namespace LeadUpdate.Tests
{
    public class LeadUpdateServiceTests
    {
        private readonly ILogger<LeadUpdateService> logger;
        private readonly IOptions<UrlSettings> options;
        private LeadUpdateService? leadUpdateService;

        private readonly Lead leadWithoutNames = new Lead
        {
            DOB = DateTimeOffset.Now,
            Gender = "Female",
            LeadId = Guid.NewGuid(),
            Nationality = "nationality"
        };

        private readonly Lead leadWithoutGender = new Lead
        {
            LeadId = Guid.NewGuid(),
            OwnerFirstName = "first name",
            OwnerFirstNameAr = "first name ar",
            OwnerLastName = "last name",
            OwnerLastNameAr = "last name ar"
        };

        private readonly ILeadService leadService;
        private readonly IDocumentService documentService;


        public LeadUpdateServiceTests()
        {
            logger = Substitute.For<ILogger<LeadUpdateService>>();
            options = Options.Create(new UrlSettings
            {
                DocumentServiceBaseUrl = "http://documentService",
                SearchServiceBaseUrl = "http://searchService"
            });
            leadService = Substitute.For<ILeadService>();
            documentService = Substitute.For<IDocumentService>();
        }

        [SetUp]
        public void SetUp()
        {
            leadService.ClearReceivedCalls();
        }

        [Test]
        public async Task UpdateLeadsAsync_IfLeadWithoutNames_AndNoEnghlishNamesInTahakomResponse_UpdatesWithArabicNames()
        {
            leadService.GetIncompleteLeadsAsync(Arg.Any<int>(), Arg.Any<int>(), Arg.Any<LeadUpdateType>(), "asc").Returns(new[] {leadWithoutNames});
            var firstName = "first name ar";
            var lastName = "last name ar";
            documentService.GetDocumentsAsync(Arg.Any<DocumentSearchRequest>()).Returns(new[]
            {
                new DocumentWithContent
                {
                    LeadId = leadWithoutNames.LeadId,
                    Content = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new TahakomResponse
                    {
                        user_info = new[]
                        {
                            new UserInfo
                            {
                                arabic_first_name = firstName,
                                arabic_family_name = lastName
                            }
                        }
                    }))
                }
            });
            leadUpdateService = new LeadUpdateService(leadService, documentService, logger);

            await leadUpdateService.UpdateLeadsAsync(10,0,LeadUpdateType.NamesAndSasInfo, "asc");

            await leadService.Received(1).PatchLeadAsync(leadWithoutNames.LeadId,
                Arg.Is<JsonPatchDocument<Lead>>(p => p.Operations.Count == 4 &&
                    p.Operations.Any(op => op.path == "/OwnerFirstName" && op.value.ToString() == firstName) &&
                    p.Operations.Any(op => op.path == "/OwnerLastName" && op.value.ToString() == lastName) &&
                    p.Operations.Any(op => op.path == "/OwnerFirstNameAr" && op.value.ToString() == firstName) &&
                    p.Operations.Any(op => op.path == "/OwnerLastNameAr" && op.value.ToString() == lastName)));
        }

        [Test]
        public async Task UpdateLeadsAsync_WhenDobNationalityGenderMissing_AreTakenFromTahakom()
        {
            leadService.GetIncompleteLeadsAsync(Arg.Any<int>(), Arg.Any<int>(), Arg.Any<LeadUpdateType>(), "asc").Returns(new[] { leadWithoutGender });
            var gender = "gender";
            var nationality = "nationality";
            var dob = DateTimeOffset.ParseExact("1990/02/02", "yyyy/MM/dd", CultureInfo.InvariantCulture);
            documentService.GetDocumentsAsync(Arg.Any<DocumentSearchRequest>()).Returns(new[]
            {
                new DocumentWithContent
                {
                    LeadId = leadWithoutGender.LeadId,
                    Content = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new TahakomResponse
                    {
                        user_info = new[]
                        {
                            new UserInfo
                            {
                                gender = gender,
                                nationality = nationality,
                                dob = dob.ToString("yyyy/MM/dd", CultureInfo.InvariantCulture)
                            }
                        }
                    }))
                }
            });
            leadUpdateService = new LeadUpdateService(leadService, documentService, logger);

            await leadUpdateService.UpdateLeadsAsync(10, 0, LeadUpdateType.NamesAndSasInfo, "asc");

            await leadService.Received(1).PatchLeadAsync(leadWithoutGender.LeadId,
                Arg.Is<JsonPatchDocument<Lead>>(p => p.Operations.Count == 3 &&
                                                     p.Operations.Any(op => op.path == "/Gender" && op.value.ToString() == gender) &&
                                                     p.Operations.Any(op => op.path == "/Nationality" && op.value.ToString() == nationality) &&
                                                     p.Operations.Any(op => op.path == "/DOB" && dob.Equals(op.value))));
        }

        [Test]
        public async Task UpdateLeadsAsync_WhenMultipleLeads_AllAreUpdated()
        {
            leadService.GetIncompleteLeadsAsync(Arg.Any<int>(), Arg.Any<int>(), Arg.Any<LeadUpdateType>(), "asc").Returns(new[] { leadWithoutGender, leadWithoutNames });
            documentService.GetDocumentsAsync(Arg.Any<DocumentSearchRequest>()).Returns(new[]
            {
                new DocumentWithContent
                {
                    LeadId = leadWithoutGender.LeadId,
                    Content = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new TahakomResponse
                    {
                        user_info = new[]
                        {
                            new UserInfo
                            {
                                gender = "gender",
                                nationality = "nationality",
                                dob = "1999/01/01"
                            }
                        }
                    }))
                },
                new DocumentWithContent
                {
                    LeadId = leadWithoutNames.LeadId,
                    Content = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new TahakomResponse
                    {
                        user_info = new[]
                        {
                            new UserInfo
                            {
                                arabic_first_name = "firstName",
                                arabic_family_name = "lastName"
                            }
                        }
                    }))
                }
            });
            leadUpdateService = new LeadUpdateService(leadService, documentService, logger);
            await leadUpdateService.UpdateLeadsAsync(10, 0, LeadUpdateType.NamesAndSasInfo, "asc");

            await leadService.Received(2).PatchLeadAsync(Arg.Any<Guid>(), Arg.Any<JsonPatchDocument<Lead>>());
        }

        [Test]
        public async Task UpdateLeadsAsync_WhenWrongDocumentFormat_UpdatesOnlyCorrectOnes()
        {
            leadService.GetIncompleteLeadsAsync(Arg.Any<int>(), Arg.Any<int>(), Arg.Any<LeadUpdateType>(), "asc").Returns(new[] { leadWithoutGender, leadWithoutNames });
            documentService.GetDocumentsAsync(Arg.Any<DocumentSearchRequest>()).Returns(new[]
            {
                new DocumentWithContent
                {
                    LeadId = leadWithoutGender.LeadId,
                    Content = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new TahakomResponse
                    {
                        user_info = new[]
                        {
                            new UserInfo
                            {
                                gender = "gender",
                                nationality = "nationality",
                                dob = "1999/01/01"
                            }
                        }
                    }))
                },
                new DocumentWithContent
                {
                    LeadId = leadWithoutNames.LeadId,
                    Content = Encoding.UTF8.GetBytes("some content")
                }
            });
            leadUpdateService = new LeadUpdateService(leadService, documentService, logger);
            await leadUpdateService.UpdateLeadsAsync(10, 0, LeadUpdateType.NamesAndSasInfo, "asc");

            await leadService.Received(1).PatchLeadAsync(leadWithoutGender.LeadId, Arg.Any<JsonPatchDocument<Lead>>());
        }


        [Test]
        public async Task UpdateLeadsAsync_IfUpdateExpiryDate_ShouldGetFromDocument()
        {
            leadService.GetIncompleteLeadsAsync(Arg.Any<int>(), Arg.Any<int>(), Arg.Any<LeadUpdateType>(), "asc").Returns(new[] { leadWithoutNames });
            var idExpiryDate = DateTimeOffset.ParseExact("2022/02/02", "yyyy/MM/dd", CultureInfo.InvariantCulture);

            documentService.GetDocumentsAsync(Arg.Any<DocumentSearchRequest>()).Returns(new[]
            {
                new DocumentWithContent
                {
                    LeadId = leadWithoutNames.LeadId,
                    Content = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new TahakomResponse
                    {
                        user_info = new[]
                        {
                            new UserInfo
                            {
                                id_expiry_date_gregorian = idExpiryDate.ToString("yyyy/MM/dd", CultureInfo.InvariantCulture)
                            }
                        }
                    }))
                }
            });
            leadUpdateService = new LeadUpdateService(leadService, documentService, logger);

            await leadUpdateService.UpdateLeadsAsync(10, 0, LeadUpdateType.ExpiryDate, "asc");

            await leadService.Received(1).PatchLeadAsync(leadWithoutNames.LeadId,
                Arg.Is<JsonPatchDocument<Lead>>(p => p.Operations.Count == 1 &&
                                                     p.Operations.Any(op => op.path == "/IdExpiryDate" && idExpiryDate.Equals(op.value))));
        }

    }
}