﻿using Common.Models.TaskComments;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface ITaskCommentService
    {
        Task<TaskCommentResponse> CreateCommentAsync(Guid taskId, TaskCommentCreateRequest request);

        Task<TaskCommentResponse> GetCommentByIdAsync(Guid commentId);

        Task<List<TaskCommentResponse>> GetCommentByTaskIdAsync(Guid taskId);
    }
}