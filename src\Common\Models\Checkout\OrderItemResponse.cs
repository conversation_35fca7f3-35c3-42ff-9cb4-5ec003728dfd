﻿using System;
using System.Collections.Generic;

namespace Common.Models.Checkout
{
    public class OrderItemResponse
    {
        public Guid OrderItemId { get; set; }

        public Guid OrderId { get; set; }

        public string? ProductType { get; set; }

        public string? ProductCode { get; set; }

        public bool DeletedFlag { get; set; } = false;

        public Guid? AddressId { get; set; }

        public Guid MerchantId { get; set; }

        public long? CreatedUnixTimestampUtc { get; set; }

        public long? UpdatedUnixTimestampUtc { get; set; }

        public string? EposTicketId { get; set; }
        public List<Guid> ProductInstanceIds { get; set; } = new List<Guid>();
        public Guid? ProductId { get; set; }
        public List<OrderItemCategory> OrderItemCategories { get; set; } = new();
    }
}
