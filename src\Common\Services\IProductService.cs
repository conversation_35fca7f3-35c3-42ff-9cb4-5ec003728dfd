﻿using Common.Models.Gle;

namespace Common.Services
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Common.Models;
    using Common.Models.Product;
    using Common.Models.ProductInstance;
    using Common.Models.TerminalDataSet;
    using Microsoft.AspNetCore.JsonPatch;

    public interface IProductService
    {
        Task<Product> GetProductByIdAsync(Guid productId);
        Task<List<ProductInstance>> GetProductInstances(List<Guid> productInstancesIds);
        Task<ProductInstance> PatchAsync(Guid productInstanceId, JsonPatchDocument<UpdateProductInstanceRequest> patchDocument);
        Task<Product[]> GetProductsByIdsAsync(Guid[] ids);
        Task<ProductInstance[]> GetProductInstancesByIdsAsync(Guid[] ids);
        Task<Guid[]> GetRelatedProductsAsync(ProductCodesRequest productCodesRequest);
        Task<List<Product>> FindProductsAsync(FindProductRequest findProductRequest);
        Task<BillPaymentServiceAndBundleFlags> GetBpProductTypesInListOfProducts(IdsRequest ids);
        Task<List<TerminalDataSetResponse>> GenerateTIDAndMIDAndAddEditTerminalDataSets(TerminalDataSetRequest terminalDataSetsRequest);
        Task<List<Guid>> UpdateTerminalProductInstancesMeta(List<UpdateProductInstanceMetaRequest> updateProductInstancesMetaRequest);
        Task<bool> IsMerchantRegisteredInGle(Guid merchantId);
        Task<List<TerminalDataSetResponse>> UpdateOrderTerminalDataSetsMcc(TerminalDataRequestMcc terminalDataSetsRequest);

        Task<MerchantInquiryResponse> ValidateTerminalRegistration(string mId);
    }
}
