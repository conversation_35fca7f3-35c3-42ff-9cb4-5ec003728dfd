{"ConfigService": {"Url": "http://uae-dev-config.uae-dev.gd-azure-dev.net", "VaultSecretsPath": "/usr/share/secrets/secrets.json", "Keys": ["ConfigService:VaultSecretsPath", "UrlSettings:DocumentServiceBaseUrl", "UrlSettings:MerchantServiceBaseUrl", "UrlSettings:GeideaFederationServiceBaseUrl", "UrlSettings:NotificationServiceBaseUrl", "UrlSettings:CheckoutServiceBaseUrl", "UrlSettings:ReferenceServiceBaseUrl", "UrlSettings:UserServiceBaseUrl", "UrlSettings:LeadServiceBaseUrl", "UrlSettings:ProductServiceBaseUrl", "UrlSettings:SearchServiceBaseUrl", "UrlSettings:DueDiligenceServiceBaseUrl", "UrlSettings:NexusBridgeApiBaseUrl", "Application:DefaultContactReasonName", "Serilog:MinimumLevel", "Keycloak:*", "Application:CorsAllowedOrigins", "Application:<PERSON>wa<PERSON>", "ApiExceptions:UseSlim", "Polly:TimeoutInSeconds", "AuthorizationPolicy:Server", "AuthorizationPolicy:Namespace", "Default:RabbitMqConfig:*", "Header:Whitelist", "GsdkSettings:*", "MerchantCheckOptions:UniqueLicenseCheckEnabled", "Default:HealthChecks:*", "FreelancerTerminalTypeFeatureToggle:EnableT300TerminalType", "ShareholderSearchConfiguration:*", "Csv:*", "TMSIntegrationFeatureToggle:EnableTMSIntegration"]}, "Header": {"Whitelist": "X-Original-For,X-Original-Proto,X-CounterpartyC<PERSON>,X-ApplicationLanguage,X-Correlation-ID,X-Appgw-Trace-Id,X-B3-<PERSON><PERSON><PERSON>,X-B3-<PERSON><PERSON>,X-B3-<PERSON>ni<PERSON>,X-B3-<PERSON><PERSON>,X-Envoy-Attempt-Count,X-Envoy-External-Address,X-Forwarded-Client-Cert,X-Forwarded-Host,X-Forwarded-For,X-Forwarded-Port,X-Forwarded-Proto,X-Original-Host,X-Original-Url,X-Real-Ip,X-Request-Id,X-Envoy-Internal,X-Consumer-Username,X-Credential-Identifier,X-Consumer-Id,X-ENVOY-DECORATOR-OPERATION,X-ENVOY-PEER-METADATA,X-ENVOY-PEER-METADATA-ID,X-FORWARDED-PATH,X-REQUESTED-WITH,X-BLUECOAT-VIA"}, "UrlSettings": {"DocumentServiceBaseUrl": "http://uae-dev-document.uae-dev.gd-azure-dev.net", "MerchantServiceBaseUrl": "http://uae-dev-merchant.uae-dev.gd-azure-dev.net", "GeideaFederationServiceBaseUrl": "http://uae-dev-federation.uae-dev.gd-azure-dev.net", "NotificationServiceBaseUrl": "http://uae-dev-notification.uae-dev.gd-azure-dev.net", "CheckoutServiceBaseUrl": "http://uae-dev-checkout.uae-dev.gd-azure-dev.net", "UserServiceBaseUrl": "http://uae-dev-user.uae-dev.gd-azure-dev.net", "ReferenceServiceBaseUrl": "http://uae-dev-reference.uae-dev.gd-azure-dev.net", "LeadServiceBaseUrl": "http://uae-dev-lead.uae-dev.gd-azure-dev.net", "ProductServiceBaseUrl": "http://uae-dev-product.uae-dev.gd-azure-dev.net", "SearchServiceBaseUrl": "http://uae-dev-search.uae-dev.gd-azure-dev.net", "DueDiligenceServiceBaseUrl": "http://uae-dev-due-dilligence.uae-dev.gd-azure-dev.net", "NexusBridgeApiBaseUrl": "http://uae-dev-nbapi.uae-dev.gd-azure-dev.net"}, "Application": {"Name": "Backoffice API", "Version": "0.1.0", "DefaultContactReasonName": "DEFAULT"}, "AuthorizationPolicy": {"Server": "http://uae-dev-openpolicy.uae-dev.gd-azure-dev.net/v1/data", "Namespace": "portal"}, "Csv": {"Separator": ",", "ProductType": "TERMINAL", "ReferenceCatalogue": "MERCHANT_CATEGORY_CODE"}, "AllowedHosts": "*", "Keycloak": {"Authority": "https://api-uae-dev.gd-azure-dev.net/auth/realms/uae-dev", "RequireHttpsMetadata": false, "PublicKey": "MIIClTCCAX0CBgFwC2lhnDANBgkqhkiG9w0BAQsFADAOMQwwCgYDVQQDDANkZXYwHhcNMjAwMjAzMTQxNTIwWhcNMzAwMjAzMTQxNzAwWjAOMQwwCgYDVQQDDANkZXYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCMCUG9nCz7FdrkTIK3lijxnX22I3InVxqijDlJoQMOkiYcmdCm0WVFBsKcJlsysKUAsmSL8p7tv9YpaYF/6DmU80h0SqwBPdK4aQMTr1n1sGawkUGi95RWOVs6SP7aR+e8VHmSRaZ1gUy2GouGYafqMHyZiVGZDcSWHiPXIPXCvUDa9jlEKFSpaGfXCJ3nlh0G17LfbA0K6kwd+vWapSAHjr12XWgqWBC3INGIYG3hbxLGSgpcElFh7aE6YxEg8PTfJpluSG9s5QZze5rhEc4umYciY4iIEHzyoCpi8Hn2BXcEYxnZmXTCP0Qm4FuaK50/YZ8AyN/rjnuNquknWQFbAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHLS35idfXu0i3yLUWET4MSRB/W51DKJDRPcZvWoQDibsjZUMgK9gXH8DJXfyyTs6zW7mziZgA49u2szibyqDRtGVZ4BJtIcbxAQfcOXoRkjyoBtqr3gd7qlvi+C4ntVmvi0I3LHSFOFUP8own76sjrtCKsE6LY4XlaPMp0e+e0OI37oVDzh5BfuJOumT8nGBT6aA0ul5MlsfT3D1BGtpAnDftbeHjVpg18Hme1i4RnpT5GjFBT07yCVTmqTg1n94WLbi9bXHQo+iE6h9x9XGUlp8bYHSmiWEoGmnmEMn0Io3Sikpy0jegsunascPEyGCIO0nrbJwN6l/qUmEv+RHzk="}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "System": "Error"}}, "Using": ["Serilog.Sinks.Console"], "Enrich": ["FromLogContext", "WithMachineName", "WithEnvironmentUserName", "WithProcessId"], "Properties": {"ApplicationName": "BackofficeAPI", "ProcessName": "BackofficeAPI"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "ApiExceptions": {"UseSlim": true}, "Polly": {"TimeoutInSeconds": 40}, "Default": {"RabbitMqConfig": {"HostName": "*************", "Port": "30567", "VirtualHost": "/", "UserName": "upgw-dev", "Password": "JU#dY6GR+py*8>LA", "SslEnabled": false}}, "GsdkSettings": {"Live": {"BaseUrl": "https://geidea-integration.sdk.finance", "EgyptBaseUrl": "https://geideaeg-integration.sdk.finance", "Username": "<EMAIL>", "Password": "eNq&9hceJ6ne%7pU"}, "Test": {"BaseUrl": "https://geidea-integration.sdk.finance", "EgyptBaseUrl": "https://geideaeg-integration.sdk.finance", "Username": "<EMAIL>", "Password": "eNq&9hceJ6ne%7pU"}, "IsTest": true, "KeycloakUrl": "https://sso.sdk.finance/auth/realms/integration/protocol/openid-connect/token", "FeatureManagement": {"MMSProductRegisteredMessages": {"IsActive": false, "AllowedCounterparties": "GEIDEA_SAUDI,GEIDEA_EGYPT,GEIDEA_UAE "}}}, "MerchantCheckOptions": {"UniqueLicenseCheckEnabled": false}, "FreelancerTerminalTypeFeatureToggle": {"EnableT300TerminalType": true}, "ShareholderSearchConfiguration": {"ShareholderCompanySearchDefaultTake": 20, "ShareholderIndividualSearchDefaultTake": 20}, "TMSIntegrationFeatureToggle": {"EnableTMSIntegration": false}}