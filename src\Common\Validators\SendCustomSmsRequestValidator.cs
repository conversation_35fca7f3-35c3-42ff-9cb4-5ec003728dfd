﻿using Common.Models;
using FluentValidation;

namespace Common.Validators
{
    public class SendCustomSmsRequestValidator : AbstractValidator<SendCustomSmsRequest>
    {
        public SendCustomSmsRequestValidator()
        {
            RuleFor(a => a.Body)
                .NotEmpty()
                .Must(x => x != null && x.Length <= 512)
                .WithErrorCode(Errors.BodyLengthValidation.Code)
                .WithMessage(Errors.BodyLengthValidation.Message);

            RuleFor(a => a.Recipients).NotNull().NotEmpty();
        }
    }
}
