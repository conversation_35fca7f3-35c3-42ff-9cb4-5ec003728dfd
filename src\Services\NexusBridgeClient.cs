﻿using Common.Models.NexusBridge;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Services
{
    public class NexusBridgeClient : INexusBridgeClient
    {
        private readonly ILogger<NexusBridgeClient> logger;

        private readonly IOptions<UrlSettings> urlOptions;
        private readonly HttpClient httpClient;
        private string NexusBridgeServiceBaseUrl => urlOptions.Value?.NexusBridgeApiBaseUrl ?? string.Empty;
        private const string NexusBridgeServiceEndpoint = "/api/v1/merchant/create";
        private const string NexusBridgeOrderEndpoint = "/api/v1/order/create";
        private const string NexusBridgeProductEndpoint = "/api/v1/product/create";

        private string MerchantServiceBaseUrl => $"{urlOptions.Value.MerchantServiceBaseUrl}/api/v1";
        private string ProductServiceBaseUrl => $"{urlOptions.Value.ProductServiceBaseUrl}/api/v1";

        public NexusBridgeClient(
            ILogger<NexusBridgeClient> logger,
           IOptions<UrlSettings> urlOptions,
           HttpClient httpClient)
        {
            this.httpClient = httpClient;
            this.logger = logger;
            this.urlOptions = urlOptions;
        }

        public async Task<string> ConnectToNexusBridge(NBMerchant nBMerchant, bool orderFlag)
        {
            string jsonResult = string.Empty;
            if(orderFlag)
            {
                var requestUri = $"{NexusBridgeServiceBaseUrl}{NexusBridgeOrderEndpoint}";
                using (logger.BeginScope("ConnectToNexusBridgeOrder({@requestUri})", requestUri))
                {
                    logger.LogInformation("Calling Order NBAPI to get Information");
                    var requestLog = new StringContent(JsonConvert.SerializeObject(nBMerchant), Encoding.UTF8, "application/json");
                    var response = await httpClient.PostAsync(requestUri, requestLog);
                    jsonResult = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        logger.LogCritical("Error when calling Order NB API service to create merchant. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, jsonResult);
                        throw new PassthroughException(response);
                    }
                }
            }
            else
            {
                var requestUri = $"{NexusBridgeServiceBaseUrl}{NexusBridgeServiceEndpoint}";
                using (logger.BeginScope("ConnectToNexusBridge({@requestUri})", requestUri))
                {
                    logger.LogInformation("Calling NBAPI to get Information");
                    var requestLog = new StringContent(JsonConvert.SerializeObject(nBMerchant), Encoding.UTF8, "application/json");
                    var response = await httpClient.PostAsync(requestUri, requestLog);
                    jsonResult = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        logger.LogCritical("Error when calling NB API service to create merchant. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, jsonResult);
                        throw new PassthroughException(response);
                    }
                }
            }
            
            return jsonResult;
        }

        public async Task<string> ConnectToNexusTerminal(NBProduct product)
        {
            string jsonResult = string.Empty;
                var requestUri = $"{NexusBridgeServiceBaseUrl}{NexusBridgeProductEndpoint}";
                using (logger.BeginScope("ConnectToNexusBridgeProduct({@requestUri})", requestUri))
                {
                    logger.LogInformation("Calling Product NBAPI to get Information");
                    var requestLog = new StringContent(JsonConvert.SerializeObject(product), Encoding.UTF8, "application/json");
                    var response = await httpClient.PostAsync(requestUri, requestLog);

                    jsonResult = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        logger.LogCritical("Error when calling Product NB API service to create merchant. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, jsonResult);
                        throw new PassthroughException(response);
                    }
                }
            return jsonResult;
        }

        public async Task<List<MerchantExternalProduct>> GetMerchantExternalProducts()
        {
            var url = $"{MerchantServiceBaseUrl}/MerchantExternalProducts";

            using (logger.BeginScope("GetAllExternalProductsAsync({@url})", url))
            {
                logger.LogInformation("Calling merchant external products");

                var response = await httpClient.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service get external products. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var products = Json.Deserialize<List<MerchantExternalProduct>>(responseBody);

                return products;
            }
        }

        public async Task<List<Common.Models.NexusBridge.MerchantAddress>> GetMerchantAddress(Guid MerchantId)
        {
            var url = $"{MerchantServiceBaseUrl}/Merchant/{MerchantId}/address";

            using (logger.BeginScope("GetMerchantAddress({@url})", url))
            {
                logger.LogInformation("Calling merchant address");

                var response = await httpClient.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to get address. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var address = Json.Deserialize<List<Common.Models.NexusBridge.MerchantAddress>>(responseBody);

                return address;
            }
        }

        public async Task<List<Common.Models.NexusBridge.MerchantBankAccountResponse>> GetBankAccountDetails(Guid MerchantId)
        {
            var url = $"{MerchantServiceBaseUrl}/Merchant/{MerchantId}/BankAccount";

            using (logger.BeginScope("GetbankAccountDetails({@url})", url))
            {
                logger.LogInformation("Calling Bank Account");

                var response = await httpClient.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to get bank account details. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var bank = Json.Deserialize<List<Common.Models.NexusBridge.MerchantBankAccountResponse>>(responseBody);

                return bank;
            }
        }

        public async Task<List<MerchantTerminal>> GetTerminalDetails(string? accountId)
        {
            var url = $"{ProductServiceBaseUrl}/Product/GetMultipleProductOnAccount/{accountId}";

            using (logger.BeginScope("GetTerminalDetails({@url})", url))
            {
                logger.LogInformation("Calling Terminal");

                var response = await httpClient.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling product service to get terminal details. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var productsOnAccount = Json.Deserialize<List<ProductsOnAccount>>(responseBody);

                var terminals = MapProductsOnAccountToTerminals(productsOnAccount);

                return terminals;
            }
        }

        private static List<MerchantTerminal> MapProductsOnAccountToTerminals(List<ProductsOnAccount> productItem)
        {
            var terminals = new List<MerchantTerminal>();
            foreach(var product in productItem)
            {
                terminals.Add(new MerchantTerminal()
                {
                    TerminalId = product.TID,
                    MerchantId = !string.IsNullOrEmpty(product.MID) ? product.MID : string.Empty,
                    TerminalStatus = product.ProductStatus,
                    Brand = product.Make,
                    Model = product.Model,
                    OrderNumber = product.OrderNumber,
                    SerialNumber = product.SerialNumber,
                });
               
            };

            return terminals;
        }
    }
}