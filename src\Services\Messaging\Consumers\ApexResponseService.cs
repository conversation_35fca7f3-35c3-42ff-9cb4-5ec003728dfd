﻿using Common.Models.Messaging.ApexProvider;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using Geidea.Utils.WebUtilities;
using System.Threading.Tasks;
using Common.Services;
using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace Services.Messaging.Consumers
{
    [ExcludeFromCodeCoverage]
    public class ApexResponseService
    {
        private readonly IApexResponseReceiver _apexResponseReceiver;
        private readonly ILogger<ApexResponseService> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public ApexResponseService(IApexResponseReceiver apexResponseReceiver, ILogger<ApexResponseService> logger, IServiceScopeFactory serviceScope)
        {
            _apexResponseReceiver = apexResponseReceiver;
            _logger = logger;
            _serviceScopeFactory = serviceScope;
        }

        public void BeginReceive()
        {
            _apexResponseReceiver.OnApexResponseReceived += MessageReceiver_OnApexResponseReceived;
            _apexResponseReceiver.Connect();
        }

        private async void MessageReceiver_OnApexResponseReceived(object? sender, ApexResponseEventArgs apexResponseEvent)
        {
            try
            {
                using var scope = _serviceScopeFactory.CreateScope();

                SetProviderValues(scope, counterparty: apexResponseEvent.Counterparty, apexResponseEvent.CorrelationId);

                var apexResponse = apexResponseEvent.apexResponse;

                var checkoutService = scope.ServiceProvider.GetRequiredService<ICheckoutService>();

                _logger.LogInformation("Processing Apex Response Message...");

                await checkoutService.UpdateOrderAfterReceivingApexResponse(apexResponse!);

                _logger.LogInformation("Apex Response Message processed successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing Apex Response Message: {Message}", ex.Message);
            }
        }

        private static void SetProviderValues(IServiceScope scope, string? counterparty, Guid? correlationId)
        {
            var counterpartyProvider = scope.ServiceProvider.GetService<ICounterpartyProvider>();
            counterpartyProvider!.SetCode(counterparty ?? string.Empty);

            var correlationIdProvider = scope.ServiceProvider.GetService<ICorrelationIdProvider>();
            correlationIdProvider!.SetCorrelationId(correlationId ?? Guid.Empty);
        }
    }
}
