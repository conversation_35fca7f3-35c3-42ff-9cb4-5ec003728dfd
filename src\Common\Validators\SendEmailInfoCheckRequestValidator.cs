﻿using Common.Models;
using FluentValidation;

#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
namespace Common.Validators
{
    public class SendEmailInfoCheckRequestValidator : AbstractValidator<SendEmailInfoCheckRequest>
    {
        public SendEmailInfoCheckRequestValidator()
        {
            RuleFor(a => a.CheckType)
                .NotEmpty()
                .Must(x => x != null && x.Length <= 32)
                .WithErrorCode(Errors.CheckTypeLengthValidation.Code)
                .WithMessage(Errors.CheckTypeLengthValidation.Message);

            RuleFor(a => a.Subject)
                .NotEmpty()
                .Must(x => x != null && x.Length <= 128)
                .WithErrorCode(Errors.SubjectLengthValidation.Code)
                .WithMessage(Errors.SubjectLengthValidation.Message);

            RuleFor(x => x.EmailAddress).SetValidator(new EmailValidator());

            When(x => x.Comments != null, () =>
            {
                RuleFor(x => x.Comments).SetValidator(new CommentValidator());
            });
        }
    }
}
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
