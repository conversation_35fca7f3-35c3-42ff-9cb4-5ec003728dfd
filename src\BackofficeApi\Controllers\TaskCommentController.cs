﻿using Common.Models.TaskComments;
using Common.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    public class TaskCommentController : ControllerBase
    {
        private readonly ITaskCommentService taskCommentService;

        public TaskCommentController(ITaskCommentService taskCommentService)
        {
            this.taskCommentService = taskCommentService;
        }

        /// <summary>
        /// Create new comment for specific task.
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("{taskId:guid}/comment")]
        [ProducesResponseType(typeof(TaskCommentResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CreateTaskComment(Guid taskId, [FromBody] TaskCommentCreateRequest request)
        {
            var newComment = await taskCommentService.CreateCommentAsync(taskId, request);
            return CreatedAtAction(nameof(GetCommentById),
                new { commentId = newComment.Id },
                newComment);
        }


        /// <summary>
        /// Get comments list by taskId.
        /// </summary>
        /// <param name="taskId"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpGet("{taskId:guid}/comment")]
        [ProducesResponseType(typeof(TaskCommentResponse[]), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCommentByTaskId(Guid taskId)
        {
            var comments = await taskCommentService.GetCommentByTaskIdAsync(taskId);
            return Ok(comments);
        }

        /// <summary>
        /// Get comment by Id.
        /// </summary>
        /// <param name="commentId"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpGet("comment/{commentId:guid}")]
        [ProducesResponseType(typeof(TaskCommentResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetCommentById(Guid commentId)
        {
            var comment = await taskCommentService.GetCommentByIdAsync(commentId);
            return Ok(comment);
        }

    }
}
