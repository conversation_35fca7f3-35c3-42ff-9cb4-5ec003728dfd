﻿using System;

namespace Common.Models
{
    public class ContactDetails
    {
        public Guid MerchantContactId { get; set; }
        public Guid MerchantPersonOfInterestId { get; set; }
        public string? Email { get; set; }
        public string? CountryPrefix { get; set; }
        public string? PhoneNumber { get; set; }
        public string? AlternativeCountryPrefix { get; set; }
        public string? AlternativePhoneNumber { get; set; }
        public string? Website { get; set; }
        public string MerchantContactReason { get; set; } = string.Empty;
    }
}
