﻿using Common;
using Common.Models;
using Common.Models.Gsdk;
using Common.Models.Merchant;
using Common.Models.Product;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Services
{
    public class GsdkService : IGsdkService
    {

        private readonly ILogger<GsdkService> _logger;
        private readonly IGsdkClient _client;
        private readonly IGsdkHelperService _helperService;
        private readonly IReferenceService _referenceService;
        private readonly IMerchantClient _merchantClient;

        public GsdkService(ILogger<GsdkService> logger,
            IGsdkClient client,
            IGsdkHelperService helperService,
            IReferenceService referenceService,
            IMerchantClient merchantClient)
        {
            _logger = logger;
            _client = client;
            _helperService = helperService;
            _referenceService = referenceService;
            _merchantClient = merchantClient;
        }

        public async Task<bool> ValidateTmscContractsForProducts(List<ProductInstance> productInstances, Merchant merchant)
        {
            var ledgerMappingCatalogues = await _referenceService.GetCataloguesAsync(new[] { Constants.Catalogues.AcquiringLedgerToGsdkMapping,
                Constants.Catalogues.ProductTypeDefaultLedgerMapping });

            string? gsdkLedger = _helperService.GetLedgerValue(ledgerMappingCatalogues, productInstances.First());
            var globalContracts = await _client.FindGlobalContracts(new GlobalContractsFilter { }, gsdkLedger);

            foreach (var productInstance in productInstances)
            {
                if (!await CheckExistanceOfContractOrAbilityToAssign(productInstance, ledgerMappingCatalogues, globalContracts, merchant))
                    return false;
            }

            return true;
        }

        private async Task<bool> CheckExistanceOfContractOrAbilityToAssign(ProductInstance productInstance, Catalogue[] ledgerMappingCatalogues, List<GlobalContractDto> globalContracts, Merchant merchant)
        {
            var paymentWay = GetPaymentWayOfProductInstance(productInstance, merchant.Counterparty);

            if (await CheckExistanceOfContractPerProductInstance(paymentWay, ledgerMappingCatalogues, productInstance, merchant))
                return true;

            var defaultContract = globalContracts.FirstOrDefault(gc => gc.IsDefault && gc.InProviderAccountCategory?.Name == paymentWay);

            return defaultContract != null && await IsValidDefaultContractForAcquirer(defaultContract, merchant);
        }

        private async Task<bool> CheckExistanceOfContractPerProductInstance(string paymentWay, Catalogue[] ledgerMappingCatalogues, ProductInstance productInstance, Merchant merchant)
        {
            var mmsLedger = _helperService.GetLedgerKey(productInstance, ledgerMappingCatalogues);

            if (merchant.Counterparty == Constants.CounterParty.Egypt)
                mmsLedger = Constants.MmsLedgers.EgyptDefault;

            var contracts = await _merchantClient.GetMerchantMmsContract(new SearchContractMappingFilter
            {
                MerchantId = merchant.MerchantDetails!.MerchantId,
                LedgerKey = mmsLedger,
                PaymentWay = paymentWay
            });

            return contracts != null && contracts.Count >= 1;
        }

        private string GetPaymentWayOfProductInstance(ProductInstance productInstance ,string merchantCountry)
        {
            switch (productInstance.Product.Type)
            {
                case Constants.ProductTypes.Terminal:
                    {
                        if (string.Equals(productInstance.Product.Code, Constants.ProductCode.SoftPos) && merchantCountry == Constants.CounterParty.Egypt)
                            return Constants.GsdkContractPaymentWay.SoftPos;
                        return Constants.GsdkContractPaymentWay.Terminal;
                    }
                case Constants.ProductTypes.MPOS:
                    {
                        return Constants.GsdkContractPaymentWay.Terminal;
                    }
                case Constants.ProductTypes.Gateway:
                    {
                        return Constants.GsdkContractPaymentWay.Gateway;
                    }
                default:
                    {
                        _logger.LogError("No contract categories mapped for productType {@productType}", productInstance.Product.Type);
                        throw new ServiceException(Errors.GsdkNoContractCategories);
                    }
            }
        }

        private async Task<bool> IsValidDefaultContractForAcquirer(GlobalContractDto defaultContract, Merchant merchant)
        {
            string? outProviderAccount = await GetOutProviderAccountAsync(merchant?.MerchantDetails?.AcquiringLedger);
            return defaultContract.ContractRules.Any(c => c.OutProviderAccount.Id == outProviderAccount);
        }

        private async Task<string?> GetOutProviderAccountAsync(string? acquiringLedger)
        {

            if (acquiringLedger != null)
            {
               var catalouges = await _referenceService.GetCataloguesAsync(new string[] { Constants.Catalogues.GsdkOutAccountProviderToAcquirer });

                return catalouges.FirstOrDefault(c => c.Key == acquiringLedger)?.Value;
            }

            return null;
        }
    }
}
