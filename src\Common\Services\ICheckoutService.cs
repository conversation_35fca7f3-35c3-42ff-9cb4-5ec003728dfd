﻿using Common.Models.Checkout;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Models.Comment;
using Common.Models.Merchant;

namespace Common.Services
{
    public interface ICheckoutService
    {
        Task UpdateOrderAsync(Guid orderId, JsonPatchDocument<OrderUpdateRequest> updateOrderRequest, Guid? userId = null, bool checkOrderStatus = true);

        Task DeleteOrderAsync(OrderDeleteRequest orderDeleteRequest);

        Task<OrderResponse[]> GetAllOrdersAsync(bool includeDeleted);

        Task<OrderItemResponse[]> GetOrderItemsByIdAsync(Guid[] orderItemIds);

        Task DeleteOrderItemAsync(Guid[] orderItemIds);

        Task<OrderResponse[]> SearchOrderAsync(OrderSearchCriteria orderSearchCriteria);

        Task UpdateOrderItemAsync(Guid orderItemId, JsonPatchDocument<OrderItemUpdateRequest> updateOrderItemRequest);

        Task<OrderCommentResponse> CreateCommentAsync(Guid orderId, CommentCreateRequest commentCreateRequest);

        Task<OrderCommentResponse[]> GetCommentByOrderIdAsync(Guid orderId);

        Task<OrderCommentResponse> GetCommentByIdAsync(Guid commentId);

        Task<OrderCommentResponse> UpdateCommentAsync(Guid commentId, CommentUpdateRequest commentUpdateRequest);

        Task DeleteCommentAsync(Guid commentId);

        Task<List<OrderStatusResponse>> GetOrdersStatusHistoryAsync(Guid orderId);

        Task<MerchantOrders> GetMerchantOrdersNotPassedProductRegisterd(Guid merchantId);

        Task<List<OrderItem>> GetOrderItemByOrderIdAsync(Guid orderId);

        Task<List<OrderBulkUploadError>> UpdateOrdersToProductRegisteredAsync(List<string> orders, Guid? userId);
    }
}
