﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.NexusBridgeLog;

public class SearchFilters
{

    public Guid ProviderId { get; set; }
    public Guid CorrelationId { get; set; }
    public Guid ParentCorrelationId { get; set; }
    public string RequestId { get; set; } = string.Empty;
    public string RequestMetaData { get; set; } = string.Empty;
    public string ResponseMetaData { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime EntryDate { get; set; }
    public string Entity { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string RequestType { get; set; } = string.Empty;
    public string EntityId { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public int Skip { get; init; }
    public int Take { get; init; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
