﻿using System;
using UtilsConstants = Geidea.Utils.Common.Constants;
using Geidea.Utils.ConditionalSerialization;

namespace Common.Models.Merchant
{
    public class EgyptMerchantExportResponse
    {

        public string? BusinessID { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? NationalId { get; set; }
        public string? City { get; set; }
        public string? Governorate { get; set; }
        public string? AddressLine { get; set; }
        public string? BusinessName { get; set; }
        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? LicenseNumber { get; set; }
        [IgnoreForRole(UtilsConstants.BackOfficeUserRoles.BankAuditor)]
        public string? Mcc { get; set; }
        public string? MerchantStatus { get; set; }
        public string? Products { get; set; }
        public string? PosMid { get; set; }
        public string? Website { get; set; }
        //public string? BankCheckStatus { get; set; }
        //public string? BankCheckStatusDate { get; set; }

        public string? DoingBusinessAsName { get; set; }
        public string? MID { get; set; }
        public string? MerchantEmail { get; set; }
        public string? CommercialLegalType { get; set; }
        public DateTime? TLExpiryDate { get; set; }
        public string? SalesPerson { get; set; }
        public string? ContactPerson { get; set; }
        public string? MerchantContactNo { get; set; }
    }
}
