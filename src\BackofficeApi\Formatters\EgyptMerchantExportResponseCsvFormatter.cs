﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using BackofficeApi.Formatters.ClassMappers;
using Common.Models.Merchant;
using CsvHelper;
using CsvHelper.Configuration;
using Geidea.Utils.ConditionalSerialization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;

namespace BackofficeApi.Formatters
{
    public class EgyptMerchantExportResponseCsvFormatter : TextOutputFormatter
    {
        private ILogger<EgyptMerchantExportResponseCsvFormatter>? logger;
        private IHttpContextAccessor? httpContextAccessor;
        private readonly Func<PropertyInfo?, bool> shouldBeExcluded;
        public EgyptMerchantExportResponseCsvFormatter()
        {
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/csv"));
            SupportedEncodings.Add(Encoding.UTF8);

            shouldBeExcluded = property =>
            {
                var ignoreAttribute = property?.GetCustomAttribute<IgnoreForRoleAttribute>();
                return ignoreAttribute != null && ignoreAttribute.IsExcluded(httpContextAccessor, logger!);
            };
        }

        protected override bool CanWriteType(Type? type)
        {
            if (typeof(EgyptMerchantExportResponse).IsAssignableFrom(type) ||
                typeof(IEnumerable<EgyptMerchantExportResponse>).IsAssignableFrom(type))
            {
                return true;
            }
            return false;
        }

        public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
        {
            var httpContext = context.HttpContext;
            var serviceProvider = httpContext.RequestServices;
            var counterParty = httpContext.Request.Headers["X-Counterpartycode"].ToString();
            logger = serviceProvider.GetRequiredService<ILogger<EgyptMerchantExportResponseCsvFormatter>>();
            httpContextAccessor = serviceProvider.GetRequiredService<IHttpContextAccessor>();

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                InjectionOptions = InjectionOptions.Escape
            };
            await using var streamWriter = context.WriterFactory(context.HttpContext.Response.Body, selectedEncoding);
            await using var csv = new CsvWriter(streamWriter, config);
            var mapperClass = new EgyptMerchantExportResponseMap(shouldBeExcluded, counterParty);

            csv.Context.RegisterClassMap(mapperClass);
            csv.WriteHeader(typeof(EgyptMerchantExportResponse));
            await csv.NextRecordAsync();

            if (context.Object is IEnumerable<EgyptMerchantExportResponse> merchants)
            {
                merchants.ToList().ForEach(async merchant =>
                {
                    WriteRow(merchant, csv);
                    await csv.NextRecordAsync();
                });
            }
            else
            {
                if (context.Object is EgyptMerchantExportResponse singleMerchant)
                {
                    WriteRow(singleMerchant, csv);
                    await csv.NextRecordAsync();
                }
            }

            await streamWriter.FlushAsync();
        }

        private void WriteRow(EgyptMerchantExportResponse merchant, IWriterRow csv)
        {
            var properties = merchant.GetType().GetProperties();
            foreach (var property in properties)
            {
                if (!shouldBeExcluded(property))
                    csv.WriteField(property.GetValue(merchant));
            }
        }
    }
}
