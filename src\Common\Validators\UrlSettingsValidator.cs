﻿using FluentValidation;
using Common.Options;

namespace Common.Validators
{
    public class UrlSettingsValidator : AbstractValidator<UrlSettings>
    {
        public UrlSettingsValidator()
        {
            RuleFor(a => a.MerchantServiceBaseUrl).NotEmpty();
            RuleFor(a => a.GeideaFederationServiceBaseUrl).NotEmpty();
            RuleFor(a => a.LeadServiceBaseUrl).NotEmpty();
            RuleFor(a => a.NotificationServiceBaseUrl).NotEmpty();
            RuleFor(a => a.DocumentServiceBaseUrl).NotEmpty();
            RuleFor(a => a.CheckoutServiceBaseUrl).NotEmpty();
            RuleFor(a => a.ReferenceServiceBaseUrl).NotEmpty();
            RuleFor(a => a.UserServiceBaseUrl).NotEmpty();
            RuleFor(a => a.ProductServiceBaseUrl).NotEmpty();
            RuleFor(a => a.SearchServiceBaseUrl).NotEmpty();
            RuleFor(a => a.NexusBridgeApiBaseUrl).NotEmpty();
        }
    }
}
