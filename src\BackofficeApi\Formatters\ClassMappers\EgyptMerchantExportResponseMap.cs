﻿using Common;
using Common.Models.Merchant;
using CsvHelper.Configuration;
using System;
using System.Reflection;

namespace BackofficeApi.Formatters.ClassMappers
{
    public sealed class EgyptMerchantExportResponseMap : ClassMap<EgyptMerchantExportResponse>
    {
        private readonly Func<string, PropertyInfo?> property = propertyName => typeof(EgyptMerchantExportResponse).GetProperty(propertyName);

        public EgyptMerchantExportResponseMap(Func<PropertyInfo?, bool> shouldIgnore, string counterParty)
        {
            if (counterParty != null && counterParty == Constants.CounterParty.Uae)
            {
                ChangeHeadersNamesByCountry("Emirates ID", "Emirates", "Trade License Number", shouldIgnore, true);
            }
            else if (counterParty != null && counterParty == Constants.CounterParty.Saudi)
            {
                ChangeHeadersNamesByCountry("National ID", "Area", "Trade License Number", shouldIgnore);
            }
            else
            {
                ChangeHeadersNamesByCountry("National ID", "Governorate", "License Number for Commercial Registration", shouldIgnore);
            }
        }

        private void ChangeHeadersNamesByCountry(string nid, string gov, string lcr, Func<PropertyInfo?, bool> shouldIgnore, bool isUAE = false)
        {
            Map(m => m.BusinessID)
               .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.BusinessID))))
               .Name("Business ID");
            Map(m => m.FirstName)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.FirstName))))
                .Name("First Name");
            Map(m => m.LastName)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.LastName))))
                .Name("Last Name");
            Map(m => m.NationalId)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.NationalId))))
                .Name(nid);
            Map(m => m.City)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.City))))
                .Name("City");
            Map(m => m.Governorate)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.Governorate))))
                .Name(gov);
            Map(m => m.AddressLine)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.AddressLine))))
                .Name("Address Line");
            Map(m => m.BusinessName)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.BusinessName))))
                .Name("Business Name");
            Map(m => m.LicenseNumber)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.LicenseNumber))))
                .Name(lcr);
            Map(m => m.Mcc)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.Mcc))))
                .Name("MCC Code");
            Map(m => m.MerchantStatus)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.MerchantStatus))))
                .Name("Merchant Status");
            Map(m => m.Products)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.Products))))
                .Name("Bundles");
            Map(m => m.PosMid)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.PosMid))))
                .Name("POS MID");
            Map(m => m.Website)
                .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.Website))))
                .Name("Website");

            if (isUAE)
            {
                Map(m => m.DoingBusinessAsName)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.DoingBusinessAsName))))
                    .Name("Doing Business As Name");
                Map(m => m.MID)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.MID))))
                    .Name("MID");
                Map(m => m.MerchantEmail)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.MerchantEmail))))
                    .Name("Merchant Email");
                Map(m => m.CommercialLegalType)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.CommercialLegalType))))
                    .Name("Commercial Legal Type");
                Map(m => m.TLExpiryDate)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.TLExpiryDate))))
                    .Name("TL Expiry Date");
                Map(m => m.SalesPerson)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.SalesPerson))))
                    .Name("Sales Exec");
                Map(m => m.ContactPerson)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.ContactPerson))))
                    .Name("Contact Person Full name");
                Map(m => m.MerchantContactNo)
                    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.MerchantContactNo))))
                    .Name("Merchant Contact No");
            }
            //Map(m => m.BankCheckStatus)
            //    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.BankCheckStatus))))
            //    .Name("Bank Review Status");
            //Map(m => m.BankCheckStatusDate)
            //    .Ignore(shouldIgnore(property(nameof(EgyptMerchantExportResponse.BankCheckStatusDate))))
            //    .Name("Bank Review Date");
        }
    }
}