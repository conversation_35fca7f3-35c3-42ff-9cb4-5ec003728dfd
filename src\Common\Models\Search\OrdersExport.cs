﻿using System;
using System.Collections.Generic;

namespace Common.Models.Search
{
    public class OrdersExport
    {
        public Guid OrderId { get; set; }

        public Guid MerchantId { get; set; }

        public Guid StoreId { get; set; }

        public Guid UserId { get; set; }

        public string? OrderStatus { get; set; }

        public bool? PaidUpfront { get; set; }

        public string isPaidUpfront { get { return (PaidUpfront == true ? "YES" : string.Empty); } }

        public string? OrderNumber { get; set; }

        public string? MerchantStatus { get; set; }
        public string? AcquiringLedger { get; set; }

        public string? RegistrationNumber { get; set; }

        public string? BusinessType { get; set; }

        public string? UnifiedId { get; set; }

        public long? CrNumber { get; set; }

        public string? MunicipalLicenseNumber { get; set; }

        public string? LegalId { get; set; }

        public string? MerchantName { get; set; }

        public decimal? Total { get; set; }

        public string? ProjectName { get; set; }

        public DateTime? CheckoutDate { get; set; }

        public DateTime? LeadCreationDate { get; set; }

        public List<OrderItemResponse>? OrderItem { get; set; }

        public List<BankAccount>? BankAccounts { get; set; }

        public string? MemberId { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public string? MCC { get; set; }

        public string? CityCr { get; set; }

        public string? AddressCr { get; set; }

        public string? AddressLine1 { get; set; }

        public string? NationalId { get; set; }

        public string? FirstNameAr { get; set; }

        public string? LastNameAr { get; set; }

        public string? City { get; set; }

        public string? Area { get; set; }

        public string? Governorate { get; set; }

        public string? Email { get; set; }

        public string? PhoneNumber { get; set; }

        public string? CounterParty { get; set; }

        public string? ReferralChannel { get; set; }

        public string? LegalName { get; set; }

        public string? BusinessDomain { get; set; }

        public string? MerchantType { get; set; }

        public string? SalesId { get; set; }

        public string? SalesPersonFirstName { get; set; }

        public string? SalesPersonLastName { get; set; }

        public string? DeliveryMethod { get; set; }

        public short? DeliveryDays { get; set; }

        public bool? ProofOfDelivery { get; set; }

        public string? BillPayments { get; set; }

        public string? StoreCity { get; set; }
        public bool? MigrationRequest { get; set; }
        public string? SaudiOrderLedger { get; set; }
        public string? AcceptedPaymentMethods { get; set; }
    }
}
