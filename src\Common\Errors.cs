﻿namespace Common
{
    public static class Errors
    {
        private const string App = "BA_";
        public static (string Message, string Code) OrderNotFound => ("Cannot find order with id {orderId}", App + "OrderNotFound");
        public static (string Message, string Code) OrderDoesNotHaveStatusRegistered => ("Order {orderId} does not have status 'PRODUCTS_REGISTERED'", App + "OrderDoesNotHaveStatusRegistered");
        public static (string Message, string Code) NoOrderItems => ("EPos error: There are no order items for order {orderId}", App + "NoOrderItems");
        public static (string Message, string Code) OrderAlreadySentToEPos => ("The order {orderId} was already send to ePos", App + "OrderAlreadySentToEPos");
        public static (string Message, string Code) OrderNotSentToEPos => ("The order contains terminals without an ePos ticket number.", App + "OrderNotSentToEPos");
        public static (string Message, string Code) OrderUpToDateInEPos => ("All the terminals in the order are already up to date in ePos.", App + "OrderUpToDateInEPos");
        public static (string Message, string Code) TerminalIsNotConfigured => ("EPos error: The terminal is not configured. Product instance {productInstanceId}", App + "TerminalIsNotConfigured");
        public static (string Message, string Code) TerminalMissingTId => ("EPos error: One or more terminals in the order are missing the TId.", App + "TerminalMissingTId");
        public static (string Message, string Code) AccountAlreadyExists => ("There is already an account with sepcified phone number.", App + "AccountAlreadyExists");
        public static (string Message, string Code) AccountEmailAlreadyExists => ("There is already an account with sepcified email.", App + "AccountEmailAlreadyExists");
        public static (string Message, string Code) GenericError => ("An unexpected error has occured.", App + "GenericError");
        public static (string Message, string Code) UserIdNotFound => ("Cannot get user id from token.", App + "UserIdNotFound");
        public static (string Message, string Code) NationalIdLength => ("The national id should have 14 digits.", App + "NationalIdLength");
        public static (string Message, string Code) NationalIdNumber => ("The national id should contain only numbers.", App + "NationalIdNumber");
        public static (string Message, string Code) Lead_InvalidEgyptNationalId => ("Invalid National Id for Egypt.", App + "Invalid_Egypt_NationalId");
        public static (string Message, string Code) LeadStatusLengthValidation => ("Lead status length validation failed", App + "LeadStatusLengthValidation");
        public static (string Message, string Code) BusinessDomainLengthValidation => ("Business domain length validation failed", App + "BusinessDomainLengthValidation");
        public static (string Message, string Code) CountryPrefixLengthValidation => ("Country prefix length validation failed", App + "CountryPrefixLengthValidation");
        public static (string Message, string Code) FirstNameLengthValidation => ("Owner first name length validation failed", App + "FirstNameLengthValidation");
        public static (string Message, string Code) LastNameLengthValidation => ("Owner last name length validation failed", App + "LastNameLengthValidation");
        public static (string Message, string Code) OwnerFirstNameArLengthValidation => ("Owner first name Ar length validation failed", App + "FirstNameArLengthValidation");
        public static (string Message, string Code) OwnerLastNameArLengthValidation => ("Owner last name Ar length validation failed", App + "OwnerLastNameArLengthValidation");
        public static (string Message, string Code) LegalNameLengthValidation => ("Legal name length validation failed", App + "LegalNameLengthValidation");
        public static (string Message, string Code) LegalNameArLengthValidation => ("Legal name Ar length validation failed", App + "LegalNameArLengthValidation");
        public static (string Message, string Code) NationalIdLengthValidation => ("The national id length validation failed", App + "NationalIdLengthValidation");
        public static (string Message, string Code) AddressLineLengthValidation => ("Address line length validation failed", App + "AddressLineLengthValidation");
        public static (string Message, string Code) CityLengthValidation => ("The city length validation failed", App + "CityLengthValidation");
        public static (string Message, string Code) CountryLengthValidation => ("The country length validation failed", App + "CountryLengthValidation");
        public static (string Message, string Code) UtmLengthValidation => ("The UTM length validation failed", App + "UtmLengthValidation");
        public static (string Message, string Code) SalesIdLengthValidation => ("The sales id length validation failed", App + "SalesIdLengthValidation");
        public static (string Message, string Code) SalesPartnerIdLengthValidation => ("The sales partner id length validation failed", App + "SalesPartnerIdLengthValidation");
        public static (string Message, string Code) NationalityLengthValidation => ("The nationality length validation failed", App + "NationalityLengthValidation");
        public static (string Message, string Code) GenderLengthValidation => ("The gender length validation failed", App + "GenderLengthValidation");
        public static (string Message, string Code) ReferralChannelLengthValidation => ("Referral channel length validation failed", App + "ReferralChannelLengthValidation");
        public static (string Message, string Code) InvalidPatch => ("Invalid patch request", App + "InvalidPatch");
        public static (string Message, string Code) ProductCodeLengthValidation => ("Product code length validation failed", App + "ProductCodeLengthValidation");
        public static (string Message, string Code) BusinessTypeLengthValidation => ("Business type length validation failed", App + "BusinessTypeLengthValidation");
        public static (string Message, string Code) RegistrationNumberLengthValidation => ("The registration number length validation failed", App + "RegistrationNumberLengthValidation");
        public static (string Message, string Code) RegistrationNumberFormatValidation => ("The registration number format validation failed", App + "RegistrationNumberFormatValidation");
        public static (string Message, string Code) AccountHolderNameLengthValidation => ("Account holder name length validation failed", App + "AccountHolderNameLengthValidation");
        public static (string Message, string Code) IbanLengthValidation => ("The IBAN length validation failed", App + "IbanLengthValidation");
        public static (string Message, string Code) Iban_Max24 => ("The IBAN must have the format SA + 22 digits, no spaces or other characters.", App + "Iban_Max24");
        public static (string Message, string Code) MunicipalLicenseNumberLengthValidation => ("Municipal license number length validation failed", App + "MunicipalLicenseNumberLengthValidation");
        public static (string Message, string Code) InvalidLegalId => ("The Legal Id should start with '7' and have a maximum length of 10.", App + "InvalidLegalId");
        public static (string Message, string Code) NewStatusLengthValidation => ("The NewStatus length validation failed", App + "NewStatusLengthValidation");
        public static (string Message, string Code) NewMccLengthValidation => ("The NewMcc length validation failed", App + "NewMccLengthValidation");
        public static (string Message, string Code) AdditionalMccLengthValidation => ("The additional Mcc length validation failed", App + "AdditionalMccLengthValidation");
        public static (string Message, string Code) NewTagLengthValidation => ("The NewTag length validation failed", App + "NewTagLengthValidation");
        public static (string Message, string Code) AcquirerReviewLengthValidation => ("Acquirer review length validation failed", App + "AcquirerReviewLengthValidation");
        public static (string Message, string Code) CommentLengthValidation => ("Comment length validation failed", App + "CommentLengthValidation");
        public static (string Message, string Code) CheckTypeLengthValidation => ("Check type length validation failed", App + "CheckTypeLengthValidation");
        public static (string Message, string Code) SubjectLengthValidation => ("Subject length validation failed", App + "SubjectLengthValidation");
        public static (string Message, string Code) PhoneLengthValidation => ("Phone length validation failed", App + "PhoneLengthValidation");
        public static (string Message, string Code) PhoneValidation => ("Invalid phone number", App + "PHONEVALIDATION");
        public static (string Message, string Code) BodyLengthValidation => ("Body length validation failed", App + "BodyLengthValidation");
        public static (string Message, string Code) PaymentReferenceLengthValidation => ("Payment reference length validation failed", App + "PaymentReferenceLengthValidation");
        public static (string Message, string Code) TrackingNumberLengthValidation => ("Tracking number length validation failed", App + "TrackingNumberLengthValidation");
        public static (string Message, string Code) TrackingUrlLengthValidation => ("Tracking Url length validation failed", App + "TrackingUrlLengthValidation");
        public static (string Message, string Code) ShipperLengthValidation => ("Shipper length validation failed", App + "ShipperLengthValidation");
        public static (string Message, string Code) CouponCodeLengthValidation => ("Coupon code length validation failed", App + "CouponCodeLengthValidation");
        public static (string Message, string Code) PaymentMethodLengthValidation => ("Payment method length validation failed", App + "PaymentMethodLengthValidation");
        public static (string Message, string Code) CompanyRegNoLengthValidation => ("CompanyRegNo length validation failed", App + "CompanyRegNoLengthValidation");
        public static (string Message, string Code) SalesNameLengthValidation => ("Sales name length validation failed", App + "SalesNameLengthValidation");
        public static (string Message, string Code) SubscriptionPlanLengthValidation => ("Subscription plan length validation failed", App + "SubscriptionPlanLengthValidation");
        public static (string Message, string Code) ProjectNameLengthValidation => ("Project name length validation failed", App + "ProjectNameLengthValidation");
        public static (string Message, string Code) NoteLengthValidation => ("Note length validation failed", App + "NoteLengthValidation");
        public static (string Message, string Code) CurrencyLengthValidation => ("Currency length validation failed", App + "CurrencyLengthValidation");
        public static (string Message, string Code) OrderStatusLengthValidation => ("Order status length validation failed", App + "OrderStatusLengthValidation");
        public static (string Message, string Code) MerchantNameLengthValidation => ("Merchant name length validation failed", App + "MerchantNameLengthValidation");
        public static (string Message, string Code) ProductTypeLengthValidation => ("Product type length validation failed", App + "ProductTypeLengthValidation");
        public static (string Message, string Code) EposTicketIdLengthValidation => ("EposTicketId length validation failed", App + "EposTicketIdLengthValidation");
        public static (string Message, string Code) UpdatedSalesIdLengthValidation => ("UpdatedSalesId length validation failed", App + "UpdatedSalesIdLengthValidation");
        public static (string Message, string Code) EmailLengthValidation => ("Email length validation failed", App + "EmailLengthValidation");
        public static (string Message, string Code) GovernorateLengthValidation => ("Governorate length validation failed", App + "GovernorateLengthValidation");
        public static (string Message, string Code) CrmLeadIdLengthValidation => ("CRMLeadId length validation failed", App + "CrmLeadIdLengthValidation");
        public static (string Message, string Code) AreaLengthValidation => ("Area length validation failed", App + "AreaLengthValidation");
        public static (string Message, string Code) TahakomTransactionIdLengthValidation => ("TahakomTransactionId length validation failed", App + "TahakomTransactionIdLengthValidation");
        public static (string Message, string Code) BusinessTypeValidation => ("BusinessType validation failed", App + "BusinessTypeValidation");
        public static (string Message, string Code) InvalidCountryPrefix => ("Invalid country prefix.", App + "InvalidCountryPrefix");
        public static (string Message, string Code) BankAccountNumber_Max34 => ("The Bank Account Number must have a maximum of 34 characters.", App + "BankAccountNumber_Max34");
        public static (string Message, string Code) BankAccountNumber_InvalidCounterparty => ("The Bank Account Number is not allowed for the current counterparty.", App + "BankAccountNumber_Counterparty");
        public static (string Message, string Code) RefBankId_InvalidCounterparty => ("The Bank Name Id is not allowed for the current counterparty.", App + "RefBankId_Counterparty");
        public static (string Message, string Code) BankAccountNumber_Invalid => ("The Bank Account Number is not valid.", App + "BankAccountNumber_Invalid");
        public static (string Message, string Code) InvalidBundleForReferralChannelBadRequest => ("Invalid Bundle for Referral Channel", App + "InvalidBundleForReferralChannelBadRequest");
        public static (string Message, string Code) InvalidProjectNameForBundleBadRequest => ("Invalid ProjectName for Bundle", App + "InvalidProjectNameForBundleBadRequest");
        public static (string Message, string Code) ProductNotFound => ("Product not found for the current counterparty.", App + "ProductNotFound");
        public static (string Message, string Code) ReferralChannelUpdateBadRequest => ("Referral Channel cannot be changed for this Lead.", App + "ReferralChannelUpdateBadRequest");
        public static (string Message, string Code) LicenseIdExistsError => ("License id already exists. Please Contact Geidea for support.", App + "LicenseNumberExistsError");
        public static (string Message, string Code) InvalidMerchantStatus => ("Invalid merchant status for GSDK federation", App + "InvalidMerchantStatus");
        public static (string Message, string Code) InvalidMerchantTagForSearch => ("The provided value for the merchant tag is not valid for the subordinate merchant search.", App + "INVALIDMERCHANTTAGFORSEARCH");
        public static (string Message, string Code) UpdateMerchantTagErrorVerified => ("Operation cannot be completed, merchant is in status Verified.", App + "UPDATEMERCHANTTAG_ERROR_VERIFIED");
        public static (string Message, string Code) UpdateMerchantTagErrorActiveAssociation => ("Operation cannot be completed while this business is linked to another business.", App + "UPDATEMERCHANTTAG_ERROR_ACTIVEASSOCIATION");
        public static (string Message, string Code) UpdateMerchantTagErrorBillPaymentsSent => ("Operation cannot be completed, business is registered in GLE.", App + "UPDATEMERCHANTTAG_ERROR_BILLPAYMENTSSENT");
        public static (string Message, string Code) OperationSuccessful => ("Operation completed successfully.", App + "OPERATIONSUCCESSFUL");
        public static (string Message, string Code) UpdateMerchantTagOperationSuccessfulWithWarning => ("Operation completed successfully. Bill Payments registration will not be made while the business doesn't have a Master business / Wholesaler assigned.", App + "UPDATEMERCHANTTAG_OPERATIONSUCCESSFUL_WITHWARNING");
        public static (string Message, string Code) ProductsNotConfigured => ("Products not configured", App + "ProductsNotConfigured");
        public static (string Message, string Code) ProductInstanceNotFound => ("Product instances not found", App + "ProductInstanceNotFound");
        public static (string Message, string Code) BillPaymentNoProductRegisteredStatus => ("This order has only Bill Payment enablement, 'Products Registered' status is not necessary.", App + "BillPaymentNoProductRegisteredStatus");
        public static (string Message, string Code) DocumentsNotFound => ("Documents not found.", App + "DocumentsNotFound");
        public static (string Message, string Code) MerchantCACStatusTaskError => ("Found not finished tasks related to this merchant.", App + "MerchantCACStatusTaskError");
        public static (string Message, string Code) RiskOfficerApprovalRequired => ("Risk officer needs to approve.", App + "RiskOfficerApprovalRequired");
        public static (string Message, string Code) MerchantCheckMustPass => ("Review the result of each business check.", App + "MerchantCheckMustPass");
        public static (string Message, string Code) RiskScoringNotHigh => ("To proceed, risk scoring must be rated as 'High'. Update business status to: Verified, Returned, or Rejected.", App + "RiskScoringNotHigh");
        public static (string Message, string Code) MerchantBankCheckMustPass => ("Merchant must pass Bank Check to be Verified.", App + "MerchantBankCheckMustPass");
        public static (string Message, string Code) RequiredMerchantCheck => ("Verified Merchant must have four checks : WorldCheck One, Match, Risk Scoring and Bank.", App + "RequiredMerchantCheck");
        public static (string Message, string Code) ViolationOfBankAndRiskCheckRule => ("To proceed, risk scoring must be rated as 'Medium' or 'Low' and Bank Check must be 'Passed'. Update business status to: Risk Approval, Returned, or Rejected", App + "ViolationOfBankAndRiskCheckRule");
        public static (string Message, string Code) ShareholderIndividualKYCCheckMustPass => ("All Shareholder Individual must pass KYC Check before moving from Merchant Underwriting status.", App + "ShareholderIndividualKYCCheckMustPass");
        public static (string Message, string Code) ProductNotAllowed => ("Product(s) not allowed", App + "ProductNotAllowed");
        public static (string Message, string Code) OrderIsNotSubmittedOrVerified => ("Order is not submitted or verified", App + "OrderIsNotSubmittedOrVerified");
        public static (string Message, string Code) OrderOrProductsNotFound => ("Order or products not found", App + "OrderOrProductsNotFound");
        public static (string Message, string Code) InvalidGetShareholderCompaniesRequest => ("Invalid Get Shareholder Companies request", App + "InvalidGetShareholderCompaniesRequest");
        public static (string Message, string Code) AlreadyExistingShareholderCompany => ("Cannot create shareholder company. It already exists.", App + "AlreadyExistingShareholderCompany");
        public static (string Message, string Code) InvalidShareholderCompanyMerchantId => ("Invalid Shareholder MerchantId", App + "InvalidShareholderMerchantId");
        public static (string Message, string Code) InvalidShareholderCompanyType => ("Invalid Shareholder Company Type", App + "InvalidShareholderCompanyType");
        public static (string Message, string Code) InvalidShareholderCompanyLicenseExpiryDate => ("Invalid Shareholder License Expiry Date", App + "InvalidShareholderLicenseExpiryDate");
        public static (string Message, string Code) InvalidShareholderCompanyLicenseLength => ("Invalid Shareholder Company License length", App + "InvalidShareholderCompanyLicenseLength");
        public static (string Message, string Code) InvalidShareholderIssueDate => ("Invalid Shareholder Company Issue Date", App + "InvalidShareholderIssueDate");
        public static (string Message, string Code) InvalidShareholderCompanyTypeLength => ("Invalid Shareholder Company Type length", App + "InvalidShareholderCompanyTypeLength");
        public static (string Message, string Code) ShareholderCompanyNameLength => ("Shareholder Company Name length validation failed", App + "ShareholderCompanyNameLength");
        public static (string Message, string Code) InvalidShareholderCompanyOwnershipPercentage => ("Invalid Shareholder Company Ownership percentage", App + "ShareholderCompanyOwnershipPercentage");
        public static (string Message, string Code) ShareholderCompanyMccLengthValidation => ("ShareholderCompany length validation failed", App + "ShareholderCompanyMccLengthValidation");
        public static (string Message, string Code) DisabledProperty => ("Property is disabled and cannot contain data. Validation failed.", App + "DisabledProperty");
        public static (string Message, string Code) InvalidCreateShareholderCompanyMerchantAssociationRequest => ("Invalid Create Shareholder Company Merchant Association Request", App + "InvalidCreateShareholderCompanyMerchantAssociationRequest");
        public static (string Message, string Code) InvalidParentBusinessTag => ("Invalid merchant tag for the parent business.", App + "INVALIDPARENTBUSINESSTAG");
        public static (string Message, string Code) InvalidSubordinateBusinessTag => ("Invalid merchant tag for the subordinate business.", App + "INVALIDSUBORDINATEBUSINESSTAG");
        public static (string Message, string Code) SubordinateBusinessAlreadyLinkedToParent => ("Cannot create business hierarchy! One or more subordinate businesses are already linked to the parent.", App + "SUBORDINATEBUSINESSALREADYLINKEDTOPARENT");
        public static (string Message, string Code) ShareholderIndividualFirstNameValidation => ("Invalid first name for individual shareholder", App + "SHAREHOLDERINDIVIDUALFIRSTNAMEVALIDATION");
        public static (string Message, string Code) ShareholderIndividualLastNameValidation => ("Invalid last name for individual shareholder", App + "SHAREHOLDERINDIVIDUALLASTNAMEVALIDATION");
        public static (string Message, string Code) ShareholderIndividualDobValidation => ("Invalid date of birth for individual shareholder", App + "SHAREHOLDERINDIVIDUALDOBVALIDATION");
        public static (string Message, string Code) ShareholderIndividualIdExpiryDateValidation => ("Invalid date of expiration for individual shareholder", App + "SHAREHOLDERINDIVIDUALIDEXPIRYDATEVALIDATION");
        public static (string Message, string Code) ShareholderIndividualPassportExpiryDateValidation => ("Invalid date of expiration for individual shareholder passport", App + "ShareholderIndividualPassportExpiryDateValidation");
        public static (string Message, string Code) ShareholderIndividualPassportValidation => ("Invalid passport number for individual shareholder", App + "ShareholderIndividualPassportValidation");
        public static (string Message, string Code) NationalityValidation => ("Invalid nationality.", App + "NATIONALITYVALIDATION");
        public static (string Message, string Code) CountryValidation => ("Invalid country.", App + "COUNTRYVALIDATION");
        public static (string Message, string Code) NationalIdIsNotUnique => ("NationalId is not unique", App + "NationalIdIsNotUnique");
        public static (string Message, string Code) InvalidGetShareholderIndividualsRequest => ("Invalid Get Shareholder Individuals request", App + "InvalidGetShareholderIndividualsRequest");
        public static (string Message, string Code) InvalidCreateIndividualShareholderAssociationsRequest => ("Invalid request", App + "InvalidCreateIndividualShareholderAssociationsRequest");
        public static (string Message, string Code) InvalidShareHolderCompanyId => ("Invalid shareholder company id", App + "InvalidShareHolderCompanyId");
        public static (string Message, string Code) ShareholderIndividualPassportOrNationalIdMandatory => ("Passport number or national id is mandatory", App + "ShareholderIndividualPassportOrNationalIdMandatory");
        public static (string Message, string Code) InvalidIndividualShareholderId => ("Invalid ShareholderId", App + "InvalidIndividualShareholderId");
        public static (string Message, string Code) KeycloakTokenError => ("Cannot get keycloak token", App + "KeycloakTokenError");
        public static (string Message, string Code) GsdkError => ("Error when calling gsdk", App + "GsdkError");
        public static (string Message, string Code) GsdkNoContractCategories => ("No contract categories returned from gsdk.", App + "GsdkNoContractCategories");
        public static (string Message, string Code) LedgerMappingNotFound => ("GSDK Ledger mapping missing", App + "LedgerMappingNotFound");
        public static (string Message, string Code) DefaultContractNotMatchMerchantAcquirer => ("Please assign TMSC Contracts to the Business before updating Orders to Product Registered.", App + "DefaultContractNotMatchMerchantAcquirer");
        public static (string Message, string Code) MerchantMCCRequired => ("Please add an MCC before updating the Merchant Status to Verified.", App + "MerchantMCCRequired");
        public static (string Message, string Code) CityNotFromGovernorate => ("City is not matched with the provided governorate", App + "CityNotFromGovernorate");
        public static (string Message, string Code) InvalidCity => ("City is not valid", App + "InvalidCity");
        public static (string Message, string Code) InvalidGovernorate => ("Governorate is not valid", App + "InvalidGovernorate");
        public static (string Message, string Code) InvalidBusinessDomain => ("BusinessDomain is not valid", App + "InvalidBusinessDomain");

        #region ShareholderCompanyIndividualLink Validation

        public static (string Message, string Code) InvalidShareholderCompanyIndividualLinkEntry => ("A company link is invalid", App + "InvalidShareholderCompanyIndividualLinkEntry");
        public static (string Message, string Code) InvalidShareholderCompanyIndividualLinkRole => ("Role provided for company is invalid", App + "InvalidShareholderCompanyIndividualLinkRole");
        public static (string Message, string Code) InvalidShareholderCompanyIndividualLinkCompanyId => ("Invalid shareholder company Id", App + "InvalidShareholderCompanyIndividualLinkCompanyId");

        #endregion

        #region MerchantIndividualLink Validation
        public static (string Message, string Code) InvalidMerchantIndividualLink => ("Invalid merchant - shareholder relation", App + "InvalidMerchantIndividualLink");
        public static (string Message, string Code) InvalidMerchantIndividualLinkMerchant => ("Invalid merchantId", App + "InvalidMerchantIndividualLinkMerchant");
        public static (string Message, string Code) InvalidMerchantIndividualLinkOwnershipPercentage => ("Invalid Shareholder Ownership percentage", App + "InvalidMerchantIndividualLinkOwnershipPercentage");

        #endregion

        public static (string Message, string Code) EnglishBusinessName => ("Invalid Information, please ensure that Business Name in English has a valid English value.", App + "EnglishBusinessName");
        public static (string Message, string Code) ArabicBusinessName => ("Invalid Information, please ensure that Business Name in Arabic has a valid Arabic value.", App + "ArabicBusinessName");
        public static (string Message, string Code) MerchantStatusNotVerified => ("Please check merchant status", App + "MerchantStatusNotVerified");
    }
}