﻿using Common.Models.Checkout;
using Common.Models.NexusBridgeLog;
using Common.Models.Search;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Security.Policy;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services
{
    public class NexusBridgeLogService : INexusBridgeLogService
    {
        private readonly ILogger<NexusBridgeLogService> logger;
        private readonly HttpClient client;
        private readonly UrlSettings urlSettingsOptions;
        public NexusBridgeLogService(ILogger<NexusBridgeLogService> logger,
             HttpClient client, IOptionsMonitor<UrlSettings> urlSettingsOptions)
        {
            this.logger = logger;
            this.client = client;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
        }

        private string RequestLogServiceBaseUrl => $"{urlSettingsOptions.RequestLogServiceBaseUrl}";
        private string LogSearchEndpoint => "/api/v1/RequestLog/search";
        public async Task<List<RequestLog>> Search(SearchFilters searchFilters)
        {
            string url = $"{RequestLogServiceBaseUrl}{LogSearchEndpoint}";
            var requestBody = new StringContent(JsonConvert.SerializeObject(searchFilters), Encoding.UTF8, "application/json");


            using (logger.BeginScope("RequestLogSearch({@url})", url))
            {
                logger.LogInformation($"Calling request log service to pull logs'.");

                var response = await client.PostAsync(url, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling request log service. Error was {StatusCode} {@responseBody}",
                       (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var requestLogs = Json.Deserialize<List<RequestLog>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                logger.LogInformation($"Found '{requestLogs.Count}' log records.");

                return requestLogs;
            }
        }
    }
}
