﻿using Common.Models;
using Common.Models.Document;
using Common.Options;
using Common.Services;
using Common.Validators;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Geidea.Utils.Validation;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Services
{
    public class DocumentClient : IDocumentClient
    {
        private readonly HttpClient client;
        private readonly ILogger<DocumentClient> logger;
        private readonly UrlSettings urlSettingsOptions;

        public DocumentClient(ILogger<DocumentClient> logger, IOptionsMonitor<UrlSettings> urlSettingsOptions, HttpClient client)
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.client = client;
        }

        private string DocumentServiceBaseUrl => $"{urlSettingsOptions.DocumentServiceBaseUrlNS}/api/v1";

        public async Task<DocumentMetadata> CreateDocumentAsync(DocumentRequest document)
        {
            new ValidationHelpers().Validate(document, new DocumentRequestValidator(), logger, "DocumentRequest length validation failed");

            var requestUri = $"{DocumentServiceBaseUrl}/document";

            using (logger.BeginScope($"CreateDocumentAsync({requestUri})"))
            {
                byte[] data;
                var stream = document.File.OpenReadStream();

                using (var br = new BinaryReader(stream))
                {
                    data = br.ReadBytes((int)stream.Length);
                }

                var bytes = new ByteArrayContent(data);

                var multiContent = new MultipartFormDataContent
                {
                    { bytes, "file", document.File.FileName },

                    { new StringContent(document.OwnerUserId.ToString()), nameof(document.OwnerUserId) },
                    { new StringContent(document.MerchantId.ToString() ?? string.Empty), nameof(document.MerchantId) },
                    { new StringContent(document.PersonOfInterestId.ToString() ?? string.Empty), nameof(document.PersonOfInterestId) },
                    { new StringContent(document.LeadId.ToString() ?? string.Empty), nameof(document.LeadId) },
                    { new StringContent(document.DocumentType!), nameof(document.DocumentType) },
                    { new StringContent(document.Language!), nameof(document.Language) },
                    { new StringContent(document.ProviderId.ToString() ?? string.Empty), nameof(document.ProviderId.ToString) }
                };

                logger.LogInformation("Calling document service to create new document.");
                var response = await client.PostAsync(requestUri, multiContent);
                var jsonResult = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling document service to create new document. Error was {statusCode} {@responseBody}",
                        response.StatusCode, jsonResult);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<DocumentMetadata>(jsonResult);
            }
        }

        public async Task DeleteDocumentsAsync(List<Guid> documentIds)
        {
            var serviceUrl = $"{DocumentServiceBaseUrl}/document/deleteDocuments";
            var body = new StringContent(JsonConvert.SerializeObject(documentIds), Encoding.UTF8, "application/json");

            using (logger.BeginScope("DeleteDocuments({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling document service to delete documents by '{documentIds.Count}' IDs.");

                var response = await client.PostAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling document service to delete documents by '{documentIds}'. " +
                        "Error was {StatusCode}. Details: {ErrorDetails}", documentIds, (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
            }
        }
    }
}
