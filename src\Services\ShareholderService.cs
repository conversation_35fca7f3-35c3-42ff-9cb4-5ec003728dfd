﻿using AutoMapper;
using Common;
using Common.Models;
using Common.Models.Document;
using Common.Models.Merchant;
using Common.Models.Shareholder;
using Common.Options;
using Common.Services;
using Common.Services.Validators;
using Geidea.Utils.Cleanup;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static Common.Constants;

namespace Services;
public class ShareholderService : IShareholderService
{
    private readonly ILogger<ShareholderService> logger;
    private readonly HttpClient client;
    private readonly IOptionsMonitor<UrlSettings> urlSettingsOptions;
    private readonly IMapper mapper;
    private readonly ICounterpartyProvider counterpartyProvider;
    private readonly ICleanupService cleanupService;
    private readonly IShareholderValidationService validationService;
    private readonly IOptionsMonitor<ShareholderSearchConfiguration> shareholderSearchConfiguration;
    private readonly IMerchantClient merchantClient;
    private readonly IShareholderClient shareholderClient;
    private readonly IDocumentClient documentClient;
    private readonly int ShareholderCompanySearchDefaultTake = 20;
    private readonly int ShareholderIndividualSearchDefaultTake = 20;
    private static readonly string shareholderCompanyPathName = nameof(MerchantShareholderCompanyResponse.ShareholderCompany);

    private string MerchantServiceShareholderBaseUrl => $"{urlSettingsOptions.CurrentValue.MerchantServiceBaseUrlNS}/api/v1/shareholder";
    private string MerchantServiceBaseUrl => $"{urlSettingsOptions.CurrentValue.MerchantServiceBaseUrlNS}";
    private const string PersonCheckEndpoint = "/api/v1/merchant/poi/checks";
    public ShareholderService(
        ILogger<ShareholderService> logger,
        HttpClient client,
        IOptionsMonitor<UrlSettings> urlSettingsOptions,
        IOptionsMonitor<ShareholderSearchConfiguration> shareholderSearchConfiguration,
        ICounterpartyProvider counterpartyProvider,
        ICleanupService cleanupService,
        IShareholderValidationService validationService,
        IMapper mapper,
        IMerchantClient merchantClient,
        IShareholderClient shareholderClient,
        IDocumentClient documentClient)
    {
        this.logger = logger;
        this.client = client;
        this.urlSettingsOptions = urlSettingsOptions;
        this.mapper = mapper;
        this.counterpartyProvider = counterpartyProvider;
        this.cleanupService = cleanupService;
        this.shareholderSearchConfiguration = shareholderSearchConfiguration;
        this.validationService = validationService;
        this.merchantClient = merchantClient;
        this.shareholderClient = shareholderClient;
        this.documentClient = documentClient;
    }

    public async Task<List<MerchantShareholderCompanyResponse>> GetShareholderCompaniesAsync(ShareholderCompaniesRequest shareholderCompaniesRequest)
    {
        if (shareholderCompaniesRequest.MerchantId != null && shareholderCompaniesRequest.MerchantId != Guid.Empty)
        {
            var result = await merchantClient.GetShareholderCompaniesBase(shareholderCompaniesRequest);

            return Json.Deserialize<List<MerchantShareholderCompanyResponse>>(result, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        logger.LogCritical("Error when trying to get Shareholder Companies." +
                           " Error was {error} .", Errors.InvalidGetShareholderCompaniesRequest.Message);
        throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGetShareholderCompaniesRequest);
    }

    public async Task<List<ShareholderCompanyResponse>> SearchShareholderCompaniesAsync(ShareholderCompaniesRequest shareholderCompaniesRequest)
    {
        if (!string.IsNullOrWhiteSpace(shareholderCompaniesRequest.Keyword))
        {
            shareholderCompaniesRequest.Take = shareholderSearchConfiguration.CurrentValue.ShareholderCompanySearchDefaultTake ??
                                               ShareholderCompanySearchDefaultTake;

            var result = await merchantClient.GetShareholderCompaniesBase(shareholderCompaniesRequest, true);

            return Json.Deserialize<List<ShareholderCompanyResponse>>(result, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        logger.LogCritical("Error when trying to get Shareholder Companies." +
                           " Error was {error} .", Errors.InvalidGetShareholderCompaniesRequest.Message);
        throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGetShareholderCompaniesRequest);
    }

    public async Task<List<MerchantIndividualCore>> SearchShareholderIndividualsAsync(
        ShareholderIndividualsSearchRequest searchRequest)
    {
        if (string.IsNullOrWhiteSpace(searchRequest.Keyword))
        {
            logger.LogCritical("Error when trying to search shareholder individuals." +
                               " Error was {error} .", Errors.InvalidGetShareholderIndividualsRequest.Message);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGetShareholderIndividualsRequest);
        }

        searchRequest.Take = shareholderSearchConfiguration.CurrentValue.ShareholderIndividualSearchDefaultTake ??
                             ShareholderIndividualSearchDefaultTake;

        return await RetrieveShareholderIndividualsAsync(searchRequest);
    }

    public async Task<ShareholderCompanyResponse> CreateShareholderCompanyAsync(CreateShareholderCompanyWithDocumentRequest request, Guid userId)
    {
        await ValidateShareholderCompanyCreateRequestAsync(request);

        ShareholderCompanyResponse? shareholderCompany = new();

        try
        {
            cleanupService.Init(
                    forward: async () => shareholderCompany = await shareholderClient.CreateShareholderCompanyAsync(request),
                    back: async () =>
                    {
                        if (shareholderCompany?.Id != Guid.Empty)
                        {
                            await shareholderClient.DeleteShareholderCompanyAsync(shareholderCompany!.Id);
                            logger.LogInformation($"Rolled back ShareholderCompany creation for ID '{shareholderCompany.Id}'.");
                        }
                    },
                    description: $"Create Shareholder Company for Merchant with ID '{request.MerchantId}'.")
                .ContinueWith(
                    forward: async () => shareholderCompany.FreelanceIds = await AddShareholderDocumentsAsync(request.FreelanceIds, Constants.DocumentType.FreelanceId, userId, shareholderCompany, shareholderCompany => shareholderCompany.MerchantId),
                    back: async () =>
                    {
                        if (shareholderCompany?.FreelanceIds != null && shareholderCompany.FreelanceIds.Any())
                        {
                            await DeleteShareholderDocumentsAsync(shareholderCompany.FreelanceIds.ToList());
                            logger.LogInformation("Rolled back Passport documents.");
                        }
                    },
                    description: $"Add Contact or Shareholder Company FreelanceID Documents")
                .ContinueWith(
                    forward: async () => shareholderCompany.CommercialRegistrations = await AddShareholderDocumentsAsync(request.CommercialRegistrations, Constants.DocumentType.CommercialRegistration, userId, shareholderCompany, shareholderCompany => shareholderCompany.MerchantId),
                    back: async () =>
                    {
                        if (shareholderCompany?.CommercialRegistrations != null && shareholderCompany.CommercialRegistrations.Any())
                        {
                            await DeleteShareholderDocumentsAsync(shareholderCompany.CommercialRegistrations.ToList());
                            logger.LogInformation("Rolled back Passport documents.");
                        }
                    },
                    description: $"Add Contact or Shareholder Company Commercial Registration Documents")
                .ContinueWith(
                    forward: async () => shareholderCompany.LegalEnterpriseLicenses = await AddShareholderDocumentsAsync(request.LegalEnterpriseLicenses, Constants.DocumentType.LegalEnterpriseLicense, userId, shareholderCompany, shareholderCompany => shareholderCompany.MerchantId),
                    back: async () =>
                    {
                        if (shareholderCompany?.LegalEnterpriseLicenses != null && shareholderCompany.LegalEnterpriseLicenses.Any())
                        {
                            await DeleteShareholderDocumentsAsync(shareholderCompany.LegalEnterpriseLicenses.ToList());
                            logger.LogInformation("Rolled back Passport documents.");
                        }
                    },
                    description: $"Add Contact or Shareholder Company Legal Enterprise License Documents");

            await cleanupService.RunAsync();
        }
        catch (Exception ex) when (ex is PassthroughException || ex is ServiceException)
        {
            throw;
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, $"General exception was thrown.");
            throw new ServiceException(HttpStatusCode.InternalServerError, Errors.GenericError);
        }

        shareholderCompany.MerchantId = request.MerchantId;
        return shareholderCompany;
    }

    public async Task<MerchantShareholderCompanyResponse> PatchShareholderCompanyAsync(ShareholderCompanyPatchRequest request)
    {
        await ValidateShareholderCompanyPatchRequestAsync(request);

        string uri = $"{MerchantServiceShareholderBaseUrl}/shareholdercompanies";
        var reqBody = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

        using (logger.BeginScope("PatchShareholderCompanyAsync({@url})", uri))
        {
            logger.LogInformation("Calling merchant service to update merchant shareholder company for Ids '{shareholderCompanyId}' and  '{merchantId}'.",
                request.ShareholderCompanyId, request.MerchantId);

            var resp = await client.PatchAsync(uri, reqBody);
            var responseBody = await resp.Content.ReadAsStringAsync();

            if (!resp.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to update shareholder company for Ids '{shareholderCompanyId}' and  '{merchantId}'." +
                    "Error was {StatusCode} {@responseBody}",
                    request.ShareholderCompanyId, request.MerchantId, (int)resp.StatusCode, responseBody);

                throw new PassthroughException(resp);
            }
            logger.LogInformation("Updated shareholder company for ids  Ids '{shareholderCompanyId}' and  '{merchantId}'.", request.ShareholderCompanyId, request.MerchantId);

            return Json.Deserialize<MerchantShareholderCompanyResponse>(responseBody);
        }
    }

    public async Task<List<MerchantShareholderIndividual>> GetMerchantIndividualsAsync(Guid merchantId)
    {
        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individuals/{merchantId}";

        using (logger.BeginScope("GetMerchantIndividuals({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to get all shareholder individuals for merchant with id {merchantId}", merchantId);

            var response = await client.GetAsync(serviceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to get Shareholder Individuals by merchant id." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var merchantIndividualsCore = Json.Deserialize<MerchantShareholderIndividualsCore>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return Map(merchantIndividualsCore);
        }
    }

    public async Task CreateShareholderCompanyMerchantAssociationAsync(ShareholderCompanyMerchantAssociationRequest request)
    {
        ValidateShareholderCompanyMerchantAssociationRequest(request);

        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/shareholdercompanies/create-association";

        using (logger.BeginScope("CreateShareholderCompanyMerchantAssociationAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to create Shareholder Company to Merchant association.");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

            var response = await client.PostAsync(serviceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant API to CreateShareholderCompanyMerchantAssociationAsync." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }
        }
    }

    public async Task<ShareholderIndividualResponse> CreateShareholderIndividualAsync(CreateShareholderIndividualWithDocumentRequest request, Guid userId)
    {
        await validationService.ValidateCreateIndividualRequest(request);
        PopulateNationalIdIfNeeded(request);

        ShareholderIndividualResponse? shareholderIndividual = new();

        try
        {
            cleanupService.Init(
                    forward: async () => shareholderIndividual = await shareholderClient.CreateShareholderIndividualAsync(request),
                    back: async () =>
                    {
                        if (shareholderIndividual?.Id != Guid.Empty)
                        {
                            await shareholderClient.DeleteShareholderIndividualAsync(shareholderIndividual!.Id);
                            logger.LogInformation($"Rolled back ShareholderIndividual creation for ID '{shareholderIndividual.Id}'.");
                        }
                    },
                  description: $"Create Shareholder Individual for Merchant with ID '{request.Merchant?.MerchantId}'.")

                .ContinueWith(
                    forward: async () => shareholderIndividual.NationalIds = await AddShareholderDocumentsAsync(request.NationalIds, Constants.DocumentType.NationalId, userId, shareholderIndividual, shareholderIndividual => shareholderIndividual.Merchant!.MerchantId),
                    back: async () =>
                    {
                        if (shareholderIndividual?.NationalIds != null && shareholderIndividual.NationalIds.Any())
                        {
                            await DeleteShareholderDocumentsAsync(shareholderIndividual.NationalIds.ToList());
                            logger.LogInformation("Rolled back National ID documents.");
                        }
                    },
                    description: $"Add Contact or Shareholder Individual NationalID Documents")
                .ContinueWith(
                    forward: async () => shareholderIndividual.Passports = await AddShareholderDocumentsAsync(request.Passports, Constants.DocumentType.Passport, userId, shareholderIndividual, shareholderIndividual => shareholderIndividual.Merchant!.MerchantId),
                    back: async () =>
                    {
                        if (shareholderIndividual?.Passports != null && shareholderIndividual.Passports.Any())
                        {
                            await DeleteShareholderDocumentsAsync(shareholderIndividual.Passports.ToList());
                            logger.LogInformation("Rolled back Passport documents.");
                        }
                    },
                    description: $"Add Contact or Shareholder Individual Passport Documents");

            await cleanupService.RunAsync();
            await AddFinscanPersonChecks(shareholderIndividual!.Id);

        }
        catch (Exception ex) when (ex is PassthroughException || ex is ServiceException)
        {
            throw;
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, $"General exception was thrown.");
            throw new ServiceException(HttpStatusCode.InternalServerError, Errors.GenericError);
        }

        shareholderIndividual.Merchant!.MerchantId = request.Merchant!.MerchantId;
        return shareholderIndividual;
    }

    public async Task CreateShareholderIndividualAssociations(ShareholderIndividualAssociationsCreateRequest request)
    {
        await validationService.ValidateShareholderIndividualAssociationsCreateRequest(request);
        await merchantClient.CreateShareholderIndividualAssociations(request);
    }

    public async Task PatchShareholderIndividualAsync(ShareholderIndividualPatchRequest request)
    {
        validationService.ValidateIndividualsPatchTemp(request);

        var uri = $"{MerchantServiceShareholderBaseUrl}/individual/patch";
        var reqBody = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

        using (logger.BeginScope("PatchShareholderIndividualAsync({@url})", uri))
        {
            logger.LogInformation("Calling merchant service to update merchant shareholder individual for person of interest ID '{poiId}' and merchant ID  '{merchantId}'.",
                request.PersonOfInterestId, request.MerchantId);

            var resp = await client.PatchAsync(uri, reqBody);
            var responseBody = await resp.Content.ReadAsStringAsync();

            if (!resp.IsSuccessStatusCode)
            {
                logger.LogCritical(
                    "Error when calling merchant service to update shareholder individual for person of interest ID '{poiId}' and merchant ID  '{merchantId}'." +
                    "Error was {StatusCode} {@responseBody}",
                    request.PersonOfInterestId, request.MerchantId, (int)resp.StatusCode, responseBody);

                throw new PassthroughException(resp);
            }
            logger.LogInformation("Updated shareholder company for ids  Ids '{shareholderCompanyId}' and  '{merchantId}'.", request.PersonOfInterestId, request.MerchantId);
        }
    }

    public async Task<ShareholderIndividualEditableStatus> GetShareholderIndividualEditableStatusAsync(Guid shareholderIndividualId)
    {
        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individual/{shareholderIndividualId}/isEditable";

        using (logger.BeginScope("GetShareholderIndividualEditableStatusAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to get the editable status of the individual with ID {shareholderIndividualId}", shareholderIndividualId);

            var response = await client.GetAsync(serviceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to get the editable status of the individual." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<ShareholderIndividualEditableStatus>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
    }

    private async Task<List<MerchantIndividualCore>> RetrieveShareholderIndividualsAsync(ShareholderIndividualsSearchRequest shareholderIndividualsSearchRequest)
    {
        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individuals/search";

        using (logger.BeginScope("GetShareholderIndividualsAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to get Shareholder Companies");

            var body = new StringContent(JsonConvert.SerializeObject(shareholderIndividualsSearchRequest), Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync(serviceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant API to search Shareholder individuals." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<List<MerchantIndividualCore>>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
    }

    private async Task ValidateShareholderCompanyCreateRequestAsync(ShareholderCompanyCreateRequest request)
    {
        await CheckShareHolderCompanyUnicityAsync(request.CompanyLicense);

        await validationService.ValidateCreateCompanyRequest(request);
    }

    private async Task CheckShareHolderCompanyUnicityAsync(string companyLicense)
    {
        var existingCompanies = await SearchShareholderCompaniesAsync(new ShareholderCompaniesRequest()
        {
            Keyword = companyLicense
        });

        if (existingCompanies.Any(x => x.CompanyLicense.Equals(companyLicense, StringComparison.InvariantCultureIgnoreCase)))
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.AlreadyExistingShareholderCompany);
        }
    }

    private async Task ValidateShareholderCompanyPatchRequestAsync(ShareholderCompanyPatchRequest request)
    {
        var companyLicenseOperation = FindOperation(request.JsonPatchDocument, GetShareHolderCompanyFullPropertyName(nameof(ShareholderCompanyResponse.CompanyLicense)));
        if (companyLicenseOperation is not null)
        {
            await CheckShareHolderCompanyUnicityAsync((string)companyLicenseOperation.value);
        }

        await validationService.ValidateEditCompanyRequest(request);
    }

    private static Operation<MerchantShareholderCompanyResponse>? FindOperation(JsonPatchDocument<MerchantShareholderCompanyResponse> o, string pathName)
     => o.Operations.Find(x => x.path.Equals(pathName, StringComparison.InvariantCultureIgnoreCase));

    private static string GetShareHolderCompanyFullPropertyName(string propName) =>
        string.Format("/{0}/{1}", shareholderCompanyPathName, propName);

    private List<MerchantShareholderIndividual> Map(MerchantShareholderIndividualsCore merchantShareholderIndividualsCore)
    {
        var allIndividuals = new List<MerchantShareholderIndividual>();

        allIndividuals.AddRange(merchantShareholderIndividualsCore.MerchantIndividuals
            .Select(MapDirectIndividual));
        allIndividuals.AddRange(merchantShareholderIndividualsCore.ShareholderCompanyIndividuals
            .Select(MapCompanyIndividual));

        var mergedIndividuals = allIndividuals
            .GroupBy(i => i.PersonOfInterestId)
            .Select(group =>
            {
                if (group.Count() == 1)
                {
                    return group.First();
                }
                var mergedRelations = group.SelectMany(x => x.Relations);
                var result = group.First();
                result.Relations = mergedRelations.Where(x => x.OrganizationRole != Constants.ShareholderIndividualsRelationTypes.Member).ToList();
                return result;
            })
            .ToList();

        return mergedIndividuals;
    }

    private MerchantShareholderIndividual MapCompanyIndividual(ShareholderCompanyIndividualCore individual)
    {
        var companyIndividual = mapper.Map<MerchantShareholderIndividual>(individual);
        companyIndividual.Relations.Add(new IndividualRelation
        {
            Type = Constants.ShareholderIndividualsRelationTypes.Company,
            OrganizationRole = individual.OrganizationRole,
            ShareholderCompanyId = individual.ShareholderCompanyId,
            ShareholderCompanyName = individual.ShareholderCompanyName
        });
        return companyIndividual;
    }

    private MerchantShareholderIndividual MapDirectIndividual(MerchantIndividualCore individual)
    {
        var merchantIndividual = mapper.Map<MerchantShareholderIndividual>(individual);
        merchantIndividual.Relations.Add(new IndividualRelation
        {
            Type = MapMerchantIndividualRelation(individual),
            MerchantId = individual.MerchantId,
            IsPrincipal = individual.IsPrincipal,
            OrganizationRole = individual.OrganizationRole,
            OwnershipPercentage = individual.OwnershipPercentage,
            WathqRelationId = individual.WathqRelationId
        });
        return merchantIndividual;
    }

    private static string MapMerchantIndividualRelation(MerchantIndividualCore merchantIndividual)
    {
        if (merchantIndividual.WathqRelationId != null && !merchantIndividual.IsPrincipal)
            return Constants.ShareholderIndividualsRelationTypes.WathqAuthorizedSignatory;

        return merchantIndividual.OrganizationRole == Constants.ShareholderIndividualsRelationTypes.Owner ?
            Constants.ShareholderIndividualsRelationTypes.DirectShareholder : merchantIndividual.OrganizationRole;
    }

    private void ValidateShareholderCompanyMerchantAssociationRequest(ShareholderCompanyMerchantAssociationRequest request)
    {
        if (request.MerchantId == Guid.Empty || request.ShareholderCompanyId == Guid.Empty)
        {
            logger.LogCritical("Error when trying to Create Shareholder Companies to Merchant Association." +
                               " Error was {error} .", Errors.InvalidCreateShareholderCompanyMerchantAssociationRequest.Message);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidCreateShareholderCompanyMerchantAssociationRequest);
        }
    }

    private static void PopulateNationalIdIfNeeded(ShareholderIndividualCreateRequest request)
    {
        if (string.IsNullOrEmpty(request.NationalId))
        {
            request.NationalId = request.PassportNo;
            request.IdExpiryDate = request.PassportExpirationDate;
        }
    }

    private async Task<List<DocumentMetadata>> AddShareholderDocumentsAsync<T>(List<IFormFile> documentCollection, string documentType, Guid userId, T merchantShareholder, Func<T, Guid> getMerchantId)
    {
        var documentList = new List<DocumentMetadata>();

        foreach (var document in documentCollection)
        {
            var documentRequest = new DocumentRequest
            {
                MerchantId = getMerchantId(merchantShareholder),
                DocumentType = documentType,
                File = document,
                Language = "en",
                OwnerUserId = userId,
            };

            var createdDocument = await documentClient.CreateDocumentAsync(documentRequest);
            documentList = documentList.Append(createdDocument).ToList();
        }

        return documentList;
    }

    private async Task DeleteShareholderDocumentsAsync(List<DocumentMetadata> documentCollection)
    {
        var documentIds = documentCollection.Select(x => x.Id).ToList();

        await documentClient.DeleteDocumentsAsync(documentIds);
    }
    private static PersonCheckRequest CreateFinscanCheckModel(Guid PersonofinterestId)
    {
        return new PersonCheckRequest
        {
            CheckDate = DateTime.UtcNow,
            CheckProvider = 0,
            CheckScore = 0,
            CheckStatus = Constants.PersonCheckStatus.FinscanCheckPending,
            CheckType = Constants.PersonCheck.FinscanCheck,
            ValidFrom = DateTime.UtcNow,
            MerchantPersonOfInterestId = PersonofinterestId
        };
    }
    public async Task<PersonCheckRequest> CreatePersonCheckAsync(PersonCheckRequest personCheck)
    {
        var requestUri = $"{MerchantServiceBaseUrl}{PersonCheckEndpoint}";
        using (logger.BeginScope("CreatePersonCheckAsync({@url})", requestUri))
        {
            logger.LogInformation($"Calling merchant service to create person check.");

            var request = JsonConvert.SerializeObject(personCheck);

            var requestBody = new StringContent(request, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(requestUri, requestBody);
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to create person check. Error was {StatusCode} {@responseBody}",
                    (int)response.StatusCode, content);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<PersonCheckRequest>(content);
        }
    }
    private async Task AddFinscanPersonChecks(Guid createdpersonofinterestid)
    {
        var _personchecks = CreateFinscanCheckModel(createdpersonofinterestid);
        await CreatePersonCheckAsync(_personchecks);
    }
}
