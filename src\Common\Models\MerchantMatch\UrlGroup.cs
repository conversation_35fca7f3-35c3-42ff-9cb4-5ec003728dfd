﻿using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class UrlGroup
{
    [Display(Name = "Exactly matched Url's")]
    public UrlClass ExactMatchUrls { get; set; } = new UrlClass();

    [Display(Name = "Close matched Url's")]
    public UrlClass CloseMatchUrls { get; set; } = new UrlClass();

    [Display(Name = "No matched Url's")]
    public UrlClass NoMatchUrls { get; set; } = new UrlClass();
}
