﻿using Common;
using Common.Models;
using Common.Models.Match;
using Common.Services;
using Microsoft.Extensions.Logging;
using Services.Extensions;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Common.Constants.MatchTemplatesFilesSettings;
using static Services.Extensions.ReflectionExtensions;

namespace Services
{
    ///<inheritdoc cref="IMatchReportBuilderService"/>
    public class MatchReportBuilderService : IMatchReportBuilderService
    {
        private readonly string tableHeaderTemplate;
        private readonly string tableRowTemplate;
        private readonly string reportTemplate;
        private const string LineDivider = "<br><hr>";
        private readonly ILogger<MatchReportBuilderService> logger;
        private Catalogue[] catalogues = Array.Empty<Catalogue>();
        private readonly IReferenceService referenceService;

        public MatchReportBuilderService(ITemplateService templateService, ILogger<MatchReportBuilderService> logger, IReferenceService referenceService)
        {
            this.logger = logger;

            logger.LogDebug("Retrieving table header template");
            tableHeaderTemplate = templateService.GetTableHeaderTemplate();

            logger.LogDebug("Retrieving table row template");
            tableRowTemplate = templateService.GetTableRowTemplate();

            logger.LogDebug("Retrieving report template template");
            reportTemplate = templateService.GetReportTemplate();

            this.referenceService = referenceService;
        }

        public async Task<string> GenerateMatchReportAsync(MatchResponse payload)
        {
            logger.LogInformation("Started generating match response report");

            logger.LogInformation("Retrieving catalogues for generating description for termination reason code");

            catalogues = await referenceService.GetCataloguesAsync(
                new string[1] { Constants.Catalogues.TerminationReasonCode }, "en");

            logger.LogInformation("Generating terminated merchants area");
            var terminatedMerchantsAreaBuilder = new StringBuilder();
            if (payload.TerminationInquiry.PossibleMerchantMatches.Any())
            {
                logger.LogInformation("Possible merchant matches found. Generating tables for each.");
                foreach (var match in payload.TerminationInquiry.PossibleMerchantMatches)
                {
                    var pendingAndFailedMerchantsCount = match.TerminatedMerchant.Select(x => x.Merchant).Count(x => x.MerchantStatus != Constants.MatchStatus.Passed);

                    terminatedMerchantsAreaBuilder.Append($"<h2>Following {pendingAndFailedMerchantsCount} terminated merchants matches found:</h2>");

                    foreach (var pair in match.TerminatedMerchant.Where(x => x.Merchant.MerchantStatus != Constants.MatchStatus.Passed))
                    {
                        terminatedMerchantsAreaBuilder.Append(CreateSection(pair.Merchant, pair.MerchantMatch, GetPropertyDisplayName<PossibleMerchantMatch>(prop => prop.TerminatedMerchant)));
                    }
                }
            }

            logger.LogInformation("Generated report.");

            return reportTemplate
                .ReplacePlaceHolder("Content", $"{terminatedMerchantsAreaBuilder}<br>");
        }

        private string CreateSection(Merchant merchant, MerchantMatch merchantMatch, string tableName)
        {
            if (string.IsNullOrEmpty(merchant.MerchantStatus) || merchant.MerchantStatus == Constants.MatchStatus.Passed) return string.Empty;

            AddTerminationReasonCodeDescription(merchant);

            var sectionBuilder = new StringBuilder();

            sectionBuilder.Append(CreateMerchantAndMerchantMatchTable(merchant, merchantMatch, tableName));

            if (merchant.Principal.Any() && merchantMatch.PrincipalMatch.Any())
            {
                for (int i = 0; i < merchant.Principal.Length; i++)
                {
                    var principal = merchant.Principal[i];

                    PrincipalMatch principalMatch;

                    if (i >= merchantMatch.PrincipalMatch.Length)
                    {
                        principalMatch = new PrincipalMatch();
                    }
                    else
                    {
                        principalMatch = merchantMatch.PrincipalMatch[i];
                    }
                    sectionBuilder.Append(CreatePrincipalAndPrincipalMatchTable(principal, principalMatch));
                }
            }
            sectionBuilder.Append(LineDivider);
            return sectionBuilder.ToString();

        }

        private string CreateMerchantAndMerchantMatchTable(Merchant merchant, MerchantMatch merchantMatch, string tableName)
        {
            var tableBuilder = new StringBuilder();

            tableBuilder.Append("<table>");
            tableBuilder.Append(tableHeaderTemplate.ReplacePlaceHolder("EntityName", tableName));

            tableBuilder.Append(tableRowTemplate
                .ReplacePlaceHolder(TableRowPropertyPlaceholder, GetPropertyDisplayName<Merchant>(i => i.MerchantStatus))
                .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, merchant.MerchantStatus)
                .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, string.Empty));

            tableBuilder.Append(tableRowTemplate
                .ReplacePlaceHolder(TableRowPropertyPlaceholder, GetPropertyDisplayName<Merchant>(i => i.TerminationReasonCode))
                .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, merchant.TerminationReasonCode)
                .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, string.Empty));

            tableBuilder.Append(tableRowTemplate
                .ReplacePlaceHolder(TableRowPropertyPlaceholder, GetPropertyDisplayName<Merchant>(i => i.AddedByAcquirerID))
                .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, merchant.AddedByAcquirerID)
                .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, string.Empty));

            tableBuilder.Append(tableRowTemplate
                .ReplacePlaceHolder(TableRowPropertyPlaceholder, GetPropertyDisplayName<Merchant>(i => i.AddedOnDate))
                .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, merchant.AddedOnDate)
                .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, string.Empty));

            tableBuilder.Append(tableRowTemplate
                .ReplacePlaceHolder(TableRowPropertyPlaceholder, GetPropertyDisplayName<Merchant>(i => i.Comments))
                .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, merchant.Comments)
                .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, string.Empty));

            foreach (var merchantPropertyName in typeof(Merchant).GetProperties()
                .OrderBy(ord => ord.GetAttribute<DisplayAttribute>(false)?.Order)
                .Select(merchantProperty => merchantProperty))
            {
                if (merchantPropertyName.Name == nameof(Merchant.Principal)) continue;
                if (merchantPropertyName.Name == nameof(Merchant.Url)) continue;

                var matchProperty = typeof(MerchantMatch).GetProperties()
                    .FirstOrDefault(x => x.Name.Contains(merchantPropertyName.Name));

                if (matchProperty == null) continue;

                var merchantValue = merchant.GetPropValue(merchantPropertyName.Name);
                if (merchantValue == null) continue;

                var matchValue = merchantMatch.GetPropValue(matchProperty.Name);
                if (matchValue == null) continue;

                tableBuilder.Append(tableRowTemplate
                    .ReplacePlaceHolder(TableRowPropertyPlaceholder, merchantPropertyName.DisplayName())
                    .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, merchantValue.ToString()?.Replace(Environment.NewLine, "<br/>").ToString())
                    .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, matchValue.ToString()));
            }



            if (merchantMatch.UrlMatch.Any() && merchant.Url.Any())
            {
                tableBuilder.Append(CreateUrlsMatchTableRow(merchant.Url, merchantMatch.UrlMatch));
            }

            tableBuilder.Append("</table>");

            return tableBuilder.ToString();
        }

        private string CreateUrlsMatchTableRow(string[] merchantUrls, UrlMatch[] matchedUrls)
        {
            var merchantCellValueBuilder = new StringBuilder();
            merchantCellValueBuilder.Append("<ul>");
            foreach (var url in merchantUrls)
            {
                merchantCellValueBuilder.Append($"<li>{url}</li>");
            }
            merchantCellValueBuilder.Append("</ul>");

            var matchCellValueBuilder = new StringBuilder();
            matchCellValueBuilder.Append("<ul>");
            foreach (var match in matchedUrls)
            {
                matchCellValueBuilder.Append($"<li>{match.Url}</li>");
            }
            matchCellValueBuilder.Append("</ul>");

            return tableRowTemplate
                .ReplacePlaceHolder(TableRowPropertyPlaceholder, GetPropertyDisplayName<MerchantMatch>(i => i.UrlMatch))
                .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, merchantCellValueBuilder.ToString()?.Replace(Environment.NewLine, "<br/>").ToString())
                .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, matchCellValueBuilder.ToString());
        }

        private string CreatePrincipalAndPrincipalMatchTable(Principal principal, PrincipalMatch principalMatch)
        {
            var tableBuilder = new StringBuilder();
            tableBuilder.Append("<br><table>");
            tableBuilder.Append(tableHeaderTemplate.ReplacePlaceHolder("EntityName", nameof(Principal)));

            foreach (var principalPropertyName in typeof(Principal).GetProperties()
                .OrderBy(ord => ord.GetAttribute<DisplayAttribute>(false)?.Order)
                .Select(principalProperty => principalProperty))
            {
                var matchProperty = typeof(PrincipalMatch).GetProperties()
                    .FirstOrDefault(x => x.Name.ToLower().Contains(principalPropertyName.Name.ToLower()));
                if (matchProperty == null) continue;

                var principalValue = principal.GetPropValue(matchProperty.Name);
                if (principalValue == null) continue;

                var matchValue = principalMatch.GetPropValue(matchProperty.Name);
                if (matchValue == null) continue;

                tableBuilder.Append(tableRowTemplate
                    .ReplacePlaceHolder(TableRowPropertyPlaceholder, principalPropertyName.DisplayName())
                    .ReplacePlaceHolder(TableRowPropertyValuePlaceholder, principalValue.ToString()?.Replace(Environment.NewLine, "<br/>").ToString())
                    .ReplacePlaceHolder(TableRowPropertyMatchPlaceholder, matchValue.ToString()));
            }

            tableBuilder.Append("</table>");
            return tableBuilder.ToString();
        }

        private void AddTerminationReasonCodeDescription(Merchant merchant)
        {
            if (merchant != null && merchant.TerminationReasonCode != null)
            {
                var terminationReasonCodeDescription = catalogues.FirstOrDefault(e => e.CatalogueName == Constants.Catalogues.TerminationReasonCode && e.Key == merchant.TerminationReasonCode)?.Value;
                if (!string.IsNullOrEmpty(terminationReasonCodeDescription))
                {
                    merchant.TerminationReasonCode = string.Format("{0} - {1}", merchant.TerminationReasonCode, terminationReasonCodeDescription);
                }
            }
        }
    }
}