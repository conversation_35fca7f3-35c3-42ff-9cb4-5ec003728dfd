﻿using System;
using System.Collections.Generic;

namespace Common.Models.Lead
{
    public class LeadExportParameters
    {
        public string? PhoneNumber { get; set; }

        public string? CountryPrefix { get; set; }

        public List<string>? PhonesWithPrefix { get; set; }

        public List<string>? Emails { get; set; }

        public List<string>? SalesIds { get; set; }

        public string? SalesId { get; set; }

        public string? SalesPartnerId { get; set; }

        public List<string>? SalesPartnerIds { get; set; }

        public List<string>? ProductCodes { get; set; }

        public DateInterval? DateInterval { get; set; }

        public string? NationalId { get; set; }

        public List<string>? NationalIds => NationalId != null ? new List<string>() { NationalId } : null;

        public Guid? LeadId { get; set; }

        public List<string>? ReferralChannels { get; set; }

        public string? Keyword { get; set; }

        public string[]? SearchIn { get; set; }

        public string? OrderBy { get; set; }

        public string? Sort { get; set; }
    }
}
