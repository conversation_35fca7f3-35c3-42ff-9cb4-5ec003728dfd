﻿using FluentValidation;
using Common.Options;

namespace Common.Validators
{
    public class UrlSettingsValidator : AbstractValidator<UrlSettings>
    {
        public UrlSettingsValidator()
        {
            RuleFor(a => a.MerchantServiceBaseUrl).NotEmpty();
            RuleFor(a => a.GeideaFederationServiceBaseUrl).NotEmpty();
            RuleFor(a => a.LeadServiceBaseUrl).NotEmpty();
            RuleFor(a => a.NotificationServiceBaseUrl).NotEmpty();
            RuleFor(a => a.DocumentServiceBaseUrl).NotEmpty();
            RuleFor(a => a.CheckoutServiceBaseUrl).NotEmpty();
            RuleFor(a => a.ReferenceServiceBaseUrl).NotEmpty();
            RuleFor(a => a.UserServiceBaseUrl).NotEmpty();
            RuleFor(a => a.ProductServiceBaseUrl).NotEmpty();
            RuleFor(a => a.SearchServiceBaseUrl).NotEmpty();
            RuleFor(a => a.NexusBridgeApiBaseUrl).NotEmpty();
            RuleFor(a => a.MerchantServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.LeadServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.NotificationServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.DocumentServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.CheckoutServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.ReferenceServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.UserServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.ProductServiceBaseUrlNS).NotEmpty();
            RuleFor(a => a.SearchServiceBaseUrlNS).NotEmpty();

        }
    }
}
