﻿using Common.Models;
using Common.Models.Account;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Common.Options;
using Microsoft.Extensions.Options;
using static Common.Constants;
using Common;
using Geidea.Utils.Counterparty.Providers;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class AccountServiceTests
    {
        private readonly Mock<ILogger<AccountService>> logger = new Mock<ILogger<AccountService>>();
        private AccountService accountService = null!;
        private readonly Mock<ISearchService> searchService = new Mock<ISearchService>();
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
        private static readonly Guid MerchantId = Guid.NewGuid();
        private static readonly Guid StoreId = Guid.NewGuid();
        private static readonly DateTime createdDate = DateTime.UtcNow;
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();
        private readonly Mock<IUserService> userService = new Mock<IUserService>();
        private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
        private readonly Mock<IOptions<KsaTeamAndDeisgnationFilterToggle>> ksaTeamAndDesignationFilterToggle = new Mock<IOptions<KsaTeamAndDeisgnationFilterToggle>>();

        public AccountServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        }

        private readonly Catalogue[] catalogueResponse = new Catalogue[]
        {
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Product,
                Key = "TEST",
                Value = "Test"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.MerchantStatus,
                Key = "MerchantStatus",
                Value = "MerchantStatus"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.BankCheckStatus,
                Key = "BANK_CHECK_PENDING",
                Value = "Bank Check Pending"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Cities,
                Key = "Test",
                Value = "Test"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.Governorates,
                Key = "Test",
                Value = "Test"
            },

        };

        private readonly AccountSearchFilters accountSearchFilters = new AccountSearchFilters()
        {
            Mid = "mid",
            BusinessName = "Business name",
            BusinessId = "business id",
            MerchantStatus = new List<string> { "MerchantStatus" },
            AccountStatus = new List<string> { "AccountStatus" },
            ChannelType = new string[] { "POS" },
            DateInterval = new DateInterval()
            {
                FromDate = DateTime.UtcNow,
                ToDate = DateTime.UtcNow
            }
        };

        private static readonly AccountResult accountResult = new AccountResult()
        {
            MerchantId = MerchantId,
            StoreId = StoreId,
            Mid = "mid",
            BusinessId = "business id",
            MerchantStatus = "MerchantStatus",
            AccountStatus = "AccountStatus",
            ChannelType = "channel type",
            CreatedDate = createdDate,
            BusinessName = "Business name",
            BusinessNameAr = "Business name ar"
        };

        private readonly AccountSearchResponse<AccountResult> accountSearchResponse = new AccountSearchResponse<AccountResult>()
        {
            Records = new List<AccountResult>()
            {
               accountResult
            },
            ReturnedRecordCount = 1,
            TotalRecordCount = 1
        };

        private AccountService GetAccountService(HttpClient httpClient)
        {
            return new AccountService(
                logger.Object,
                urlSettingsOptions.Object,
                referenceService.Object,
                httpClient,
                userService.Object,
                counterpartyProvider.Object,
                ksaTeamAndDesignationFilterToggle.Object);
        }

        [Test]
        public async Task FindAsync()
        {
            accountService = GetAccountService(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(accountSearchResponse)));
            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            var result = await accountService.FindAsync(accountSearchFilters, Guid.NewGuid());

            result.Records.Count.Should().Be(1);
            result.Records[0].Should().BeEquivalentTo(accountResult);
        }

        [Test]
        public void FindAsync_PassthroughError()
        {
            accountService = GetAccountService(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));
            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            accountService.Invoking(x => x.FindAsync(accountSearchFilters, Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }


    }
}
