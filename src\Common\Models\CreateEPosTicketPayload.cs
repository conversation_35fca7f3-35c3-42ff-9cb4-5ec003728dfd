﻿namespace Common.Models
{
    using Common.Helpers;
    using System;

    public class CreateEPosTicketPayload : ILogable
    {
        public Guid ProductInstanceId { get; set; }
        public string TerminalId { get; set; } = null!;
        public string RetailerName { get; set; } = null!;
        public string? RetailerId { get; set; } 
        public string? TRSM { get; set; }
        public string ProviderBank { get; set; } = null!;
        public string? Street { get; set; }
        public string Mobile { get; set; } = null!;
        public int? CityId { get; set; }
        public string TerminalType { get; set; } = null!;
        public string? MCC { get; set; }
        public string MposMode { get; set; } = null!;
        public string? MposRefNo { get; set; }
        public decimal? MposPrice { get; set; }
        public string? MposSalesEmail { get; set; }
        public string? RegType { get; set; }
        public string? OldTerminal { get; set; }
        public int? SoftwareType { get; set; }
        public string Counterparty { get; set; } = null!;

        public string ToGDPRCompliantString()
        {
            return $"CreateEPosTicketPayload object : ProductInstanceId = {ProductInstanceId}, RetailerId = {RetailerId}";
        }
    }
}