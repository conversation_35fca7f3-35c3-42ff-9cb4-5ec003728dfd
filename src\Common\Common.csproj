﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>latest</LangVersion>
		<Nullable>enable</Nullable>
		<TreatWarningsAsErrors>true</TreatWarningsAsErrors>
		<CheckForOverflowUnderflow>True</CheckForOverflowUnderflow>
		<ProjectGuid>{efcabeba-40fe-44ec-b08b-275be4ee1af4}</ProjectGuid>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Geidea.Messaging" Version="2.2.103" />
		<PackageReference Include="Geidea.Utils.ApplicationLanguage" Version="2.0.4" />
		<PackageReference Include="Geidea.Utils.Common" Version="1.0.8" />
		<PackageReference Include="Geidea.Utils.ConditionalSerialization" Version="1.0.96" />
		<PackageReference Include="Geidea.Utils.Counterparty" Version="2.0.232" />
		<PackageReference Include="Geidea.Utils.ReferenceData" Version="1.0.103" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
		<PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="6.0.12" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
		<PackageReference Include="System.Text.Json" Version="6.0.7" />
	</ItemGroup>

</Project>
