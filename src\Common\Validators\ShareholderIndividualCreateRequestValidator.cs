﻿using Common.Models.Shareholder;
using FluentValidation;
using System;
using System.Linq;

namespace Common.Validators;

public class ShareholderIndividualCreateRequestValidator : AbstractValidator<ShareholderIndividualCreateRequest>
{
    public ShareholderIndividualCreateRequestValidator()
    {
        RuleFor(x => x.FirstName)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x.Length <= 64)
            .WithErrorCode(Errors.ShareholderIndividualFirstNameValidation.Code)
            .WithMessage(Errors.ShareholderIndividualFirstNameValidation.Message);

        RuleFor(x => x.LastName)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x.Length <= 64)
            .WithErrorCode(Errors.ShareholderIndividualLastNameValidation.Code)
            .WithMessage(Errors.ShareholderIndividualLastNameValidation.Message);

        RuleFor(x => x.DOB)
            .Must(x => x != null && !x.Equals(DateTime.MinValue) && !x.Equals(DateTime.MaxValue))
            .WithErrorCode(Errors.ShareholderIndividualDobValidation.Code)
            .WithMessage(Errors.ShareholderIndividualDobValidation.Message);

        RuleFor(x => x.PhoneNumber)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 16)
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber))
            .WithErrorCode(Errors.PhoneLengthValidation.Code)
            .WithMessage(Errors.PhoneLengthValidation.Message)
            .Matches(@"^[0-9]{6,10}$")
            .WithErrorCode(Errors.PhoneValidation.Code)
            .WithMessage(Errors.PhoneValidation.Message);

        RuleFor(x => x.PhonePrefix)
            .Must(x => x!.StartsWith("+") && x.Length <= 10)
            .When(x => !string.IsNullOrEmpty(x.PhonePrefix))
            .WithErrorCode(Errors.CountryPrefixLengthValidation.Code)
            .WithMessage(Errors.CountryPrefixLengthValidation.Message);

        RuleFor(x => x.Nationality)
            .Must(x => !string.IsNullOrWhiteSpace(x) && x!.Length <= 64)
            .When(x => x.Nationality != null)
            .WithErrorCode(Errors.NationalityLengthValidation.Code)
            .WithMessage(Errors.NationalityLengthValidation.Message);

        RuleFor(x => x.Merchant!)
           .SetValidator(new MerchantIndividualLinkValidator())
           .When(x => x.Merchant != null);

        When(x => x.ShareholderCompanies != null && x.ShareholderCompanies.Any(), () =>
        {
            RuleForEach(x => x.ShareholderCompanies)
                .SetValidator(new ShareholderCompanyIndividualLinkValidator());
        });

        When(x => x.Address != null && x.Counterparty == Constants.CounterParty.Saudi, () =>
        {
            RuleFor(x => x.Address!.Governorate)
                .Must(x => string.IsNullOrWhiteSpace(x))
                .WithErrorCode(Errors.DisabledProperty.Code)
                .WithMessage(Errors.DisabledProperty.Message);

            RuleFor(x => x.Address!.Area)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Address!.Area != null)
                .WithErrorCode(Errors.AreaLengthValidation.Code)
                .WithMessage(Errors.AreaLengthValidation.Message);

            RuleFor(x => x.Address!.City)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Address!.City != null)
                .WithErrorCode(Errors.CityLengthValidation.Code)
                .WithMessage(Errors.CityLengthValidation.Message);
        });

        When(x => x.Address != null && x.Counterparty == Constants.CounterParty.Egypt, () =>
        {
            RuleFor(x => x.Address!.Area)
                .Must(x => string.IsNullOrWhiteSpace(x))
                .WithErrorCode(Errors.DisabledProperty.Code)
                .WithMessage(Errors.DisabledProperty.Message);

            RuleFor(x => x.Address!.Governorate)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Address!.Governorate != null)
                .WithErrorCode(Errors.GovernorateLengthValidation.Code)
                .WithMessage(Errors.GovernorateLengthValidation.Message);

            RuleFor(x => x.Address!.City)
                .Must(x => !string.IsNullOrWhiteSpace(x) && x?.Length <= 255)
                .When(x => x.Address!.City != null)
                .WithErrorCode(Errors.CityLengthValidation.Code)
                .WithMessage(Errors.CityLengthValidation.Message);
        });

    }
}
