﻿using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Common.Options;
using Common.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Microsoft.AspNetCore.JsonPatch;
using Common.Models.Lead;
using Geidea.Utils.Exceptions;
using System;
using Common.Models;
using Common.Models.User;
using AutoMapper;
using Common;
using Geidea.Utils.Cleanup;
using System.Net;
using Common.Models.Document;
using Microsoft.AspNetCore.Http;
using Common.Validators;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Json;
using Geidea.Utils.Validation;
using UtilsConstants = Geidea.Utils.Common.Constants;
using System.Text.Json;
using Geidea.Utils.ApplicationLanguage.Providers;
using static Common.Constants;
using System.Linq;
using Common.Models.Merchant;
using Common.Models.Product;
using Geidea.Utils.ReferenceData;

namespace Services
{
    public class LeadService : ILeadService
    {
        private readonly ILogger<LeadService> logger;
        private readonly IOptionsMonitor<UrlSettings> urlSettingsOptions;
        private readonly HttpClient client;
        private readonly IUserService userService;
        private readonly IMapper mapper;
        private readonly ICleanupService cleanupService;
        private readonly IDocumentService documentService;
        private readonly ICounterpartyProvider counterpartyProvider;
        private readonly IApplicationLanguage applicationLanguage;
        private readonly IActiveCampaignService activeCampaignService;
        private readonly IProductService productService;
        private readonly IReferenceService referenceService;
        private readonly IMerchantService merchantService;
        private readonly IOptionsMonitor<MerchantCheckOptions> merchantCheckOptions;

        public LeadService(
            ILogger<LeadService> logger,
            IOptionsMonitor<UrlSettings> urlSettingsOptions,
            HttpClient client,
            IUserService userService,
            IMapper mapper,
            ICleanupService cleanupService,
            IDocumentService documentService,
            ICounterpartyProvider counterpartyProvider,
            IApplicationLanguage applicationLanguage,
            IActiveCampaignService activeCampaignService,
            IProductService productService,
            IReferenceService referenceService,
            IMerchantService merchantService,
            IOptionsMonitor<MerchantCheckOptions> merchantCheckOptions)
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions;
            this.client = client;
            this.userService = userService;
            this.mapper = mapper;
            this.cleanupService = cleanupService;
            this.documentService = documentService;
            this.counterpartyProvider = counterpartyProvider;
            this.applicationLanguage = applicationLanguage;
            this.activeCampaignService = activeCampaignService;
            this.productService = productService;
            this.referenceService = referenceService;
            this.merchantService = merchantService;
            this.merchantCheckOptions = merchantCheckOptions;
        }

        private string LeadApiBasePath => urlSettingsOptions.CurrentValue?.LeadServiceBaseUrlNS ?? string.Empty;
        private string SearchServiceBaseUrl => urlSettingsOptions.CurrentValue?.SearchServiceBaseUrlNS ?? string.Empty;
        private const string LeadEndpoint = "/api/v1/Lead";
        private const string LeadAdvancedSearchEndpoint = "/api/v1/Lead/advancedSearch";
        private const string LeadExportEndpoint = "/api/v1/Lead/export";

        public async Task<LeadSearchResponse> SearchLeadsAsync(LeadSearchParameters searchParameters)
        {
            string serviceUrl = $"{SearchServiceBaseUrl}{LeadAdvancedSearchEndpoint}";

            using (logger.BeginScope("SearchLeadsAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling search service to get leads.");

                var response = await client.PostAsync(serviceUrl, new StringContent(JsonConvert.SerializeObject(searchParameters), Encoding.UTF8, "application/json"));
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling search service to get leads. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<LeadSearchResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<Lead> UpdateLeadAsync(Guid leadId, JsonPatchDocument<Lead> leadPatch)
        {
            await CanUpdateLeadReferralChannel(leadId, leadPatch);

            var patchedLead = await PatchLeadByIdAsync(leadId, leadPatch);

            if (patchedLead.LeadStatus == LeadStatus.Converted)
                await PatchMerchantByLeadIdAsync(leadId, leadPatch);

            return patchedLead;
        }

        public async Task<Lead> PatchLeadByIdAsync(Guid leadId, JsonPatchDocument<Lead> leadPatch)
        {
            var leadData = new Lead() { LeadDetails = new LeadDetails() };
            try
            {
                leadPatch.ApplyTo(leadData);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Invalid patch.");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatch);
            }

            new ValidationHelpers().Validate(leadData, new LeadValidator(counterpartyProvider), logger, "Lead length validation failed!");
            await ValidateLicenseNumberPatch(leadId, leadPatch);

            return await PatchLeadAsync(leadId, leadPatch);
        }

        private async Task ValidateLicenseNumberPatch(Guid leadId, JsonPatchDocument<Lead> leadPatch)
        {
            var registrationNumberPatchValue = leadPatch.Operations.Find(x => x.path.Contains("RegistrationNumber", StringComparison.InvariantCultureIgnoreCase))?.value;
            var municipalityLicensePatchValue = leadPatch.Operations.Find(x => x.path.Contains("MunicipalLicenseNumber", StringComparison.InvariantCultureIgnoreCase))?.value;
            var legalIdPatchValue = leadPatch.Operations.Find(x => x.path.Contains("LegalId", StringComparison.InvariantCultureIgnoreCase))?.value;
            var businessTypePatchValue = leadPatch.Operations.Find(x => x.path.Contains("BusinessType", StringComparison.InvariantCultureIgnoreCase))?.value;

            if (registrationNumberPatchValue == null
               && municipalityLicensePatchValue == null
               && legalIdPatchValue == null
               && businessTypePatchValue == null)
            {
                return;
            }

            var valdiationParams = await GetValidationLicenseIdParams(leadId, businessTypePatchValue?.ToString(), registrationNumberPatchValue?.ToString(), municipalityLicensePatchValue?.ToString(), legalIdPatchValue?.ToString());

            if (valdiationParams.BusinessType == null && (valdiationParams.RegistrationNumber != null || valdiationParams.MunicipalityLicense != null || valdiationParams.LegalId != null))
            {
                return;
            }

            await merchantService.ValidateLicenseIdIsUsedAsync(null,
                        valdiationParams.BusinessType,
                        valdiationParams.RegistrationNumber,
                        valdiationParams.MunicipalityLicense,
                        valdiationParams.LegalId);
        }

        private async Task<(string? BusinessType, string? RegistrationNumber, string? MunicipalityLicense, string? LegalId)> GetValidationLicenseIdParams(Guid leadId, string? businessType, string? registrationNumber, string? municipalityLicense, string? legalId)
        {

            var businessTypeToCheck = string.IsNullOrWhiteSpace(businessType) ? null : businessType;
            var registrationNumberToCheck = string.IsNullOrWhiteSpace(registrationNumber) ? null : registrationNumber;
            var municipalityLicenseToCheck = string.IsNullOrWhiteSpace(municipalityLicense) ? null : municipalityLicense;
            var legalIdToCheck = string.IsNullOrWhiteSpace(legalId) ? null : legalId;

            if (businessTypeToCheck != null && (registrationNumberToCheck != null || municipalityLicenseToCheck != null || legalIdToCheck != null))
            {
                if (!ValidBusinessTypeLicenseCombination(businessTypeToCheck, registrationNumberToCheck, municipalityLicenseToCheck, legalIdToCheck))
                {
                    return (null, null, null, null);
                }

                return (businessTypeToCheck, registrationNumberToCheck, municipalityLicenseToCheck, legalIdToCheck);
            }

            return await GetValidationLicenseIdParamsWithLeadDetails(leadId, businessTypeToCheck, registrationNumberToCheck, municipalityLicenseToCheck, legalIdToCheck);
        }

        private async Task<(string? BusinessType, string? RegistrationNumber, string? MunicipalityLicense, string? LegalId)> GetValidationLicenseIdParamsWithLeadDetails(Guid leadId, string? businessTypeToCheck, string? registrationNumberToCheck, string? municipalityLicenseToCheck, string? legalIdToCheck)
        {
            var lead = await GetLeadByIdAsync(leadId);

            if (lead.LeadDetails == null)
            {
                return (null, null, null, null);
            }

            if (businessTypeToCheck == null)
            {
                businessTypeToCheck = lead.LeadDetails?.BusinessType;
            }
            if (businessTypeToCheck == null)
            {
                return (null, null, null, null);
            }

            if (!ValidBusinessTypeLicenseCombination(businessTypeToCheck, registrationNumberToCheck, municipalityLicenseToCheck, legalIdToCheck))
            {
                return (null, null, null, null);
            }

            if (registrationNumberToCheck == null)
            {
                registrationNumberToCheck = lead.LeadDetails?.RegistrationNumber;
            }

            if (municipalityLicenseToCheck == null)
            {
                municipalityLicenseToCheck = lead.LeadDetails?.MunicipalLicenseNumber;
            }

            if (legalIdToCheck == null)
            {
                legalIdToCheck = lead.LeadDetails?.LegalId;
            }

            return (businessTypeToCheck, registrationNumberToCheck, municipalityLicenseToCheck, legalIdToCheck);
        }

        private static bool ValidBusinessTypeLicenseCombination(string? businessTypeToCheck, string? registrationNumberToCheck, string? municipalityLicenseToCheck, string? legalIdToCheck)
        {
            //SoleTrader and Limited must not be used with a municipal license number
            if ((businessTypeToCheck == BusinessType.Limited || businessTypeToCheck == BusinessType.SoleTrader)
                                && !string.IsNullOrWhiteSpace(municipalityLicenseToCheck))
                return false;

            //SoleTrader and Limited must not be used with a legal enterprise
            if ((businessTypeToCheck == BusinessType.Limited || businessTypeToCheck == BusinessType.SoleTrader)
                                && !string.IsNullOrWhiteSpace(legalIdToCheck))
                return false;

            //MunicipalEntity must not be used with a registration number
            if (businessTypeToCheck == BusinessType.MunicipalEntity && !string.IsNullOrWhiteSpace(registrationNumberToCheck))
                return false;

            //MunicipalEntity must not be used with a legal enterprise
            if (businessTypeToCheck == BusinessType.MunicipalEntity && !string.IsNullOrWhiteSpace(legalIdToCheck))
                return false;

            //LegalEnterprise must not be used with a registration number
            if (businessTypeToCheck == BusinessType.LegalEnterprise && !string.IsNullOrWhiteSpace(registrationNumberToCheck))
                return false;

            //LegalEnterprise must not be used with a municipal license number
            if (businessTypeToCheck == BusinessType.LegalEnterprise && !string.IsNullOrWhiteSpace(municipalityLicenseToCheck))
                return false;

            return true;
        }

        public async Task<List<LeadCreationResponse>> GetLeadCreationAsync(IdsRequest leadIds)
        {
            string leadServiceUrl = $"{LeadApiBasePath}{LeadEndpoint}/creation";
            var body = new StringContent(JsonConvert.SerializeObject(leadIds), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetLeadCreationAsync({@leadServiceUrl})", leadServiceUrl))
            {
                logger.LogInformation($"Calling lead service to get lead creation.");

                var response = await client.PostAsync(leadServiceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling lead service to get lead creation . Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<LeadCreationResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        private async Task<Lead> GetLeadByIdAsync(Guid leadId)
        {
            string leadServiceUrl = $"{LeadApiBasePath}{LeadEndpoint}/{leadId}";

            using (logger.BeginScope("GetLeadCreationAsync({@leadServiceUrl})", leadServiceUrl))
            {
                logger.LogInformation($"Calling lead service to get lead creation.");

                var response = await client.GetAsync(leadServiceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling lead service to get lead creation . Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<Lead>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<LeadExportResponse[]> ExportLeadsAsync(LeadExportParameters exportParameters)
        {
            string serviceUrl = $"{SearchServiceBaseUrl}{LeadExportEndpoint}";

            using (logger.BeginScope("ExportLeadsAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling search service to get leads export.");

                var response = await client.PostAsync(serviceUrl, new StringContent(JsonConvert.SerializeObject(exportParameters), Encoding.UTF8, "application/json"));
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling search service to get leads for export. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<LeadExportResponse[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task UpdateSalesIdAsync(string initialSalesId, string updatedSalesId)
        {
            string leadServiceUrl = $"{LeadApiBasePath}{LeadEndpoint}/salesId";
            var requestBody = new StringContent(JsonConvert.SerializeObject(new { initialSalesId, updatedSalesId }), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateSalesIdAsync({@leadServiceUrl})", leadServiceUrl))
            {
                logger.LogInformation($"Calling lead service to update salesId for all leads with salesId '{initialSalesId}' to '{updatedSalesId}'.");

                var response = await client.PutAsync(leadServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling lead service to get lead update salesId . Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }
            }
        }

        public async Task<LeadWithDocumentsResponse> CreateLeadAsync(LeadCreateRequest leadCreateRequest, Guid userId)
        {
            await ValidateLeadRequest(leadCreateRequest);
            await ValidateProductIdsAsync(leadCreateRequest.LeadProductIds.ToArray());
            if (merchantCheckOptions.CurrentValue.UniqueLicenseCheckEnabled)
            {
                await merchantService.ValidateLicenseIdIsUsedAsync(null, leadCreateRequest.LeadDetails.BusinessType, leadCreateRequest.LeadDetails.RegistrationNumber, leadCreateRequest.LeadDetails.MunicipalLicenseNumber, leadCreateRequest.LeadDetails.LegalId);
            }
            await AssignBundleToReferralSpecificTypes(leadCreateRequest);
            await CheckIfAccountExists(leadCreateRequest);
            leadCreateRequest.LeadDetails.Tag ??= Common.Constants.MerchantTag.Retail;

            var createdLead = new LeadWithDocumentsResponse();

            try
            {
                cleanupService.Init(
                        forward: async () => createdLead = mapper.Map<LeadWithDocumentsResponse>(await CreateLeadCallAsync(mapper.Map<LeadRequest>(leadCreateRequest))),
                        back: async () => await RemoveLeadDataAsync(createdLead.LeadId),
                        description: $"Create new lead.")
                    .ContinueWith(
                        forward: async () =>
                        {
                            if (this.counterpartyProvider.GetCode() == UtilsConstants.CounterpartySaudi)
                            {
                                createdLead = await AddLeadDocumentsForSaudi(createdLead, leadCreateRequest, userId);
                            }
                            else
                            {
                                createdLead = await AddLeadDocumentsForEgypt(createdLead, leadCreateRequest, userId);
                            }
                        },
                        back: null,
                        description: $"Add documents for lead with id '{createdLead.LeadId}'.");

                await cleanupService.RunAsync();

                SendMessageToActiveCampaign(createdLead);
            }
            catch (PassthroughException)
            {
                throw;
            }
            catch (ServiceException)
            {
                throw;
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, $"General exception was thrown.");
                throw new ServiceException(HttpStatusCode.InternalServerError, Errors.GenericError);
            }

            return createdLead;
        }

        private async Task ValidateProductIdsAsync(Guid[] leadProductIds)
        {
            var product = await productService.GetProductsByIdsAsync(leadProductIds);
            CheckIfProductsAreForbidden(product);

            if (product == null || product.Length != leadProductIds.Length)
            {
                logger.LogError("Product not found for the current counterparty");
                throw new ServiceException(Errors.ProductNotFound);
            }
        }

        public async Task<List<Catalogue>> GetReferenceDataAsync()
        {
            var catalogNames = new string[] {
                Catalogues.AcquiringLedger,Catalogues.NbeCities,Catalogues.NbeCitiesToGovernarotes,Catalogues.NbeGovernarotes ,Catalogues.CitiesToGovernarotes,
                Catalogues.Cities ,Catalogues.Governorates,Catalogues.BusinessDomain,Catalogues.NbeBusinessDomain,
                Catalogues.AlxCities, Catalogues.AlxGovernarotes, Catalogues.AlxCitiesToGovernarotes, Catalogues.AlxBusinessDomain };
            var refData = await referenceService.GetCataloguesAsync(catalogNames, "EN");
            var refDataList = refData.ToList();

            return refDataList;
        }

        private static void UpdateRefernceDataWithCompinedCities(List<Catalogue> refData)
        {
            var compinedCityValues = refData.FirstOrDefault(r => r.CatalogueName == Common.Constants.Catalogues.NbeCities &&
            !string.IsNullOrEmpty(r.Value) && r.Value.Contains('/'));

            if (compinedCityValues != null && !string.IsNullOrEmpty(compinedCityValues.Value))
            {
                refData.Remove(compinedCityValues);

                var compinedCitiesValues = compinedCityValues.Value.Split('/');

                foreach (var c in compinedCitiesValues)
                {
                    var cityData = new Catalogue
                    {
                        Key = compinedCityValues.Key,
                        CatalogueName = compinedCityValues.CatalogueName,
                        Value = c
                    };

                    refData.Add(cityData);
                }
            }
        }

        private async Task<string> SetAcquiringLedger(LeadCreateRequest lead)
        {
            string acquiringLedger = string.Empty;
            var products = await productService.GetProductsByIdsAsync(lead.LeadProductIds.ToArray());
            var productCode = products.FirstOrDefault();

            switch (productCode?.Code)
            {
                case Common.Constants.ProductCode.GoAir:
                    acquiringLedger = Common.Constants.AcquiringLedger.NBE;
                    break;
                default:
                    acquiringLedger = Common.Constants.AcquiringLedger.MB;
                    break;
            }

            return acquiringLedger;
        }

        private void CheckIfProductsAreForbidden(Product[] product)
        {
            if (product.Any(x => x.Code == ForbiddenProductCodes.SpectraSp530 || x.Code == ForbiddenProductCodes.Vx675))
            {
                logger.LogError("Product(s) not allowed.");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.ProductNotAllowed);
            }
        }

        public async Task DeleteLeadCallAsync(Guid leadId)
        {
            string leadServiceUrl = $"{LeadApiBasePath}{LeadEndpoint}/{leadId}";

            using (logger.BeginScope("DeleteLeadCallAsync({@leadServiceUrl})", leadServiceUrl))
            {
                logger.LogInformation("Calling lead service to delete lead with id: '{@leadId}'.", leadId);

                var response = await client.DeleteAsync(leadServiceUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    logger.LogCritical("Error when calling lead service to delete lead. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }
            }
        }

        private async Task AssignBundleToReferralSpecificTypes(LeadCreateRequest leadCreateRequest)
        {
            var product = (await productService.GetProductsByIdsAsync(leadCreateRequest.LeadProductIds.ToArray())).FirstOrDefault();
            var catalogues = await referenceService.GetCataloguesAsync(new string[] { Catalogues.ProductCodeToReferralChannel, Catalogues.ReferralChannel }, "EN");
            var productCodeToReferral = catalogues.FirstOrDefault(x => x.CatalogueName == Catalogues.ProductCodeToReferralChannel && x.Key == product!.Code);
            var referralChannelWithProductsRestrictionList = catalogues.Where(x => x.CatalogueName == Catalogues.ProductCodeToReferralChannel).Select(x => x.Value).ToList();

            if (leadCreateRequest.ReferralChannel == ReferralChannel.UNASSIGNED && productCodeToReferral != null)
            {
                var referralChannel = catalogues.FirstOrDefault(x => x.CatalogueName == Catalogues.ReferralChannel && x.Key == productCodeToReferral!.Value);
                leadCreateRequest.ReferralChannel = referralChannel!.Key;
                return;
            }

            if ((productCodeToReferral != null && !referralChannelWithProductsRestrictionList.Contains(leadCreateRequest.ReferralChannel!)) ||
                (productCodeToReferral == null && referralChannelWithProductsRestrictionList.Contains(leadCreateRequest.ReferralChannel!)))
            {
                logger.LogError("The selected Bundle is not compatible with the ReferralChannel");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidBundleForReferralChannelBadRequest);
            }
        }


        private async Task RemoveLeadDataAsync(Guid leadId)
        {
            await documentService.DeleteDocumentsForLeadAsync(leadId);
            await DeleteLeadCallAsync(leadId);
        }

        private async Task ValidateLeadRequest(LeadCreateRequest leadCreateRequest)
        {
            string acquirer = string.Empty;

            var refData = await GetReferenceDataAsync();
            var refDataUtils = refData.Select(r => new ReferenceData { CatalogueName = r.CatalogueName, Key = r.Key, Value = r.Value }).ToList();

            if (counterpartyProvider.GetCode() == Geidea.Utils.Common.Constants.CounterpartyEgypt)
            {
                acquirer = await SetAcquiringLedger(leadCreateRequest);
                UpdateRefernceDataWithCompinedCities(refData);
            }

            var validationResult = new LeadCreateRequestValidator(counterpartyProvider.GetCode(), acquirer, refDataUtils).Validate(leadCreateRequest);

            if (!validationResult.IsValid)
            {
                var errorDescription = ErrorMessageCollection.FromValidationResult(validationResult);
                logger.LogError("Lead create length validation failed: {@errors}", errorDescription);

                throw new ValidationException(validationResult);
            }
        }


        private async Task<LeadWithDocumentsResponse> AddLeadDocumentsForSaudi(LeadWithDocumentsResponse lead, LeadCreateRequest leadCreateRequest, Guid userId)
        {
            foreach (var generalContractDocument in leadCreateRequest.GeneralContractDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.GeneralContract, generalContractDocument));

            foreach (var document in leadCreateRequest.MunicipalityDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.MunicipalityLicense, document));

            foreach (var document in leadCreateRequest.LegalEnterpriseDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.LegalEnterpriseLicense, document));

            foreach (var bankDocument in leadCreateRequest.BankDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.BankStatement, bankDocument));

            return lead;
        }

        private async Task<LeadWithDocumentsResponse> AddLeadDocumentsForEgypt(LeadWithDocumentsResponse lead, LeadCreateRequest leadCreateRequest, Guid userId)
        {
            foreach (var bankDocument in leadCreateRequest.NationalIdDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.NationalId, bankDocument));

            foreach (var freelancerDocument in leadCreateRequest.CommercialRegistrationDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.CommercialRegistration, freelancerDocument));

            foreach (var generalContractDocument in leadCreateRequest.ShopPhotoDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.ShopPhoto, generalContractDocument));

            foreach (var document in leadCreateRequest.AdditionalDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.AdditionalDocuments, document));

            foreach (var bankDocument in leadCreateRequest.BankDocuments)
                lead.LeadDocuments.Add(await SaveDocumentAsync(lead.LeadId, userId, DocumentType.BankStatement, bankDocument));

            return lead;
        }

        private async Task<DocumentMetadata> SaveDocumentAsync(Guid leadId, Guid userId, string documentType, IFormFile documentData)
        {
            var documentRequest = new DocumentRequest
            {
                LeadId = leadId,
                DocumentType = documentType,
                File = documentData,
                Language = "en",
                OwnerUserId = userId
            };

            return await documentService.CreateDocumentAsync(documentRequest);
        }

        private async Task CheckIfAccountExists(LeadCreateRequest leadCreateRequest)
        {
            var userExistsResponse = await userService.CheckUserExistsAsync(new UserExistsRequest { PhoneNumber = leadCreateRequest.PhoneNumber, CountryPrefix = leadCreateRequest.CountryPrefix });

            if (userExistsResponse.PhoneIsUsed == true)
            {
                logger.LogInformation($"There is already an account with the phone number: '{leadCreateRequest.PhoneWithPrefix}'.");
                throw new ServiceException(Errors.AccountAlreadyExists);
            }

            if (!string.IsNullOrEmpty(leadCreateRequest.OwnerEmail) && userExistsResponse.EmailIsUsed == true)
            {
                logger.LogInformation($"There is already an account with the email: '{leadCreateRequest.OwnerEmail}'.");
                throw new ServiceException(Errors.AccountEmailAlreadyExists);
            }
        }

        private async Task<Lead> CreateLeadCallAsync(LeadRequest leadRequest)
        {
            string leadServiceUrl = $"{LeadApiBasePath}{LeadEndpoint}";
            var requestBody = new StringContent(JsonConvert.SerializeObject(leadRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("CreateLeadAsync({@leadServiceUrl})", leadServiceUrl))
            {
                logger.LogInformation($"Calling lead service to create lead.");

                var response = await client.PostAsync(leadServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling lead service to create lead . Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<Lead>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        private void SendMessageToActiveCampaign(Lead lead)
        {
            try
            {
                var counterParty = counterpartyProvider.GetCode();
                var language = applicationLanguage.GetLanguage();

                var activeCampaignRequest = new CreateActiveCampaignRequest
                {
                    LeadId = lead.LeadId,
                    UserId = null,
                    MerchantId = null,
                    Language = language,
                    CounterParty = counterParty,
                    OnboardingStatus = UtilsConstants.ActiveCampaign.LeadCreated
                };

                activeCampaignService.SendActiveCampaignRequest(activeCampaignRequest);
            }
            catch (Exception ex)
            {
                logger.LogError($"Error when sending active campaign message to RabbitMQ, the error was {@ex}", ex.Message);
            }
        }

        private async Task PatchMerchantByLeadIdAsync(Guid leadId, JsonPatchDocument<Lead> leadPatch)
        {
            var merchantPatch = new JsonPatchDocument<Merchant>();

            if (leadPatch.Operations.Any(m => m.path.Equals(LeadPatch.LeadReferralChannel)))
            {
                var referralChannel = leadPatch.Operations
                    .FirstOrDefault(m => m.path.Equals(LeadPatch.LeadReferralChannel))?.value.ToString();

                merchantPatch.Replace(x => x.MerchantDetails!.ReferralChannel, referralChannel);
            }

            await merchantService.PatchMerchantByLeadIdAsync(leadId, merchantPatch);
        }

        private async Task CanUpdateLeadReferralChannel(Guid leadId, JsonPatchDocument<Lead> leadPatch)
        {
            if (!leadPatch.Operations.Any(m => m.path.Equals(LeadPatch.LeadReferralChannel)))
                return;

            var referralChannel = leadPatch.Operations
                .FirstOrDefault(m => m.path.Equals(LeadPatch.LeadReferralChannel))?.value.ToString();

            var referralChannelRestrictionsCatalog =
                await referenceService.GetCataloguesAsync(new[] { Catalogues.ReferralChannelRestrictions });

            if (referralChannelRestrictionsCatalog.Any(x => x.Key == referralChannel))
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.ReferralChannelUpdateBadRequest);
            }

            var lead = await GetLeadByIdAsync(leadId);

            if (referralChannelRestrictionsCatalog.Any(x => x.Key == lead?.ReferralChannel))
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.ReferralChannelUpdateBadRequest);
            }
        }

        private async Task<Lead> PatchLeadAsync(Guid leadId, JsonPatchDocument<Lead> leadPatch)
        {
            string leadServiceUrl = $"{LeadApiBasePath}{LeadEndpoint}/{leadId}";
            var body = new StringContent(JsonConvert.SerializeObject(leadPatch), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateLeadAsync({@leadServiceUrl})", leadServiceUrl))
            {
                logger.LogInformation("Calling lead service to update lead with id '{@leadId}'.", leadId);

                var response = await client.PatchAsync(leadServiceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical(
                        "Error when calling lead service update lead with id '{leadId}'. Error was {StatusCode} {@responseBody}",
                        leadId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation("Updated lead with id '{@leadId}'.", leadId);
                return Json.Deserialize<Lead>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }
    }
}
