﻿using Geidea.Utils.ReferenceData;
using System;
using System.Collections.Generic;

namespace Common.Models.NexusBridge;

public class MerchantCommissionConfig
{
    public Guid Id { get; set; }
    public Guid MerchantId { get; set; }
    public string? ProductCode { get; set; }
    public string? CommissionType { get; set; }
    public int? VatPercentage { get; set; }
    public string? IsInterchange { get; set; }
    public string? TransactionType { get; set; }


    public decimal Value { get; set; }

}