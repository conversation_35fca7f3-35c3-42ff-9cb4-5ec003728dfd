﻿using System;
using System.Collections.Generic;

namespace Common.Models.Merchant
{
    public class MerchantExportStore
    {
        public Guid MerchantId { get; set; }
        public Guid? LeadId { get; set; }
        public string? MerchantType { get; set; }
        public string? MerchantStatus { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public MerchantDetails StoreDetails { get; set; } = new MerchantDetails();
        public IReadOnlyCollection<MerchantAddress> StoreAddresses { get; set; } = new List<MerchantAddress>();
    }
}
