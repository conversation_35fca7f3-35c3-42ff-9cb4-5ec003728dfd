﻿using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Common.Helpers
{
    public static class UrlBuilder
    {
        public static string AddQueryParams(string baseUrl, object obj)
        {
            var result = new List<string>();
            var props = obj.GetType().GetProperties().Where(p => p.GetValue(obj, null) != null);
            foreach (var p in props)
            {
                var value = p.GetValue(obj, null);
                var enumerable = value as ICollection;
                if (enumerable != null)
                {
                    result.AddRange(from object v in enumerable select string.Format("{0}={1}", p.Name, HttpUtility.UrlEncode(v.ToString())));
                }
                else
                {
                    result.Add(string.Format("{0}={1}", p.Name, HttpUtility.UrlEncode(value?.ToString())));
                }
            }

            var queryParams = string.Join("&", result.ToArray());

            if (!string.IsNullOrEmpty(queryParams))
                return $"{baseUrl}?{queryParams}";
            return baseUrl;
        }
    }
}