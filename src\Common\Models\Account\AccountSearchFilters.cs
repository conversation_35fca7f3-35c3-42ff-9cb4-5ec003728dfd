﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Account;

[ExcludeFromCodeCoverage]
public class AccountSearchFilters : BaseSearchCriteria
{
    public string? Mid { get; set; }
    public string? BusinessName { get; set; }
    public string? BusinessId { get; set; }
    public string[]? ChannelType { get; set; }
    public List<string>? MerchantStatus { get; set; } = new List<string>();
    public List<string>? AccountStatus { get; set; } = new List<string>();
    public DateInterval? DateInterval { get; set; }
    public string? Keyword { get; set; }
    public string[]? SearchIn { get; set; }
    public bool IsSalesTeam { get; set; } = false;
    public List<string>? DesignationSalesId { get; set; }
}
