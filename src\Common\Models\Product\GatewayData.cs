﻿using System;
using System.Collections.Generic;
using Geidea.Messages.GatewayConfiguration;

namespace Common.Models.Product
{
    public class GatewayData
    {
        public string? MerchantGatewayKey { get; set; }
        public string? MpgsMsoProvider { get; set; }
        public string? MpgsMerchantId { get; set; }
        public string? MpgsApiKey { get; set; }
        public string? GsdkTid { get; set; }
        public string? GsdkMid { get; set; }
        public string? GsdkSecretKey { get; set; }
        public string? CyberSourceApiIdentifier { get; set; }
        public string? CyberSourceApiKey { get; set; }
        public string? CyberSourceMerchantId { get; set; }
        public string? CyberSourceMerchantKeyId { get; set; }
        public string? CyberSourceOrgUnitId { get; set; }
        public string? CyberSourceSharedSecretKey { get; set; }
        public string DefaultPaymentOperation { get; set; } = "Pay";
        public string? ApiPassword { get; set; }
        public string? ApplePartnerInternalMerchantIdentifier { get; set; }
        public string? AppleCsr { get; set; }
        public string? AppleCertificatePrivateKey { get; set; }
        public string? ApplePaymentProcessingCertificate { get; set; }
        public DateTime? ApplePaymentProcessingCertificateExpiryDate { get; set; }
        public string? AppleCertificatePrivateKeyNew { get; set; }
        public string? ApplePaymentProcessingCertificateNew { get; set; }
        public DateTime? ApplePaymentProcessingCertificateExpiryDateNew { get; set; }
        public string? AppleDeveloperId { get; set; }
        public bool IsMeezaDigitalEnabled { get; set; }
        public bool IsApplePayWebEnabled { get; set; }
        public bool IsApplePayMobileEnabled { get; set; }
        public bool IsApplePayMobileCertificateAvailable { get; set; }
        public string MerchantName { get; set; } = string.Empty;
        public string? MerchantNameAr { get; set; }
        public string? MerchantWebsite { get; set; }
        public string? MerchantDomain => ExtractDomainNameFromURL(MerchantWebsite);
        public string? MerchantLogoUrl { get; set; }
        public string? MerchantCountry { get; set; }
        public string? CallbackUrl { get; set; }
        public bool IsTest { get; set; } = true;
        public bool IsLuhnCheckActive { get; set; } = true;
        public bool IsTokenizationEnabled { get; set; } = true;
        public bool IsCallbackEnabled { get; set; } = true;
        public bool IsPaymentMethodSelectionEnabled { get; set; }
        public bool IsTransactionReceiptEnabled { get; set; }
        public bool IsFederationToGsdkEnabled { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsRefundEnabled { get; set; } = true;
        public bool UseMpgsApiV60 { get; set; }
        public bool MerchantPaymentNotification { get; set; }
        public bool CustomerPaymentNotification { get; set; }
        public bool IsCallbackEmailNotificationEnabled { get; set; } = true;
        public string? MerchantNotificationEmail { get; set; }
        public string? CustomerNotificationFromEmail { get; set; }
        public string? CallbackNotificationEmail { get; set; }
        public IReadOnlyCollection<string> AllowedInitiatedByValues { get; set; } = new List<string>();
        public IReadOnlyCollection<string> Currencies { get; set; } = new List<string>();
        public IReadOnlyCollection<CardBrandProvider> CardBrandProviders { get; set; } = new List<CardBrandProvider>();

        public static string? ExtractDomainNameFromURL(string? url)
        {
            if (url == null)
                return null;

            try
            {
                return new Uri(url.Contains("://") ? url : ("http://" + url), UriKind.Absolute).Host;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}
