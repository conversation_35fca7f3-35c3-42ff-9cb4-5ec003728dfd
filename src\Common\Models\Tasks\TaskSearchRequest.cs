﻿using System;
using System.Collections.Generic;

namespace Common.Models.Tasks
{
    public class TaskSearchRequest
    {
        public string? Keyword { get; set; }

        public string[]? SearchIn { get; set; }

        public int Skip { get; set; } = 0;

        public int Take { get; set; } = 10;

        public string? OrderBy { get; set; }

        public string Sort { get; set; } = "desc";

        public Guid? MerchantId { get; set; }

        public DateInterval? DateInterval { get; set; }

        public List<Guid?>? Assignees { get; set; }

        public List<string>? Statuses { get; set; }

        public List<string>? Types { get; set; }
    }
}
