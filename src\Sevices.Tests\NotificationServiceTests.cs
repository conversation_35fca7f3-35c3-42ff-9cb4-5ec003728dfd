﻿using Common;
using Common.Models;
using Common.Options;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.Net;
using static Common.Constants;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class NotificationServiceTests
    {
        private readonly Mock<ILogger<NotificationService>> logger = new Mock<ILogger<NotificationService>>();
        private NotificationService notificationService = null!;
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
        private readonly Mock<IOptionsMonitor<StatusChangeAuToEmailConfiguration>> autoEmailOptions = new Mock<IOptionsMonitor<StatusChangeAuToEmailConfiguration>>();

        public NotificationServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
            autoEmailOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.StatusChangeAuToEmailConfiguration);
        }

        private readonly SendEmailInfoCheckRequest sendEmailInfoCheckRequest = new SendEmailInfoCheckRequest()
        {
            Subject = "Subject",
            CheckType = "CheckType",
            EmailAddress = "<EMAIL>",
            Comments = "Comment"
        };
        private readonly SendStatusUpdateEmailRequest sendStatusUpdateEmailRequest = new SendStatusUpdateEmailRequest()
        {
            Language = "EN",
            RecipientName = "Recipient",
            Recipient = "<EMAIL>",
            CurrentStatus = "DocumentCheck",
            BusinessId = "123456",
            OrderId = "123",
            ChannelType = ChannelType.CardPresent
        };

        private readonly SendSmsInfoCheckRequest sendSmsInfoCheckRequest = new SendSmsInfoCheckRequest()
        {
            CheckType = "CheckType",
            PhoneNumber = "+***********",
            Comments = "Comment"
        };

        private readonly SendCustomSmsRequest sendCustomSmsRequest = new SendCustomSmsRequest()
        {
            Body = "Body",
            Recipients = new string[] { "+***********" }
        };

        private readonly SendCustomEmailRequest sendCustomEmailRequest = new SendCustomEmailRequest()
        {
            Subject = "Subject",
            Body = "Body",
            Recipients = new string[] { "+***********" }
        };

        private readonly SendWelcomeEmialRequest sendWelcomeEmialRequest = new SendWelcomeEmialRequest()
        {
            Language = "en",
            Recipient = "<EMAIL>",
            RecipientName = "testName"
        };

        [Test]
        public void SendEmailInfoCheckAsync()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                 autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendEmailInfoCheckAsync(sendEmailInfoCheckRequest))
                .Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void SendBusinessStatusChangeEmailAsync()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                 autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendEmailForBusinessStatusChange(sendStatusUpdateEmailRequest))
                .Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void TriggerAutoEmailForStatusChange_BoardingCompleted()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                 autoEmailOptions.Object);

            notificationService.Invoking(x => x.TriggerAutoEmailForStatusChange(MerchantStatus.BoardingCompleted, sendStatusUpdateEmailRequest))
                .Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void TriggerAutoEmailForStatusChange_ComplianceApproval()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                 autoEmailOptions.Object);

            notificationService.Invoking(x => x.TriggerAutoEmailForStatusChange(MerchantStatus.ComplianceApproval, sendStatusUpdateEmailRequest))
                .Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void TriggerAutoEmailForStatusChange_RiskApproval()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                 autoEmailOptions.Object);

            notificationService.Invoking(x => x.TriggerAutoEmailForStatusChange(MerchantStatus.RiskApproval, sendStatusUpdateEmailRequest))
                .Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void TriggerAutoEmailForStatusChange_OrderStatus_Verified()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                 autoEmailOptions.Object);

            notificationService.Invoking(x => x.TriggerAutoEmailForStatusChange(OrderStatus.Verified, sendStatusUpdateEmailRequest))
                .Should().NotThrowAsync<Exception>();
        }
        [Test]
        public void SendOrderStatusChangeEmailAsync()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                 autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendEmaillForOrderStatusChange(sendStatusUpdateEmailRequest))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void SendEmailInfoCheckAsync_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest),
                autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendEmailInfoCheckAsync(sendEmailInfoCheckRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public void SendBusinessStatusChangeEmailAsync_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest),
                autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendEmailForBusinessStatusChange(sendStatusUpdateEmailRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public void SendOrderStatusChangeEmailAsync_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest),
                autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendEmaillForOrderStatusChange(sendStatusUpdateEmailRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendEmailInfoCheckAsync_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                autoEmailOptions.Object);

            var invalidRequest = new SendEmailInfoCheckRequest()
            {
                Subject = "",
                CheckType = "",
                EmailAddress = ""
            };

            notificationService.Invoking(x => x.SendEmailInfoCheckAsync(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public void SendBusinessStatusChangeEmailAsync_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                autoEmailOptions.Object);

            var invalidRequest = new SendStatusUpdateEmailRequest()
            {
                CurrentStatus = "",
                Recipient = "",
                RecipientName = "",
                Language = "",
                BusinessId = "",
                OrderId = ""
            };

            notificationService.Invoking(x => x.SendEmailForBusinessStatusChange(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public void SendOrderStatusChangeEmailAsync_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                autoEmailOptions.Object);

            var invalidRequest = new SendStatusUpdateEmailRequest()
            {
                CurrentStatus = "",
                Recipient = "",
                RecipientName = "",
                Language = "",
                BusinessId = "",
                OrderId = ""
            };

            notificationService.Invoking(x => x.SendEmaillForOrderStatusChange(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendSmsInfoCheckAsync()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendSmsInfoCheckAsync(sendSmsInfoCheckRequest))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void SendSmsInfoCheckAsync_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest),
                autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendSmsInfoCheckAsync(sendSmsInfoCheckRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendSmsInfoCheckAsync_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object
                );

            var invalidRequest = new SendSmsInfoCheckRequest()
            {
                CheckType = "",
                PhoneNumber = ""
            };

            notificationService.Invoking(x => x.SendSmsInfoCheckAsync(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendCustomEmailAsync()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendCustomEmailAsync(sendCustomEmailRequest))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void SendCustomEmailAsync_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendCustomEmailAsync(sendCustomEmailRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendCustomEmailAsync_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            var invalidRequest = new SendCustomEmailRequest()
            {
                Subject = "",
                Body = "",
                Recipients = new string[] { }
            };

            notificationService.Invoking(x => x.SendCustomEmailAsync(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendCustomSmsAsync()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendCustomSmsAsync(sendCustomSmsRequest))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void SendCustomSmsAsync_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendCustomSmsAsync(sendCustomSmsRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendCustomSmsAsync_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            var invalidRequest = new SendCustomSmsRequest()
            {
                Body = "",
                Recipients = new string[] { }
            };

            notificationService.Invoking(x => x.SendCustomSmsAsync(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        [TestCase("usqtavlnvhomxloknhykzgbhmeymhnjgqgsmqrnhglfgmnyowuvmzyvnfaxpejpmqomfdnmlobavbrfdyxdemwhxziepvstrreqguvtomknarfpzdpczysimflwukwaax", "CheckType", "Comments", "<EMAIL>")]
        [TestCase("Subject", "tmlygfsrctjihufgwvvhdtliszjnhzhkv", "Comments", "<EMAIL>")]
        [TestCase("Subject", "CheckType", "yfkedqprvykbcbwjoljdjtjwtjklzqsuxerqnlubxatqgbqjvrknyaditunxkzskxiisljcqvtaulzqugtliacoucwtcfexvbshnzelrhtxcszwzblggoteskrmgfszhuzyshlbflnllcxzseubqacwqsoringcjqvoriubaramtfvunxyhyxflkgcfjzcsnghdnzuhrmuxutfaynhuacawxvioweiyuvedkkpecqnuwgxjrjhkdtzzclezkohrzikhdjalsolngcgwyzpvlektaqdfbyjqhhqddbdsjusygiagqdzosajwwvhmcrilayrgkdbgfavoddpzglnykshvbdjcarqptgobkezxlsikhphdqhrevfqcczazwlndkskcqrbveelfosqhlgohbnhcyzyslvkiuzwhfogrwxcsinamcpesjumrszwlndbytuqevnpxkkgutjamktcdfmivyifymalcaulvgcqjqlwikjkjgvzzoywcmngnjgwtld", "<EMAIL>")]
        [TestCase("Subject", "CheckType", "Comments", "<EMAIL>")]
        public void SendEmailInfoCheckAsync_SendEmailInfoCheckRequestValidator_ShouldThrowValidationException(string subject, string checkType, string comments, string emailAddress)
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            var sendEmailCheckRequest = new SendEmailInfoCheckRequest()
            {
                Subject = subject,
                CheckType = checkType,
                Comments = comments,
                EmailAddress = emailAddress,
            };

            notificationService.Invoking(x => x.SendEmailInfoCheckAsync(sendEmailCheckRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.SubjectLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CheckTypeLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CommentLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.EmailLengthValidation.Code)));
        }

        [Test]
        public void SendEmailInfoCheckAsync_SendEmailInfoCheckRequestValidator_ShouldNotThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendEmailInfoCheckAsync(sendEmailInfoCheckRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("tmlygfsrctjihufgwvvhdtliszjnhzhkv", "Comments", "+***********")]
        [TestCase("CheckType", "yfkedqprvykbcbwjoljdjtjwtjklzqsuxerqnlubxatqgbqjvrknyaditunxkzskxiisljcqvtaulzqugtliacoucwtcfexvbshnzelrhtxcszwzblggoteskrmgfszhuzyshlbflnllcxzseubqacwqsoringcjqvoriubaramtfvunxyhyxflkgcfjzcsnghdnzuhrmuxutfaynhuacawxvioweiyuvedkkpecqnuwgxjrjhkdtzzclezkohrzikhdjalsolngcgwyzpvlektaqdfbyjqhhqddbdsjusygiagqdzosajwwvhmcrilayrgkdbgfavoddpzglnykshvbdjcarqptgobkezxlsikhphdqhrevfqcczazwlndkskcqrbveelfosqhlgohbnhcyzyslvkiuzwhfogrwxcsinamcpesjumrszwlndbytuqevnpxkkgutjamktcdfmivyifymalcaulvgcqjqlwikjkjgvzzoywcmngnjgwtld", "+***********")]
        [TestCase("CheckType", "Comments", "+***********4444423233333")]
        public void SendSmsInfoCheckAsync_SendSmsInfoCheckRequestValidator_ShouldThrowValidationException(string checkType, string comments, string phoneNumber)
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            var sendSmsCheckRequest = new SendSmsInfoCheckRequest()
            {
                CheckType = checkType,
                Comments = comments,
                PhoneNumber = phoneNumber
            };

            notificationService.Invoking(x => x.SendSmsInfoCheckAsync(sendSmsCheckRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.CheckTypeLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.CommentLengthValidation.Code)
                            || TestsHelper.HasErrorCode(x, Errors.PhoneLengthValidation.Code)));
        }

        [Test]
        public void SendSmsInfoCheckAsync_SendSmsInfoCheckRequestValidator_ShouldNotThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendSmsInfoCheckAsync(sendSmsInfoCheckRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("usqtavlnvhomxloknhykzgbhmeymhnjgqgsmqrnhglfgmnyowuvmzyvnfaxpejpmqomfdnmlobavbrfdyxdemwhxziepvstrreqguvtomknarfpzdpczysimflwukwaax")]
        public void SendCustomEmailAsync_SendCustomEmailRequestValidator_ShouldThrowValidationException(string subject)
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            var sendCustomEmail = new SendCustomEmailRequest()
            {
                Subject = subject,
                Body = "Body",
                Recipients = new string[] { "+***********" }
            };

            notificationService.Invoking(x => x.SendCustomEmailAsync(sendCustomEmail)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.SubjectLengthValidation.Code));
        }

        [Test]
        public void SendCustomEmailAsync_SendCustomEmailRequestValidator_ShouldNotThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendCustomEmailAsync(sendCustomEmailRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("yfkedqprvykbcbwjoljdjtjwtjklzqsuxerqnlubxatqgbqjvrknyaditunxkzskxiisljcqvtaulzqugtliacoucwtcfexvbshnzelrhtxcszwzblggoteskrmgfszhuzyshlbflnllcxzseubqacwqsoringcjqvoriubaramtfvunxyhyxflkgcfjzcsnghdnzuhrmuxutfaynhuacawxvioweiyuvedkkpecqnuwgxjrjhkdtzzclezkohrzikhdjalsolngcgwyzpvlektaqdfbyjqhhqddbdsjusygiagqdzosajwwvhmcrilayrgkdbgfavoddpzglnykshvbdjcarqptgobkezxlsikhphdqhrevfqcczazwlndkskcqrbveelfosqhlgohbnhcyzyslvkiuzwhfogrwxcsinamcpesjumrszwlndbytuqevnpxkkgutjamktcdfmivyifymalcaulvgcqjqlwikjkjgvzzoywcmngnjgwtld")]
        public void SendCustomSmsAsync_SendCustomSmsRequestValidator_ShouldThrowValidationException(string body)
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            var sendCustomSmsRequest = new SendCustomSmsRequest()
            {
                Body = body,
                Recipients = new string[] { "+***********" }
            };

            notificationService.Invoking(x => x.SendCustomSmsAsync(sendCustomSmsRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.BodyLengthValidation.Code));
        }

        [Test]
        public void SendCustomSmsAsync_SendCustomSmsRequestValidator_ShouldNotThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendCustomSmsAsync(sendCustomSmsRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        public void SendWelcomeEmialForCPMerchantAccount()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendWelcomeEmialForCPMerchantAccount(sendWelcomeEmialRequest))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void SendWelcomeEmialForCPMerchantAccount_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendWelcomeEmialForCPMerchantAccount(sendWelcomeEmialRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendWelcomeEmialForCPMerchantAccount_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            var invalidRequest = new SendWelcomeEmialRequest()
            {
                RecipientName = "",
                Recipient = ""
            };

            notificationService.Invoking(x => x.SendWelcomeEmialForCPMerchantAccount(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public void SendWelcomeEmialForCPMerchantAccount_SendWelcomeEmialRequestValidator_ShouldThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            var sendCustomEmail = new SendWelcomeEmialRequest()
            {
                RecipientName = "",
                Recipient = ""
            };

            notificationService.Invoking(x => x.SendWelcomeEmialForCPMerchantAccount(sendCustomEmail)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.SubjectLengthValidation.Code));
        }

        [Test]
        public void SendWelcomeEmialForCPMerchantAccount_SendWelcomeEmialRequestValidator_ShouldNotThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendWelcomeEmialForCPMerchantAccount(sendWelcomeEmialRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }
        [Test]
        public void SendWelcomeEmialForCNPMerchantAccount()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendWelcomeEmialForCPMerchantAccount(sendWelcomeEmialRequest))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void SendWelcomeEmialForCNPMerchantAccount_PassthroughtError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendWelcomeEmialForCNPMerchantAccount(sendWelcomeEmialRequest))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public void SendWelcomeEmialForCNPMerchantAccount_ValidationError()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            var invalidRequest = new SendWelcomeEmialRequest()
            {
                RecipientName = "",
                Recipient = ""
            };

            notificationService.Invoking(x => x.SendWelcomeEmialForCNPMerchantAccount(invalidRequest))
                .Should().ThrowAsync<ValidationException>()
                        .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public void SendWelcomeEmialForCNPMerchantAccount_SendWelcomeEmialRequestValidator_ShouldThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest), autoEmailOptions.Object);

            var sendCustomEmail = new SendWelcomeEmialRequest()
            {
                RecipientName = "",
                Recipient = ""
            };

            notificationService.Invoking(x => x.SendWelcomeEmialForCNPMerchantAccount(sendCustomEmail)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && TestsHelper.HasErrorCode(x, Errors.SubjectLengthValidation.Code));
        }

        [Test]
        public void SendWelcomeEmialForCNPMerchantAccount_SendWelcomeEmialRequestValidator_ShouldNotThrowValidationException()
        {
            notificationService = new NotificationService(
                logger.Object,
                urlSettingsOptions.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK), autoEmailOptions.Object);

            notificationService.Invoking(x => x.SendWelcomeEmialForCNPMerchantAccount(sendWelcomeEmialRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }
    }
}
