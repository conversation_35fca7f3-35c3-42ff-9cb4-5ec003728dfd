﻿using Common.Models.Sales;
using Common.Models.User;
using Common.Options;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class UserServiceTests
    {
        private readonly Mock<ILogger<UserService>> logger = new Mock<ILogger<UserService>>();
        private UserService userService = null!;
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();

        private readonly UserShortResponse userShortResponse = new UserShortResponse()
        {
            Id = Guid.NewGuid(),
            FirstName = "FirstName",
            LastName = "LastName"
        };
        private readonly Guid userId = new Guid();
        private readonly UserSearchParameters userSearchParameters = new UserSearchParameters
        {
            LeadId = Guid.NewGuid(),
            UserId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            Email = "<EMAIL>",
            PhoneNumber = "722585849",
            CountryPrefix = "+40",
            Groups = new List<string> { "firstGroup", "secondGroup" }
        };
        private readonly UserResponse userWithRoles = new UserResponse
        {
            UserId = Guid.NewGuid(),
            SalesId = "1234"
        };

        public UserServiceTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        }

        [Test]
        public async Task GetUsersAsync_Return_User()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new UserShortResponse[] { userShortResponse })),
                urlSettingsOptions.Object);

            var result = await userService.GetUsersAsync(Guid.NewGuid());

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(userShortResponse);
        }

        [Test]
        public void GetUsersAsync_PassthroughtError()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.GetUsersAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void RemoveRolesForMerchantAsync()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.RemoveRolesForMerchantAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void RemoveRolesForMerchantAsync_PassthroughtError()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.RemoveRolesForMerchantAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetAllUsersAsync_Returns_Users()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new UserShortResponse[] { userShortResponse })),
                urlSettingsOptions.Object);

            var result = await userService.GetAllUsersAsync();

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(userShortResponse);
        }

        [Test]
        public void GetAllUsersAsync_PassthroughError()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.GetAllUsersAsync())
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void ActivateUserAsync_WhenCoreServiceReturnsSuccessStatusCode_DoesNotThrowException()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NoContent, string.Empty),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.ActivateUserAsync(userId))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void ActivateUserAsync_WhenCoreServiceReturnsError_ThrowsPassthroughException()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.ActivateUserAsync(userId))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DisableUserAsync_WhenCoreServiceReturnsSuccessStatusCode_DoesNotThrowException()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NoContent, string.Empty),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.ActivateUserAsync(userId))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DisableUserAsync_WhenCoreServiceReturnsError_ThrowsPassthroughException()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.DisableUserAsync(userId))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void PatchUserAsync_WhenCoreServiceReturnsSuccessStatusCode_DoesNotThrowException()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NoContent, string.Empty),
                urlSettingsOptions.Object);

            var patch = new JsonPatchDocument<UpdateBackOfficeUserRequest>();
            patch.Add(x => x.RefBankId, 1);

            userService.Invoking(x => x.PatchBackOfficeUserAsync(userId, patch))
                .Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void PatchUserAsync_WhenCoreServiceReturnsError_ThrowsPassthroughException()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            var patch = new JsonPatchDocument<UpdateBackOfficeUserRequest>();
            patch.Add(x => x.RefBankId, 1);

            userService.Invoking(x => x.PatchBackOfficeUserAsync(userId, patch))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task SearchUsersAsync_WhenUsingValidDate_ReturnsUsers()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new UserResponse[] { userWithRoles })),
                urlSettingsOptions.Object);

            var result = await userService.SearchUsersAsync(userSearchParameters);

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(userWithRoles);
        }

        [Test]
        public void SearchUsersAsync_WhenCoreServiceThrowsException_ThrowsPassthroughError()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.SearchUsersAsync(userSearchParameters))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task UpdateUserSalesIdAsync_WhenSuccessResponseFromUserService_NoPassthroughtError()
        {
            var userSalesIdResponse = new UserSalesIdResponse { UserId = Guid.NewGuid(), InitialSalesId = "GDS0001", UpdatedSalesId = "GDS0002" };
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(userSalesIdResponse)),
                urlSettingsOptions.Object);

            var result = await userService.UpdateUserSalesIdAsync(new UserSalesIdRequest { UserId = Guid.NewGuid(), UpdatedSalesId = "GDS0002" });
            result.Should().BeEquivalentTo(userSalesIdResponse);
        }

        [Test]
        public void UpdateUserSalesIdAsync_WhenErrorResponseFromUserService_PassthroughtError()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.InternalServerError),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.UpdateUserSalesIdAsync(new UserSalesIdRequest { UserId = Guid.NewGuid(), UpdatedSalesId = "GDS0002" }))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.InternalServerError);
        }

        [Test]
        public async Task GetUserByIdAsync_WhenSuccessResponseFromUserService_ReturnsUser()
        {
            var userResponse = new User { Id = Guid.NewGuid(), SalesId = "GDS0001", Email = "<EMAIL>", PhoneNumber = "745745", CountryPrefix = "+966", IsEmailConfirmed = true, FirstName = "Ion", LastName = "Popescu" };
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(userResponse)),
                urlSettingsOptions.Object);

            var result = await userService.GetUserByIdAsync(userResponse.Id);
            result.Should().BeEquivalentTo(userResponse);
        }

        [Test]
        public void GetUserByIdAsync_WhenErrorResponseFromUserService_PassthroughtError()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.InternalServerError),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.GetUserByIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.InternalServerError);
        }

        [Test]
        public async Task CheckUserExistsAsync_WhenCoreServiceReturnsData_ShouldReturnData()
        {
            var coreServiceResponse = new UserExistsResponse { EmailIsUsed = true, PhoneIsUsed = false };

            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(coreServiceResponse)),
                urlSettingsOptions.Object);

            var response = await userService.CheckUserExistsAsync(new UserExistsRequest { Email = "<EMAIL>", PhoneNumber = "1234567", CountryPrefix = "+966" });
            response.Should().BeEquivalentTo(coreServiceResponse);
        }

        [Test]
        public void CheckUserExistsAsync_WhenCoreServiceReturnsError_ShouldThrowPassthroughtError()
        {
            userService = new UserService(
                logger.Object,
                TestsHelper.CreateHttpClient(HttpStatusCode.NotFound),
                urlSettingsOptions.Object);

            userService.Invoking(x => x.CheckUserExistsAsync(new UserExistsRequest { Email = "<EMAIL>", PhoneNumber = "1234567", CountryPrefix = "+966" }))
                .Should().ThrowAsync<PassthroughException>()
                        .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }
    }
}
