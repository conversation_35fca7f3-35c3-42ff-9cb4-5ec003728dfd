﻿namespace Common.Models.Product
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    public class ProductInstance
    {
        private object? metadata;

        public List<ProductInstance> Children { get; set; } = new List<ProductInstance>();
        public object? Data { get; set; }
        public Guid? ParentId { get; set; }
        public bool EPosTicketCompleted { get; set; }
        public string? EPosTicketId { get; set; }
        public bool? EPosBillPayments { get; set; }

        //[NotMapped]
        public object? Metadata
        {
            get
            {
                if (metadata == null && Data != null &&
                    (Product.Type == "M_POS" || Product.Type == "TERMINAL"))
                {
                    metadata = JsonConvert.DeserializeObject(Data.ToString() ?? string.Empty, typeof(TerminalData)) as TerminalData;
                }
                if (metadata == null && Data != null && Product.Type == "GWAY")
                {
                    metadata = JsonConvert.DeserializeObject(Data.ToString() ?? string.Empty, typeof(GatewayData)) as GatewayData;
                }
                return metadata;
            }
        }

        public ProductShortResponse Product { get; set; } = null!;
        public Guid ProductInstanceId { get; set; }
        public bool DeletedFlag { get; set; }

        public string? Mid { get; set; }
    }
}