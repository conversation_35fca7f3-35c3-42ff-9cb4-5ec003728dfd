﻿using Microsoft.AspNetCore.Http;
using System;

namespace Common.Models.Document
{
    public class DocumentRequest
    {
        public Guid OwnerUserId { get; set; }
        public Guid? MerchantId { get; set; }
        public Guid? PersonOfInterestId { get; set; }
        public Guid? LeadId { get; set; }
        public string? DocumentType { get; set; }
        public string? Language { get; set; }
        public Guid? ProviderId { get; set; }
        public IFormFile File { get; set; } = null!;
    }
}
