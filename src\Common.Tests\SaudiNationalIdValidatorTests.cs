﻿using FluentValidation.TestHelper;

namespace Common.Tests;

public class SaudiNationalIdValidatorTests
{
    [TestCase("")]
    [TestCase(" ")]
    [TestCase("This is a very long first name and should really not be allowed as it is well over 10 ch.")]
    [TestCase("Less")]
    public void SaudiNationalIdValidator_InvalidSaudiNationalId_ShouldFail(string nationalId)
    {
        var result = new SaudiNationalIdValidator().TestValidate(nationalId);

        Assert.That(result.IsValid, Is.False);
    }

    [TestCase("1234567890")]
    public void SaudiNationalIdValidator_ValidSaudiNationalId_ShouldSucceede(string nationalId)
    {
        var result = new SaudiNationalIdValidator().TestValidate(nationalId);

        Assert.That(result.IsValid, Is.True);
    }
}
