﻿using System;
using System.Collections.Generic;

namespace Common.Models.Checkout
{
    public class OrderResponse
    {
        public Guid OrderId { get; set; }

        public Guid? AgreementId { get; set; }

        public Guid MerchantId { get; set; }

        public Guid StoreId { get; set; }
        
        public Guid UserId { get; set; }

        public Guid? AddressId { get; set; }

        public string OrderStatus { get; set; } = null!;

        public string? MerchantStatus { get; set; }

        public List<OrderItemResponse>? OrderItem { get; set; }

        public string? PaymentReference { get; set; }

        public string? TrackingNumber { get; set; }

        public string? TrackingUrl { get; set; }

        public string? Shipper { get; set; }

        public DateTime? ShippedDate { get; set; }

        public string? CouponCode { get; set; }

        public string? PaymentMethod { get; set; }

        public string? CompanyRegNo { get; set; }

        public string? SalesName { get; set; }

        public string? SubscriptionPlan { get; set; }

        public string? ProjectName { get; set; }

        public string? ReferralChannel { get; set; }

        public string? Note { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string? OrderNumber { get; set; }

        public int OrderCommentsCount { get; set; }

        public bool DeletedFlag { get; set; }

        public string MerchantName { get; set; } = null!;

        public DateTime? CheckoutDate { get; set; }

        public int? Subtotal { get; set; }

        public int? Discount { get; set; }

        public int? VatPercent { get; set; }

        public int? Vat { get; set; }

        public int? Total { get; set; }

        public int? MonthlySubtotal { get; set; }

        public int? MonthlyVat { get; set; }

        public int? MonthlyTotal { get; set; }

        public int? MonthlyDiscount { get; set; }

        public int? YearlySubtotal { get; set; }

        public int? YearlyVat { get; set; }

        public int? YearlyTotal { get; set; }

        public int? YearlyDiscount { get; set; }

        public string? Currency { get; set; }

        public string? BillPayments { get; set; }
    }
}
