﻿using Common.Models;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators
{
    public class SendWelcomeEmialRequestValidator : AbstractValidator<SendWelcomeEmialRequest>
    {
        public SendWelcomeEmialRequestValidator()
        {
            RuleFor(a => a.RecipientName).NotNull().NotEmpty();

            RuleFor(a => a.Recipient).NotNull().NotEmpty();
        }
    }
}
