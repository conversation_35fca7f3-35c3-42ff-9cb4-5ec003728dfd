﻿using System;
using System.Collections.Generic;
using Common.Models.Shareholder;
using System.Threading.Tasks;

namespace Common.Services;

public interface IShareholderService
{
    Task<List<MerchantShareholderCompanyResponse>> GetShareholderCompaniesAsync(ShareholderCompaniesRequest shareholderCompaniesRequest);
    Task<List<ShareholderCompanyResponse>> SearchShareholderCompaniesAsync(ShareholderCompaniesRequest shareholderCompaniesRequest);
    Task<ShareholderCompanyResponse> CreateShareholderCompanyAsync(ShareholderCompanyCreateRequest request);
    Task<List<MerchantShareholderIndividual>> GetMerchantIndividualsAsync(Guid merchantId);
    Task CreateShareholderCompanyMerchantAssociationAsync(ShareholderCompanyMerchantAssociationRequest request);
    Task CreateShareholderIndividualAsync(ShareholderIndividualCreateRequest request);
    Task<List<MerchantIndividualCore>> SearchShareholderIndividualsAsync(ShareholderIndividualsSearchRequest searchRequest);
    Task<MerchantShareholderCompanyResponse> PatchShareholderCompanyAsync(ShareholderCompanyPatchRequest request);
    Task CreateShareholderIndividualAssociations(ShareholderIndividualAssociationsCreateRequest request);
    Task PatchShareholderIndividualAsync(ShareholderIndividualPatchRequest request);
    Task<ShareholderIndividualEditableStatus> GetShareholderIndividualEditableStatusAsync(Guid shareholderIndividualId);
}