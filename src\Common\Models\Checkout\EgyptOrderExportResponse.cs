﻿using System.Collections.Generic;

namespace Common.Models.Checkout
{
    public class EgyptOrderExportResponse
    {
        public string? BusinessName { get; set; }
        public string? OrderNumber { get; set; }
        public string? OrderStatus { get; set; }
        public string? MerchantStatus { get; set; }
        public string? MemberId { get; set; }
        public string? Phone { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? NationalId { get; set; }
        public string? Email { get; set; }
        public string? AccountHolderName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? IBAN { get; set; }
        public string? BankName { get; set; }
        public string? BusinessDomain { get; set; }
        public string? MCC { get; set; }
        public string? MerchantType { get; set; }
        public string? Governorate { get; set; }
        public string? BusinessAddress { get; set; }
        public string? CommercialRegistration { get; set; }
        public string? SalesId { get; set; }
        public string? SalesPersonFirstName { get; set; }
        public string? SalesPersonLastName { get; set; }
        public int? Quantity { get; set; }
        public string? DeliveryMethod { get; set; }
        public short? DeliveryDays { get; set; }
        public bool? ProofOfDelivery { get; set; }
        public string? BillPayments { get; set; }
        public List<EgyptProductExportResponse> ProductExport { get; set; } = new List<EgyptProductExportResponse>();
        public string? Acquirer { get; set; }
        public string? City { get; set; }
        public string? StoreCity { get; set; }
    }
}
