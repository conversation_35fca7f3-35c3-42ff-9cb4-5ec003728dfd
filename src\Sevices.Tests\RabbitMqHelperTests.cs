﻿namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    using NSubstitute;
    using NUnit.Framework;
    using GeideaPaymentGateway.Utils.RabbitMQ;

    public class RabbitMqHelperTests
    {
        [Test]
        public void RabbitMqHelperCreatesConnectionFactorySuccessfully()
        {
            var rabbitMqConfig = Substitute.For<RabbitMqConfig>();

            Assert.DoesNotThrow(() => RabbitMqHelper.CreateConnectionFactory(rabbitMqConfig));
        }
    }
}
