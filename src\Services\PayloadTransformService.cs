﻿using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Xml.Linq;
using Common.Models;
using Common.Services;
using Newtonsoft.Json.Linq;

namespace Services
{
    public class PayloadTransformService : IPayloadTransformService
    {
        public object GetXmlObject(string xml)
        {
            var xDocument = XDocument.Parse(xml);

            XNamespace @namespace = "urn:oasis:names:tc:SAML:2.0:assertion";
            var attributes = xDocument.Descendants(@namespace + "Attribute");

            var attributeList = attributes.Select(GetXmlAttribute).Where(a => a.Name != string.Empty);

            var result = Unwrap(attributeList);
            return result;
        }

        public object[] GetJsonObjects(string file)
        {
            List<object> results = new List<object>();
            List<JToken> tokens = new List<JToken>();

            var jtoken = JToken.Parse(file);
            var jArray = jtoken as JArray;
            if (jArray != null)
            {
                tokens.AddRange((jArray).AsEnumerable());
            }
            else
            {
                tokens.Add(jtoken);
            }

            foreach (JToken token in tokens)
            {
                results.Add(Unwrap(GetJsonAttributes(JToken.Parse(token.ToString()))));
            }

            return results.ToArray();
        }

        private IReadOnlyCollection<Attribute> GetJsonAttributes(JToken token)
        {
            var dict = new Dictionary<string, string>();

            foreach (var (path, value) in GetFlatJsonChildren(token))
                dict.Add(path, value);

            return dict.Select(d => new Attribute
            {
                Name = d.Key,
                Value = d.Value == null ? string.Empty : d.Value.ToString()
            }).ToList();
        }

        private object Unwrap(IEnumerable<Attribute> attributes)
        {
            object result = new object();

            if (attributes != null)
            {
                result = new ExpandoObject();
                var properties = (IDictionary<string, object>)result;

                foreach (var attribute in attributes)
                {
                    properties.Add(attribute.Name, attribute.Value);
                }
            }

            return result;
        }

        private static IEnumerable<(string path, string value)> GetFlatJsonChildren(JToken token)
        {
            foreach (var child in token.Children())
            {
                if (token.Type != JTokenType.Array && token.Children().First().Type != JTokenType.Property &&
                    !child.Children().Any())
                    yield return (child.Path.Replace(".", "_"), child.ToString());

                foreach (var childChild in GetFlatJsonChildren(child))
                    yield return childChild;
            }
        }

        private static Attribute GetXmlAttribute(XElement xElement)
        {
            return new Attribute
            {
                Name = xElement.Attributes("Name").FirstOrDefault()?.Value.TrimEnd('/').Split('/').LastOrDefault() ??
                       string.Empty,
                Value = xElement.Value
            };
        }
    }
}