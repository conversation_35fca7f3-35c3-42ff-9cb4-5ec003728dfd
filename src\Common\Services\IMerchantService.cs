﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Common.Models;
using Common.Models.Comment;
using Common.Models.Gsdk;
using Common.Models.Merchant;
using FluentValidation.Results;
using Microsoft.AspNetCore.JsonPatch;

namespace Common.Services
{
    public interface IMerchantService
    {
        Task<MerchantSearchResponse<MerchantApiResult>> FindAsync(MerchantSearchFilters filters, CancellationToken cancellationToken = default);
        Task DeleteMerchantsAsync(MerchantDeleteRequest merchantDeleteRequest);
        Task<dynamic> GetContactDetailsAsync(Guid contactId);
        Task<ContactDetails?> GetMerchantContactsAsync(Guid merchantId);
        Task<IReadOnlyCollection<AdditionalMccUpdate>> UpdateMerchantAdditionalMccsAsync(Guid merchantId, IReadOnlyCollection<AdditionalMccUpdate> updateMerchantAdditionalMccs);
        Task<MerchantExportResponse[]> ExportAllMerchantsAsync();
        Task<MerchantExportResponse> ExportMerchantAsync(Guid merchantId);
        Task<MerchantPersonOfInterest[]> GetPeopleByMerchantIdAsync(Guid merchantId);
        Task<List<MerchantStatusResponse>> GetMerchantStatusHistoryAsync(Guid merchantId);
        Task<List<MerchantShortExportResponse>> GetMerchantShortExportAsync(IdsRequest idsRequest);
        Task<MerchantDetails[]> GetMerchantsDetailsAsync();
        Task<bool> CreateMerchantExternalIdentifier(MerchantExternalIdentifier externalIdentifier);
        Task<IReadOnlyCollection<GsdkMerchant>> SearchGsdkMerchants(SearchGsdkMerchantsRequest searchGsdkMerchantsRequest);
        Task<MerchantCommentResponse> CreateCommentAsync(Guid merchantId, CommentCreateRequest commentCreateRequest);
        Task<List<MerchantCommentResponse>> GetCommentByMerchantIdAsync(Guid merchantId);
        Task<MerchantCommentResponse> GetCommentByIdAsync(Guid commentId);
        Task<MerchantCommentResponse> UpdateCommentAsync(Guid commentId, CommentUpdateRequest commentUpdateRequest);
        Task DeleteCommentAsync(Guid commentId);
        Task UpdateSalesIdAsync(string initialSalesId, string updatedSalesId);
        Task UpdateMerchantSalesIdByLead(Guid leadId, string salesId);
        Task PatchMerchantByLeadIdAsync(Guid leadId, JsonPatchDocument<Merchant> merchantPatch);
        Task ValidateLicenseIdIsUsedAsync(Guid? merchantId, string? businessType, string? registrationNumber = null, string? municipalityLicense = null, string? legalId = null);
        Task<MerchantUpdateResponse> PatchMerchantAsync(Guid merchantId, JsonPatchDocument<PatchMerchantRequest> patchDocument);
        Task<List<EgyptMerchantExportResponse>> ExportEgyptMerchantsAsync(MerchantSearchFilters searchCriteria);
        Task ValidateMerchantBusinessInformation(Guid merchantId, JsonPatchDocument<PatchMerchantRequest> patchDocument);

        Task<(ComplianceRiskModel, AcquiringRiskModel)> CalculateRisk(CompanyDetails companyDetails);
        Task<CalculatedRisk> GetRiskCalculation(string businessId);


    }
}