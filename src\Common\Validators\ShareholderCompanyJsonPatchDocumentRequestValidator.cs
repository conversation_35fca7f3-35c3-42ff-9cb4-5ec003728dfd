﻿using Common.Models.Shareholder;
using FluentValidation;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Common.Validators;

public class ShareholderCompanyJsonPatchDocumentRequestValidator : AbstractValidator<JsonPatchDocument<MerchantShareholderCompanyResponse>>
{
    private readonly List<string> acceptedKsaBusinessTypeList = new()
    {
        Constants.BusinessType.LegalEnterprise,
        Constants.BusinessType.Limited,
        Constants.BusinessType.MunicipalEntity,
        Constants.BusinessType.SoleTrader,
        Constants.BusinessType.Other
    };

    private readonly List<string> acceptedEgBusinessTypeList = new()
    {
        Constants.BusinessType.Limited,
        Constants.BusinessType.Other
    };

    private readonly List<string> acceptedUaeBusinessTypeList = new()
    {
        Constants.BusinessType.Limited,
        Constants.BusinessType.LLC,
        Constants.BusinessType.SoleEstablishment,
        Constants.BusinessType.PSC,
        Constants.BusinessType.JSC,
        Constants.BusinessType.Partnership,
        Constants.BusinessType.Branch,
    };

    private static readonly string NameOfOwnershipPercentage = nameof(MerchantShareholderCompanyResponse.OwnershipPercentage);
    private static readonly string NameOfCompanyName = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.CompanyName);
    private static readonly string NameOfCompanyType = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.CompanyType);
    private static readonly string NameOfCompanyLicense = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.CompanyLicense);
    private static readonly string NameOfLicenseExpiryDate = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.LicenseExpiryDate);
    private static readonly string NameOfIssueDate = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.IssueDate);
    private static readonly string NameOfMccCode = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.MccCode);
    private static readonly string NameOfPhoneNumber = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.PhoneNumber);
    private static readonly string NameOfPhonePrefix = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.PhonePrefix);
    private static readonly string NameOfCountry = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress.Country);
    private static readonly string NameOfGovernorate = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress.Governorate);
    private static readonly string NameOfCity = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress.City);
    private static readonly string NameOfArea = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress.Area);
    private static readonly string NameOfAddress = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress.Address);
    private static readonly string shareholderCompanyPathName = nameof(MerchantShareholderCompanyResponse.ShareholderCompany);
    private static readonly string shareholderCompanyAdressPathName = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress);

    private static List<string> allowedMerchantShareholderCompanyResponseUpdateProperties = new()
    {
        NameOfOwnershipPercentage,
    };

    private static List<string> allowedShareholderCompanyResponseUpdateProperties = new()
    {
        NameOfCompanyName, NameOfCompanyType, NameOfCompanyLicense, NameOfLicenseExpiryDate, NameOfIssueDate, NameOfMccCode, NameOfPhoneNumber, NameOfPhonePrefix
    };

    private static List<string> allowedShareholderCompanyAddressResponseUpdateProperties = new()
    {
        NameOfCountry, NameOfGovernorate, NameOfCity, NameOfArea, NameOfAddress
    };


    public ShareholderCompanyJsonPatchDocumentRequestValidator(string counterparty)
    {
        RuleFor(x => x.Operations)
          .Must(BeAllowed)
          .When(o => o.Operations != null)
          .WithErrorCode(Errors.InvalidPatch.Code)
          .WithMessage(Errors.InvalidPatch.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfCompanyName)))
           .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length <= 150)
           .When(o => FindOperation(o, GetShareHolderCompanyFullPropertyName(NameOfCompanyName)) != null)
           .WithErrorCode(Errors.ShareholderCompanyNameLength.Code)
           .WithMessage(Errors.ShareholderCompanyNameLength.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfCompanyLicense)))
          .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o.value.ToString()?.Length <= 36)
          .When(o => FindOperation(o, GetShareHolderCompanyFullPropertyName(NameOfCompanyLicense)) != null)
          .WithErrorCode(Errors.InvalidShareholderCompanyLicenseLength.Code)
          .WithMessage(Errors.InvalidShareholderCompanyLicenseLength.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)))
          .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()))
          .When(o => FindOperation(o, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)) != null)
          .WithErrorCode(Errors.InvalidShareholderCompanyTypeLength.Code)
          .WithMessage(Errors.InvalidShareholderCompanyTypeLength.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfLicenseExpiryDate)))
          .Must(o => o?.value != null && DateTime.TryParse(o.value.ToString()!, out DateTime value)
          && value > DateTime.MinValue && value < DateTime.MaxValue)
          .When(o => ExitsOperationAndValueForShareHolderCompany(o, NameOfLicenseExpiryDate))
          .WithErrorCode(Errors.InvalidShareholderCompanyLicenseExpiryDate.Code)
          .WithMessage(Errors.InvalidShareholderCompanyLicenseExpiryDate.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfIssueDate)))
            .Must(o => DateTime.TryParse(o!.value.ToString()!, out DateTime value)
             && value > DateTime.MinValue && value < DateTime.MaxValue)
            .When(o => ExitsOperationAndValueForShareHolderCompany(o, NameOfIssueDate))
            .WithErrorCode(Errors.InvalidShareholderIssueDate.Code)
            .WithMessage(Errors.InvalidShareholderIssueDate.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfMccCode)))
          .Must(o => o!.value.ToString()?.Length <= 10)
          .When(o => ExitsOperationAndValueForShareHolderCompany(o, NameOfMccCode))
          .WithErrorCode(Errors.ShareholderCompanyMccLengthValidation.Code)
          .WithMessage(Errors.ShareholderCompanyMccLengthValidation.Message);

        RuleFor(x => FindOperation(x, GetMerchantShareHolderCompanyFullPropertyName(NameOfOwnershipPercentage)))
          .Must(o => o?.value != null && int.TryParse(o.value.ToString(), out int ownershipPercentage) && ownershipPercentage > 0 && ownershipPercentage <= 100)
          .When(o => ExitsOperationAndValueForMerchantShareHolderCompany(o, NameOfOwnershipPercentage))
          .WithErrorCode(Errors.InvalidShareholderCompanyOwnershipPercentage.Code)
          .WithMessage(Errors.InvalidShareholderCompanyOwnershipPercentage.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfPhoneNumber)))
           .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && Regex.IsMatch(o?.value?.ToString()!, @"^[0-9]{6,10}$"))
           .When(o => ExitsOperationAndValueForShareHolderCompany(o, NameOfPhoneNumber))
           .WithErrorCode(Errors.PhoneValidation.Code)
           .WithMessage(Errors.PhoneValidation.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfPhonePrefix)))
          .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && Regex.IsMatch(o?.value?.ToString()!, @"(?:^)\+[0-9]{1,3}(?:$)"))
          .When(o => ExitsOperationAndValueForShareHolderCompany(o, NameOfPhonePrefix))
          .WithErrorCode(Errors.InvalidCountryPrefix.Code)
          .WithMessage(Errors.InvalidCountryPrefix.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfCountry)))
           .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length >= 2 && o?.value?.ToString()?.Length <= 3)
           .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfCountry))
           .WithErrorCode(Errors.CountryLengthValidation.Code)
           .WithMessage(Errors.CountryLengthValidation.Message);

        RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfAddress)))
          .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length <= 50)
          .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfAddress))
          .WithErrorCode(Errors.AddressLineLengthValidation.Code)
          .WithMessage(Errors.AddressLineLengthValidation.Message);

        When(x => counterparty == Constants.CounterParty.Saudi, () =>
        {
            RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfGovernorate)))
              .Must(o => string.IsNullOrWhiteSpace(o?.value?.ToString()))
              .When(o => FindOperation(o, GetShareHolderCompanyAdressFullPropertyName(NameOfGovernorate)) != null)
              .WithErrorCode(Errors.DisabledProperty.Code)
              .WithMessage(Errors.DisabledProperty.Message);

            RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfArea)))
              .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length <= 255)
              .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfArea))
              .WithErrorCode(Errors.AreaLengthValidation.Code)
              .WithMessage(Errors.AreaLengthValidation.Message);

        });

        When(x => counterparty == Constants.CounterParty.Egypt, () =>
        {
            RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)))
             .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && acceptedEgBusinessTypeList.Contains(o.value.ToString()!))
             .When(o => FindOperation(o, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)) != null)
             .WithErrorCode(Errors.InvalidShareholderCompanyType.Code)
             .WithMessage(Errors.InvalidShareholderCompanyType.Message);

            RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfArea)))
              .Must(o => string.IsNullOrWhiteSpace(o?.value?.ToString()))
              .When(o => FindOperation(o, GetShareHolderCompanyAdressFullPropertyName(NameOfArea)) != null)
              .WithErrorCode(Errors.DisabledProperty.Code)
              .WithMessage(Errors.DisabledProperty.Message);

            RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfGovernorate)))
              .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length <= 255)
              .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfGovernorate))
              .WithErrorCode(Errors.GovernorateLengthValidation.Code)
              .WithMessage(Errors.GovernorateLengthValidation.Message);


        });

        When(x => counterparty == Constants.CounterParty.Uae,
            () =>
            {
                RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)))
               .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && acceptedUaeBusinessTypeList.Contains(o.value.ToString()!))
               .When(o => FindOperation(o, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)) != null)
               .WithErrorCode(Errors.InvalidShareholderCompanyType.Code)
               .WithMessage(Errors.InvalidShareholderCompanyType.Message);

                RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfArea)))
               .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length <= 255)
               .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfArea))
               .WithErrorCode(Errors.AreaLengthValidation.Code)
               .WithMessage(Errors.AreaLengthValidation.Message);

                RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfGovernorate)))
               .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length <= 255)
               .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfGovernorate))
               .WithErrorCode(Errors.GovernorateLengthValidation.Code)
               .WithMessage(Errors.GovernorateLengthValidation.Message);
            });

        When(x => !IsCounterpartyEgyptOrUae(counterparty),
          () =>
          {
              RuleFor(x => FindOperation(x, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)))
               .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && acceptedKsaBusinessTypeList.Contains(o.value.ToString()!))
               .When(o => FindOperation(o, GetShareHolderCompanyFullPropertyName(NameOfCompanyType)) != null)
               .WithErrorCode(Errors.InvalidShareholderCompanyType.Code)
               .WithMessage(Errors.InvalidShareholderCompanyType.Message);
          });

        When(x => IsCounterpartyEgyptOrUaeOrSaudi(counterparty),
         () =>
         {
             RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfCity)))
              .Must(o => !string.IsNullOrWhiteSpace(o?.value?.ToString()) && o?.value?.ToString()?.Length <= 255)
              .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfCity))
              .WithErrorCode(Errors.CityLengthValidation.Code)
              .WithMessage(Errors.CityLengthValidation.Message);
         });

        When(x => !IsCounterpartyEgyptOrUaeOrSaudi(counterparty),
            () =>
            {
                RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfGovernorate)))
                  .Must(o => string.IsNullOrWhiteSpace(o?.value?.ToString()))
                  .When(o => FindOperation(o, GetShareHolderCompanyAdressFullPropertyName(NameOfGovernorate)) != null)
                  .WithErrorCode(Errors.DisabledProperty.Code)
                  .WithMessage(Errors.DisabledProperty.Message);

                RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfArea)))
                  .Must(o => string.IsNullOrWhiteSpace(o?.value?.ToString()))
                  .When(o => FindOperation(o, GetShareHolderCompanyAdressFullPropertyName(NameOfArea)) != null)
                  .WithErrorCode(Errors.DisabledProperty.Code)
                  .WithMessage(Errors.DisabledProperty.Message);

                RuleFor(x => FindOperation(x, GetShareHolderCompanyAdressFullPropertyName(NameOfCity)))
                  .Must(o => string.IsNullOrWhiteSpace(o?.value?.ToString()))
                  .When(o => ExitsOperationAndValueForShareHolderCompanyAdress(o, NameOfCity))
                  .WithErrorCode(Errors.DisabledProperty.Code)
                  .WithMessage(Errors.DisabledProperty.Message);
            });
    }

    private static bool BeAllowed(List<Operation<MerchantShareholderCompanyResponse>> operations)
    {
        var operationPaths = operations.Select(o => o?.path?.ToLowerInvariant());

        var allProperties = allowedMerchantShareholderCompanyResponseUpdateProperties.Select(x => GetMerchantShareHolderCompanyFullPropertyName(x).ToLowerInvariant())
           .Concat(allowedShareholderCompanyResponseUpdateProperties.Select(x => GetShareHolderCompanyFullPropertyName(x).ToLowerInvariant()))
           .Concat(allowedShareholderCompanyAddressResponseUpdateProperties.Select(x => GetShareHolderCompanyAdressFullPropertyName(x).ToLowerInvariant()));

        return operationPaths.All(allProperties.Contains);
    }

    private static Operation<MerchantShareholderCompanyResponse>? FindOperation(JsonPatchDocument<MerchantShareholderCompanyResponse> o, string pathName)
        => o.Operations.Find(x => x.path.Equals(pathName, StringComparison.InvariantCultureIgnoreCase));

    private static string GetMerchantShareHolderCompanyFullPropertyName(string propName) =>
        string.Format("/{0}", propName);

    private static string GetShareHolderCompanyFullPropertyName(string propName) =>
        string.Format("/{0}/{1}", shareholderCompanyPathName, propName);
    private static string GetShareHolderCompanyAdressFullPropertyName(string propName) =>
        string.Format("/{0}/{1}/{2}", shareholderCompanyPathName, shareholderCompanyAdressPathName, propName);

    private static bool IsCounterpartyEgyptOrSaudi(string counterparty)
        => counterparty == Constants.CounterParty.Saudi
                  || counterparty == Constants.CounterParty.Egypt;
    private static bool IsCounterpartyEgyptOrUae(string counterparty)
        => counterparty == Constants.CounterParty.Uae
                  || counterparty == Constants.CounterParty.Egypt;

    private static bool IsCounterpartyEgyptOrUaeOrSaudi(string counterparty)
       => counterparty == Constants.CounterParty.Uae
                 || counterparty == Constants.CounterParty.Egypt
                 || counterparty == Constants.CounterParty.Saudi;

    private static bool ExitsOperationAndValueForMerchantShareHolderCompany(JsonPatchDocument<MerchantShareholderCompanyResponse> o, string pathName)
    {
        var operation = FindOperation(o, GetMerchantShareHolderCompanyFullPropertyName(pathName));

        return OperationHaveNotNullValue(operation);
    }

    private static bool ExitsOperationAndValueForShareHolderCompanyAdress(JsonPatchDocument<MerchantShareholderCompanyResponse> o, string pathName)
    {
        var operation = FindOperation(o, GetShareHolderCompanyAdressFullPropertyName(pathName));

        return OperationHaveNotNullValue(operation);
    }

    private static bool ExitsOperationAndValueForShareHolderCompany(JsonPatchDocument<MerchantShareholderCompanyResponse> o, string pathName)
    {
        var operation = FindOperation(o, GetShareHolderCompanyFullPropertyName(pathName));

        return OperationHaveNotNullValue(operation);
    }

    private static bool OperationHaveNotNullValue(Operation<MerchantShareholderCompanyResponse>? operation)
        => operation != null && operation.value != null;
}

