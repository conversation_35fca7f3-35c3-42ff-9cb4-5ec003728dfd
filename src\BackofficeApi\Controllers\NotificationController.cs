﻿using Common.Models;
using Common.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Geidea.Utils.Policies.Evaluation;

namespace BackofficeApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1")]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService notificationService;
        private readonly Authorized authorized;

        public NotificationController(INotificationService notificationService, Authorized authorized)
        {
            this.notificationService = notificationService;
            this.authorized = authorized;
        }

        /// <summary>
        /// Sends out a "check your email" type of notification to the sepcified recipient.
        /// </summary>
        /// <param name="request">Contains the subject, the type of onboarding check, recipient's address and custom comments if any.</param>
        /// <returns></returns>
        [HttpPost("notification/emailinfocheck")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SendEmailInfoCheck(SendEmailInfoCheckRequest request)
        {
            if (!await authorized.To.Send.Notifications())
            {
                return Forbid();
            }

            if (request == null)
            {
                return BadRequest();
            }

            await notificationService.SendEmailInfoCheckAsync(request);
            return NoContent();
        }

        [HttpPost("notification/smsinfocheck")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SendSmsInfoCheck(SendSmsInfoCheckRequest request)
        {
            if (!await authorized.To.Send.Notifications())
            {
                return Forbid();
            }

            if (request == null)
            {
                return BadRequest();
            }

            await notificationService.SendSmsInfoCheckAsync(request);
            return NoContent();
        }

        [HttpPost("notification/customemail")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SendCustomEmail(SendCustomEmailRequest request)
        {
            if (!await authorized.To.Send.Notifications())
            {
                return Forbid();
            }

            if (request == null)
            {
                return BadRequest();
            }

            await notificationService.SendCustomEmailAsync(request);
            return NoContent();
        }

        [HttpPost("notification/customsms")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> SendCustomSms(SendCustomSmsRequest request)
        {
            if (!await authorized.To.Send.CustomSms())
            {
                return Forbid();
            }

            if (request == null)
            {
                return BadRequest();
            }

            await notificationService.SendCustomSmsAsync(request);
            return NoContent();
        }
    }
}
