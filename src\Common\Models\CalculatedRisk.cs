﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models
{
    public class CalculatedRisk
    {

        public string BusinessId { get; set; } = string.Empty;

        public string MerchantStatus { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public string Counterparty { get; set; } = string.Empty;

        public ComplianceRisks? ComplianceRisk { get; set; }

        public AcquiringRisks? AcquiringRisk { get; set; }
    }

    public class ComplianceRisks
    {
        public string RiskLevel { get; set; } = string.Empty;

        public string RiskScore { get; set; } = string.Empty;

        public string DueDiligenceLevel { get; set; } = string.Empty;
    }

    public class AcquiringRisks
    {
        public string RiskLevel { get; set; } = string.Empty;
    }
}
