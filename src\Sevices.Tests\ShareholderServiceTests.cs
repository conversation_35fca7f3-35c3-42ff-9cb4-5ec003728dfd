﻿using AutoMapper;
using BackofficeApi;
using Common;
using Common.Models.Shareholder;
using Common.Options;
using Common.Services;
using Common.Services.Validators;
using Common.Tests.InputModels;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Cleanup;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests;

public class ShareholderServiceTests
{
    private readonly Mock<ILogger<ShareholderService>> logger = new();
    private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new();
    private readonly Mock<IOptionsMonitor<ShareholderSearchConfiguration>> shareholderSearchConfiguration = new();
    private ILogger<CleanupService> loggerCleanup = Substitute.For<ILogger<CleanupService>>();
    private ICounterpartyProvider counterpartyProvider = Substitute.For<ICounterpartyProvider>();
    private ICleanupService cleanupService = Substitute.For<ICleanupService>();
    private IShareholderService shareholderService = Substitute.For<IShareholderService>();
    private readonly Mock<IShareholderValidationService> validationService = new();
    private readonly IMapper mapper;
    private readonly Mock<IMerchantClient> merchantClient = new();
    private readonly Mock<IShareholderClient> shareholderClient = new();
    private readonly Mock<IDocumentClient> documentClient = new();
    private static readonly Guid merchantId = Guid.NewGuid();
    private static readonly Guid shareholderCompanyId = Guid.NewGuid();

    private readonly List<MerchantShareholderCompanyResponse> merchantShareholderCompanyResponse = new()
    {
        new MerchantShareholderCompanyResponse()
        {
            MerchantId = merchantId,
            ShareholderCompany = new ShareholderCompanyResponse()
            {
                Id = shareholderCompanyId,
                ShareholderCompanyAddress = new ShareholderCompanyAddressResponse()
                {
                    Id = Guid.NewGuid(),
                    ShareholderCompanyId = shareholderCompanyId,
                    Area = "Test",
                    City = "Test",
                    Country = "SA",
                    Address = "Test Address"
                },
                CompanyLicense = "TestLicense",
                CompanyName = "TestCompany",
                CompanyType = "TestCompanyType",
                Counterparty = Constants.CounterParty.Saudi,
                CreatedDate = DateTime.Now,
                CreatedBy = Guid.NewGuid().ToString(),
                LicenseExpiryDate = DateTime.Now.AddMonths(1),
                IssueDate = DateTime.Now.AddMonths(-11),
                MccCode = "123",
                PhonePrefix = "+966",
                PhoneNumber = "**********"
            },
            OwnershipPercentage = 30,
            ShareholderCompanyId = shareholderCompanyId
        }
    };

    private readonly List<ShareholderCompanyResponse> shareholderCompanySearchResponse = new()
    {
        new ShareholderCompanyResponse
        {
            Id = shareholderCompanyId,
            ShareholderCompanyAddress = new ShareholderCompanyAddressResponse()
            {
                Id = Guid.NewGuid(),
                ShareholderCompanyId = shareholderCompanyId,
                Area = "Test",
                City = "Test",
                Country = "SA",
                Address = "Test Address"
            },
            CompanyLicense = "TestLicense",
            CompanyName = "TestCompany",
            CompanyType = "TestCompanyType",
            Counterparty = Constants.CounterParty.Saudi,
            CreatedDate = DateTime.Now,
            CreatedBy = Guid.NewGuid().ToString(),
            LicenseExpiryDate = DateTime.Now.AddMonths(1),
            IssueDate = DateTime.Now.AddMonths(-11),
            MccCode = "123",
            PhonePrefix = "+966",
            PhoneNumber = "**********"
        }
    };

    private readonly ShareholderCompanyCreateRequest shareholderCompanyCreateRequest = new()
    {
        CompanyLicense = "**********",
        CompanyName = "TestCompany",
        CompanyType = "SOLE_TRADER",
        Counterparty = Constants.CounterParty.Saudi,
        CreatedBy = Guid.NewGuid().ToString(),
        LicenseExpiryDate = DateTime.Now.AddMonths(1),
        IssueDate = null,
        MccCode = null,
        PhonePrefix = "+966",
        PhoneNumber = "**********",
        OwnershipPercentage = 30,
        Area = "Test",
        City = "Test",
        Country = "SA",
        Address = "Test Address",
        MerchantId = Guid.NewGuid()
    };

    private readonly ShareholderCompanyResponse shareholderCompanyResponse = new()
    {
        Id = shareholderCompanyId,
        ShareholderCompanyAddress = new ShareholderCompanyAddressResponse()
        {
            Id = Guid.NewGuid(),
            ShareholderCompanyId = shareholderCompanyId,
            Area = "Test",
            City = "Test",
            Country = "SA",
            Address = "Test Address"
        },
        CompanyLicense = "**********",
        CompanyName = "TestCompany",
        CompanyType = "SOLE_TRADER",
        Counterparty = Constants.CounterParty.Saudi,
        CreatedDate = DateTime.Now,
        CreatedBy = Guid.NewGuid().ToString(),
        LicenseExpiryDate = DateTime.Now.AddMonths(1),
        IssueDate = DateTime.Now.AddMonths(-11),
        MccCode = "123",
        PhonePrefix = "+966",
        PhoneNumber = "**********"
    };

    private readonly MerchantIndividualCore merchantIndividualCore = new()
    {
        MerchantId = Guid.NewGuid(),
        LastName = "lastname"
    };

    private readonly ShareholderIndividualCreateRequest shareholderIndividualCreateRequest = new()
    {
        FirstName = "John",
        LastName = "Walker",
        DOB = DateTime.Now.AddYears(-30),
        NationalId = "**********",
        PassportNo = "HG123",
        PassportExpirationDate = DateTime.Now.AddYears(3),
        KYCCheck = "PENDING",
        PhonePrefix = "+966",
        PhoneNumber = "**********",
        PEP = false,
        Address = new ShareholderIndividualAddress
        {
            Area = "Test Area",
            City = "Test City",
            Country = "SA",
            Governorate = "AE-AZ",
            AddressInfo = "Test Address",
            Email = "<EMAIL>",
            PhoneNumber = "**********",
        },
        Merchant = new MerchantIndividualLink
        {
            MerchantId = Guid.NewGuid(),
            OrganizationRole = "Test Role",
            OwnershipPercentage = 50,
        },
        ShareholderCompanies = new List<ShareholderCompanyIndividualLink>
        {
            new ShareholderCompanyIndividualLink
            {
                ShareHolderCompanyId = Guid.NewGuid(),
                OrganizationRole = "Test Role",
            },
        },
        Counterparty = Constants.CounterParty.Saudi,
        CreatedBy = Guid.NewGuid().ToString(),
    };

    private readonly ShareholderIndividualResponse shareholderIndividualResponse = new()
    {
        Id = shareholderCompanyId,
        FirstName = "John",
        LastName = "Walker",
        DOB = DateTime.Now.AddYears(-30),
        NationalId = "**********",
        PassportNo = "HG123",
        PassportExpirationDate = DateTime.Now.AddYears(3),
        KYCCheck = "PENDING",
        PhonePrefix = "+966",
        PhoneNumber = "**********",
        PEP = false,
    };

    private readonly CreateShareholderIndividualWithDocumentRequest shareholderIndividualWithDocumentRequest = new CreateShareholderIndividualWithDocumentRequest
    {
        FirstName = "John",
        LastName = "Walker",
        DOB = DateTime.Now.AddYears(-30),
        NationalId = "**********",
        PassportNo = "HG123",
        PassportExpirationDate = DateTime.Now.AddYears(3),
        KYCCheck = "PENDING",
        PhonePrefix = "+966",
        PhoneNumber = "**********",
        PEP = false,
        Address = new ShareholderIndividualAddress
        {
            Area = "Test Area",
            City = "Test City",
            Country = "SA",
            Governorate = "AE-AZ",
            AddressInfo = "Test Address",
            Email = "<EMAIL>",
            PhoneNumber = "**********",
        },
        Merchant = new MerchantIndividualLink
        {
            MerchantId = Guid.NewGuid(),
            OrganizationRole = "Test Role",
            OwnershipPercentage = 50,
        },
        ShareholderCompanies = new List<ShareholderCompanyIndividualLink>
        {
            new ShareholderCompanyIndividualLink
            {
                ShareHolderCompanyId = Guid.NewGuid(),
                OrganizationRole = "Test Role",
            },
        },
        Counterparty = Constants.CounterParty.Saudi,
        CreatedBy = Guid.NewGuid().ToString(),
        NationalIds = new List<IFormFile> { new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy National ID file")), 0, 0, "NationalId", "nationalId.txt") },
        Passports = new List<IFormFile> { new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy Passport file")), 0, 0, "Passport", "passport.txt") }
    };

    private readonly CreateShareholderCompanyWithDocumentRequest shareholderCompanyWithDocumentRequest = new CreateShareholderCompanyWithDocumentRequest
    {
        CompanyLicense = "**********",
        CompanyName = "TestCompany",
        CompanyType = "SOLE_TRADER",
        LicenseExpiryDate = DateTime.Now.AddMonths(1),
        IssueDate = null,
        MccCode = null,
        PhonePrefix = "+966",
        PhoneNumber = "**********",
        OwnershipPercentage = 30,
        Area = "Test",
        City = "Test",
        Country = "SA",
        Address = "Test Address",
        MerchantId = Guid.NewGuid(),
        Counterparty = Constants.CounterParty.Saudi,
        CreatedBy = Guid.NewGuid().ToString(),
        FreelanceIds = new List<IFormFile> { new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy Freelance ID file")), 0, 0, "FreelanceId", "freelanceId.txt") },
        CommercialRegistrations = new List<IFormFile> { new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy Commercial Registration file")), 0, 0, "CommercialRegistration", "commercialRegistration.txt") },
        LegalEnterpriseLicenses = new List<IFormFile> { new FormFile(new MemoryStream(Encoding.UTF8.GetBytes("This is a dummy Legal Enterprise License file")), 0, 0, "LegalEnterpriseLicense", "legalEnterpriseLicense.txt") }
    };

    public ShareholderServiceTests()
    {
        var profile = new AutoMapping();
        var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        mapper = new Mapper(configuration);
        urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
        shareholderSearchConfiguration.Setup(x => x.CurrentValue).Returns(TestsHelper.ShareholderSearchConfiguration);
    }

    [SetUp]
    public void Setup()
    {
        cleanupService = new CleanupService(loggerCleanup);
    }

    [Test]
    public async Task GetShareholderCompanies_ForMerchant_ReturnsSuccessfully()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK);

        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false)).
            Returns(Task.FromResult(JsonConvert.SerializeObject(merchantShareholderCompanyResponse)));

        var shareholderService = CreateShareholderService(httpClient);

        var result = await shareholderService.GetShareholderCompaniesAsync(new ShareholderCompaniesRequest
        {
            MerchantId = merchantId
        });

        result.Count.Should().Be(1);
        result.Should().BeEquivalentTo(merchantShareholderCompanyResponse);
    }

    [Test]
    public async Task GetShareholderCompanies_ByMerchantIdAndKeyword()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK);

        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false)).
            Returns(Task.FromResult(JsonConvert.SerializeObject(merchantShareholderCompanyResponse)));

        var shareholderService = CreateShareholderService(httpClient);

        var result = await shareholderService.GetShareholderCompaniesAsync(new ShareholderCompaniesRequest
        {
            MerchantId = merchantId,
            Keyword = "TestCompany"
        });

        result.Count.Should().Be(1);
        result.Should().BeEquivalentTo(merchantShareholderCompanyResponse);
    }

    [Test]
    public async Task SearchShareholderCompaniesAsync_SearchByKeyword_IsSuccessful()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK);
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), true))
            .Returns(Task.FromResult(JsonConvert.SerializeObject(shareholderCompanySearchResponse)));
        var shareholderService = CreateShareholderService(httpClient);

        var result = await shareholderService.SearchShareholderCompaniesAsync(new ShareholderCompaniesRequest
        {
            Keyword = "TestCompany"
        });

        result.Count.Should().Be(1);
        result.Should().BeEquivalentTo(shareholderCompanySearchResponse);
    }

    [Test]
    public async Task GetShareholderCompanies_ShouldThrow_BadRequest_Exception()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest);
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x => x.GetShareholderCompaniesAsync(new ShareholderCompaniesRequest())).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task SearchShareholderIndividualsAsync_SearchByKeyword_IsSuccessful()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK,
            JsonConvert.SerializeObject(new List<MerchantIndividualCore>() { merchantIndividualCore }));

        var shareholderService = CreateShareholderService(httpClient);

        var result = await shareholderService.SearchShareholderIndividualsAsync(new ShareholderIndividualsSearchRequest
        {
            Keyword = "lastname"
        });

        result.Count.Should().Be(1);
        result.Should().BeEquivalentTo(new List<MerchantIndividualCore>() { merchantIndividualCore });
    }

    [Test]
    public async Task SearchShareholderIndividualsAsync_ShouldThrow_BadRequest_Exception()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest);
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x => x.SearchShareholderIndividualsAsync(new ShareholderIndividualsSearchRequest() { Keyword = "any" })).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task SearchShareholderIndividualsAsync_WhenKeyWordIsMissing_ShouldThrowException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK,
            JsonConvert.SerializeObject(new List<MerchantIndividualCore>() { merchantIndividualCore }));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x => x.GetShareholderCompaniesAsync(new ShareholderCompaniesRequest())).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task SearchShareholderCompaniesAsync_ShouldThrow_Exception()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.InternalServerError);
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), true))
            .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.InternalServerError)));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x => x.SearchShareholderCompaniesAsync(new ShareholderCompaniesRequest()
        {
            Keyword = "Test"
        })).Should()
            .ThrowAsync<PassthroughException>()
            .Where(x => x.StatusCode == HttpStatusCode.InternalServerError);
    }

    [Test]
    public async Task GetShareholderCompaniesAsync_ShouldThrow_Exception()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.InternalServerError);
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false))
            .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.InternalServerError)));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x => x.GetShareholderCompaniesAsync(new ShareholderCompaniesRequest()
        {
            MerchantId = Guid.NewGuid()
        })).Should()
            .ThrowAsync<PassthroughException>()
            .Where(x => x.StatusCode == HttpStatusCode.InternalServerError);
    }

    [Test]
    public async Task CreateShareholderCompany_ShouldCreateSuccessfully()
    {
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), true))
            .Returns(Task.FromResult(JsonConvert.SerializeObject(new List<ShareholderCompanyResponse>())));

        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(shareholderCompanyResponse));
        var shareholderService = CreateShareholderService(httpClient);
        validationService.Setup(x => x.ValidateCreateCompanyRequest(It.IsAny<ShareholderCompanyCreateRequest>())).Returns(Task.FromResult(merchantIndividualCore));
        shareholderClient.Setup(x => x.CreateShareholderCompanyAsync(It.IsAny<ShareholderCompanyCreateRequest>()))
            .Returns(Task.FromResult(shareholderCompanyResponse));
        var result = await shareholderService.CreateShareholderCompanyAsync(shareholderCompanyWithDocumentRequest, Guid.NewGuid());

        Assert.That(result.CompanyLicense, Is.EqualTo(shareholderCompanyCreateRequest.CompanyLicense));
    }

    [Test]
    public async Task CreateShareholderCompany_Should_ThrowValidationException()
    {
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), true))
           .Returns(Task.FromResult(JsonConvert.SerializeObject(new List<ShareholderCompanyResponse>())));
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(shareholderCompanyResponse));
        var shareholderService = CreateShareholderService(httpClient);
        validationService.Setup(x => x.ValidateCreateCompanyRequest(It.IsAny<ShareholderCompanyCreateRequest>())).Throws(new Exception());
        await shareholderService.Invoking(x => x.CreateShareholderCompanyAsync(new CreateShareholderCompanyWithDocumentRequest()
        {
            CompanyLicense = "**********",
            CompanyName = "TestCompany",
            CompanyType = "Failing",
            Counterparty = Constants.CounterParty.Saudi,
            CreatedBy = Guid.NewGuid().ToString(),
            LicenseExpiryDate = DateTime.Now.AddMonths(1),
            IssueDate = DateTime.Now.AddMonths(-11),
            MccCode = "123",
            PhonePrefix = "+966",
            PhoneNumber = "**********",
            OwnershipPercentage = 30,
            Area = "Test",
            City = "Test",
            Country = "SA",
            Address = "Test Address",
            MerchantId = Guid.NewGuid()
        }, Guid.NewGuid()))
        .Should()
        .ThrowAsync<Exception>();
    }

    [TestCase(Constants.ShareholderIndividualsRelationTypes.PoaHolder)]
    [TestCase(Constants.ShareholderIndividualsRelationTypes.Ubo)]
    [TestCase(Constants.ShareholderIndividualsRelationTypes.Stakeholder)]
    [TestCase(Constants.ShareholderIndividualsRelationTypes.DirectShareholder)]
    public async Task GetMerchantIndividuals_WhenNoError_ShouldReturnMappedData(string organizationRole)
    {
        var doubleRelationNationatinalId = "1234";
        var doublePoiId = Guid.NewGuid();
        var coreServiceResponse = new MerchantShareholderIndividualsCore()
        {
            MerchantIndividuals = new List<MerchantIndividualCore>()
            {
                new MerchantIndividualCore()
                {
                    NationalId = doubleRelationNationatinalId,
                    PersonOfInterestId = doublePoiId,
                    OrganizationRole = organizationRole,
                }
            },
            ShareholderCompanyIndividuals = new List<ShareholderCompanyIndividualCore>()
            {
                new ShareholderCompanyIndividualCore()
                {
                    NationalId = doubleRelationNationatinalId,
                    PersonOfInterestId= doublePoiId
                },
                new ShareholderCompanyIndividualCore()
                {
                    NationalId = "4563"
                }
            }
        };
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK,
            JsonConvert.SerializeObject(coreServiceResponse));

        var shareholderService = CreateShareholderService(httpClient);

        var result = await shareholderService.GetMerchantIndividualsAsync(Guid.NewGuid());
        var doubleRelation = result.First(p => p.PersonOfInterestId == doublePoiId).Relations;

        result.Count.Should().Be(2);
        doubleRelation.Should().HaveCount(2);
        doubleRelation.Find(r => r.Type == Constants.ShareholderIndividualsRelationTypes.Company).Should().NotBeNull();
        doubleRelation.Find(r => r.Type == organizationRole).Should().NotBeNull();
        doubleRelation.Find(r => r.OrganizationRole == organizationRole).Should().NotBeNull();
    }

    [Test]
    public async Task CreateShareholderCompanyMerchantAssociationAsync_ThrowsException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest);
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x =>
                x.CreateShareholderCompanyMerchantAssociationAsync(new ShareholderCompanyMerchantAssociationRequest()))
            .Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task CreateShareholderCompanyMerchantAssociationAsync_IsSuccessful()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK);
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x =>
                x.CreateShareholderCompanyMerchantAssociationAsync(new ShareholderCompanyMerchantAssociationRequest()
                {
                    MerchantId = merchantId,
                    ShareholderCompanyId = shareholderCompanyId
                }))
            .Should()
            .NotThrowAsync<ServiceException>();
    }

    [Test]
    public async Task CreateShareholderIndividualAsync_ValidationFails_ShouldThrowPassthroughException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadGateway);
        validationService.Setup(x => x.ValidateCreateIndividualRequest(It.IsAny<ShareholderIndividualCreateRequest>())).Throws(new Exception());
        shareholderClient.Setup(x => x.CreateShareholderIndividualAsync(It.IsAny<ShareholderIndividualCreateRequest>()))
            .Returns(Task.FromResult(shareholderIndividualResponse));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService
           .Invoking(x => x.CreateShareholderIndividualAsync(new CreateShareholderIndividualWithDocumentRequest(), Guid.NewGuid()))
           .Should().ThrowAsync<Exception>();
    }

    [Test]
    public async Task CreateShareholderIndividualAsync_MerchantServiceCallFails_ShouldThrowPassthroughException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadGateway);
        validationService.Setup(x => x.ValidateCreateIndividualRequest(It.IsAny<ShareholderIndividualCreateRequest>())).Verifiable();
        shareholderClient.Setup(x => x.CreateShareholderIndividualAsync(It.IsAny<ShareholderIndividualCreateRequest>()))
            .Throws(new PassthroughException(new HttpResponseMessage()));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService
            .Invoking(x => x.CreateShareholderIndividualAsync(new CreateShareholderIndividualWithDocumentRequest(), Guid.NewGuid()))
            .Should().ThrowAsync<PassthroughException>();
    }

    [Test]
    public async Task CreateShareholderIndividualAsync_MerchantServiceCallSuccess_ShouldNotThrowPassthroughException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK);
        validationService.Setup(x => x.ValidateCreateIndividualRequest(It.IsAny<ShareholderIndividualCreateRequest>())).Verifiable();
        shareholderClient.Setup(x => x.CreateShareholderIndividualAsync(It.IsAny<ShareholderIndividualCreateRequest>()))
            .Returns(Task.FromResult(shareholderIndividualResponse));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService
            .Invoking(x => x.CreateShareholderIndividualAsync(new CreateShareholderIndividualWithDocumentRequest(), Guid.NewGuid()))
            .Should().NotThrowAsync<PassthroughException>();
    }

    [Test]
    public async Task PatchShareholderCompanyAsync_WhenLicenseExitsInDatabase_ShouldThrowException()
    {
        var request = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = ShareholderCompanyRequestInputHelper.GetSaudiValidJsonPatchRequest(),
            MerchantId = Guid.NewGuid(),
            ShareholderCompanyId = Guid.NewGuid()
        };
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK,
            JsonConvert.SerializeObject(new List<ShareholderCompanyResponse>() { new ShareholderCompanyResponse() }));
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), true))
            .Returns(Task.FromResult(JsonConvert.SerializeObject(new List<ShareholderCompanyResponse>() { new ShareholderCompanyResponse() { CompanyLicense = "CompanyLicense" } })));
        var shareholderService = CreateShareholderService(httpClient);
        await shareholderService.Invoking(x => x.PatchShareholderCompanyAsync(request)).Should().ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task PatchShareholderCompanyAsync_WhenValidationFails_ShouldThrowException()
    {
        var request = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = ShareholderCompanyRequestInputHelper.GetSaudiValidJsonPatchRequest(),
            MerchantId = Guid.NewGuid(),
            ShareholderCompanyId = Guid.NewGuid()
        };
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new List<ShareholderCompanyResponse>()));
        validationService.Setup(x => x.ValidateEditCompanyRequest(It.IsAny<ShareholderCompanyPatchRequest>())).Throws(new Exception());
        var shareholderService = CreateShareholderService(httpClient);
        await shareholderService.Invoking(x => x.PatchShareholderCompanyAsync(request)).Should().ThrowAsync<Exception>();
    }

    [Test]
    public async Task PatchShareholderCompanyAsync_WhenValidRequest_ReturnExpectedData()
    {
        var response = new MerchantShareholderCompanyResponse() { MerchantId = Guid.NewGuid() };
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(response));
        var request = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = ShareholderCompanyRequestInputHelper.GetSaudiValidJsonPatchRequest(),
            MerchantId = Guid.NewGuid(),
            ShareholderCompanyId = Guid.NewGuid()
        };
        validationService.Setup(x => x.ValidateEditCompanyRequest(It.IsAny<ShareholderCompanyPatchRequest>()))
            .Returns(Task.FromResult(new List<ShareholderCompanyResponse>()));
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), true))
            .Returns(Task.FromResult(JsonConvert.SerializeObject(new List<ShareholderCompanyResponse>() { new ShareholderCompanyResponse() { CompanyLicense = "CompanyLicensee" } })));
        var shareholderService = CreateShareholderService(httpClient);

        var result = await shareholderService.PatchShareholderCompanyAsync(request);
        result.Should().BeEquivalentTo(response);
    }

    [Test]
    public async Task PatchShareholderCompanyAsync_WhenServiceThrowExceptioin_ShouldThrowException()
    {
        var request = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = ShareholderCompanyRequestInputHelper.GetSaudiValidJsonPatchRequest(),
            MerchantId = Guid.NewGuid(),
            ShareholderCompanyId = Guid.NewGuid()
        };
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new List<ShareholderCompanyResponse>()));
        validationService.Setup(x => x.ValidateEditCompanyRequest(It.IsAny<ShareholderCompanyPatchRequest>()))
            .Returns(Task.FromResult(new List<ShareholderCompanyResponse>()));
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), true)).Throws(new Exception());
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService.Invoking(x => x.PatchShareholderCompanyAsync(request)).Should().ThrowAsync<Exception>();
    }

    [Test]
    public async Task PatchShareholderIndividualAsync_WhenValidRequest_ShouldNotThrowException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new OkResult()));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService
            .Invoking(x => x.PatchShareholderIndividualAsync(new ShareholderIndividualPatchRequest()))
            .Should()
            .NotThrowAsync();
    }

    [Test]
    public async Task PatchShareholderIndividualAsync_WhenServiceThrowException_ShouldThrowException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, JsonConvert.SerializeObject("Error"));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService
            .Invoking(x => x.PatchShareholderIndividualAsync(new ShareholderIndividualPatchRequest()))
            .Should()
            .ThrowAsync<Exception>();
    }

    [Test]
    public async Task GetShareholderIndividualEditableStatusAsync_ReturnsSuccessfully()
    {
        var editableStatus = new ShareholderIndividualEditableStatus
        {
            IndividualIsPrincipal = true,
            IndividualIsFromWathq = false
        };
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(editableStatus));
        var shareholderService = CreateShareholderService(httpClient);

        var result = await shareholderService.GetShareholderIndividualEditableStatusAsync(Guid.NewGuid());

        result.Should().BeEquivalentTo(editableStatus);
    }

    [Test]
    public async Task GetShareholderIndividualEditableStatusAsync_WhenServiceThrowsException_ShouldThrowException()
    {
        var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, JsonConvert.SerializeObject("Error"));
        var shareholderService = CreateShareholderService(httpClient);

        await shareholderService
            .Invoking(x => x.GetShareholderIndividualEditableStatusAsync(Guid.Empty))
            .Should()
            .ThrowAsync<Exception>();
    }

    private ShareholderService CreateShareholderService(HttpClient httpClient)
    {
        return new ShareholderService(
            logger.Object,
            httpClient,
            urlSettingsOptions.Object,
            shareholderSearchConfiguration.Object,
            counterpartyProvider,
            cleanupService,
            validationService.Object,
            mapper,
            merchantClient.Object,
            shareholderClient.Object,
            documentClient.Object);
    }
}