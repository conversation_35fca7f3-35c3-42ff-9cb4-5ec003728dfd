﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using BackofficeApi;
using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Comment;
using Common.Models.Gle;
using Common.Models.Gsdk;
using Common.Models.Merchant;
using Common.Models.Postilion;
using Common.Models.Product;
using Common.Models.ProductInstance;
using Common.Models.Shareholder;
using Common.Models.TerminalDataSet;
using Common.Models.User;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using NUnit.Framework;
using Services;
using Services.Messaging;
using static Common.Constants;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class CheckoutServiceTests
    {
        private readonly ILogger<CheckoutService> logger = Substitute.For<ILogger<CheckoutService>>();
        private CheckoutService checkoutService = null!;
        private readonly Mock<IUserService> userService = new Mock<IUserService>();
        private readonly IEPosMessagingService ePosMessagingService = Substitute.For<IEPosMessagingService>();
        private readonly IPostilionService postilionService = Substitute.For<IPostilionService>();
        private readonly Mock<ICheckoutClient> checkoutClient = new Mock<ICheckoutClient>();
        private readonly Mock<IProductService> productService = new Mock<IProductService>();
        private readonly Mock<ISearchService> searchService = new Mock<ISearchService>();
        private readonly Mock<IGsdkService> gsdkService = new Mock<IGsdkService>();
        private readonly Mock<IGsdkClient> gsdkClient = new Mock<IGsdkClient>();
        private readonly Mock<INotificationService> notificationService = new Mock<INotificationService>();
        private readonly Mock<IShareholderService> shareholderService = new Mock<IShareholderService>();
        private readonly Mock<IDueDiligenceService> dueDiligenceService = new Mock<IDueDiligenceService>();
        private readonly Mock<INexusBridgeService> nexusBridgeService = new Mock<INexusBridgeService>();

        private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
        private readonly IGSDKMerchantAdapterService mmsMerchantAdapterService;
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();
        private readonly Mock<IMerchantClient> merchantClient = new Mock<IMerchantClient>();
        private readonly IContractService contractService;
        private readonly Mapper mapper;

        private readonly GleComposePayloadMessagingClient gleComposePayloadMessagingClient;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly ILogger<MessageClient> gleMessageClientLogger;
        private readonly IOptionsMonitor<RabbitMqOptions> rabbitMqOptions;

        [SetUp]
        public void Setup()
        {
            contractService.ClearReceivedCalls();
            gleComposePayloadMessagingClient.ClearReceivedCalls();
            logger.ClearReceivedCalls();
            mmsMerchantAdapterService.ClearReceivedCalls();
            contractService.ClearReceivedCalls();
            ePosMessagingService.ClearReceivedCalls();
            postilionService.ClearReceivedCalls();
            productService.Invocations.Clear();
            shareholderService.Invocations.Clear();
        }

        public CheckoutServiceTests()
        {
            mmsMerchantAdapterService = Substitute.For<IGSDKMerchantAdapterService>();
            contractService = Substitute.For<IContractService>();

            var profile = new AutoMapping();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
            mapper = new Mapper(configuration);

            gleMessageClientLogger = Substitute.For<ILogger<MessageClient>>();
            httpContextAccessor = Substitute.For<IHttpContextAccessor>();
            rabbitMqOptions = Substitute.For<IOptionsMonitor<RabbitMqOptions>>();
            gleComposePayloadMessagingClient =
                Substitute.For<GleComposePayloadMessagingClient>(httpContextAccessor, gleMessageClientLogger,
                    rabbitMqOptions);

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(new Catalogue[]
                {
                    new Catalogue()
                    {
                        CatalogueName = Catalogues.ReferralChannelRestrictions, Key = "SABB_GENESIS",
                        Value = "SABB_GENESIS"
                    }
                }));
        }

        private readonly OrderResponse orderResponse = new OrderResponse()
        {
            OrderId = Guid.NewGuid(),
            AgreementId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            UserId = Guid.NewGuid(),
            AddressId = Guid.NewGuid(),
            OrderStatus = Constants.OrderStatus.ProductRegistered,
            PaymentReference = "PaymentReference",
            TrackingNumber = "TrackingNumber",
            TrackingUrl = "TrackingUrl",
            Shipper = "Shipper",
            ShippedDate = DateTime.UtcNow,
            CouponCode = "CouponCode",
            PaymentMethod = "PaymentMethod",
            CompanyRegNo = "CompanyRegNo",
            SalesName = "SalesName",
            SubscriptionPlan = "SubscriptionPlan",
            ProjectName = "SABB_GENESIS",
            ReferralChannel = "UNASSIGNED",
            Note = "Note",
            MerchantName = "MerchantName",
            OrderItem = new List<OrderItemResponse>()
            {
                new OrderItemResponse()
                {
                    OrderId = Guid.NewGuid(),
                    OrderItemId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    AddressId = Guid.NewGuid(),
                    ProductType = "ProductType",
                    DeletedFlag = false,
                    EposTicketId = "EposTicketId",
                    ProductCode = "GENESIS_SMART",
                    ProductInstanceIds = new List<Guid>()
                    {
                        Guid.Empty,
                        Guid.Empty
                    }
                }
            },
            CreatedDate = DateTime.UtcNow,
            UpdatedDate = DateTime.UtcNow,
            OrderNumber = "EX_1132",
            OrderCommentsCount = 1,
            DeletedFlag = false,
            CheckoutDate = DateTime.UtcNow,
            Subtotal = 1,
            Discount = 1,
            VatPercent = 1,
            Vat = 1,
            Total = 1,
            MonthlySubtotal = 1,
            MonthlyVat = 1,
            MonthlyTotal = 1,
            MonthlyDiscount = 1,
            YearlyDiscount = 1,
            YearlySubtotal = 1,
            YearlyTotal = 1,
            YearlyVat = 1,
            Currency = "SAR",
        };

        private readonly OrderDeleteRequest orderDeleteRequest = new OrderDeleteRequest()
        { OrderId = new Guid[] { Guid.NewGuid() } };

        private readonly OrderItemResponse orderItemResponse = new OrderItemResponse()
        {
            OrderItemId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            OrderId = Guid.NewGuid(),
            AddressId = Guid.NewGuid(),
            CreatedUnixTimestampUtc = 1,
            UpdatedUnixTimestampUtc = 1,
            DeletedFlag = false,
            ProductType = "ProductType",
            EposTicketId = "EposTicketId"
        };

        private readonly OrderSearchCriteria orderSearchCriteria = new OrderSearchCriteria()
        {
            OrderStatus = new List<string> { "OrderStatus" },
            MerchantStatus = new List<string> { "MerchantStatus" },
            MerchantId = Guid.NewGuid(),
            AmountIntervals = new List<AmountInterval>()
            {
                new AmountInterval() {AmountMin = 1, AmountMax = 100},
                new AmountInterval() {AmountMin = 1000, AmountMax = 2000}
            },
            DateInterval = new DateInterval
            {
                FromDate = DateTime.UtcNow,
                ToDate = DateTime.UtcNow
            },
            ProjectName = new List<string> { "ProjectName" }
        };

        private readonly CommentCreateRequest commentCreateRequest = new CommentCreateRequest()
        {
            CommentText = "text"
        };

        private readonly CommentUpdateRequest commentUpdateRequest = new CommentUpdateRequest()
        {
            CommentText = "new text"
        };

        private readonly OrderCommentResponse orderCommentResponse = new OrderCommentResponse()
        {
            OrderId = Guid.NewGuid(),
            CommentId = Guid.NewGuid(),
            CommentText = "text",
            DeletedFlag = false,
            CreatedBy = "user",
            UpdatedBy = "user",
            CreatedDate = DateTime.UtcNow,
            UpdatedDate = DateTime.UtcNow,
            AuthorName = "name",
            AuthorRole = "role"
        };

        private readonly SendWelcomeEmialRequest sendWelcomeEmialRequest = new SendWelcomeEmialRequest()
        {
            Language = "en",
            Recipient = "<EMAIL>",
            RecipientName = "testName"
        };

        private readonly List<OrderStatusResponse> orderStatusResponse = new List<OrderStatusResponse>()
        {
            new OrderStatusResponse()
            {
                OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                UpdatedBy = new Guid("5e72fe84-1e25-41a9-be0a-17453b2d0b68").ToString(),
                OrderStatusStartDate = DateTime.Today,
                OrderStatus = "status"
            },
            new OrderStatusResponse()
            {
                OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                UpdatedBy = Guid.NewGuid().ToString(),
                OrderStatusStartDate = DateTime.Today,
                OrderStatus = "status"
            }
        };

        private CheckoutService CreateCheckoutServiceInstance()
        {
            return new CheckoutService(
                logger,
                userService.Object,
                productService.Object,
                mmsMerchantAdapterService,
                mapper,
                counterpartyProvider.Object,
                referenceService.Object,
                merchantClient.Object,
                contractService,
                searchService.Object,
                gleComposePayloadMessagingClient,
                checkoutClient.Object,
                ePosMessagingService,
                postilionService,
                gsdkService.Object,
                notificationService.Object,
                shareholderService.Object,
                dueDiligenceService.Object,
                nexusBridgeService.Object
            );
        }

        private PostilionMessageBody postilionMessageBody = new PostilionMessageBody();

        [Test]
        public async Task UpdateOrderAsync_ShouldCallCreateMerchant_WhenOrderStatusIsVerified()
        {
            // Arrange
            var orderId = Guid.NewGuid();
            var storeId = Guid.NewGuid();
            var merchantId = Guid.NewGuid();
            var orderResponse = new OrderResponse
            {
                MerchantId = merchantId,
                StoreId = storeId,
                OrderStatus = OrderStatus.Verified
            };
            var merchant = new Merchant
            {
                MerchantId = merchantId,
                MerchantStatus = MerchantStatus.BoardingCompleted,
                MerchantDetails = new MerchantDetails
                {
                    BusinessEmail = "<EMAIL>",
                    ReferralChannel = "SomeChannel"
                }
            };
            var contacts = new List<MerchantShareholderIndividual>
            {
                new MerchantShareholderIndividual
                {
                   NationalId ="1",
                   Nationality ="hh",
                   FirstName ="name"
                }
            };
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            var store = new Merchant();
            var order = new OrderUpdateRequest
            {
                OrderStatus = "Verified"
            };
            var patchDoc = new JsonPatchDocument<OrderUpdateRequest>();
            patchDoc.Replace(o => o.OrderStatus, "Verified");
            patchDoc.ApplyTo(order);
            var emialRequest = new SendWelcomeEmialRequest
            {
                Language = "EN",
                Recipient = merchant.MerchantDetails.BusinessEmail,
                RecipientName = "Owner Name"
            };

            checkoutClient.Setup(c => c.GetOrderByIdAsync(orderId)).ReturnsAsync(orderResponse);
            merchantClient.Setup(m => m.GetCoreMerchantAsync(merchantId)).ReturnsAsync(merchant);
            shareholderService.Setup(s => s.GetMerchantIndividualsAsync(merchantId)).ReturnsAsync(contacts);
            merchantClient.Setup(m => m.GetStoreAsync(merchantId, storeId)).ReturnsAsync(store);
            nexusBridgeService.Setup(n => n.CreateMerchant(It.IsAny<Merchant>(), It.IsAny<List<MerchantShareholderIndividual>>(), It.IsAny<Merchant>(), It.IsAny<OrderResponse>(), true)).Returns(Task.CompletedTask);
            checkoutService = CreateCheckoutServiceInstance();
            // Act
            await checkoutService.UpdateOrderAsync(orderId, patchDoc, userId: null, checkOrderStatus: false);

            // Assert
            nexusBridgeService.Verify(n => n.CreateMerchant(merchant, contacts, store, orderResponse, false), Times.Once);
        }

        [Test]
        public async Task GetByOrderNumberAsync_WhenValid_ShouldReturnExpectedData()
        {
            checkoutClient.Setup(x => x.GetByOrderNumberAsync("id")).Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.GetByOrderNumberAsync("id");

            result.Should().BeEquivalentTo(orderResponse);
        }

        [Test]
        public void GetByOrderNumberAsync_WhenNotFound_ShouldThrowPassthroughtError()
        {
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync("id"))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.GetByOrderNumberAsync("id"))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }


        [Test]
        public void UpdateOrderAsync()
        {
            orderResponse.ProjectName = "test";

            checkoutService = CreateCheckoutServiceInstance();

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "OrderStatus ");
            patch.Replace(e => e.MerchantName, "MerchantName");
            patch.Replace(e => e.OrderStatus, "PRODUCTS_REGISTERED");
            patch.Replace(e => e.ProjectName, "test");

            var orderUpdateMessage = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();
            mmsMerchantAdapterService.Received(0).SendOrderUpdateProductRegisteredMessage(orderUpdateMessage);
        }
        [Test]
        public void UpdateOrderAsyncForVerification_In_Progress()
        {
            orderResponse.ProjectName = "test";

            checkoutService = CreateCheckoutServiceInstance();

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "VERIFICATION_IN_PROGRESS");
            patch.Replace(e => e.ProjectName, "test");

            merchantClient.Setup(x => x.GetStoreAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
               .Returns(Task.FromResult(new Merchant
               {
                   MerchantId = Guid.NewGuid(),
                   AccountConfig = new MerchantAccountConfig
                   {
                       DCCProvider = "test",
                       PayoutDay = 7,
                       PayoutCapAmount = 100,
                       OrderSecurityDepositPaymentMode = "test",
                       SettlementCurrency = "test",
                       TransactionCurrency = "test",
                       AcceptedPaymentMethods = "VC,MR",
                       OrderSecurityDepositPaymentReference = "test",
                       OrderSetupFeePaymentMode = "test",
                       OrderSetupFeePaymentReference = "test",
                       PayoutSchedule = "test",
                       SettlementTimeFrame = 1,
                       PerTransactionLimit = 1,
                       PerTransactionRefundLimit = 1,
                       TransactionType = "test",
                       PayoutMinAmount = 100,
                       AmexMID = "test",
                       TamaraMID = "test"
                   },
                   CommissionTypes = new List<MerchantCommissionConfig> { new MerchantCommissionConfig { CommissionType = "P", ProductCode = "AL", Value = (decimal)0.80 } }
               }));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);

            var responseConfig = new OrderConfigurationResponse
            {
                OrderAccountConfig = new OrderAccountConfig
                {
                    DCCProvider = "test",
                    PayoutDay = 7,
                    PayoutCapAmount = 100,
                    OrderSecurityDepositPaymentMode = "test",
                    SettlementCurrency = "test",
                    TransactionCurrency = "test",
                    AcceptedPaymentMethods = "VC,MR",
                    OrderSecurityDepositPaymentReference = "test",
                    OrderSetupFeePaymentMode = "test",
                    OrderSetupFeePaymentReference = "test",
                    PayoutSchedule = "test",
                    SettlementTimeFrame = 1,
                    PerTransactionLimit = 1,
                    PerTransactionRefundLimit = 1,
                    PayoutMinAmount = 100,
                    AmexMID = "test",
                    TamaraMID = "test",
                    TransactionType = "test"
                },
                OrderCommissionConfigs = new List<OrderCommissionConfig> { new OrderCommissionConfig { CommissionType = "P", ProductCode = "AL", Value = (decimal)0.80 } }
            };
            orderResponse.OrderStatus = OrderStatus.VerificationInProgress;
            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                 .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);

            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));

            checkoutClient
                .Setup(x => x.GetOrderConfigAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(responseConfig));
            checkoutClient
              .Setup(x => x.PatchOrderConfigAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderConfigurationResponse>>()));
            nexusBridgeService.Setup(client => client.CreateMerchant(It.IsAny<Merchant>(), It.IsAny<List<MerchantShareholderIndividual>>(), It.IsAny<Merchant>(), It.IsAny<OrderResponse>(), true));

            checkoutService.Invoking(x => x.UpdateOrderAsync(orderResponse.OrderId, patch))
                .Should()
                .NotThrowAsync<ServiceException>();
        }

        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_ProductRegistered_Or_Cancelled()
        {
            mmsMerchantAdapterService.ClearReceivedCalls();

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredSaudi));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.ProductRegistered);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();
            mmsMerchantAdapterService.Received(0)
                .SendOrderUpdateProductRegisteredMessage(Arg.Any<OrderUpdateMessage>());

            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.Cancelled);
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderResponse.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();
            await contractService.Received(1).UpdateMerchantContractAsDeletedAsync(orderResponse.OrderId);
        }

        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_ProductRegistered_Egypt_ShouldSendEposMessage()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));
            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.ProductRegistered);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

        }
        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_ProductRegistered_UAE_ShouldSendWelcomeEmailForCP()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            merchantClient
                .Setup(x => x.GetStoreAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.CPmerchantAccount));
            notificationService.Setup(x => x.SendWelcomeEmialForCPMerchantAccount(sendWelcomeEmialRequest));
            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.ProductRegistered);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

        }

        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_ProductRegistered_UAE_ShouldSendWelcomeEmailForCNP()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            merchantClient
                .Setup(x => x.GetStoreAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.CNPmerchantAccount));
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));
            notificationService.Setup(x => x.SendWelcomeEmialForCNPMerchantAccount(sendWelcomeEmialRequest));
            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.ProductRegistered);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();
            await ePosMessagingService.Received(0)
                .CreateOrderEPosTicketAsync(Arg.Is<Guid>(x => x == orderResponse.OrderId));

        }
        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_ProductRegistered_UAEHSBCMerchant_ShouldNotSendWelcomeEmail()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedHSBCMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            merchantClient
                .Setup(x => x.GetStoreAsync(It.IsAny<Guid>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.CPmerchantAccount));
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));
            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.ProductRegistered);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();
            await ePosMessagingService.Received(0)
                .CreateOrderEPosTicketAsync(Arg.Is<Guid>(x => x == orderResponse.OrderId));

        }

        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_Verified_Egypt_NBE_BANK()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));

            merchantClient.Setup(x => x.GetMerchantAcquiringLedgerByStoreIdAsync(Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3")))
                .Returns(Task.FromResult(TestsHelper.merchantAcquiringLedgerInfoWithDefaultStore));

            productService.Setup(x => x.GenerateTIDAndMIDAndAddEditTerminalDataSets(It.IsAny<TerminalDataSetRequest>()))
                .Returns(Task.FromResult(TestsHelper.terminalDataSetsResponse));

            productService.Setup(x => x.UpdateTerminalProductInstancesMeta(It.IsAny<List<UpdateProductInstanceMetaRequest>>()))
                .Returns(Task.FromResult(TestsHelper.updatedTerminalDataSetResponse));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);

            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));

            searchService.Setup(x => x.GetOrderProductInstancesWithTerminalDataAsync(orderResponse.OrderId, new List<string> { ProductType.Terminal, ProductType.Mpos })).
                Returns(Task.FromResult(
                    new Common.Models.Search.OrderWithInstances
                    {
                        OrderId = orderResponse.OrderId,
                        OrderNumber = "test",
                        StoreId = Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3"),
                        Instances = new List<Common.Models.Search.InstanceWithTerminalData> {
                            new Common.Models.Search.InstanceWithTerminalData(){},
                            new Common.Models.Search.InstanceWithTerminalData()
                        }
                    }
                ));

            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));

            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.Verified);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

            merchantClient.Setup(x => x.GetMerchantAcquiringLedgerByStoreIdAsync(Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3")))
                .Returns(Task.FromResult(TestsHelper.merchantAcquiringLedgerInfoWithoutDefaultStore));

            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

        }
        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_ProductRegistered_Egypt_NBE_BANK_ShouldSentToPostilion()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));

            merchantClient.Setup(x => x.GetMerchantAcquiringLedgerByStoreIdAsync(Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3")))
                .Returns(Task.FromResult(TestsHelper.merchantAcquiringLedgerInfoWithDefaultStore));

            productService.Setup(x => x.GenerateTIDAndMIDAndAddEditTerminalDataSets(It.IsAny<TerminalDataSetRequest>()))
                .Returns(Task.FromResult(TestsHelper.terminalDataSetsResponse));

            productService.Setup(x => x.UpdateTerminalProductInstancesMeta(It.IsAny<List<UpdateProductInstanceMetaRequest>>()))
                .Returns(Task.FromResult(TestsHelper.updatedTerminalDataSetResponse));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyUae);

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);

            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));

            searchService.Setup(x => x.GetOrderProductInstancesWithTerminalDataAsync(orderResponse.OrderId, new List<string> { ProductType.Terminal, ProductType.Mpos })).
                Returns(Task.FromResult(
                    new Common.Models.Search.OrderWithInstances
                    {
                        OrderId = orderResponse.OrderId,
                        OrderNumber = "test",
                        StoreId = Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3"),
                        Instances = new List<Common.Models.Search.InstanceWithTerminalData> {
                            new Common.Models.Search.InstanceWithTerminalData(){ ProductInstanceId = new Guid("00000000-0000-0000-0000-000000000001")},
                            new Common.Models.Search.InstanceWithTerminalData(){ ProductInstanceId = new Guid("00000000-0000-0000-0000-000000000002")}
                        }
                    }
                ));

            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));

            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.ProductRegistered);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

            postilionService.Received(0).SendMessageToPostilion(postilionMessageBody);

        }
        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_Verified_Egypt_NBE_BANK_ShouldCatchException()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));

            merchantClient.Setup(x => x.GetMerchantAcquiringLedgerByStoreIdAsync(Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3")))
                .Throws(new PassthroughException(new HttpResponseMessage()));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);

            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));

            searchService.Setup(x => x.GetOrderProductInstancesWithTerminalDataAsync(orderResponse.OrderId, new List<string> { ProductType.Terminal, ProductType.Mpos })).
                Returns(Task.FromResult(
                    new Common.Models.Search.OrderWithInstances
                    {
                        OrderId = orderResponse.OrderId,
                        OrderNumber = "test",
                        StoreId = Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3"),
                        Instances = new List<Common.Models.Search.InstanceWithTerminalData> {
                            new Common.Models.Search.InstanceWithTerminalData(){},
                            new Common.Models.Search.InstanceWithTerminalData()
                        }
                    }
                ));

            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));

            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.Verified);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<PassthroughException>();

        }

        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatus_Verified_WithoutProductInstanceWithTerminalData_ShouldSuccess()
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));

            merchantClient.Setup(x => x.GetMerchantAcquiringLedgerByStoreIdAsync(Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3")))
                .Returns(Task.FromResult(TestsHelper.merchantAcquiringLedgerInfoWithDefaultStore));

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);

            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));

            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));

            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.Verified);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

            searchService.Setup(x => x.GetOrderProductInstancesWithTerminalDataAsync(orderResponse.OrderId, new List<string> { ProductType.Terminal, ProductType.Mpos })).
                Returns(Task.FromResult(
                    new Common.Models.Search.OrderWithInstances
                    {
                        OrderId = orderResponse.OrderId,
                        OrderNumber = "test",
                        StoreId = Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3"),
                    }
                ));

            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

            searchService.Setup(x => x.GetOrderProductInstancesWithTerminalDataAsync(orderResponse.OrderId, new List<string> { ProductType.Terminal, ProductType.Mpos })).
                Returns(Task.FromResult(
                    new Common.Models.Search.OrderWithInstances
                    {
                        OrderId = orderResponse.OrderId,
                        OrderNumber = "test",
                        StoreId = Guid.Parse("0075495E-B046-45CD-89E7-74F0A6D80CA3"),
                        Instances = new List<Common.Models.Search.InstanceWithTerminalData>()
                    }
                ));

            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();
        }

        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatusDifferent()
        {
            mmsMerchantAdapterService.ClearReceivedCalls();
            contractService.ClearReceivedCalls();

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredSaudi));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.Verified);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .NotThrowAsync<ServiceException>();

            mmsMerchantAdapterService.Received(0)
                .SendOrderUpdateProductRegisteredMessage(Arg.Any<OrderUpdateMessage>());
            await contractService.Received(0).UpdateMerchantContractAsDeletedAsync(orderResponse.OrderId);
        }

        [Test]
        public void UpdateOrderAsync_ValidateProjectNameAndBundle_ShouldThrowException_ForOldValue()
        {
            checkoutService = CreateCheckoutServiceInstance();

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "OrderStatus ");
            patch.Replace(e => e.MerchantName, "MerchantName");
            patch.Replace(e => e.OrderStatus, "PRODUCTS_REGISTERED");
            patch.Replace(e => e.ProjectName, "test");

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(new Catalogue[]
                {
                    new Catalogue()
                    {
                        CatalogueName = Constants.Catalogues.ReferralChannelRestrictions,
                        Key = "SABB_GENESIS",
                        Value = "SABB_GENESIS"
                    }
                }));

            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .ThrowAsync<ServiceException>();
        }

        [Test]
        public void UpdateOrderAsync_ValidateProjectNameAndBundle_ShouldThrowException_ForNewValue()
        {
            orderResponse.ProjectName = "test";

            checkoutService = CreateCheckoutServiceInstance();

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "OrderStatus ");
            patch.Replace(e => e.MerchantName, "MerchantName");
            patch.Replace(e => e.OrderStatus, "PRODUCTS_REGISTERED");
            patch.Replace(e => e.ProjectName, "SABB_GENESIS");

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(new Catalogue[]
                {
                    new Catalogue()
                    {
                        CatalogueName = Constants.Catalogues.ReferralChannelRestrictions,
                        Key = "SABB_GENESIS",
                        Value = "SABB_GENESIS"
                    }
                }));

            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .ThrowAsync<ServiceException>();
        }

        [Test]
        public void UpdateOrderAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "OrderStatus ");
            patch.Replace(e => e.MerchantName, "MerchantName");

            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }
        [Test]
        public async Task UpdateOrderAsync_WhenMerchantStatusIsVerified_ShouldCallNexusBridge()
        {
            mmsMerchantAdapterService.ClearReceivedCalls();
            contractService.ClearReceivedCalls();

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredSaudi));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant { MerchantId = Guid.NewGuid() }));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            checkoutClient
               .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
               .Returns(Task.FromResult(orderResponse));
            nexusBridgeService.Setup(client => client.CreateMerchant(It.IsAny<Merchant>(), It.IsAny<List<MerchantShareholderIndividual>>(), It.IsAny<Merchant>(), It.IsAny<OrderResponse>(), true));

            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.Verified);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .ThrowAsync<ServiceException>();

            mmsMerchantAdapterService.Received(0)
                .SendOrderUpdateProductRegisteredMessage(Arg.Any<OrderUpdateMessage>());
            await contractService.Received(0).UpdateMerchantContractAsDeletedAsync(orderResponse.OrderId);
        }

        [Test]
        public async Task UpdateOrderAsync_WhenOrderStatusVerifiedAndMerchantStatusNotVerified_ShouldThrowException()
        {
            mmsMerchantAdapterService.ClearReceivedCalls();
            contractService.ClearReceivedCalls();

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredSaudi));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new Merchant { MerchantId = Guid.NewGuid() }));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            checkoutClient
               .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
               .Returns(Task.FromResult(orderResponse));

            checkoutService = CreateCheckoutServiceInstance();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();
            orderUpdateRequestPatch.Replace(e => e.OrderStatus, OrderStatus.Verified);

            var orderUpdate = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            await checkoutService.Invoking(x => x.UpdateOrderAsync(orderUpdate.OrderId, orderUpdateRequestPatch))
                .Should()
                .ThrowAsync<ServiceException>();

            mmsMerchantAdapterService.Received(0)
                .SendOrderUpdateProductRegisteredMessage(Arg.Any<OrderUpdateMessage>());
            await contractService.Received(0).UpdateMerchantContractAsDeletedAsync(orderResponse.OrderId);
        }

        [Test]
        public void DeleteOrderAsync()
        {
            checkoutClient
                .Setup(x => x.DeleteOrderAsync(It.IsAny<OrderDeleteRequest>()))
                .Returns(Task.CompletedTask);
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.DeleteOrderAsync(orderDeleteRequest)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteOrderAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.DeleteOrderAsync(It.IsAny<OrderDeleteRequest>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.DeleteOrderAsync(orderDeleteRequest))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetAllOrdersAsync()
        {
            checkoutClient
                .Setup(x => x.GetAllOrdersAsync(It.IsAny<bool>()))
                .Returns(Task.FromResult(new[] { orderResponse }));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.GetAllOrdersAsync(false);

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderResponse);
        }

        [Test]
        public void GetAllOrdersAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.GetAllOrdersAsync(It.IsAny<bool>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.GetAllOrdersAsync(false))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetOrderItemsByIdAsync()
        {
            checkoutClient
                .Setup(x => x.GetOrderItemsByIdAsync(It.IsAny<Guid[]>()))
                .Returns(Task.FromResult(new[] { orderItemResponse }));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.GetOrderItemsByIdAsync(new Guid[] { Guid.NewGuid() });

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderItemResponse);
        }

        [Test]
        public void GetOrderItemsByIdAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.GetOrderItemsByIdAsync(It.IsAny<Guid[]>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.GetOrderItemsByIdAsync(new Guid[] { Guid.NewGuid() }))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task SearchOrderAsync()
        {
            var orderSearchResponse = new OrderSearchResponse
            { TotalRecordCount = 1, ReturnedRecordCount = 1, Records = new OrderResponse[] { orderResponse } };
            checkoutClient
                .Setup(x => x.SearchOrderAsync(It.IsAny<CoreOrderSearchCriteria>()))
                .Returns(Task.FromResult(orderSearchResponse.Records));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.SearchOrderAsync(orderSearchCriteria);

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderResponse);
        }

        [Test]
        public void SearchOrderAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.SearchOrderAsync(It.IsAny<CoreOrderSearchCriteria>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.SearchOrderAsync(orderSearchCriteria))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteOrderItemAsync()
        {
            checkoutClient
                .Setup(x => x.DeleteOrderItemAsync(It.IsAny<Guid[]>()))
                .Returns(Task.FromResult(Task.CompletedTask));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.DeleteOrderItemAsync(new Guid[] { Guid.NewGuid() })).Should()
                .NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteOrderItemAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.DeleteOrderItemAsync(It.IsAny<Guid[]>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.DeleteOrderItemAsync(new Guid[] { Guid.NewGuid() }))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }


        [Test]
        public void UpdateOrderItemAsync()
        {
            checkoutClient
                .Setup(x => x.UpdateOrderItemAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderItemUpdateRequest>>()))
                .Returns(Task.FromResult(Task.CompletedTask));
            checkoutService = CreateCheckoutServiceInstance();

            var patch = new JsonPatchDocument<OrderItemUpdateRequest>();

            checkoutService.Invoking(x => x.UpdateOrderItemAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateOrderItemAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.UpdateOrderItemAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderItemUpdateRequest>>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            var patch = new JsonPatchDocument<OrderItemUpdateRequest>();

            checkoutService.Invoking(x => x.UpdateOrderItemAsync(Guid.NewGuid(), patch))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task CreateCommentAsync()
        {
            checkoutClient
                .Setup(x => x.CreateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentCreateRequest>()))
                .Returns(Task.FromResult(orderCommentResponse));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.CreateCommentAsync(Guid.NewGuid(), commentCreateRequest);
            result.Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void CreateCommentAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.CreateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentCreateRequest>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.CreateCommentAsync(Guid.NewGuid(), commentCreateRequest))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetCommentByIdAsync()
        {
            checkoutClient
                .Setup(x => x.GetCommentByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderCommentResponse));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.GetCommentByIdAsync(Guid.NewGuid());
            result.Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void GetCommentByIdAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.GetCommentByIdAsync(It.IsAny<Guid>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.GetCommentByIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task UpdateCommentAsync()
        {
            checkoutClient
                .Setup(x => x.UpdateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentUpdateRequest>()))
                .Returns(Task.FromResult(orderCommentResponse));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.UpdateCommentAsync(Guid.NewGuid(), commentUpdateRequest);
            result.Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void UpdateCommentAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.UpdateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentUpdateRequest>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.UpdateCommentAsync(Guid.NewGuid(), commentUpdateRequest))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteCommentAsync()
        {
            checkoutClient
                .Setup(x => x.DeleteCommentAsync(It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.DeleteCommentAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteCommentAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.DeleteCommentAsync(It.IsAny<Guid>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.DeleteCommentAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetCommentByOrderIdAsync()
        {
            checkoutClient
                .Setup(x => x.GetCommentByOrderIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(new[] { orderCommentResponse }));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.GetCommentByOrderIdAsync(Guid.NewGuid());
            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void GetCommentByOrderIdAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.GetCommentByOrderIdAsync(It.IsAny<Guid>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.GetCommentByOrderIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetOrdersStatusHistoryAsync()
        {
            var expectedOrderStatusResponses = new List<OrderStatusResponse>()
            {
                new OrderStatusResponse()
                {
                    OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                    UpdatedBy = "FirstName LastName",
                    OrderStatusStartDate = DateTime.Today,
                    OrderStatus = "status"
                },
                new OrderStatusResponse()
                {
                    OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                    UpdatedBy = Constants.User.DefaultUserValue,
                    OrderStatusStartDate = DateTime.Today,
                    OrderStatus = "status"
                }
            };

            var user = new Common.Models.User.User()
            {
                Id = new Guid("5e72fe84-1e25-41a9-be0a-17453b2d0b68"),
                FirstName = "FirstName",
                LastName = "LastName",
                Email = "<EMAIL>"
            };
            userService.Setup(x => x.GetAllUsersAsync())
                .Returns(Task.FromResult(new Common.Models.User.User[] { user }));

            checkoutClient
                .Setup(x => x.GetOrdersStatusHistoryAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderStatusResponse));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.GetOrdersStatusHistoryAsync(Guid.NewGuid());
            result.Count.Should().Be(2);
            result.Should().BeEquivalentTo(expectedOrderStatusResponses);
        }

        [Test]
        public void GetOrdersStatusHistoryAsync_PassthroughError()
        {
            checkoutClient
                .Setup(x => x.GetOrdersStatusHistoryAsync(It.IsAny<Guid>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            checkoutService.Invoking(x => x.GetOrdersStatusHistoryAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task SearchOrderAsync_whenProductServiceReturnsNoProductIds_ReturnsEmptyOrdersArray()
        {
            checkoutClient
                .Setup(x => x.SearchOrderAsync(It.IsAny<CoreOrderSearchCriteria>()))
                .Returns(Task.FromResult(Array.Empty<OrderResponse>()));
            productService.Setup(x => x.GetRelatedProductsAsync(It.IsAny<ProductCodesRequest>()))
                .Returns(Task.FromResult(Array.Empty<Guid>()));
            checkoutService = CreateCheckoutServiceInstance();

            var result = await checkoutService.SearchOrderAsync(new OrderSearchCriteria()
            { ProductCodes = new List<string>() { "GO_AIR" } });
            result.Length.Should().Be(0);
        }

        [Test]
        [TestCase(
            "qnotoieboynveiqqiefcewznjllzjfhizcktkzhdqddrjpzenbxiegxadfnpvwkjssqyqkiadyshmnlguvckkukzyuvkxhzlwihsfyqhdqpgixnnrwohkgtjzksbhywxngcequsrjkhsozcllrpudkoidjjbvcctgvpsvfhezhedckgaosoojtieqhjgkanpzohwkniyvtlsiwilxekyawrcomdovrfnlnskrgqopzltetqpdfdsiwnymgzbjlynzugycopvarwrtlfllumjqglfcmjkfaxtrurmectuzmeomhumzpwofvislhxnwjlgercaiencyzzahthlahdzkpybcynbccdgumqpesltnufslwqziikabkrennqxrtbqwgtfldrfqkgnrkhxagvsyttbcomydnfzfzecoqdvyplgeiftyycxcxlhvfpvlgsgzyiflillpxfjiduagzymdlpkyhwmcvncrakevlyjocfvbznaxsqevttqctudmwbsn")]
        public void UpdateCommentAsync_CommentValidator_ShouldThrowValidationException(string commentText)
        {
            checkoutClient
                .Setup(x => x.UpdateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentUpdateRequest>()))
                .Returns(Task.FromResult(new OrderCommentResponse()));
            checkoutService = CreateCheckoutServiceInstance();

            var commentId = Guid.NewGuid();

            var commentTextUpdateRequest = new CommentUpdateRequest()
            {
                CommentText = commentText
            };

            checkoutService.Invoking(x => x.UpdateCommentAsync(commentId, commentTextUpdateRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                            TestsHelper.HasErrorCode(x, Errors.CommentLengthValidation.Code));
        }

        [Test]
        [TestCase("test")]
        public void UpdateCommentAsync_CommentValidator_ShouldNotThrowValidationException(string commentText)
        {
            checkoutClient
                .Setup(x => x.UpdateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentUpdateRequest>()))
                .Returns(Task.FromResult(new OrderCommentResponse()));
            checkoutService = CreateCheckoutServiceInstance();

            Guid commentId = Guid.NewGuid();

            var commentTextUpdateRequest = new CommentUpdateRequest()
            {
                CommentText = commentText
            };

            checkoutService.Invoking(x => x.UpdateCommentAsync(commentId, commentTextUpdateRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase(
            "qnotoieboynveiqqiefcewznjllzjfhizcktkzhdqddrjpzenbxiegxadfnpvwkjssqyqkiadyshmnlguvckkukzyuvkxhzlwihsfyqhdqpgixnnrwohkgtjzksbhywxngcequsrjkhsozcllrpudkoidjjbvcctgvpsvfhezhedckgaosoojtieqhjgkanpzohwkniyvtlsiwilxekyawrcomdovrfnlnskrgqopzltetqpdfdsiwnymgzbjlynzugycopvarwrtlfllumjqglfcmjkfaxtrurmectuzmeomhumzpwofvislhxnwjlgercaiencyzzahthlahdzkpybcynbccdgumqpesltnufslwqziikabkrennqxrtbqwgtfldrfqkgnrkhxagvsyttbcomydnfzfzecoqdvyplgeiftyycxcxlhvfpvlgsgzyiflillpxfjiduagzymdlpkyhwmcvncrakevlyjocfvbznaxsqevttqctudmwbsn")]
        public void CreateCommentAsync_CommentValidator_ShouldThrowValidationException(string commentText)
        {
            checkoutClient
                .Setup(x => x.UpdateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentUpdateRequest>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.BadRequest)));
            checkoutService = CreateCheckoutServiceInstance();

            Guid orderId = Guid.NewGuid();

            var commentTextCreateRequest = new CommentCreateRequest()
            {
                CommentText = commentText
            };

            checkoutService.Invoking(x => x.CreateCommentAsync(orderId, commentTextCreateRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                            TestsHelper.HasErrorCode(x, Errors.CommentLengthValidation.Code));
        }

        [Test]
        [TestCase("test")]
        public void CreateCommentAsync_CommentValidator_ShouldNotThrowValidationException(string commentText)
        {
            checkoutClient
                .Setup(x => x.CreateCommentAsync(It.IsAny<Guid>(), It.IsAny<CommentCreateRequest>()))
                .Returns(Task.FromResult(new OrderCommentResponse()));
            checkoutService = CreateCheckoutServiceInstance();

            var orderId = Guid.NewGuid();

            var commentTextCreateRequest = new CommentCreateRequest()
            {
                CommentText = commentText
            };

            checkoutService.Invoking(x => x.CreateCommentAsync(orderId, commentTextCreateRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("ewjgotssrdyijzjuzqsbqqryalhuewxvtnzywrhygyasnwhcozseconkeuypddinu", "12345")]
        [TestCase("Test", "12345123451234512345")]
        public void UpdateCommentAsync_OrderItemUpdateRequestValidator_ShouldThrowValidationException(
            string productType, string eposTicketId)
        {
            checkoutClient
                .Setup(x => x.UpdateOrderItemAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderItemUpdateRequest>>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.BadRequest)));
            checkoutService = CreateCheckoutServiceInstance();

            Guid addressId = Guid.NewGuid();
            Guid orderItemId = Guid.NewGuid();

            var orderItemUpdate = new OrderItemUpdateRequest()
            {
                ProductType = productType,
                AddressId = addressId,
                EposTicketId = eposTicketId
            };

            var orderItemUpdatesData = new JsonPatchDocument<OrderItemUpdateRequest>();

            orderItemUpdatesData.Replace(e => e.ProductType, orderItemUpdate.ProductType);
            orderItemUpdatesData.Replace(e => e.AddressId, orderItemUpdate.AddressId);
            orderItemUpdatesData.Replace(e => e.EposTicketId, orderItemUpdate.EposTicketId);

            checkoutService.Invoking(x => x.UpdateOrderItemAsync(orderItemId, orderItemUpdatesData)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.ProductTypeLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.EposTicketIdLengthValidation.Code)));
        }

        [Test]
        [TestCase("Test", "12345")]
        [TestCase(null, null)]
        public void UpdateCommentAsync_OrderItemUpdateRequestValidator_ShouldNotThrowValidationException(
            string productType, string eposTicketId)
        {
            checkoutClient
                .Setup(x => x.UpdateOrderItemAsync(It.IsAny<Guid>(), It.IsAny<JsonPatchDocument<OrderItemUpdateRequest>>()))
                .Returns(Task.CompletedTask);
            checkoutService = CreateCheckoutServiceInstance();

            Guid addressId = Guid.NewGuid();
            Guid orderItemId = Guid.NewGuid();

            var orderItemUpdate = new OrderItemUpdateRequest()
            {
                ProductType = productType,
                AddressId = addressId,
                EposTicketId = eposTicketId
            };

            var orderItemUpdatesData = new JsonPatchDocument<OrderItemUpdateRequest>();

            orderItemUpdatesData.Replace(e => e.ProductType, orderItemUpdate.ProductType);
            orderItemUpdatesData.Replace(e => e.AddressId, orderItemUpdate.AddressId);
            orderItemUpdatesData.Replace(e => e.EposTicketId, orderItemUpdate.EposTicketId);

            checkoutService.Invoking(x => x.UpdateOrderItemAsync(orderItemId, orderItemUpdatesData)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [Test]
        [TestCase("PaymentReference", "ewjgotssrdyijzjuzqsbqqryalhuewxvtnzywrhygyasnwhcozseconkeuypddinu")]
        [TestCase("TrackingNumber", "apuoeniedcukxkcijluqywiefsrgakevw")]
        [TestCase("TrackingUrl",
            "wfpconrxcrjsxidithciwbztrfygenzpgpacavvppvhytjenaynlgeynrwfdpchlmoevoldinimtfxrynqwyphowfdjlnqqouweirimmibmkvimtutceojxnlfpwugnqdldokrxufhxftwqrewikevgudfpydbdogvngytguoxnnrksgiqrfixzcutvlkxvukaizthujybcigmmqdlovvzmrzqqluvqcdoimkmpnvyumkaovlbcaasbotzsuxqvxs")]
        [TestCase("Shipper", "ewjgotssrdyijzjuzqsbqqryalhuewxvtnzywrhygyasnwhcozseconkeuypddinu")]
        [TestCase("CouponCode", "apuoeniedcukxkcijluqywiefsrgakevw")]
        [TestCase("PaymentMethod", "apuoeniedcukxkcijluqywiefsrgakevw")]
        [TestCase("CompanyRegNo", "apuoeniedcukxkcijluqywiefsrgakevw")]
        [TestCase("SalesName", "ewjgotssrdyijzjuzqsbqqryalhuewxvtnzywrhygyasnwhcozseconkeuypddinu")]
        [TestCase("SubscriptionPlan", "apuoeniedcukxkcijluqywiefsrgakevw")]
        [TestCase("ProjectName", "apuoeniedcukxkcijluqywiefsrgakevw")]
        [TestCase("Note",
            "zgmcxyqnsczcwviqqevtaeieekurerexckcbarclegdqnwdsxvnazfpsbusgmbkzflcosrkbsvmwvfxeenqzlyzferfwlhqmulnyytbpiobttfqhmaezgtvykvwlowpillgovvltpftjhexfewhmdzwbmhikvgxoemtwmtvsgnittozkgigmleqlczphouqiakksoprnquzjpohotyhlxaxibnbgetzltefrkxqlgroolqjaeqkxazmafyccuqsmfxafrmsfaoxdoqgywrjhixfqbwovvacmnukzkyegxxmjfylmqomtrgkbapliegmxaydilrupeatfhjmuszbuwkluazmehpmkrzwengwdcqesnltzqtuouqyfwqiosvrawvqmlbzeerqvbtefpsvkknnffknpzrpczixhgcdwvdbxnqpkrjmbjqkgkcgvpmlrmyonajfwdhwfuwlsinuoztqpfhnwhoggfyyfyaiieauhrkxebuaaqnwyqbmfbrelv")]
        [TestCase("OrderStatus", "apuoeniedcukxkcijluqywiefsrgakevw")]
        [TestCase("MerchantName", "ewjgotssrdyijzjuzqsbqqryalhuewxvtnzywrhygyasnwhcozseconkeuypddinu")]
        [TestCase("Currency", "kpsu")]
        public void UpdateOrderAsync_OrderUpdateRequestValidator_ShouldThrowValidationException(string path,
            string value)
        {
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.BadRequest)));
            checkoutService = CreateCheckoutServiceInstance();

            Guid orderId = Guid.NewGuid();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();

            orderUpdateRequestPatch.Operations.Add(new Operation<OrderUpdateRequest>("replace", path, null, value));

            checkoutService.Invoking(x => x.UpdateOrderAsync(orderId, orderUpdateRequestPatch)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest
                            && (TestsHelper.HasErrorCode(x, Errors.PaymentReferenceLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.TrackingNumberLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.TrackingUrlLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.ShipperLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.CouponCodeLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.PaymentMethodLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.CompanyRegNoLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.SalesNameLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.SubscriptionPlanLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.ProjectNameLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.NoteLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.OrderStatusLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.MerchantNameLengthValidation.Code)
                                || TestsHelper.HasErrorCode(x, Errors.CurrencyLengthValidation.Code)));
        }

        [TestCase("PaymentReference", "PaymentReference")]
        [TestCase("TrackingNumber", "TrackingNumber")]
        [TestCase("TrackingUrl", "TrackingUrl")]
        [TestCase("Shipper", "Shipper")]
        [TestCase("CouponCode", "CouponCode")]
        [TestCase("PaymentMethod", "PaymentMethod")]
        [TestCase("CompanyRegNo", "CompanyRegNo")]
        [TestCase("SalesName", "SalesName")]
        [TestCase("SubscriptionPlan", "SubscriptionPlan")]
        [TestCase("ProjectName", "ProjectName")]
        [TestCase("Note", "Note")]
        [TestCase("OrderStatus", "OrderStatus")]
        [TestCase("MerchantName", "MerchantName")]
        [TestCase("Currency", "Ren")]
        public void UpdateOrderAsync_OrderUpdateRequestValidator_ShouldNotThrowValidationException(string path,
            string value)
        {
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.BadRequest)));
            checkoutService = CreateCheckoutServiceInstance();

            Guid orderId = Guid.NewGuid();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();

            orderUpdateRequestPatch.Operations.Add(new Operation<OrderUpdateRequest>("replace", path, null, value));

            checkoutService.Invoking(x => x.UpdateOrderAsync(orderId, orderUpdateRequestPatch)).Should()
                .NotThrowAsync<ValidationException>();
        }

        [TestCase("OrderStatus", "PRODUCTS_REGISTERED")]
        public void UpdateOrderAsync_ProductRegisteredOrderStatus_ShouldThrowTerminalProductsNotConfiguredErrorSaudi(
            string path, string value)
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalNotConfiguredSaudi));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            Guid orderId = Guid.NewGuid();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();

            orderUpdateRequestPatch.Operations.Add(new Operation<OrderUpdateRequest>("replace", path, null, value));

            checkoutService.Invoking(x => x.UpdateOrderAsync(orderId, orderUpdateRequestPatch)).Should()
                .ThrowAsync<ServiceException>();
        }

        [TestCase("OrderStatus", "PRODUCTS_REGISTERED")]
        public void UpdateOrderAsync_ProductRegisteredOrderStatus_ShouldThrowTerminalProductsNotConfiguredErrorEgypt(
            string path, string value)
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalNotConfiguredEgypt));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            Guid orderId = Guid.NewGuid();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();

            orderUpdateRequestPatch.Operations.Add(new Operation<OrderUpdateRequest>("replace", path, null, value));

            checkoutService.Invoking(x => x.UpdateOrderAsync(orderId, orderUpdateRequestPatch)).Should()
                .ThrowAsync<ServiceException>();
        }

        [TestCase("OrderStatus", "PRODUCTS_REGISTERED")]
        public void UpdateOrderAsync_ProductRegisteredOrderStatus_ShouldThrowGatewayProductsNotConfiguredError(
            string path, string value)
        {
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceGatewayNotConfigured));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            Guid orderId = Guid.NewGuid();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();

            orderUpdateRequestPatch.Operations.Add(new Operation<OrderUpdateRequest>("replace", path, null, value));

            checkoutService.Invoking(x => x.UpdateOrderAsync(orderId, orderUpdateRequestPatch)).Should()
                .ThrowAsync<ServiceException>();
        }

        [TestCase("OrderStatus", "PRODUCTS_REGISTERED")]
        public void UpdateOrderAsync_ProductRegisteredOrderStatus_ShouldThrowGatewayIfMerchantStatusNotVerified(
            string path, string value)
        {
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.notVerifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            Guid orderId = Guid.NewGuid();

            var orderUpdateRequestPatch = new JsonPatchDocument<OrderUpdateRequest>();

            orderUpdateRequestPatch.Operations.Add(new Operation<OrderUpdateRequest>("replace", path, null, value));

            checkoutService.Invoking(x => x.UpdateOrderAsync(orderId, orderUpdateRequestPatch)).Should()
                .ThrowAsync<ServiceException>();
        }

        [TestCase(OrderStatus.ProductRegistered, true, false, 1)]
        [TestCase(OrderStatus.Verified, true, false, 0)]
        [TestCase(OrderStatus.Submitted, true, false, 0)]
        [TestCase(OrderStatus.ProductRegistered, false, true, 0)]
        [TestCase(OrderStatus.Verified, false, true, 1)]
        [TestCase(OrderStatus.Submitted, false, true, 1)]
        [TestCase(OrderStatus.ProductRegistered, false, false, 0)]
        public void UpdateOrderAsync_BillPayment_ShouldSendGleMessage(string orderStatus, bool hasBillPaymentBundle, bool hasBillPaymentService, int expectedGleComposeCalls)
        {
            gleComposePayloadMessagingClient.ClearReceivedCalls();

            orderResponse.ProjectName = "test";

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = hasBillPaymentBundle,
                    HasBillPaymentService = hasBillPaymentService
                }));

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, orderStatus);
            patch.Replace(e => e.ProjectName, "test");

            var orderUpdateMessage = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();

            gleComposePayloadMessagingClient.Received(expectedGleComposeCalls).SendGleComposePayloadMessage(Arg.Any<GleComposePayloadRequest>());

            mmsMerchantAdapterService.Received(0).SendOrderUpdateProductRegisteredMessage(orderUpdateMessage);
        }

        [TestCase(OrderStatus.ProductRegistered, true, false, 1, Constants.ProductTypes.Terminal, Constants.GsdkContractPaymentWay.Terminal)]
        [TestCase(OrderStatus.ProductRegistered, true, false, 1, Constants.ProductTypes.MPOS, Constants.GsdkContractPaymentWay.Terminal)]
        [TestCase(OrderStatus.ProductRegistered, true, false, 1, Constants.ProductTypes.Gateway, Constants.GsdkContractPaymentWay.Gateway)]
        [TestCase(OrderStatus.ProductRegistered, true, false, 1, Constants.ProductTypes.Terminal, Constants.GsdkContractPaymentWay.SoftPos)]
        public void UpdateOrderAsync_NBEMerchantHasTMSCContract_ShouldUpdate(string orderStatus, bool hasBillPaymentBundle, bool hasBillPaymentService, int expectedGleComposeCalls, string productType, string paymentWay)
        {
            gleComposePayloadMessagingClient.ClearReceivedCalls();

            orderResponse.ProjectName = "test";

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = hasBillPaymentBundle,
                    HasBillPaymentService = hasBillPaymentService
                }));


            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.GetProductInstanceConfiguredEgypt(productType)));

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            merchantClient
                .Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.GetMerchant(orderResponse.MerchantId, Constants.CounterParty.Egypt, Constants.MmsLedgers.EgyptNBEBank)));

            referenceService
                .Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GsdkEgyptCatalogues));

            gsdkService
                .Setup(x => x.ValidateTmscContractsForProducts(It.IsAny<List<ProductInstance>>(), It.IsAny<Merchant>()))
                .Returns(Task.FromResult(true));

            merchantClient
                .Setup(x => x.GetMerchantMmsContract(It.IsAny<SearchContractMappingFilter>()))
                .Returns(Task.FromResult(new List<Common.Models.Merchant.MerchantExternalContractMappingResponse>() { new Common.Models.Merchant.MerchantExternalContractMappingResponse() { Id = Guid.NewGuid() } }));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, orderStatus);
            patch.Replace(e => e.ProjectName, "test");

            var orderUpdateMessage = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();

            gleComposePayloadMessagingClient.Received(expectedGleComposeCalls).SendGleComposePayloadMessage(Arg.Any<GleComposePayloadRequest>());

            mmsMerchantAdapterService.Received(0).SendOrderUpdateProductRegisteredMessage(orderUpdateMessage);
        }

        [TestCase(OrderStatus.ProductRegistered, true, false, 1)]
        public void UpdateOrderAsync_HasAbilityToAssigenDefaultContractForNBEMerchant_ShouldUpdate(string orderStatus, bool hasBillPaymentBundle, bool hasBillPaymentService, int expectedGleComposeCalls)
        {
            gleComposePayloadMessagingClient.ClearReceivedCalls();

            orderResponse.ProjectName = "test";

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = hasBillPaymentBundle,
                    HasBillPaymentService = hasBillPaymentService
                }));


            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                 .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            merchantClient
                .Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.GetMerchant(orderResponse.MerchantId, Constants.CounterParty.Egypt, Constants.MmsLedgers.EgyptNBEBank)));

            referenceService
                .Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GsdkEgyptCatalogues));

            gsdkService
                .Setup(x => x.ValidateTmscContractsForProducts(It.IsAny<List<ProductInstance>>(), It.IsAny<Merchant>()))
                .Returns(Task.FromResult(true));

            gsdkClient
                .Setup(x => x.FindGlobalContracts(It.IsAny<GlobalContractsFilter>(), It.IsAny<string>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GetGlobalContractsWithDefaultContract(Constants.GsdkContractPaymentWay.Terminal, Constants.OutProviderAccounts.EgyptNBEBank)));

            merchantClient
                .Setup(x => x.GetMerchantMmsContract(It.IsAny<SearchContractMappingFilter>()))
                .Returns(Task.FromResult<List<MerchantExternalContractMappingResponse>>(null!));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, orderStatus);
            patch.Replace(e => e.ProjectName, "test");

            var orderUpdateMessage = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();

            gleComposePayloadMessagingClient.Received(expectedGleComposeCalls).SendGleComposePayloadMessage(Arg.Any<GleComposePayloadRequest>());

            mmsMerchantAdapterService.Received(0).SendOrderUpdateProductRegisteredMessage(orderUpdateMessage);
        }

        [TestCase(OrderStatus.ProductRegistered, true, false, 0)]
        public void UpdateOrderAsync_HasNoAbilityToAssignContractAndNBEMerchantNotHasContract_ShouldThrowException(string orderStatus, bool hasBillPaymentBundle, bool hasBillPaymentService, int expectedGleComposeCalls)
        {
            gleComposePayloadMessagingClient.ClearReceivedCalls();

            orderResponse.ProjectName = "test";

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = hasBillPaymentBundle,
                    HasBillPaymentService = hasBillPaymentService
                }));


            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));

            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            checkoutClient
                .Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            merchantClient
                .Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.GetMerchant(orderResponse.MerchantId, Constants.CounterParty.Egypt, Constants.MmsLedgers.EgyptNBEBank)));

            referenceService
                .Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GsdkEgyptCatalogues));

            gsdkService
                .Setup(x => x.ValidateTmscContractsForProducts(It.IsAny<List<ProductInstance>>(), It.IsAny<Merchant>()))
                .Returns(Task.FromResult(false));

            gsdkClient
                .Setup(x => x.FindGlobalContracts(It.IsAny<GlobalContractsFilter>(), It.IsAny<string>(), It.IsAny<string>()))
                .Returns(Task.FromResult(TestsHelper.GetGlobalContractsWithDefaultContract(Constants.GsdkContractPaymentWay.Terminal, Constants.OutProviderAccounts.EgyptMisrBank)));

            merchantClient
                .Setup(x => x.GetMerchantMmsContract(It.IsAny<SearchContractMappingFilter>()))
                .Returns(Task.FromResult<List<MerchantExternalContractMappingResponse>>(null!));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, orderStatus);
            patch.Replace(e => e.ProjectName, "test");

            var orderUpdateMessage = new OrderUpdateMessage { OrderId = orderResponse.OrderId };
            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .ThrowAsync<ServiceException>();

            gleComposePayloadMessagingClient.Received(expectedGleComposeCalls).SendGleComposePayloadMessage(Arg.Any<GleComposePayloadRequest>());

            mmsMerchantAdapterService.Received(0).SendOrderUpdateProductRegisteredMessage(orderUpdateMessage);
        }

        [Test]
        public void UpdateOrderAsync_BillPayment_InvalidCounterparty_ShouldNotSendGleMessage()
        {
            productService.Invocations.Clear();
            orderResponse.ProjectName = "test";

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "");
            patch.Replace(e => e.ProjectName, "test");

            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();

            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never());
        }

        [Test]
        public void UpdateOrderAsync_BillPayment_EmptyOrderStatus_ShouldNotSendGleMessage()
        {
            productService.Invocations.Clear();
            orderResponse.ProjectName = "test";

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, string.Empty);
            patch.Replace(e => e.ProjectName, "test");

            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();

            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never());
        }

        [TestCaseSource(nameof(GetInvalidOrderItems))]
        public void UpdateOrderAsync_BillPayment_NoOrderItems_ShouldNotSendGleMessage(List<OrderItemResponse>? orderItems)
        {
            productService.Invocations.Clear();

            var order = new OrderResponse
            {
                OrderId = Guid.NewGuid(),
                AgreementId = Guid.NewGuid(),
                MerchantId = Guid.NewGuid(),
                StoreId = Guid.NewGuid(),
                UserId = Guid.NewGuid(),
                AddressId = Guid.NewGuid(),
                OrderStatus = "OrderStauts",
                PaymentReference = "PaymentReference",
                TrackingNumber = "TrackingNumber",
                TrackingUrl = "TrackingUrl",
                Shipper = "Shipper",
                ShippedDate = DateTime.UtcNow,
                CouponCode = "CouponCode",
                PaymentMethod = "PaymentMethod",
                CompanyRegNo = "CompanyRegNo",
                SalesName = "SalesName",
                SubscriptionPlan = "SubscriptionPlan",
                ProjectName = "SABB_GENESIS",
                ReferralChannel = "UNASSIGNED",
                Note = "Note",
                MerchantName = "MerchantName",
                OrderItem = orderItems,
                CreatedDate = DateTime.UtcNow,
                UpdatedDate = DateTime.UtcNow,
                OrderNumber = "1",
                OrderCommentsCount = 1,
                DeletedFlag = false,
                CheckoutDate = DateTime.UtcNow,
                Subtotal = 1,
                Discount = 1,
                VatPercent = 1,
                Vat = 1,
                Total = 1,
                MonthlySubtotal = 1,
                MonthlyVat = 1,
                MonthlyTotal = 1,
                MonthlyDiscount = 1,
                YearlyDiscount = 1,
                YearlySubtotal = 1,
                YearlyTotal = 1,
                YearlyVat = 1,
                Currency = "SAR"
            };

            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(
                new BillPaymentServiceAndBundleFlags
                {
                    HasBillPaymentBundle = true,
                    HasBillPaymentService = false
                }));

            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>()))
                .Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            checkoutClient
                .Setup(x => x.UpdateOrderAsync(It.IsAny<JsonPatchDocument<OrderUpdateRequest>>(), It.IsAny<Guid>()))
                .Returns(Task.FromResult(order));
            checkoutService = CreateCheckoutServiceInstance();
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(TestsHelper.verifiedMerchant));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, string.Empty);
            patch.Replace(e => e.ProjectName, "test");

            checkoutService.Invoking(x => x.UpdateOrderAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();

            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never());
        }

        [Test]
        public async Task UpdateOrdersToProductRegisteredAsync_WhenOrderIsNotInValidStatus_ShouldReturnObjectWithError()
        {
            orderResponse.OrderStatus = "invalid";
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync(It.IsAny<string>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            List<string> orderNumbers = new List<string> { orderResponse.OrderNumber! };

            logger.ClearReceivedCalls();

            var response = await checkoutService.UpdateOrdersToProductRegisteredAsync(orderNumbers, null);
            response.Count.Should().Be(1);
            response[0].OrderNumber.Should().Be(orderResponse.OrderNumber);
            response[0].ErrorMessage.Should().Be(Errors.OrderIsNotSubmittedOrVerified.Message);
            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never);
            UpdateProductToProductRegistered_And_Federation_ShouldNotHappen();
        }

        [Test]
        public async Task UpdateOrdersToProductRegisteredAsync_WhenOrderIsNotInValidStatus_AndSecondOrderIsNotFound_ShouldReturnObjectWithErrors()
        {
            orderResponse.OrderStatus = "invalid";
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync(It.IsAny<string>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            string invalidOrderNumber = "AnotherNumber";
            List<string> orderNumbers = new List<string> { orderResponse.OrderNumber!, string.Empty, orderResponse.OrderNumber! + " ", invalidOrderNumber };

            logger.ClearReceivedCalls();

            var response = await checkoutService.UpdateOrdersToProductRegisteredAsync(orderNumbers, null);

            response.Count.Should().Be(2);
            response[0].OrderNumber.Should().Be(orderResponse.OrderNumber);
            response[0].ErrorMessage.Should().Be(Errors.OrderIsNotSubmittedOrVerified.Message);
            response[1].OrderNumber.Should().Be(invalidOrderNumber);
            response[1].ErrorMessage.Should().Be(Errors.OrderIsNotSubmittedOrVerified.Message);
            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never);
            UpdateProductToProductRegistered_And_Federation_ShouldNotHappen();
        }

        [Test]
        public async Task UpdateOrdersToProductRegisteredAsync_WhenOrderNotFound_ShouldReturnObjectWithError()
        {
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync(It.IsAny<string>()))
                .Throws(new PassthroughException(new HttpResponseMessage(HttpStatusCode.NotFound)));
            checkoutService = CreateCheckoutServiceInstance();

            List<string> orderNumbers = new List<string> { orderResponse.OrderNumber! };

            logger.ClearReceivedCalls();
            var response = await checkoutService.UpdateOrdersToProductRegisteredAsync(orderNumbers, null);

            response.Count.Should().Be(1);
            response[0].OrderNumber.Should().Be(orderResponse.OrderNumber);
            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never);

            UpdateProductToProductRegistered_And_Federation_ShouldNotHappen();
        }

        [Test]
        public async Task UpdateOrdersToProductRegisteredAsync_WhenMerchantNotVerified_ShouldReturnObjectWithError()
        {
            orderResponse.OrderStatus = Constants.OrderStatus.Verified;
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>())).Returns(Task.FromResult(TestsHelper.notVerifiedMerchant));
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync(It.IsAny<string>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            List<string> orderNumbers = new List<string> { orderResponse.OrderNumber! };

            logger.ClearReceivedCalls();
            var response = await checkoutService.UpdateOrdersToProductRegisteredAsync(orderNumbers, null);

            response.Count.Should().Be(1);
            response[0].OrderNumber.Should().Be(orderResponse.OrderNumber);
            response[0].ErrorMessage.Should().Be(Errors.InvalidMerchantStatus.Message);
            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never);

            UpdateProductToProductRegistered_And_Federation_ShouldNotHappen();
        }

        [Test]
        public async Task UpdateOrdersToProductRegisteredAsync_WhenProductNotConfigured_ShouldReturnObjectWithError()
        {
            orderResponse.OrderStatus = Constants.OrderStatus.Verified;
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>())).Returns(Task.FromResult(TestsHelper.productInstanceTerminalNotConfiguredEgypt));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>())).Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync(It.IsAny<string>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutService = CreateCheckoutServiceInstance();

            List<string> orderNumbers = new List<string> { orderResponse.OrderNumber! };

            logger.ClearReceivedCalls();
            var response = await checkoutService.UpdateOrdersToProductRegisteredAsync(orderNumbers, null);

            response.Count.Should().Be(1);
            response[0].OrderNumber.Should().Be(orderResponse.OrderNumber);
            response[0].ErrorMessage.Should().Be(Errors.ProductsNotConfigured.Message);
            productService.Verify(mock => mock.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>()), Times.Never);

            UpdateProductToProductRegistered_And_Federation_ShouldNotHappen();
        }

        [Test]
        [TestCase(Geidea.Utils.Common.Constants.CounterpartySaudi)]
        [TestCase(Geidea.Utils.Common.Constants.CounterpartyEgypt)]
        public async Task UpdateOrdersToProductRegisteredAsync_WhenValidData_ShouldReturnEmptyObject(string counterparty)
        {
            orderResponse.OrderStatus = Constants.OrderStatus.Verified;
            var productInstance = counterparty switch
            {
                Utils.Common.Constants.CounterpartySaudi => TestsHelper.productInstanceTerminalConfiguredSaudi,
                Utils.Common.Constants.CounterpartyEgypt => TestsHelper.productInstanceTerminalConfiguredEgypt,
                _ => TestsHelper.productInstanceTerminalConfiguredSaudi
            };
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>())).Returns(Task.FromResult(productInstance));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>())).Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(counterparty);
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync(It.IsAny<string>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutClient.Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(new BillPaymentServiceAndBundleFlags
            {
                HasBillPaymentBundle = false,
                HasBillPaymentService = false
            }));

            checkoutService = CreateCheckoutServiceInstance();

            List<string> orderNumbers = new List<string> { orderResponse.OrderNumber! };

            logger.ClearReceivedCalls();
            var response = await checkoutService.UpdateOrdersToProductRegisteredAsync(orderNumbers, null);

            response.Count.Should().Be(0);
            gleComposePayloadMessagingClient.Received(0).SendGleComposePayloadMessage(Arg.Any<GleComposePayloadRequest>());
            UpdateProductToProductRegistered_And_Federation_ShouldHappen(counterparty);
        }

        [Test]
        public async Task UpdateOrdersToProductRegisteredAsync_WhenValidData_ShouldTriggerGle()
        {
            orderResponse.OrderStatus = Constants.OrderStatus.Verified;
            productService.Setup(x => x.GetProductInstances(It.IsAny<List<Guid>>())).Returns(Task.FromResult(TestsHelper.productInstanceTerminalConfiguredEgypt));
            merchantClient.Setup(x => x.GetCoreMerchantAsync(It.IsAny<Guid>())).Returns(Task.FromResult(TestsHelper.verifiedMerchant));
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);
            checkoutClient
                .Setup(x => x.GetByOrderNumberAsync(It.IsAny<string>()))
                .Returns(Task.FromResult(orderResponse));
            checkoutClient.Setup(x => x.GetOrderByIdAsync(It.IsAny<Guid>()))
                .Returns(Task.FromResult(orderResponse));
            productService.Setup(x => x.GetBpProductTypesInListOfProducts(It.IsAny<IdsRequest>())).Returns(Task.FromResult(new BillPaymentServiceAndBundleFlags
            {
                HasBillPaymentBundle = true,
                HasBillPaymentService = false
            }));

            checkoutService = CreateCheckoutServiceInstance();

            List<string> orderNumbers = new List<string> { orderResponse.OrderNumber! };

            logger.ClearReceivedCalls();
            var response = await checkoutService.UpdateOrdersToProductRegisteredAsync(orderNumbers, null);

            response.Count.Should().Be(0);
            gleComposePayloadMessagingClient.Received(1).SendGleComposePayloadMessage(Arg.Any<GleComposePayloadRequest>());
            UpdateProductToProductRegistered_And_Federation_ShouldHappen(Utils.Common.Constants.CounterpartySaudi);
        }

        private void UpdateProductToProductRegistered_And_Federation_ShouldNotHappen()
        {
            mmsMerchantAdapterService.Received(0).SendOrderUpdateProductRegisteredMessage(Arg.Any<OrderUpdateMessage>());
            var calls = logger.ReceivedCalls();
            foreach (var call in calls)
            {
                var arguments = call.GetArguments();
                foreach (var argument in arguments)
                {
                    var argMsg = argument != null ? argument!.ToString() : "";
                    argMsg.Should().NotContain("Calling checkout API to update order with id");
                }

            }
        }

        private void UpdateProductToProductRegistered_And_Federation_ShouldHappen(string counterparty)
        {
            mmsMerchantAdapterService.Received(1).SendOrderUpdateProductRegisteredMessage(Arg.Any<OrderUpdateMessage>());
           /* if (counterparty == Utils.Common.Constants.CounterpartyEgypt)
            {
                ePosMessagingService.Received(1).CreateOrderEPosTicketAsync(Arg.Any<Guid>());
            }*/
            var calls = logger.ReceivedCalls();
            bool logCallFound = false;
            foreach (var call in calls)
            {
                var arguments = call.GetArguments();
                foreach (var argument in arguments)
                {
                    var argMsg = argument != null ? argument!.ToString() : " ";
                    if (argMsg != null && argMsg.Contains("Calling checkout API to update order with id"))
                    {
                        logCallFound = true;
                        break;
                    }
                }
                if (logCallFound)
                    break;
            }
        }

        private static IEnumerable<TestCaseData> GetInvalidOrderItems()
        {
            yield return new TestCaseData((List<OrderItemResponse?>)null!);
            yield return new TestCaseData(new List<OrderItemResponse>());
        }
    }
}