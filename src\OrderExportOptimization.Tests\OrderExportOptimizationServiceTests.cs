using System;
using System.Collections.Generic;
using System.Net;
using Common.Models.Checks;
using Common.Models.Merchant;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using NUnit.Framework;

namespace OrderExportOptimization.Tests
{
    class OrderExportOptimizationServiceTests
    {
        private IMerchantService merchantService = Substitute.For<IMerchantService>();
        private ILogger<OrderExportOptimizationService> logger =  Substitute.For<ILogger<OrderExportOptimizationService>>();
        private IChecksService checksService =  Substitute.For<IChecksService>();
        private readonly IOptionsMonitor<UrlSettings> urlSettingsOptions = Substitute.For<IOptionsMonitor<UrlSettings>>();
        private OrderExportOptimizationService service = null!;

        [SetUp]
        public void Setup()
        {
            urlSettingsOptions.CurrentValue.Returns(TestsHelper.UrlSettingsOptions);

            var merchantsDetails = new MerchantDetails[]{
                new MerchantDetails()
                {
                    MerchantId = Guid.NewGuid(),
                    CityCr = "",
                    AddressCr="",
                    MerchantName=""
                }
            };

            var merchantId = new Guid[]
            {
                merchantsDetails[0].MerchantId
            };

            var fullInfo = new FullInfoCheck()
            {
                MerchantId = merchantId[0],
                Address = new Address() { General = new GeneralAddressDetails() { Address = "test"} },
                CrName = "test",
                Location = new Location() { Name = "test"}
            };

            var ckecks = new List<FullInfoCheck>();
            ckecks.Add(fullInfo);

            merchantService.GetMerchantsDetailsAsync().Returns(merchantsDetails);
            checksService.GetMerchantFullInfoChecks(Arg.Any<Guid[]>()).Returns(ckecks);
            //merchantService.PatchMerchantDetailsAsync(Arg.Any<MerchantDetails>()).Returns(Task.FromResult);
        }

        [Test]
        public void UpdateMerchantsDetails()
        {
            service =  new OrderExportOptimizationService(logger, merchantService, checksService,
                                                          TestsHelper.CreateHttpClient(HttpStatusCode.OK),
                                                          urlSettingsOptions);

            service.Invoking(x => x.UpdateMerchantsDetails()).Should().NotThrowAsync<Exception>();
        }
    }
}