﻿using Common.Models.Chain;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators.Chain;

public class ChainAddressRequestValidator : AbstractValidator<ChainAddressRequest>
{
    public ChainAddressRequestValidator()
    {
        RuleFor(a => a.BuildingNumber).MaximumLength(10);
        RuleFor(a => a.BuildingName).MaximumLength(50);
        RuleFor(a => a.Country).MaximumLength(255);
        RuleFor(a => a.City).MaximumLength(255);
        RuleFor(a => a.AddressLine1).MaximumLength(255);
        RuleFor(a => a.AddressLine2).MaximumLength(100);
        RuleFor(a => a.Zip).MaximumLength(255);
    }
}
