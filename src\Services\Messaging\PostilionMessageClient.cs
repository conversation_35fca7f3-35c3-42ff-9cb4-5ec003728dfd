﻿using Common.Models.Postilion;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Diagnostics.CodeAnalysis;

namespace Services.Messaging
{
    [ExcludeFromCodeCoverage]
    public class PostilionMessageClient : MessageClient
    {
        public PostilionMessageClient(IHttpContextAccessor contextAccessor,
                                      ILogger<MessageClient> logger,
                                      IOptionsMonitor<RabbitMqOptions> rabbitMqOption,
                                      QueueSettings queue)
                                      : base(contextAccessor, logger, rabbitMqOption, queue)
        {
        }

        public void Send(PostilionMessageBody payload)
        {
            try
            {
                if (connection == null || !connection.IsOpen)
                {
                    Connect();
                }

                base.SendMessage(payload);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Unable to send message via RabbitMQ");
            }
        }
    }
}
