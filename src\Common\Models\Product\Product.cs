﻿namespace Common.Models.Product
{
    using System;
    using System.Collections.Generic;

    public class Product
    {
        public Guid Id { get; set; }
        public string Code { get; set; } = null!;
        public int Version { get; set; }
        public string Type { get; set; } = null!;
        public string Availability { get; set; } = null!;
        public string? Description { get; set; }
        public DateTime? ValidFrom { get; set; }
        public DateTime? ValidTo { get; set; }
        public List<Price> Prices { get; set; } = new List<Price>();
        public List<ProductPart> Parts { get; set; } = new List<ProductPart>();
    }
}
