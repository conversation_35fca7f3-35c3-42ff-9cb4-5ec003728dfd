﻿using Common.Models.Document;
using FluentValidation;

namespace Common.Validators
{
    public class DocumentRequestValidator : AbstractValidator<DocumentRequest>
    {
        public DocumentRequestValidator()
        {
            RuleFor(x => x.DocumentType)
                .Must(x => x != null && x.Length <= 32)
                    .WithErrorCode(Errors.DocumentType_Length.Code)
                    .WithMessage(Errors.DocumentType_Length.Message);

            RuleFor(x => x.Language)
                .Must(x => x != null && x.Length <= 16)
                    .WithErrorCode(Errors.Language_Length.Code)
                    .WithMessage(Errors.Language_Length.Message);

            RuleFor(x => x.DocumentReference)
                .Must(x => x!.Length <= 64)
                .When(x => x.DocumentReference != null)
                    .WithErrorCode(Errors.DocumentReference_Length.Code)
                    .WithMessage(Errors.DocumentReference_Length.Message);
        }
    }
}
