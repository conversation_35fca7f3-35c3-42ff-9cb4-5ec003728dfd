﻿namespace Services
{
    using System;
    using System.Linq;
    using Common.Models.Checkout;
    using Common.Options;
    using Common.Services;
    using Geidea.Utils.Messaging.Base;
    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Services.Messaging;

    public class GSDKMerchantAdapterService : IGSDKMerchantAdapterService
    {
        private readonly MMSOrderUpdateMessagingClient mmsOrderUpdateMessagingClient;
        private readonly ILogger<GSDKMerchantAdapterService> logger;
        private readonly IOptionsMonitor<GsdkSettings> gsdkSettings;
        private readonly IHttpContextAccessor contextAccessor;

        public GSDKMerchantAdapterService(MMSOrderUpdateMessagingClient mmsOrderUpdateMessagingClient,
            ILogger<GSDKMerchantAdapterService> logger,
            IOptionsMonitor<GsdkSettings> gsdkSettings,
            IHttpContextAccessor contextAccessor)
        {
            this.mmsOrderUpdateMessagingClient = mmsOrderUpdateMessagingClient;
            this.logger = logger;
            this.gsdkSettings = gsdkSettings;
            this.contextAccessor = contextAccessor;
        }

        public void SendOrderUpdateProductRegisteredMessage(OrderUpdateMessage message)
        {
            var productRegisteredSettings = gsdkSettings.CurrentValue.FeatureManagement.MMSProductRegisteredMessages;
            if (!productRegisteredSettings.IsActive ||
                !productRegisteredSettings.AllowedCounterparties.Split(",").Contains(contextAccessor.GetCounterparty()))
            {
                logger.LogInformation("Order update product registered message not sent for counterparty {@counterpartyCode} because the feature is not enabled.",
                    contextAccessor.GetCounterparty());
                return;
            }

            logger.LogInformation("Sending order update message on ProductRegistered for order with id {@orderId}",
                message.OrderId);
            mmsOrderUpdateMessagingClient.SendMessage(message);
        }
    }
}
