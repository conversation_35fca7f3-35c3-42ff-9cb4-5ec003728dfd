﻿using Common.Models.Chain;
using FluentValidation;
using Geidea.Utils.ReferenceData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators.Chain;

public class ChainContactModelValidator : AbstractValidator<ChainContactModel>
{
    public ChainContactModelValidator(List<ReferenceData> refData, bool isIdRequired = false)
    {
        if (isIdRequired)
        {
            RuleFor(x => x.ChainContactId).NotNull().NotEmpty();
        }
        RuleFor(x => x.Email).NotNull().NotEmpty().EmailAddress();
        RuleFor(x => x.PhoneNumber).NotNull().NotEmpty();
        RuleFor(x => x.ContactName).NotNull().NotEmpty();
        RuleFor(x => x.RoleDesignation).NotNull().NotEmpty()
            .ChildRules(x =>
                  {
                      RuleFor(l => l)
                           .Must(l => IsValidRefData(l.RoleDesignation ?? "", Constants.Catalogues.Designation, refData))
                           .When(l => !string.IsNullOrEmpty(l.RoleDesignation))
                           .WithErrorCode(Errors.InvalidRoleDesignation.Code)
                           .WithMessage(Errors.InvalidRoleDesignation.Message);
                  }); ;
    }

    private static bool IsValidRefData(string currValue, string catalogueName, List<ReferenceData> refData)
    {
        return refData.Exists(r => string.Equals(r.CatalogueName, catalogueName, StringComparison.OrdinalIgnoreCase) && string.Equals(r.Key, currValue, StringComparison.OrdinalIgnoreCase));
    }
}
