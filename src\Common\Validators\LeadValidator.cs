﻿using Common.Models.Lead;
using FluentValidation;
using Geidea.Utils.Counterparty.Providers;

namespace Common.Validators
{
    public class LeadValidator : AbstractValidator<Lead>
    {
        public LeadValidator(ICounterpartyProvider counterpartyProvider)
        {
            RuleFor(x => x.LeadStatus)
                .Must(x => x!.Length <= 32)
                .When(x => x.LeadStatus != null)
                .WithErrorCode(Errors.LeadStatusLengthValidation.Code)
                .WithMessage(Errors.LeadStatusLengthValidation.Message);

            RuleFor(x => x.BusinessDomain)
                .Must(x => x!.Length <= 32)
                .When(x => x.BusinessDomain != null)
                .WithErrorCode(Errors.BusinessDomainLengthValidation.Code)
                .WithMessage(Errors.BusinessDomainLengthValidation.Message);

            RuleFor(x => x.CountryPrefix)
                .Must(x => x!.Length <= 16)
                .When(x => x.CountryPrefix != null)
                .WithErrorCode(Errors.CountryPrefixLengthValidation.Code)
                .WithMessage(Errors.CountryPrefixLengthValidation.Message);

            RuleFor(x => x.OwnerFirstName)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerFirstName != null)
                .WithErrorCode(Errors.FirstNameLengthValidation.Code)
                .WithMessage(Errors.FirstNameLengthValidation.Message);

            RuleFor(x => x.OwnerLastName)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerLastName != null)
                .WithErrorCode(Errors.LastNameLengthValidation.Code)
                .WithMessage(Errors.LastNameLengthValidation.Message);

            RuleFor(x => x.OwnerFirstNameAr)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerFirstNameAr != null)
                .WithErrorCode(Errors.OwnerFirstNameArLengthValidation.Code)
                .WithMessage(Errors.OwnerFirstNameArLengthValidation.Message);

            RuleFor(x => x.OwnerLastNameAr)
                .Must(x => x!.Length <= 64)
                .When(x => x.OwnerLastNameAr != null)
                .WithErrorCode(Errors.OwnerLastNameArLengthValidation.Code)
                .WithMessage(Errors.OwnerLastNameArLengthValidation.Message);

            RuleFor(x => x.LegalName)
                .Must(x => x!.Length <= 64)
                .When(x => x.LegalName != null)
                .WithErrorCode(Errors.LegalNameLengthValidation.Code)
                .WithMessage(Errors.LegalNameLengthValidation.Message);

            RuleFor(x => x.LegalNameAr)
                .Must(x => x!.Length <= 64)
                .When(x => x.LegalNameAr != null)
                .WithErrorCode(Errors.LegalNameArLengthValidation.Code)
                .WithMessage(Errors.LegalNameArLengthValidation.Message);

            RuleFor(x => x.NationalId)
                .Must(x => x!.Length <= 32)
                .When(x => x.NationalId != null)
                .WithErrorCode(Errors.NationalIdLengthValidation.Code)
                .WithMessage(Errors.NationalIdLengthValidation.Message);

            RuleFor(x => x.AddressLine)
                .Must(x => x!.Length <= 128)
                .When(x => x.AddressLine != null)
                .WithErrorCode(Errors.AddressLineLengthValidation.Code)
                .WithMessage(Errors.AddressLineLengthValidation.Message);

            RuleFor(x => x.City)
                .Must(x => x!.Length <= 32)
                .When(x => x.City != null)
                .WithErrorCode(Errors.CityLengthValidation.Code)
                .WithMessage(Errors.CityLengthValidation.Message);

            RuleFor(x => x.Country)
                .Must(x => x!.Length <= 32)
                .When(x => x.Country != null)
                .WithErrorCode(Errors.CountryLengthValidation.Code)
                .WithMessage(Errors.CountryLengthValidation.Message);

            RuleFor(x => x.UTM)
                .Must(x => x!.Length <= 1000)
                .When(x => x.UTM != null)
                .WithErrorCode(Errors.UtmLengthValidation.Code)
                .WithMessage(Errors.UtmLengthValidation.Message);

            RuleFor(x => x.SalesId)
                .Must(x => x!.Length <= 10)
                .When(x => x.SalesId != null)
                .WithErrorCode(Errors.SalesIdLengthValidation.Code)
                .WithMessage(Errors.SalesIdLengthValidation.Message);

            RuleFor(x => x.Nationality)
                .Must(x => x!.Length <= 64)
                .When(x => x.Nationality != null)
                .WithErrorCode(Errors.NationalityLengthValidation.Code)
                .WithMessage(Errors.NationalityLengthValidation.Message);

            RuleFor(x => x.Gender)
                .Must(x => x!.Length <= 16)
                .When(x => x.Gender != null)
                .WithErrorCode(Errors.GenderLengthValidation.Code)
                .WithMessage(Errors.GenderLengthValidation.Message);

            RuleFor(x => x.ReferralChannel)
                .Must(x => x!.Length <= 16)
                .When(x => x.ReferralChannel != null)
                .WithErrorCode(Errors.ReferralChannelLengthValidation.Code)
                .WithMessage(Errors.ReferralChannelLengthValidation.Message);

            RuleForEach(x => x.LeadProducts).SetValidator(new LeadProductValidator());

#pragma warning disable CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
            When(x => x.LeadDetails != null, () =>
            {
                RuleFor(x => x.LeadDetails).SetValidator(new LeadDetailsValidator(counterpartyProvider.GetCode()));
            });

            When(x => x.OwnerEmail != null, () =>
            {
                RuleFor(x => x.OwnerEmail).SetValidator(new EmailValidator());
            });

            When(x => x.PhoneNumber != null, () =>
            {
                RuleFor(x => x.PhoneNumber).SetValidator(new PhoneValidator());
            });
#pragma warning restore CS8620 // Argument cannot be used for parameter due to differences in the nullability of reference types.
        }
    }
}
