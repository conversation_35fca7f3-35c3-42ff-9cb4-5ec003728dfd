﻿using Common;
using Common.Models.Merchant;
using Common.Options;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSubstitute;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Common.Models;
using Common.Services;
using Microsoft.AspNetCore.JsonPatch;
using Common.Models.Checkout;
using Common.Models.Match;
using Microsoft.AspNetCore.JsonPatch.Operations;
using System.IO;
using System.Xml.XPath;
using Geidea.Messages.Merchant;
using Geidea.Utils.Counterparty.Providers;
using static Common.Constants;
using Moq;
using System.Linq;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class MerchantClientTests
    {
        private readonly ILogger<MerchantClient> logger = Substitute.For<ILogger<MerchantClient>>();
        private MerchantClient merchantClient = null!;
        private readonly IOptionsMonitor<UrlSettings> urlSettingsOptions = Substitute.For<IOptionsMonitor<UrlSettings>>();
        private readonly IOptionsMonitor<ApplicationOptions> appOptions = Substitute.For<IOptionsMonitor<ApplicationOptions>>();

        public MerchantClientTests()
        {
            urlSettingsOptions.CurrentValue.Returns(TestsHelper.UrlSettingsOptions);
            appOptions.CurrentValue.Returns(TestsHelper.AppOptions);
        }

        [Test]
        public async Task GetCoreMerchantAsync()
        {
            var merchantId = Guid.NewGuid();

            var merchant = new Common.Models.Merchant.Merchant()
            {
                MerchantId = merchantId,
                MerchantStatus = Constants.MerchantStatus.Verified
            };

            var expectedMerchant = new Common.Models.Merchant.Merchant()
            {
                MerchantId = merchantId,
                MerchantStatus = Constants.MerchantStatus.Verified
            };

            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(merchant))
                );

            var result = await merchantClient.GetCoreMerchantAsync(Guid.NewGuid());

            result.Should().BeEquivalentTo(expectedMerchant);
        }
        [Test]
        public async Task GetChecksForMerchant()
        {
            var checkModels = new List<MerchantCheck>
            {
                new MerchantCheck
                {
                    CheckDate = DateTime.Now,
                    CheckProvider = 1,
                    CheckScore = 3,
                    CheckStatus = CheckStatus.WorldCheckOneKYCNoHit,
                    CheckType = CheckType.WorldCheckOne,
                    MerchantCheckId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    ValidFrom = DateTime.Now,
                    ValidTo = DateTime.Now.AddDays(3)
                },
                new MerchantCheck
                {
                    CheckDate = DateTime.Now,
                    CheckProvider = 3,
                    CheckScore = 2,
                    CheckStatus = CheckStatus.MatchCheckNoHit,
                    CheckType = CheckType.MatchCheck,
                    MerchantCheckId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    ValidFrom = DateTime.Now,
                    ValidTo = DateTime.Now.AddDays(3)
                },
                 new MerchantCheck
                {
                    CheckDate = DateTime.Now,
                    CheckProvider = 3,
                    CheckScore = 2,
                    CheckStatus = CheckStatus.BankCheckPassed,
                    CheckType = CheckType.BankCheck,
                    MerchantCheckId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    ValidFrom = DateTime.Now,
                    ValidTo = DateTime.Now.AddDays(3)
                },
                 new MerchantCheck
                {
                    CheckDate = DateTime.Now,
                    CheckProvider = 3,
                    CheckScore = 2,
                    CheckStatus = CheckStatus.RiskScoringLow,
                    CheckType = CheckType.RiskScoring,
                    MerchantCheckId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    ValidFrom = DateTime.Now,
                    ValidTo = DateTime.Now.AddDays(3)
                }
            };
            merchantClient = new MerchantClient(logger, urlSettingsOptions, TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(checkModels)));

            var createdChecks = await merchantClient.GetChecksForMerchant(Guid.NewGuid());
            createdChecks.Should().NotBeNullOrEmpty();
            createdChecks.Should().HaveCount(4);
            createdChecks.Should().BeEquivalentTo(checkModels);
        }
        [Test]
        public void GetChecksForMerchant_Exception()
        {
            merchantClient = new MerchantClient(logger, urlSettingsOptions, TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, ""));

            merchantClient.Invoking(x => x.GetChecksForMerchant(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>();
        }
        [Test]
        public void PatchMerchantAsync()
        {

            merchantClient = new MerchantClient(
                            logger,
                            urlSettingsOptions,
                            TestsHelper.CreateHttpClient(HttpStatusCode.OK)
                            );
            var patch = new JsonPatchDocument<PatchMerchantRequest>();

            patch.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.BoardingCompleted
                    });
            merchantClient.Invoking(x => x.PatchMerchantAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();
        }
        [Test]
        public void PatchMerchantAsync_PassthroughError()
        {
            merchantClient = new MerchantClient(
                 logger,
                 urlSettingsOptions, (TestsHelper.CreateHttpClient(HttpStatusCode.NotFound)));

            var patch = new JsonPatchDocument<PatchMerchantRequest>();

            patch.Operations.Add(
                    new Operation<PatchMerchantRequest>()
                    {
                        op = "replace",
                        path = "merchantStatus",
                        value = Constants.MerchantStatus.BoardingCompleted
                    });

            merchantClient.Invoking(x => x.PatchMerchantAsync(Guid.NewGuid(), patch))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }
        [Test]
        public async Task GetMerchantAcquiringLedgerByStoreIdAsync_ShouldReturnSuccess()
        {
            var storeId = Guid.NewGuid();

            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(TestsHelper.merchantAcquiringLedgerInfoWithoutDefaultStore))
                );

            var result = await merchantClient.GetMerchantAcquiringLedgerByStoreIdAsync(storeId);

            result.Should().BeEquivalentTo(TestsHelper.merchantAcquiringLedgerInfoWithoutDefaultStore);
        }

        [Test]
        public async Task GetMerchantAcquiringLedgerByStoreIdAsync_ShouldThrowException()
        {
            var storeId = Guid.NewGuid();

            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest)
                );
            try
            {
                await merchantClient.GetMerchantAcquiringLedgerByStoreIdAsync(storeId);
                Assert.IsTrue(true);
            }
            catch (Exception ex)
            {
                Assert.AreEqual(ex.GetType(), typeof(PassthroughException));
            }
        }

        [Test]
        public void GetMerchantTag_WhenServiceReturnsError_ShouldThrowAsyncPassthroughException()
        {
            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error code")
            );

            merchantClient.Invoking(x => x.GetCoreMerchantWithHierarchy(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task GetMerchantTag_WhenValidData_ShouldReturnMerchantTag()
        {
            var merchant = TestsHelper.GetCoreMerchantWithHierarchy(Guid.Empty);

            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(merchant))
            );

            var result = await merchantClient.GetCoreMerchantWithHierarchy(Guid.NewGuid());
            result.Tag.Should().BeEquivalentTo(Constants.MerchantTag.MasterBusiness);
        }

        [Test]
        public async Task CreateBusinessHierarchyAsync_ShouldCallService()
        {
            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new NoContentResult()))
            );

            await merchantClient.Invoking(x =>
                    x.CreateBusinessHierarchyAsync(Guid.NewGuid(), new List<Guid> { Guid.NewGuid(), Guid.NewGuid() }))
                .Should().NotThrowAsync();
        }

        [Test]
        public async Task CreateBusinessHierarchyAsync_ShouldThrowException()
        {
            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error code")
            );

            await merchantClient.Invoking(x =>
                    x.CreateBusinessHierarchyAsync(Guid.Empty, new List<Guid> { Guid.NewGuid(), Guid.NewGuid() }))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task DeleteBusinessHierarchyAsync_ShouldCallService()
        {
            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new NoContentResult()))
            );

            await merchantClient.Invoking(x =>
                    x.DeleteBusinessHierarchyAsync(Guid.NewGuid(), Guid.NewGuid()))
                .Should().NotThrowAsync();
        }

        [Test]
        public async Task DeleteBusinessHierarchyAsync_ShouldThrowException()
        {
            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error code")
            );

            await merchantClient.Invoking(x =>
                    x.DeleteBusinessHierarchyAsync(Guid.Empty, Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        public async Task SearchPersonOfInterest_ShouldCallService()
        {
            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new NoContentResult()))
            );

            await merchantClient.Invoking(x =>
                    x.SearchPersonOfInterest(new PoiSearchCriteriaRequest()))
                .Should().NotThrowAsync();
        }

        [Test]
        public async Task SearchPersonOfInterest_ShouldThrowException()
        {
            merchantClient = new MerchantClient(
                logger,
                urlSettingsOptions,
                TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error code")
            );

            await merchantClient.Invoking(x =>
                    x.SearchPersonOfInterest(new PoiSearchCriteriaRequest()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }

        [Test]
        public async Task GetMerchantMmsContract_OkStatusCode_ReturnContract()
        {

            var merchantId = Guid.NewGuid();
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(new List<MerchantExternalContractMappingResponse>
                {
                    new MerchantExternalContractMappingResponse
                    {
                    LedgerKey = Constants.MmsLedgers.EgyptDefault,
                    MerchantId = merchantId,
                    PaymentWay = Constants.GsdkContractPaymentWay.Terminal
                    }
                 }
                ));

            merchantClient = new MerchantClient(logger, urlSettingsOptions, httpClient);
            var contracts = await merchantClient.GetMerchantMmsContract(new SearchContractMappingFilter
            {
                LedgerKey = Constants.MmsLedgers.EgyptDefault,
                PaymentWay = Constants.GsdkContractPaymentWay.Terminal,
                MerchantId = merchantId
            });

            Assert.NotNull(contracts);
            Assert.That(contracts.Count, Is.EqualTo(1));
        }

        [Test]
        public void GetMerchantMmsContract_BadStatusCode_ShouldThrowPassthroughException()
        {

            var merchantId = Guid.NewGuid();
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error Code");

            merchantClient = new MerchantClient(logger, urlSettingsOptions, httpClient);
            merchantClient.Invoking(x => x.GetMerchantMmsContract(new SearchContractMappingFilter
            {
                LedgerKey = Constants.MmsLedgers.EgyptDefault,
                PaymentWay = Constants.GsdkContractPaymentWay.Terminal,
                MerchantId = merchantId
            })).Should().ThrowAsync<PassthroughException>();
        }
        [Test]
        public async Task GetStoresAsync_WhenValidData_ShouldReturnStore()
        {
            merchantClient = new MerchantClient(
              logger,
              urlSettingsOptions,
              TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(TestsHelper.expectedStore.ToList()))
              );
            var stores = await merchantClient.GetStoresAsync(Guid.NewGuid());
            stores.Should().BeEquivalentTo(TestsHelper.expectedStore.ToList());
        }
        [Test]
        public void GetStoresAsync_WhenServiceReturnsError_ShouldThrowAsyncPassthroughException()
        {
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error Code");

            merchantClient = new MerchantClient(logger, urlSettingsOptions, httpClient);

            merchantClient.Invoking(x => x.GetStoresAsync(Guid.NewGuid()))
                 .Should().ThrowAsync<PassthroughException>()
                 .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public void GetMerchantBusinessInformationAsync_WhenServiceReturnsError()
        {
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error Code");

            merchantClient = new MerchantClient(logger, urlSettingsOptions, httpClient);

            merchantClient.Invoking(x => x.GetMerchantBusinessInformationAsync(It.IsAny<Guid[]>()))
                 .Should().ThrowAsync<PassthroughException>()
                 .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public async Task GetMerchantBusinessInformationAsync_ShouldReturnMerchantInfo()
        {
            var merchantInfo = new MerchantBusinessInformation[]
            {
                new MerchantBusinessInformation
                {
                    LegalName = "legal name",
                    LegalNameAr = "legal name ar"
                }
            };
            merchantClient = new MerchantClient(
              logger,
              urlSettingsOptions,
              TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(merchantInfo))
              );

            var merchantBusinessInformation = await merchantClient.GetMerchantBusinessInformationAsync(It.IsAny<Guid[]>());
            merchantBusinessInformation.Should().BeEquivalentTo(merchantInfo);
        }
        [Test]
        public async Task GetStoreAsync_WhenValidData_ShouldReturnStore()
        {
            merchantClient = new MerchantClient(
              logger,
              urlSettingsOptions,
              TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(TestsHelper.merchantAccount))
              );

            var result = await merchantClient.GetStoreAsync(Guid.NewGuid(), Guid.NewGuid());
            result.Should().BeEquivalentTo(TestsHelper.merchantAccount);
        }

        [Test]
        public void GetStoreAsync_WhenServiceReturnsError_ShouldThrowAsyncPassthroughException()
        {
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error Code");

            merchantClient = new MerchantClient(logger, urlSettingsOptions, httpClient);

            merchantClient.Invoking(x => x.GetStoreAsync(Guid.NewGuid(), Guid.NewGuid()))
                 .Should().ThrowAsync<PassthroughException>()
                 .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
        [Test]
        public async Task GetMerchantMemberStatusByMerchantId_ShouldGetMerchantMemberStatus()
        {
            var merchantId = Guid.NewGuid();

            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                     JsonSerializer.Serialize(new MerchantMemberStatusResponse
                     {
                         MemberId = "123",
                         MerchantStatus = "Verified"
                     }));
            merchantClient = new MerchantClient(logger, urlSettingsOptions, httpClient);
            var memberStatusResponse = await merchantClient.GetMerchantMemberStatusByMerchantId(merchantId);
            Assert.NotNull(memberStatusResponse);
            Assert.AreEqual("123", memberStatusResponse.MemberId);
            Assert.AreEqual("Verified", memberStatusResponse.MerchantStatus);

        }
        [Test]
        public async Task GetMerchantMemberStatusByMerchantId_WhenServiceReturnsError_ShouldThrowAsyncPassthroughException()
        {
            var merchantId = Guid.NewGuid();
            var httpClient = TestsHelper.CreateHttpClient(HttpStatusCode.BadRequest, "Error Code");
            merchantClient = new MerchantClient(logger, urlSettingsOptions, httpClient);
            await merchantClient.Invoking(x => x.GetMerchantMemberStatusByMerchantId(merchantId))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
        }
    }
}