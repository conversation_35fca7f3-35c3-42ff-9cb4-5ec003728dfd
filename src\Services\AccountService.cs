﻿using AutoMapper;
using Common.Models;
using Common.Models.Account;
using Common.Models.Merchant;
using Common.Options;
using Common.Services;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using static Common.Constants;
using Constants = Common.Constants;

namespace Services
{
    public class AccountService : IAccountService
    {
        private readonly ILogger<AccountService> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly HttpClient client;
        private readonly Common.Services.IReferenceService referenceService;
        private readonly IUserService userService;
        private readonly ICounterpartyProvider counterpartyProvider;
        private readonly IOptions<KsaTeamAndDeisgnationFilterToggle> ksaTeamAndDesignationFilterToggle;
        private string SearchServiceBaseUrl => $"{urlSettingsOptions.SearchServiceBaseUrlNS}/api/v1";

        public AccountService(
           ILogger<AccountService> logger,
           IOptionsMonitor<UrlSettings> urlSettingsOptions,
           Common.Services.IReferenceService referenceService,
           HttpClient client,
           IUserService userService,
           ICounterpartyProvider counterpartyProvider,
           IOptions<KsaTeamAndDeisgnationFilterToggle> ksaTeamAndDesignationFilterToggle
           )
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.client = client;
            this.referenceService = referenceService;
            this.userService = userService;
            this.counterpartyProvider = counterpartyProvider;
            this.ksaTeamAndDesignationFilterToggle = ksaTeamAndDesignationFilterToggle;
        }

        public async Task<AccountSearchResponse<AccountResult>> FindAsync(AccountSearchFilters filters, Guid userId, CancellationToken cancellationToken = default)
        {
            if (filters.IsSalesTeam && ksaTeamAndDesignationFilterToggle.Value.EnablTeamAndDeisgnationFilter && counterpartyProvider.GetCode().Equals(Constants.CounterParty.Saudi))
                filters.DesignationSalesId = await userService.ApplyteamAndDesgnationFilters(userId);

            var searchServiceUrl = $"{SearchServiceBaseUrl}/Account/advancedSearch";

            using (logger.BeginScope("AdvancedSearchAsync({@searchServiceUrl})", searchServiceUrl))
            {
                logger.LogInformation("Calling search service to search for accounts");

                var body = new StringContent(JsonConvert.SerializeObject(filters), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(searchServiceUrl, body, cancellationToken);
                var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling search service to search for accounts. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var accounts = Json.Deserialize<AccountSearchResponse<AccountResult>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                var mappedResponse = Map(accounts);
                return mappedResponse;
            }

        }

        public async Task<List<AccountExport>> ExportAccountsAsync(AccountSearchFilters searchCriteria)
        {
            var accounts = await GetAccountsBySearchCriteria(searchCriteria);
            var catalogues = await referenceService.GetCataloguesAsync(new string[] { Constants.Catalogues.MerchantStatus });

            var accountsExport = MapAccountsForExport(accounts.Records, catalogues);

            return accountsExport;
        }

        private static List<AccountExport> MapAccountsForExport(IList<AccountExportResult> accounts, Catalogue[] catalogues)
        {
            var accountsExport = new List<AccountExport>();

            foreach (var account in accounts)
            {

                AccountExport accExport = new AccountExport();

                accExport.Mid = account.Mid;
                accExport.BusinessName = account.BusinessName;
                accExport.BusinessNameAr = account.BusinessNameAr;
                accExport.BusinessId = account.BusinessId;
                accExport.ChannelType = account.ChannelType;
                accExport.MerchantStatus = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.MerchantStatus && x.Key == account.MerchantStatus)?.Value;
                accExport.AccountStatus = account.AccountStatus;
                accExport.CreatedDate = account.CreatedDate.ToString() ?? string.Empty;

                accountsExport.Add(accExport);
            }

            return accountsExport;
        }

        private async Task<AccountExportResponse<AccountExportResult>> GetAccountsBySearchCriteria(AccountSearchFilters searchCriteria)
        {
            string serviceUrl = $"{SearchServiceBaseUrl}/Account/export";
            var requestBody = new StringContent(JsonConvert.SerializeObject(searchCriteria), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetAccountsBySearchCriteria({@searchServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling search service API to export all accounts by search criteria");

                var response = await client.PostAsync(serviceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {

                    logger.LogCritical("Error when calling search service API to export accounts. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var accounts = Json.Deserialize<AccountExportResponse<AccountExportResult>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                var mappedResponse = MapAccountExport(accounts);
                return mappedResponse;
            }
        }

        private AccountSearchResponse<AccountResult> Map(AccountSearchResponse<AccountResult> coreResponse)
        {
            var result = new AccountSearchResponse<AccountResult>()
            {
                ReturnedRecordCount = coreResponse.ReturnedRecordCount,
                TotalRecordCount = coreResponse.TotalRecordCount
            };

            var records = coreResponse.Records
                .Select(m =>
                {
                    var apiResult = m;
                    return apiResult;
                });

            result.Records = records.OfType<AccountResult>().ToList();
            return result;
        }

        private AccountExportResponse<AccountExportResult> MapAccountExport(AccountExportResponse<AccountExportResult> coreResponse)
        {
            var result = new AccountExportResponse<AccountExportResult>()
            {
                ReturnedRecordCount = coreResponse.ReturnedRecordCount,
                TotalRecordCount = coreResponse.TotalRecordCount
            };

            var records = coreResponse.Records
                .Select(m =>
                {
                    var apiResult = m;
                    return apiResult;
                });

            result.Records = records.OfType<AccountExportResult>().ToList();
            return result;
        }

    }
}