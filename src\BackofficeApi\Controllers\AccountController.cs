﻿using Common.Models.Merchant;
using Common.Services;
using Geidea.Utils.Policies.Evaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Services;
using System.Threading.Tasks;
using System.Threading;
using System;
using Common.Models.Account;
using BackofficeApi.Extensions;
using CsvHelper.Configuration;
using CsvHelper;
using System.Globalization;
using System.IO;
using System.Text;

namespace BackofficeApi.Controllers;

[Authorize]
[ApiController]
[Route("api/v1")]
public class AccountController : ControllerBase
{
    private readonly IAccountService accountService;
    private readonly Authorized authorized;

    public AccountController(IAccountService accountService, Authorized authorized)
    {
        this.accountService = accountService;
        this.authorized = authorized;
    }

    /// <summary>
    /// Finds all accounts based o search criteria in the form of an OData query.
    /// </summary>
    /// <returns>An array of accounts objects matching the search criteria.</returns>
    [HttpPost("account/advancedSearch")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> Find([FromBody] AccountSearchFilters filters, CancellationToken cancellationToken = default)
    {
        if (!await authorized.To.View.Account())
        {
            return Forbid();
        }

        var response = await accountService.FindAsync(filters, this.GetUserId(), cancellationToken);
        return Ok(response);
    }

    [Produces("application/json", "text/csv")]
    [HttpPost("accounts/export")]
    [ProducesResponseType(typeof(AccountExport[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> ExportAccounts([FromBody] AccountSearchFilters searchCriteria)
    {
        if (!await authorized.To.Export.Account())
        {
            return Forbid();
        }

        var csvBuilder = new StringBuilder();
        var accounts = await accountService.ExportAccountsAsync(searchCriteria);
        var csvWriter = new CsvWriter(new StringWriter(csvBuilder), new CsvConfiguration(CultureInfo.InvariantCulture));
        csvWriter.WriteRecords(accounts);
        return Content(csvBuilder.ToString());
    }
}