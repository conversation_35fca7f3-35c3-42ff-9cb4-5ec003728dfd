﻿using Common.Models.Match;
using System;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface IDueDiligenceService
    {
        Task<MatchResponse> RetrieveMerchantMatchReport(Guid documentId);
        Task SendWorldCheckOneRequest(Guid merchantId);
        Task SendFinscanCheckRequest(Guid merchantId);
        Task SendRiskScoringCheckRequest(Guid merchantId);
        Task TriggerChecks(Guid merchantId);
    }
}
