﻿using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Search;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using Services.OrderExport;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests.OrderExportTests
{
    public class OrderExportServiceTests
    {
        private readonly OrderExportService orderExportService;
        private readonly Mock<IReferenceService> referenceService = new Mock<IReferenceService>();
        private readonly Mock<ISearchService> searchService = new Mock<ISearchService>();
        private readonly Mock<IUserService> userService = new Mock<IUserService>();
        private readonly Mock<ICounterpartyProvider> counterpartyProvider = new Mock<ICounterpartyProvider>();
        private Mock<IOptions<KsaTeamAndDeisgnationFilterToggle>> ksaTeamsAndDesignationFeatureToggle = new Mock<IOptions<KsaTeamAndDeisgnationFilterToggle>>();
        private Mock<IOptions<UaeTeamAndDeisgnationFilterToggle>> UaeTeamsAndDesignationFeatureToggle = new Mock<IOptions<UaeTeamAndDeisgnationFilterToggle>>();


        public OrderExportServiceTests()
        {
            orderExportService = new OrderExportService(referenceService.Object, searchService.Object, userService.Object, counterpartyProvider.Object, ksaTeamsAndDesignationFeatureToggle.Object, UaeTeamsAndDesignationFeatureToggle.Object);
        }

        private readonly Catalogue[] catalogueResponse = new Catalogue[]
        {
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.AccountNumber,
                Key = Constants.Catalogues.DefaultKey,
                Value = "34567"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.AccountNumber,
                Key = "Other key",
                Value = "111112"
            },
            new Catalogue()
            {
                CatalogueName = Constants.Catalogues.CommercialRegistration,
                Key = Constants.Catalogues.DefaultKey,
                Value = "3456"
                },
            new Catalogue
            {
                Key = "2",
                Value = "Dammam",
                CatalogueName = Constants.Catalogues.Cities
            },
            new Catalogue
            {
                Key = "1",
                Value = "Riyadh East",
                CatalogueName = Constants.Catalogues.Areas
            },
            new Catalogue
            {
                Key= "VERIFIED",
                CatalogueName = Constants.Catalogues.MerchantStatus,
                Value = "Verified"
            },
            new Catalogue
            {
                Key = "VERIFIED",
                Value = "Verified",
                CatalogueName = Constants.Catalogues.OrderStatus
            },
            new Catalogue
            {
                Key = "UNASSIGNED",
                Value = "Unassigned",
                CatalogueName = Constants.Catalogues.ReferralChannel
            },
            new Catalogue
            {
                Key = "PROJECT_A",
                Value = "Rental",
                CatalogueName = Constants.Catalogues.ProjectName
            }
        };

        [Test]
        public async Task ExportByIdTest()
        {
            var orderId = Guid.NewGuid();
            var orders = new List<OrdersExport>() {
                new OrdersExport()
                {
                    OrderId = orderId,
                    BankAccounts = new List<BankAccount>(){new BankAccount()},
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED"
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));

            var orderExport = await orderExportService.ExportOrderByIdAsync(orderId);

            orderExport.Should().NotBeNull();
        }

        [Test]
        public async Task ExportAllTest()
        {
            var order = Guid.NewGuid();
            var orderItem = Guid.NewGuid();
            var productInstance = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = order,
                    CounterParty = "GEIDEA_SAUDI",
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BusinessType = "LIMITED",
                    UnifiedId = "test",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItem,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType="TERMINAL",
                                    OrderItemId = orderItem,
                                    ProductInstanceId = productInstance,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"MIDMerchantReference\"," +
                                            "\"FullTId\": \"FullTId\",\"POSDataCode\": \"POSDataCode\"," +
                                            "\"TId\": \"TId\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstance
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                new OrdersExport {
                    OrderId = order,
                    CounterParty = "GEIDEA_SAUDI",
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BusinessType = "SOLE_TRADER",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItem,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType="TERMINAL",
                                    OrderItemId = orderItem,
                                    ProductInstanceId = productInstance,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"MIDMerchantReference\"," +
                                            "\"FullTId\": \"FullTId\",\"POSDataCode\": \"POSDataCode\"," +
                                            "\"TId\": \"TId\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstance
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                new OrdersExport {
                    OrderId = order,
                    CounterParty = "GEIDEA_SAUDI",
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BusinessType = "MUNICIPAL_ENTITY",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItem,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType="TERMINAL",
                                    OrderItemId = orderItem,
                                    ProductInstanceId = productInstance,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"MIDMerchantReference\"," +
                                            "\"FullTId\": \"FullTId\",\"POSDataCode\": \"POSDataCode\"," +
                                            "\"TId\": \"TId\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstance
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
            ordersExport[0].BusinessType.Should().Be("LIMITED");
            ordersExport[0].UnifiedId.Should().Be("test");
            ordersExport[1].BusinessType.Should().Be("SOLE_TRADER");
            ordersExport[1].UnifiedId.Should().BeNull();
            ordersExport[2].BusinessType.Should().Be("MUNICIPAL_ENTITY");
            ordersExport[2].UnifiedId.Should().BeNull();
        }

        [Test]
        public async Task ExportAllTest_WithNoProductInstance()
        {
            var order = Guid.NewGuid();
            var orderItem = Guid.NewGuid();
            var productInstance = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = order,
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItem,
                            ProductCode = "test"
                        }
                    }
                },
                new OrdersExport {
                    OrderId = Guid.NewGuid(),
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
        }

        [Test]
        public async Task ExportAllTest_WithSupportedProductType()
        {
            var order = Guid.NewGuid();
            var orderItem = Guid.NewGuid();
            var productInstance = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = order,
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItem,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType="WSB_ADDON",
                                    OrderItemId = orderItem,
                                    ProductInstanceId = productInstance,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"MIDMerchantReference\"," +
                                            "\"FullTId\": \"FullTId\",\"POSDataCode\": \"POSDataCode\"," +
                                            "\"TId\": \"TId\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstance
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                new OrdersExport {
                    OrderId = Guid.NewGuid(),
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
        }

        [Test]
        public async Task ExportAllTest_WithProductInstanceChildren()
        {
            var order = Guid.NewGuid();
            var orderItem = Guid.NewGuid();
            var productInstance = Guid.NewGuid();

            var orders = new List<OrdersExport>
            {
                new OrdersExport {
                    OrderId = order,
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() },
                    OrderItem  = new List<Common.Models.Search.OrderItemResponse>()
                    {
                        new Common.Models.Search.OrderItemResponse()
                        {
                            OrderItemId = orderItem,
                            ProductInstances = new List<ProductInstanceResponse>()
                            {
                                new ProductInstanceResponse()
                                {
                                    ProductType="TERMINAL",
                                    OrderItemId = orderItem,
                                    ProductInstanceId = productInstance,
                                    Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"MIDMerchantReference\"," +
                                            "\"FullTId\": \"FullTId\",\"POSDataCode\": \"POSDataCode\"," +
                                            "\"TId\": \"TId\","+"}",
                                    Children = new List<ProductInstanceChildren>()
                                    {
                                        new ProductInstanceChildren()
                                        {
                                            ParentId = productInstance,
                                            ProductType = "TERMINAL",
                                            Data= "{\"ShortName_EN\": \"ShortName\",\"Mcc\": \"MCC\"," +
                                            "\"MIDMerchantReference\": \"MIDMerchantReference\"," +
                                            "\"FullTId\": \"FullTId\",\"POSDataCode\": \"POSDataCode\"," +
                                            "\"TId\": \"TId\","+"}"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                new OrdersExport {
                    OrderId = Guid.NewGuid(),
                    MerchantStatus = "VERIFIED",
                    OrderStatus = "VERIFIED",
                    BankAccounts = new List<BankAccount>(){ new BankAccount() }
                }
            };

            referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>())).Returns(Task.FromResult(catalogueResponse));
            searchService.Setup(x => x.ExportOrdersAsync(It.IsAny<OrderSearchCriteria>())).Returns(Task.FromResult(orders));
            var ordersExport = await orderExportService.ExportOrdersAsync(new OrderSearchCriteria());

            ordersExport.Should().NotBeNull();
            ordersExport.Count.Should().Be(orders.Count);
        }
    }
}
