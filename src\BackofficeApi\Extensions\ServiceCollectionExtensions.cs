﻿using Common.Options;
using Common.Validators;
using FluentValidation;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BackofficeApi.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddApplicationOptions(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddOptions();

            services.AddOptions<ApplicationOptions>().Bind(configuration.GetSection("Application"))
               .Validate(o =>
               {
                   new ApplicationOptionsValidator().ValidateAndThrow(o);
                   return true;
               });

            services.AddOptions<UrlSettings>().Bind(configuration.GetSection("UrlSettings"))
               .Validate(o =>
               {
                   new UrlSettingsValidator().ValidateAndThrow(o);
                   return true;
               });

            services.AddOptions<CsvOptions>().Bind(configuration.GetSection("Csv"))
               .Validate(o =>
               {
                   new CsvOptionsValidator().ValidateAndThrow(o);
                   return true;
               });

            services.AddOptions<ExceptionOptions>().Bind(configuration.GetSection("ApiExceptions"));
            services.AddOptions<GsdkSettings>().Bind(configuration.GetSection("GsdkSettings"));
            services.Configure<TmsIntegrationFeatureToggle>(configuration.GetSection("TMSIntegrationFeatureToggle"));

            return services;
        }
    }
}
