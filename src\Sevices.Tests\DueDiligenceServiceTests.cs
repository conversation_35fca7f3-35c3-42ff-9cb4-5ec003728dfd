﻿using Common;
using Common.Models.Match;
using Common.Options;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Cleanup;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using NUnit.Framework;
using Services;
using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class DueDiligenceServiceTests
    {
        private readonly Mock<ILogger<DueDiligenceService>> logger = new Mock<ILogger<DueDiligenceService>>();
        private readonly Mock<IOptions<UrlSettings>> urlSettingsOptions = new Mock<IOptions<UrlSettings>>();
        private readonly CleanupService cleanupService = null!;
        private readonly Mock<ILogger<CleanupService>> loggerCleanup = new Mock<ILogger<CleanupService>>();
        private IOptions<WorldCheckFeatureManagement> worldCheckOneFeatureToggle = null!;
        private IOptions<FinscanCheckFeatureToggle> finscanCheckFeatureToggle = null!;
        private IOptions<MatchCheckFeatureToggle> matchCheckFeatureToggle = null!;
        private IOptions<SASCheckFeatureToggle> sasCheckFeatureToggle = null!;
        private IOptions<RiskScoringFeatureToggle> riskScoringFeatureToggle = null!;
        private readonly Mock<ICounterpartyProvider> counterpartyProvider = new();

        public DueDiligenceServiceTests()
        {
            urlSettingsOptions.Setup(x => x.Value).Returns(TestsHelper.UrlSettingsOptions);
            cleanupService = new CleanupService(loggerCleanup.Object);
            worldCheckOneFeatureToggle = Substitute.For<IOptions<WorldCheckFeatureManagement>>();
            finscanCheckFeatureToggle = Substitute.For<IOptions<FinscanCheckFeatureToggle>>();
            matchCheckFeatureToggle = Substitute.For<IOptions<MatchCheckFeatureToggle>>();
            sasCheckFeatureToggle = Substitute.For<IOptions<SASCheckFeatureToggle>>();
            riskScoringFeatureToggle = Substitute.For<IOptions<RiskScoringFeatureToggle>>();
            worldCheckOneFeatureToggle.Value.Returns(new WorldCheckFeatureManagement() { AllowedCounterparties = "GEIDEA_UAE" });
            finscanCheckFeatureToggle.Value.Returns(new FinscanCheckFeatureToggle() { AllowedCounterparties = "GEIDEA_SAUDI" });
            matchCheckFeatureToggle.Value.Returns(new MatchCheckFeatureToggle() { AllowedCounterparties = "GEIDEA_SAUDI", EnableMatchCheck = true });
            sasCheckFeatureToggle.Value.Returns(new SASCheckFeatureToggle() { AllowedCounterparties = "GEIDEA_SAUDI" });
            riskScoringFeatureToggle.Value.Returns(new RiskScoringFeatureToggle() { AllowedCounterparties = "GEIDEA_UAE" });
        }

        [Test]
        public async Task DueDiligenceService_should_call_correct_endpoint_and_return_deserialized_value()
        {
            // Arrange
            var instance = new MatchResponse();

            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(instance)),
                })
                .Verifiable();

            var dueDiligenceService = new DueDiligenceService(
                client: new HttpClient(handlerMock.Object),
                logger: logger.Object,
                urlSettingsOptions: urlSettingsOptions.Object,
                worldCheckOneFeatureToggle, counterpartyProvider.Object,
                finscanCheckFeatureToggle, matchCheckFeatureToggle, sasCheckFeatureToggle, riskScoringFeatureToggle);

            // Act
            var report = await dueDiligenceService.RetrieveMerchantMatchReport(Guid.Empty);

            // Assert
            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.DueDiligenceServiceBaseUrlNS}/api/v1/match/{Guid.Empty}");

            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Get && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());

            report.Should().NotBeNull();
        }

        [Test]
        public async Task DueDiligenceService_ShouldtriggerWorldCheckOne()
        {
            // Arrange
            var instance = new MatchResponse();
            var merchantId = Guid.NewGuid();
            var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject("Success")),
                })
                .Verifiable();
            counterpartyProvider.Setup(x => x.GetCode()).Returns(Constants.CounterParty.Uae);

            var dueDiligenceService = new DueDiligenceService(
                client: new HttpClient(handlerMock.Object),
                logger: logger.Object,
                urlSettingsOptions: urlSettingsOptions.Object,
                worldCheckOneFeatureToggle,
                counterpartyProvider.Object, finscanCheckFeatureToggle, matchCheckFeatureToggle, sasCheckFeatureToggle, riskScoringFeatureToggle);

            // Act
            await dueDiligenceService.TriggerChecks(Guid.Empty);

            // Assert

            var expectedUri = new Uri($"{urlSettingsOptions.Object.Value.DueDiligenceServiceBaseUrlNS}/api/v1/WorldCheckOne/Business/Create/{Guid.Empty}");

            handlerMock.Protected().Verify("SendAsync",
                Times.Exactly(1),
                ItExpr.Is<HttpRequestMessage>(req => req.Method == HttpMethod.Post && req.RequestUri == expectedUri),
                ItExpr.IsAny<CancellationToken>());


        }



    }
}
