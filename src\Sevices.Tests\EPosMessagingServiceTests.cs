﻿using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Merchant;
using Common.Models.Product;
using Common.Models.User;
using Common.Services;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Messaging;
using Geidea.Utils.Messaging.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using NUnit.Framework;
using Services;
using Services.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common.Enums;
using FluentAssertions;
using Geidea.Messages.Epos.Messages;
using Geidea.Utils.Common;
using NSubstitute.ExceptionExtensions;
using Constants = Common.Constants;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    class EPosMessagingServiceTests
    {
        private IProductService productService = null!;
        private ILogger<EPosMessagingService> loggerService = null!;
        private ILogger<MessageClient> messageClientLogger = null!;
        private IHttpContextAccessor httpContextAccessor = null!;
        private QueueSettings queueSettings = null!;
        private IOptionsMonitor<RabbitMqOptions> rabbitMqOptions = null!;
        private EPosMessagingClient messageClient = null!;
        private EPosMessagingSoftwareTypeClient ePosSwTypeClient = null!;
        private IReferenceService referenceService = null!;
        private IUserService userService = null!;
        private ICounterpartyProvider counterpartyProvider = null!;
        private EPosMessagingService eposMessagingService = null!;
        private ICheckoutClient checkoutClient = null!;
        private IMerchantClient merchantClient = null!;

        [SetUp]
        public void Setup()
        {
            checkoutClient = Substitute.For<ICheckoutClient>();
            merchantClient = Substitute.For<IMerchantClient>();
            productService = Substitute.For<IProductService>();
            loggerService = Substitute.For<ILogger<EPosMessagingService>>();
            messageClientLogger = Substitute.For<ILogger<MessageClient>>();
            httpContextAccessor = Substitute.For<IHttpContextAccessor>();
            queueSettings = new QueueSettings();
            rabbitMqOptions = Substitute.For<IOptionsMonitor<RabbitMqOptions>>();
            messageClient = Substitute.For<EPosMessagingClient>(messageClientLogger, httpContextAccessor, queueSettings, rabbitMqOptions);
            ePosSwTypeClient = Substitute.For<EPosMessagingSoftwareTypeClient>(httpContextAccessor, messageClientLogger, rabbitMqOptions);
            referenceService = Substitute.For<IReferenceService>();
            userService = Substitute.For<IUserService>();
            counterpartyProvider = Substitute.For<ICounterpartyProvider>();
            referenceService.WhenForAnyArgs(x => x.GetCataloguesAsync(new string[] { "PRODUCT" }).Returns(
                Task.FromResult(new Catalogue[] {
                                    new Catalogue {
                                        CatalogueName = "PRODUCT",
                                        Key = "A920",
                                        Value = "A920 Pos"
                                    }
                                })));
            eposMessagingService = new EPosMessagingService(messageClient, ePosSwTypeClient ,merchantClient, checkoutClient, productService, referenceService, userService, counterpartyProvider, TestsHelper.GetFreelancerTerminalTypeFeatureToggle(), loggerService);
        }

        [Test]
        public void CreateOrderEPosTicketAsync_OrderDoesNotExist_ReturnsFalse()
        {
            OrderResponse orderResponse = null!;
            var orderId = new Guid();
            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));

            var exception = Assert.ThrowsAsync<ServiceException>(async () => await eposMessagingService.CreateOrderEPosTicketAsync(orderId));
            Assert.AreEqual(exception!.ProblemDetails.Detail, Errors.OrderNotFound.Message);
        }


        [Test]
        public void CreateOrderEPosTicketAsync_OrderDoesNotHaveStatusProductsRegistered_ReturnsFalse()
        {
            OrderResponse orderResponse = new OrderResponse { OrderId = Guid.NewGuid() };
            checkoutClient.GetOrderByIdAsync(orderResponse.OrderId).Returns(Task.FromResult(orderResponse));

            var exception = Assert.ThrowsAsync<ServiceException>(async () => await eposMessagingService.CreateOrderEPosTicketAsync(orderResponse.OrderId));
            Assert.AreEqual(exception!.ProblemDetails.Detail, Errors.OrderDoesNotHaveStatusRegistered.Message);
        }

        [Test]
        public void CreateOrderEPosTicketAsync_AllProductInstancesAreSentToEpos_ErrorIsReturned()
        {

            var orderId = Guid.NewGuid();
            var merchant = new MerchantEPosInformation();
            var product = new ProductShortResponse { Type = "M_POS", Code = "A920" };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), EPosTicketId = "0", Product = product };
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                MerchantId = Guid.NewGuid(),
                PaymentMethod = "ON_DELIVERY",
                OrderNumber = "GX_00001",
                Total = 0,
                Vat = 0,
                SalesName = "",
                OrderItem = new List<OrderItemResponse> { orderItem },
                OrderStatus = "PRODUCTS_REGISTERED"
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            merchantClient.GetMerchantEPosInformationAsync(orderResponse.MerchantId).Returns(Task.FromResult(merchant));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance }));

            var exception = Assert.ThrowsAsync<ServiceException>(async () => await eposMessagingService.CreateOrderEPosTicketAsync(orderId, true));
            Assert.AreEqual(exception!.ProblemDetails.Detail, Errors.OrderAlreadySentToEPos.Message);
        }

        [Test]
        public void CreateOrderEPosTicketAsync_ProductInstancesNotContainsTerminals_OrderNotSentToEpos()
        {
            var orderId = Guid.NewGuid();
            var merchant = new MerchantEPosInformation();
            var product = new ProductShortResponse { Type = "BUNDLE", Code = "PAYMENT_GATEWAY_BUNDLE" };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), EPosTicketId = "0", Product = product };
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                MerchantId = Guid.NewGuid(),
                PaymentMethod = "ON_DELIVERY",
                OrderNumber = "GX_00001",
                Total = 0,
                Vat = 0,
                SalesName = "",
                OrderItem = new List<OrderItemResponse> { orderItem },
                OrderStatus = "PRODUCTS_REGISTERED"
            };

            var payload = new CreateEPosTicketPayload();
            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            merchantClient.GetMerchantEPosInformationAsync(orderResponse.MerchantId).Returns(Task.FromResult(merchant));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance }));

            messageClient.Received(0).SendMessageToEPos(payload);
        }

        [Test]
        public async Task CreateOrderEPosTicketAsync_ProductInstanceEposTicketNotNull_NoEPosRequestIsMadeForThatProductInstance()
        {
            var orderId = Guid.NewGuid();
            var merchant = new MerchantEPosInformation
            {
                Area = "Area",
                CityId = "1",
                ExternalIdentifier = "123",
                FirstName = "Fn",
                LastName = "Ln",
                MerchantId = Guid.NewGuid(),
                PrincipalTelephone = "**********",
                BusinessName = "Business Name",
                BusinessNameAr = "Business Name AR"
            };

            var product = new ProductShortResponse { Type = "M_POS", Code = "A920" };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), EPosTicketId = "0", Product = product };
            var productInstance1 = new ProductInstance { ProductInstanceId = Guid.NewGuid(), EPosTicketId = null, Product = product, Data = "{ \"Mcc\": \"23\", \"TId\": \"12\", \"POSDataCode\":\"12\" ,\"ProviderBank\":\"DEFAULT_BANK\", \"MIDMerchantReference\":\"123456\" }" };
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                MerchantId = Guid.NewGuid(),
                PaymentMethod = "ON_DELIVERY",
                OrderNumber = "GX_00001",
                Total = 0,
                Vat = 0,
                SalesName = "",
                OrderItem = new List<OrderItemResponse> { orderItem },
                OrderStatus = "PRODUCTS_REGISTERED"
            };

            referenceService.GetCataloguesAsync(Arg.Any<string[]>()).Returns(
                Task.FromResult(new Catalogue[] {
                    new Catalogue {
                        CatalogueName = "ACQUIRING_LEDGER",
                        Key = "DEFAULT_BANK",
                        Value = "Riyadh"
                    }
                }));

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            merchantClient.GetMerchantEPosInformationAsync(orderResponse.MerchantId).Returns(Task.FromResult(merchant));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance, productInstance1 }));

            var payload = new CreateEPosTicketPayload();
            messageClient.WhenForAnyArgs(x => x.SendMessageToEPos(payload)).DoNotCallBase();

            await eposMessagingService.CreateOrderEPosTicketAsync(orderId);
            loggerService.Received(1).LogInformation($"Product instance {productInstance.ProductInstanceId} has already been sent to EPos");
        }

        [Test]
        public void CreateOrderEPosTicketAsync_ProductTypeIsNotTerminal_NoEposRequestIsMadeForIt()
        {

            var orderId = Guid.NewGuid();
            var merchant = new MerchantEPosInformation();
            var product = new ProductShortResponse { Type = "" };
            var product1 = new ProductShortResponse { Type = "M_POS" };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), Product = product };
            var productInstance1 = new ProductInstance { ProductInstanceId = Guid.NewGuid(), Product = product1 };
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                MerchantId = Guid.NewGuid(),
                OrderItem = new List<OrderItemResponse> { orderItem },
                OrderStatus = "PRODUCTS_REGISTERED"
            };
            var payload = new CreateEPosTicketPayload();

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            merchantClient.GetMerchantEPosInformationAsync(orderResponse.MerchantId).Returns(Task.FromResult(merchant));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance, productInstance1 }));

            messageClient.WhenForAnyArgs(x => x.SendMessageToEPos(payload)).DoNotCallBase();

            Assert.ThrowsAsync<ServiceException>(async () => await eposMessagingService.CreateOrderEPosTicketAsync(orderId));
            messageClient.DidNotReceive().SendMessageToEPos(payload);
        }

        [Test]
        public async Task CreateOrderEPosTicketAsync_ProductTypeIsTerminal_EposRequestIsMade_Saudi()
        {

            var orderId = Guid.NewGuid();
            var merchant = new MerchantEPosInformation
            {
                Area = "Area",
                CityId = "1",
                ExternalIdentifier = "123",
                FirstName = "Fn",
                LastName = "Ln",
                SalesId = "GDSL000004",
                BusinessType = "SOLE_TRADER",
                MerchantId = Guid.NewGuid(),
                PrincipalTelephone = "**********",
                BusinessName = "TEST",
                BusinessNameAr = "TEST AR"
            };
            UserResponse[] users =
            {
                new UserResponse
                {
                    UserId = Guid.NewGuid(),
                    SalesId = "GDSL000004",
                    Email = "<EMAIL>"
                }
            };
            var product = new ProductShortResponse
            {
                Type = "M_POS",
                Code = "A920",
                Prices = new List<PriceShortResponse> {
                    new PriceShortResponse { PerItemPrice = 10040, ChargeType = Constants.ChargeType.Reccurrring },
                    new PriceShortResponse { PerItemPrice = 10000, ChargeType = Constants.ChargeType.SetupCharge },
                    new PriceShortResponse { PerItemPrice = 60000, ChargeType = "OTHER" },
                    new PriceShortResponse { PerItemPrice = null, ChargeType = Constants.ChargeType.SetupCharge }
                }
            };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), Product = product, Data = "{ \"Mcc\": \"23\", \"TId\": \"12\",\"POSDataCode\":\"12\" ,\"ProviderBank\":\"DEFAULT_BANK\", \"MIDMerchantReference\":\"123456\" , \"ExternalBankTId\":\"********\" }" };
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                MerchantId = Guid.NewGuid(),
                PaymentMethod = "ON_DELIVERY",
                OrderNumber = "GX_00001",
                Total = 0,
                Vat = 0,
                SalesName = "",
                OrderItem = new List<OrderItemResponse> { orderItem },
                OrderStatus = "PRODUCTS_REGISTERED"
            };
            referenceService.GetCataloguesAsync(Arg.Any<string[]>()).Returns(
                Task.FromResult(new Catalogue[] {
                    new Catalogue {
                        CatalogueName = "ACQUIRING_LEDGER",
                        Key = "DEFAULT_BANK",
                        Value = "Riyadh"
                    }
                }));
            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            merchantClient.GetMerchantEPosInformationAsync(orderResponse.MerchantId).Returns(Task.FromResult(merchant));
            userService.SearchUsersAsync(Arg.Any<UserSearchParameters>()).Returns(Task.FromResult(users));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance }));

            messageClient.WhenForAnyArgs(x => x.SendMessageToEPos(Arg.Any<CreateEPosTicketPayload>())).DoNotCallBase();
            counterpartyProvider.GetCode().Returns(Geidea.Utils.Common.Constants.CounterpartySaudi);

            await eposMessagingService.CreateOrderEPosTicketAsync(orderId);
            messageClient.Received(1).SendMessageToEPos(Arg.Is<CreateEPosTicketPayload>(x => x.MposPrice == 200.4m && x.RegType == "M" && x.OldTerminal == "********" && x.TerminalType == "T300_4G"));
        }

        [Test]
        public async Task CreateOrderEPosTicketAsync_ProductTypeIsTerminal_EposRequestIsMade_Egypt()
        {

            var orderId = Guid.NewGuid();
            var merchant = new MerchantEPosInformation
            {
                Governorate = "Governorate",
                CityId = "1",
                ExternalIdentifier = "123",
                FirstName = "Fn",
                LastName = "Ln",
                SalesId = "GDSL000004",
                BusinessType = "SOLE_TRADER",
                MerchantId = Guid.NewGuid(),
                PrincipalTelephone = "**********"
            };
            UserResponse[] users =
            {
                new UserResponse
                {
                    UserId = Guid.NewGuid(),
                    SalesId = "GDSL000004",
                    Email = "<EMAIL>"
                }
            };
            var product = new ProductShortResponse
            {
                Type = "M_POS",
                Code = "A920",
                Prices = new List<PriceShortResponse> {
                    new PriceShortResponse { PerItemPrice = 10040, ChargeType = Constants.ChargeType.Reccurrring },
                    new PriceShortResponse { PerItemPrice = 10000, ChargeType = Constants.ChargeType.SetupCharge },
                    new PriceShortResponse { PerItemPrice = 60000, ChargeType = "OTHER" },
                    new PriceShortResponse { PerItemPrice = null, ChargeType = Constants.ChargeType.SetupCharge }
                }
            };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), Product = product, Data = "{ \"Mcc\": \"23\", \"TId\": \"12\",\"POSDataCode\":\"12\" ,\"ProviderBank\":\"DEFAULT_BANK\", \"MIDMerchantReference\":\"123456\" , \"ExternalBankTId\":\"********\", \"ShortName_EN\":\"test\" }" };
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                MerchantId = Guid.NewGuid(),
                PaymentMethod = "ON_DELIVERY",
                OrderNumber = "GX_00001",
                Total = 0,
                Vat = 0,
                SalesName = "",
                OrderItem = new List<OrderItemResponse> { orderItem },
                OrderStatus = "PRODUCTS_REGISTERED"
            };
            referenceService.GetCataloguesAsync(Arg.Any<string[]>()).Returns(
                Task.FromResult(new Catalogue[] {
                    new Catalogue {
                        CatalogueName = "ACQUIRING_LEDGER",
                        Key = "DEFAULT_BANK",
                        Value = "Riyadh"
                    }
                }));
            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            merchantClient.GetMerchantEPosInformationAsync(orderResponse.MerchantId).Returns(Task.FromResult(merchant));
            userService.SearchUsersAsync(Arg.Any<UserSearchParameters>()).Returns(Task.FromResult(users));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance }));

            messageClient.WhenForAnyArgs(x => x.SendMessageToEPos(Arg.Any<CreateEPosTicketPayload>())).DoNotCallBase();
            counterpartyProvider.GetCode().Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            await eposMessagingService.CreateOrderEPosTicketAsync(orderId);
            messageClient.Received(1).SendMessageToEPos(Arg.Is<CreateEPosTicketPayload>(x => x.MposPrice == 200.4m && x.RegType == "M" && x.OldTerminal == "********" && x.TerminalType == "A920"));
        }

        [Test]
        public async Task CreateOrderEPosTicketAsync_ProductTypeIsTerminalAndHasBillPayment_EposRequestIsMadeWithSoftwareType_Egypt()
        {

            var orderId = Guid.NewGuid();
            var merchant = new MerchantEPosInformation
            {
                Governorate = "Governorate",
                CityId = "1",
                ExternalIdentifier = "123",
                FirstName = "Fn",
                LastName = "Ln",
                SalesId = "GDSL000004",
                BusinessType = "SOLE_TRADER",
                MerchantId = Guid.NewGuid(),
                PrincipalTelephone = "**********"
            };
            UserResponse[] users =
            {
                new UserResponse
                {
                    UserId = Guid.NewGuid(),
                    SalesId = "GDSL000004",
                    Email = "<EMAIL>"
                }
            };
            var product = new ProductShortResponse
            {
                Type = "M_POS",
                Code = "A920",
                Prices = new List<PriceShortResponse>
                {
                    new PriceShortResponse { PerItemPrice = 10040, ChargeType = Constants.ChargeType.Reccurrring },
                    new PriceShortResponse { PerItemPrice = 10000, ChargeType = Constants.ChargeType.SetupCharge },
                    new PriceShortResponse { PerItemPrice = 60000, ChargeType = "OTHER" },
                    new PriceShortResponse { PerItemPrice = null, ChargeType = Constants.ChargeType.SetupCharge }
                }
            };

            var bundleProductInstance = new ProductInstance()
            {
                ProductInstanceId = Guid.NewGuid(),
                Product = new ProductShortResponse()
                {
                    Type = "Bundle",
                    Code = "GO_SMART"
                }
            };

            var terminalProductInstance = new ProductInstance
            {
                ProductInstanceId = Guid.NewGuid(),
                Product = product,
                ParentId = bundleProductInstance.ProductInstanceId,
                Data =
                    "{ \"Mcc\": \"23\", \"TId\": \"12\",\"POSDataCode\":\"12\" ,\"ProviderBank\":\"DEFAULT_BANK\", \"MIDMerchantReference\":\"123456\" , \"ExternalBankTId\":\"********\", \"ShortName_EN\":\"test\" }"
            };

            var billPaymentProductInstance = new ProductInstance
            {
                ProductInstanceId = Guid.NewGuid(),
                ParentId = bundleProductInstance.ProductInstanceId,
                Product = new ProductShortResponse()
                {
                    Code = Constants.ProductCode.BillPayment,
                    Type = "SERVICE"
                }
            };

            bundleProductInstance.Children = new List<ProductInstance>() { terminalProductInstance, billPaymentProductInstance };

            var orderItem = new OrderItemResponse
            { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { terminalProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                MerchantId = Guid.NewGuid(),
                PaymentMethod = "ON_DELIVERY",
                OrderNumber = "GX_00001",
                Total = 0,
                Vat = 0,
                SalesName = "",
                OrderItem = new List<OrderItemResponse> { orderItem },
                OrderStatus = "PRODUCTS_REGISTERED"
            };
            referenceService.GetCataloguesAsync(Arg.Any<string[]>()).Returns(
                Task.FromResult(new Catalogue[]
                {
                new Catalogue
                {
                    CatalogueName = "ACQUIRING_LEDGER",
                    Key = "DEFAULT_BANK",
                    Value = "Riyadh"
                }
                }));
            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            merchantClient.GetMerchantEPosInformationAsync(orderResponse.MerchantId).Returns(Task.FromResult(merchant));
            userService.SearchUsersAsync(Arg.Any<UserSearchParameters>()).Returns(Task.FromResult(users));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));

            messageClient.WhenForAnyArgs(x => x.SendMessageToEPos(Arg.Any<CreateEPosTicketPayload>())).DoNotCallBase();
            counterpartyProvider.GetCode().Returns(Geidea.Utils.Common.Constants.CounterpartyEgypt);

            await eposMessagingService.CreateOrderEPosTicketAsync(orderId);
            messageClient.Received(1).SendMessageToEPos(Arg.Is<CreateEPosTicketPayload>(x => x.SoftwareType == 1));
        }

        [Test]
        public async Task GetEPosActions_WhenOrderNotSentToEPos_ShouldReturnCreateAction()
        {
            var orderId = Guid.NewGuid();
            
            var product = new ProductShortResponse
            {
                Type = "M_POS",
                Code = "A920"
            };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), Product = product};
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };
          
            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance }));

            var action = await eposMessagingService.GetEPosActions(orderId);

            action.Should().Be(EPosAction.CreateEPosTicket);
        }

        [Test]
        public async Task GetEPosActions_WhenOrderSentToEPosForCreate_ShouldReturnUpdateAction()
        {
            var orderId = Guid.NewGuid();

            var bundleProductInstance = GetBundleProductInstanceWithBp(false);

            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { bundleProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));

            var action = await eposMessagingService.GetEPosActions(orderId);

            action.Should().Be(EPosAction.UpdateEPosSwType);
        }

        [Test]
        public async Task GetEPosActions_WhenOrderSentToEPosForCreateAndTerminalWithoutBillPayment_ShouldReturnNoAction()
        {
            var orderId = Guid.NewGuid();

            var bundleProductInstance = GetBundleProductInstanceWithBp(false);
            bundleProductInstance.Children.RemoveAt(1);

            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { bundleProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));

            var action = await eposMessagingService.GetEPosActions(orderId);

            action.Should().Be(EPosAction.NoAction);
        }

        [Test]
        public async Task GetEPosActions_WhenOrderSentToEPos_ShouldReturnNoAction()
        {
            var orderId = Guid.NewGuid();

            var bundleProductInstance = GetBundleProductInstanceWithBp(true);

            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { bundleProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));

            var action = await eposMessagingService.GetEPosActions(orderId);

            action.Should().Be(EPosAction.NoAction);
        }

        [Test]
        public async Task SendEPosSwTypeUpdate_WhenOrderNotFound_ShouldThrow()
        { 
            OrderResponse orderResponse = null!;
            checkoutClient.GetOrderByIdAsync(Arg.Any<Guid>()).Returns(Task.FromResult(orderResponse));

            await eposMessagingService.Invoking(e => e.SendEPosSwTypeUpdate(Guid.NewGuid()))
                .Should()
                .ThrowAsync<ServiceException>()
                .Where(x => x.ProblemDetails.Type == Errors.OrderNotFound.Code);
        }

        [Test]
        public async Task SendEPosSwTypeUpdate_WhenNotAllTerminalsHaveTicketNumber_ShouldThrow()
        {
            var orderId = Guid.NewGuid();

            var product = new ProductShortResponse
            {
                Type = "M_POS",
                Code = "A920"
            };
            var productInstance = new ProductInstance { ProductInstanceId = Guid.NewGuid(), Product = product };
            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { productInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { productInstance }));

            await eposMessagingService.Invoking(e => e.SendEPosSwTypeUpdate(orderId))
                .Should()
                .ThrowAsync<ServiceException>()
                .Where(x => x.ProblemDetails.Type == Errors.OrderNotSentToEPos.Code);
        }

        [Test]
        public async Task SendEPosSwTypeUpdate_WhenTerminalsAreUpToDateInEPos_ShouldThrow()
        {
            var orderId = Guid.NewGuid();

            var bundleProductInstance = GetBundleProductInstanceWithBp(true);

            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { bundleProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));


            await eposMessagingService.Invoking(e => e.SendEPosSwTypeUpdate(orderId))
                .Should()
                .ThrowAsync<ServiceException>()
                .Where(x => x.ProblemDetails.Type == Errors.OrderUpToDateInEPos.Code);
        }

        [Test]
        public async Task SendEPosSwTypeUpdate_WhenTerminalWithMissingTId_ShouldThrow()
        {
            var orderId = Guid.NewGuid();

            var bundleProductInstance = GetBundleProductInstanceWithBp(false);
            var terminalProductInstance = bundleProductInstance
                .Children
                .FirstOrDefault(t => t.Product.Type == "M_POS");
            terminalProductInstance!.Data = "{ \"TId\": null }";

            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { bundleProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));


            await eposMessagingService.Invoking(e => e.SendEPosSwTypeUpdate(orderId))
                .Should()
                .ThrowAsync<ServiceException>()
                .Where(x => x.ProblemDetails.Type == Errors.TerminalMissingTId.Code);
        }

        [Test]
        public async Task SendEPosSwTypeUpdate_WhenTerminalWithoutBillPayment_ShouldThrow()
        {
            var orderId = Guid.NewGuid();
            const string tId = "1324";
            var bundleProductInstance = GetBundleProductInstanceWithBp(false);
            bundleProductInstance.Children.RemoveAt(1);
            var terminalProductInstance = bundleProductInstance
                .Children
                .FirstOrDefault(t => t.Product.Type == "M_POS");
            terminalProductInstance!.Data = $"{{ \"TId\": \"{tId}\" }}";

            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { bundleProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));

            await eposMessagingService.Invoking(e => e.SendEPosSwTypeUpdate(orderId))
                .Should()
                .ThrowAsync<ServiceException>()
                .Where(x => x.ProblemDetails.Type == Errors.OrderUpToDateInEPos.Code);
        }

        [Test]
        public async Task SendEPosSwTypeUpdate_WhenValidationPassed_ShouldCallEposClient()
        {
            var orderId = Guid.NewGuid();
            const string tId = "1324";
            var bundleProductInstance = GetBundleProductInstanceWithBp(false);
            var terminalProductInstance = bundleProductInstance
                .Children
                .FirstOrDefault(t => t.Product.Type == "M_POS");
            terminalProductInstance!.Data = $"{{ \"TId\": \"{tId}\" }}";

            var orderItem = new OrderItemResponse { OrderItemId = Guid.NewGuid(), ProductInstanceIds = new List<Guid> { bundleProductInstance.ProductInstanceId } };
            var orderResponse = new OrderResponse
            {
                OrderNumber = "GX_00001",
                OrderItem = new List<OrderItemResponse> { orderItem }
            };

            checkoutClient.GetOrderByIdAsync(orderId).Returns(Task.FromResult(orderResponse));
            productService.GetProductInstances(Arg.Any<List<Guid>>())
                .Returns(Task.FromResult(new List<ProductInstance> { bundleProductInstance }));


            await eposMessagingService.SendEPosSwTypeUpdate(orderId);

            ePosSwTypeClient
                .Received(1)
                .SendMessageToEPos(Arg.Is<UpdateTerminalSoftwareMessage>(x => x.SoftwareType == 1 && x.TerminalId == tId));
        }

        private static ProductInstance GetBundleProductInstanceWithBp(bool bpSentToEPos)
        {
            var terminalProduct = new ProductShortResponse
            {
                Type = "M_POS",
                Code = "A920"
            };
            var bpProduct = new ProductShortResponse
            {
                Type = "SERVICES",
                Code = "BILL_PAYMENT"
            };
            var bundleProduct = new ProductShortResponse
            {
                Type = "BUNDLE",
                Code = "GO_SMART"
            };

            var bundleProductInstance = new ProductInstance
            {
                ProductInstanceId = Guid.NewGuid(),
                Product = bundleProduct,
                ParentId = null
            };
            var bpProductInstance = new ProductInstance
            {
                ProductInstanceId = Guid.NewGuid(),
                Product = bpProduct,
                ParentId = bundleProductInstance.ProductInstanceId
            };
            var terminalProductInstance = new ProductInstance
            {
                ProductInstanceId = Guid.NewGuid(),
                Product = terminalProduct,
                ParentId = bundleProductInstance.ProductInstanceId,
                EPosTicketId = "132",
                EPosBillPayments = bpSentToEPos
            };
            bundleProductInstance.Children = new List<ProductInstance> { terminalProductInstance, bpProductInstance };
            return bundleProductInstance;
        }
    }
}
