﻿using Common.Models.User;
using FluentValidation;

namespace Common.Validators
{
    public class UserSalesIdRequestValidator : AbstractValidator<UserSalesIdRequest>
    {
        public UserSalesIdRequestValidator()
        {
            RuleFor(x => x.UpdatedSalesId)
                .Must(x => x.Length <= 10)
                .WithErrorCode(Errors.UpdatedSalesIdLengthValidation.Code)
                .WithMessage(Errors.UpdatedSalesIdLengthValidation.Message);
        }
    }
}
