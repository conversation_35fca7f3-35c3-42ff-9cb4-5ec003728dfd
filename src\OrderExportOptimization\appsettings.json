{"ConfigUrlOld": {"Url": "http://geideapaymentgateway-db01-dev.francecentral.cloudapp.azure.com:5001", "VaultSecretsPath": "/usr/share/secrets/secrets.json", "Keys": ["ConfigUrlOld:VaultSecretsPath", "UrlSettings:DocumentServiceBaseUrl", "UrlSettings:MerchantServiceBaseUrl", "UrlSettings:CheckoutServiceBaseUrl", "UrlSettings:UserServiceBaseUrl", "UrlSettings:ProductServiceBaseUrl", "Application:DefaultContactReasonName", "Serilog:MinimumLevel", "Application:CorsAllowedOrigins", "Application:<PERSON>wa<PERSON>", "ApiExceptions:UseSlim", "AuthorizationPolicy:Server", "AuthorizationPolicy:Namespace"]}, "UrlSettings": {"DocumentServiceBaseUrl": "http://dev-document.dev.gd-azure-dev.net", "MerchantServiceBaseUrl": "http://dev-merchant.dev.gd-azure-dev.net", "CheckoutServiceBaseUrl": "http://dev-checkout.dev.gd-azure-dev.net", "UserServiceBaseUrl": "http://dev-user.dev.gd-azure-dev.net", "ProductServiceBaseUrl": "http://dev-product.dev.gd-azure-dev.net"}, "Application": {"Name": "Backoffice API", "Version": "0.1.0", "DefaultContactReasonName": "DEFAULT"}, "AuthorizationPolicy": {"Server": "http://localhost:8181/v1/data", "Namespace": "portal"}, "Serilog": {"MinimumLevel": "Debug", "Using": ["Serilog.Sinks.Console"], "Enrich": ["FromLogContext", "WithMachineName", "WithEnvironmentUserName", "WithProcessId"], "Properties": {"ApplicationName": "BackofficeAPI", "ProcessName": "BackofficeAPI"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}}