﻿using Common.Models.Merchant;
using Common.Options;
using Common.Services;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace OrderExportOptimization
{
    public class OrderExportOptimizationService : IOrderExportOptimizationService
    {
        private readonly IChecksService _checksService;
        private readonly HttpClient _client;
        private readonly ILogger<OrderExportOptimizationService> _logger;
        private readonly IMerchantService _merchantService;
        private readonly UrlSettings _urlSettingsOptions;

        public OrderExportOptimizationService(ILogger<OrderExportOptimizationService> logger, IMerchantService merchantService,
                                                IChecksService checksService, HttpClient client, IOptionsMonitor<UrlSettings> urlSettingsOptions)
        {
            _logger = logger;
            _merchantService = merchantService;
            _checksService = checksService;
            _client = client;
            _client.DefaultRequestHeaders.Add("X-CounterpartyCode", "GEIDEA_SAUDI");
            _urlSettingsOptions = urlSettingsOptions.CurrentValue;
        }

        private string MerchantServiceBaseUrl => $"{_urlSettingsOptions.MerchantServiceBaseUrlNS}/api/v1";

        public async Task UpdateMerchantsDetails()
        {
            var merchantsDetails = await _merchantService.GetMerchantsDetailsAsync();
            merchantsDetails = merchantsDetails.Where(x => string.IsNullOrEmpty(x.CityCr) || string.IsNullOrEmpty(x.AddressCr) ||
                    string.IsNullOrEmpty(x.MerchantName)).ToArray();

            var merchantIds = merchantsDetails.Select(x => x.MerchantId).ToArray();
            var checks = await _checksService.GetMerchantFullInfoChecks(merchantIds);

            if (checks?.Count > 0)
            {
                var patchDocument = new JsonPatchDocument<MerchantViewModel>();

                foreach (var check in checks)
                {
                    var merchantDetails = merchantsDetails.FirstOrDefault(mrcId => mrcId.MerchantId == check.MerchantId)!;

                    patchDocument.Replace(x => x.MerchantDetails.CityCr, check.Location.Name);
                    patchDocument.Replace(x => x.MerchantDetails.MerchantName, check.CrName);
                    patchDocument.Replace(x => x.MerchantDetails.AddressCr, check.Address.General.Address);

                    await PatchMerchantDetailsAsync(merchantDetails.MerchantId, patchDocument);
                    _logger.LogInformation($"Total amount of items updated are : '{checks.Count}'");
                }
            }
        }

        private async Task PatchMerchantDetailsAsync(Guid merchantId, JsonPatchDocument<MerchantViewModel> updateMerchantDetails)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/Merchant?merchantId={merchantId}";
            var body = new StringContent(JsonConvert.SerializeObject(updateMerchantDetails), Encoding.UTF8, "application/json");

            using (_logger.BeginScope("PatchMerchantAsync({@serviceUrl})", serviceUrl))
            {
                _logger.LogInformation($"Calling merchant API to patch merchant with id '{merchantId}'");

                var response = await _client.PatchAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogCritical("Error when calling merchant API to get default contact by merchant id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                }
            }
        }
    }
}