﻿using System;
using System.Collections.Generic;

namespace Common.Models.Checkout
{
    public class OrderSearchCriteria : BaseSearchCriteria
    {
        public string? Keyword { get; set; }
        public Guid? OrderId { get; set; }
        public List<string> SearchIn { get; set; } = new List<string>();
        public List<string> OrderStatus { get; set; } = new List<string>();
        public List<string> MerchantStatus { get; set; } = new List<string>();
        public List<string> AcquiringBanks { get; set; } = new List<string>();
        public string? TerminalTID { get; set; }
        public List<AmountInterval> AmountIntervals { get; set; } = new List<AmountInterval>();
        public DateInterval? DateInterval { get; set; }
        public Guid? MerchantId { get; set; }
        public List<Guid> StoreIds { get; set; } = new();
        public string? SalesId { get; set; }
        public List<string> ProjectName { get; set; } = new List<string>();
        public List<string> ProductCodes { get; set; } = new List<string>();
        public List<bool?> ProofOfDeliveries { get; set; } = new List<bool?> { };
        public List<string> CityCodes { get; set; } = new List<string> { };
        public bool IsSalesTeam { get; set; } = false;
        public List<string>? DesignationSalesId { get; set; }
        public string? MID { get; set; }

    }
}
