﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Common.Models.Gsdk;
using Common.Models.Merchant;
using Common.Services;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services;
using static Common.Constants;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class GsdkMerchantMappingServiceTests
    {
        private const string KeycloakToken = "Bearer ey56hk9089hjkh";
        private readonly string organizationId = Guid.NewGuid().ToString();
        private GsdkMerchant merchant = null!;

        private IMerchantService merchantService = null!;
        private IFederationService federationService = null!;
        private ILogger<GsdkMerchantMappingService> logger = null!;

        private IGsdkMerchantMappingService gsdkMerchantMappingService = null!;

        [SetUp]
        public void Setup()
        {
            merchant = new GsdkMerchant
            {
                MerchantId = Guid.NewGuid(),
                MemberId = "6712508675"
            };

            merchantService = Substitute.For<IMerchantService>();

            merchantService.SearchGsdkMerchants(Arg.Any<SearchGsdkMerchantsRequest>())
                .Returns(new List<GsdkMerchant> { merchant });

            merchantService.CreateMerchantExternalIdentifier(Arg.Any<MerchantExternalIdentifier>()).Returns(true);

            federationService = Substitute.For<IFederationService>();

            federationService.GetMerchantOrganizationIdByPhoneAsync(Arg.Any<Guid>(), Arg.Any<string>())
                .Returns(organizationId);

            federationService.GetMerchantOrganizationIdByEmailAsync(Arg.Any<Guid>(), Arg.Any<string>())
                .Returns(organizationId);

            logger = Substitute.For<ILogger<GsdkMerchantMappingService>>();

            gsdkMerchantMappingService = new GsdkMerchantMappingService(
                federationService,
                merchantService,
                logger);
        }

        [Test]
        public async Task MapExistingMerchantsByPhoneIsSuccessful()
        {
            merchant.Phone = "+3597867576";

            var notMappedMerchants = await gsdkMerchantMappingService.MapExistingMerchants();

            Assert.That(notMappedMerchants, Is.Empty, "Collection should be empty");
            
            await federationService.Received(1).GetMerchantOrganizationIdByPhoneAsync(merchant.MerchantId, RemovePhonePlusSign(merchant.Phone));
            await federationService.DidNotReceive().GetMerchantOrganizationIdByEmailAsync(Arg.Any<Guid>(), Arg.Any<string>());
            await merchantService.Received(1).CreateMerchantExternalIdentifier(Arg.Is<MerchantExternalIdentifier>(x =>
                x.MerchantId == merchant.MerchantId &&
                x.IdentifierKey == Gsdk.IdentifierKey &&
                x.IdentifierValue == organizationId &&
                x.ExternalSourceId == Gsdk.ExternalSourceId));
        }

        [Test]
        public async Task MapExistingMerchantsByEmailWhenPhoneIsNotAvailableIsSuccessful()
        {
            merchant.Email = "<EMAIL>";

            var notMappedMerchants = await gsdkMerchantMappingService.MapExistingMerchants();
            
            Assert.That(notMappedMerchants, Is.Empty, "Collection should be empty");

            await federationService.DidNotReceive().GetMerchantOrganizationIdByPhoneAsync(Arg.Any<Guid>(), Arg.Any<string>());
            await federationService.Received(1).GetMerchantOrganizationIdByEmailAsync(merchant.MerchantId, merchant.Email);
            await merchantService.Received(1).CreateMerchantExternalIdentifier(Arg.Is<MerchantExternalIdentifier>(x =>
                x.MerchantId == merchant.MerchantId &&
                x.IdentifierKey == Gsdk.IdentifierKey &&
                x.IdentifierValue == organizationId &&
                x.ExternalSourceId == Gsdk.ExternalSourceId));
        }

        [Test]
        public async Task MapExistingMerchantsByEmailWhenFailToRetrieveByPhoneIsSuccessful()
        {
            merchant.Phone = "+3597867576";
            merchant.Email = "<EMAIL>";

            federationService.GetMerchantOrganizationIdByPhoneAsync(
                    Arg.Any<Guid>(),
                    Arg.Any<string>())
                .Returns(string.Empty);

            var notMappedMerchants = await gsdkMerchantMappingService.MapExistingMerchants();

            Assert.That(notMappedMerchants, Is.Empty, "Collection should be empty");

            await federationService.Received(1).GetMerchantOrganizationIdByPhoneAsync(merchant.MerchantId, RemovePhonePlusSign(merchant.Phone));
            await federationService.Received(1).GetMerchantOrganizationIdByEmailAsync(merchant.MerchantId, merchant.Email);
            await merchantService.Received(1).CreateMerchantExternalIdentifier(Arg.Is<MerchantExternalIdentifier>(x =>
                x.MerchantId == merchant.MerchantId &&
                x.IdentifierKey == Gsdk.IdentifierKey &&
                x.IdentifierValue == organizationId &&
                x.ExternalSourceId == Gsdk.ExternalSourceId));
        }

        [Test]
        public async Task MapExistingMerchantsWhenFailToRetrieveByPhoneAndEmailIsNotAvailableIsNotSuccessful()
        {
            merchant.Phone = "+3597867576";

            federationService.GetMerchantOrganizationIdByPhoneAsync(
                    Arg.Any<Guid>(),
                    Arg.Any<string>())
                .Returns(string.Empty);

            var notMappedMerchants = await gsdkMerchantMappingService.MapExistingMerchants();
            
            AssertResponse(notMappedMerchants);
            await federationService.Received(1).GetMerchantOrganizationIdByPhoneAsync(merchant.MerchantId, RemovePhonePlusSign(merchant.Phone));
            await federationService.DidNotReceive().GetMerchantOrganizationIdByEmailAsync(Arg.Any<Guid>(), Arg.Any<string>());
            await merchantService.DidNotReceive().CreateMerchantExternalIdentifier(Arg.Any<MerchantExternalIdentifier>());
        }
        
        [Test]
        public async Task MapExistingMerchantsWhenFailToRetrieveByPhoneAndEmailIsNotSuccessful()
        {
            merchant.Phone = "+3597867576";
            merchant.Email = "<EMAIL>";

            federationService.GetMerchantOrganizationIdByPhoneAsync(
                    Arg.Any<Guid>(),
                    Arg.Any<string>())
                .Returns(string.Empty);

            federationService.GetMerchantOrganizationIdByEmailAsync(
                    Arg.Any<Guid>(),
                    Arg.Any<string>())
                .Returns(string.Empty);

            var notMappedMerchants = await gsdkMerchantMappingService.MapExistingMerchants();

            AssertResponse(notMappedMerchants);
            await federationService.Received(1).GetMerchantOrganizationIdByPhoneAsync(merchant.MerchantId, RemovePhonePlusSign(merchant.Phone));
            await federationService.Received(1).GetMerchantOrganizationIdByEmailAsync(merchant.MerchantId, merchant.Email);
            await merchantService.DidNotReceive().CreateMerchantExternalIdentifier(Arg.Any<MerchantExternalIdentifier>());
        }

        [Test]
        public async Task MapExistingMerchantsWhenFailToStoreOrganizationIdIsNotSuccessful()
        {
            merchant.Phone = "+3597867576";

            merchantService.CreateMerchantExternalIdentifier(Arg.Any<MerchantExternalIdentifier>()).Returns(false);

            var notMappedMerchants = await gsdkMerchantMappingService.MapExistingMerchants();

            AssertResponse(notMappedMerchants);
            await federationService.Received(1).GetMerchantOrganizationIdByPhoneAsync(merchant.MerchantId, RemovePhonePlusSign(merchant.Phone));
            await federationService.DidNotReceive().GetMerchantOrganizationIdByEmailAsync(Arg.Any<Guid>(), Arg.Any<string>());
            await merchantService.Received(1).CreateMerchantExternalIdentifier(Arg.Is<MerchantExternalIdentifier>(x =>
                x.MerchantId == merchant.MerchantId &&
                x.IdentifierKey == Gsdk.IdentifierKey &&
                x.IdentifierValue == organizationId &&
                x.ExternalSourceId == Gsdk.ExternalSourceId));
        }

        private static string RemovePhonePlusSign(string phone)
        {
            return phone.Replace("+", string.Empty);
        }

        private void AssertResponse(IReadOnlyCollection<GsdkMappingResponse> notMappedMerchants)
        {
            Assert.That(notMappedMerchants, Is.Not.Empty, "Collection should not be empty");

            var mappedMerchant = notMappedMerchants.First();
            Assert.That(mappedMerchant.MemberId, Is.EqualTo(merchant.MemberId), "Incorrect memberId");
            Assert.That(mappedMerchant.MerchantId, Is.EqualTo(merchant.MerchantId), "Incorrect merchantId");
        }
    }
}