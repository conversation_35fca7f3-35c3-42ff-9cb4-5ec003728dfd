﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Gsdk
{
    public class GlobalContractDto
    {
        public string Id { get; set; } = string.Empty;
        public string? ContractName { get; set; }
        public bool IsDefault { get; set; }
        public GlobalContractInProviderAccountCategoryDto? InProviderAccountCategory { get; set; }
        public List<GlobalContractRuleDto> ContractRules { get; set; } = new List<GlobalContractRuleDto>();
    }
}