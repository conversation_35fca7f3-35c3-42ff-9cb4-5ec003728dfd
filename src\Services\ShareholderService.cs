﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using Common;
using Common.Models.Shareholder;
using Common.Options;
using Common.Services;
using Common.Services.Validators;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Services;
public class ShareholderService : IShareholderService
{
    private readonly ILogger<ShareholderService> logger;
    private readonly HttpClient client;
    private readonly IOptionsMonitor<UrlSettings> urlSettingsOptions;
    private readonly IMapper mapper;
    private readonly IShareholderValidationService validationService;
    private readonly IOptionsMonitor<ShareholderSearchConfiguration> shareholderSearchConfiguration;
    private readonly IMerchantClient merchantClient;
    private readonly int ShareholderCompanySearchDefaultTake = 20;
    private readonly int ShareholderIndividualSearchDefaultTake = 20;
    private static readonly string shareholderCompanyPathName = nameof(MerchantShareholderCompanyResponse.ShareholderCompany);

    private string MerchantServiceShareholderBaseUrl => $"{urlSettingsOptions.CurrentValue.MerchantServiceBaseUrl}/api/v1/shareholder";

    public ShareholderService(ILogger<ShareholderService> logger,
        HttpClient client,
        IOptionsMonitor<UrlSettings> urlSettingsOptions,
        IOptionsMonitor<ShareholderSearchConfiguration> shareholderSearchConfiguration,
        IShareholderValidationService validationService,
        IMapper mapper,
        IMerchantClient merchantClient)
    {
        this.logger = logger;
        this.client = client;
        this.urlSettingsOptions = urlSettingsOptions;
        this.mapper = mapper;
        this.shareholderSearchConfiguration = shareholderSearchConfiguration;
        this.validationService = validationService;
        this.merchantClient = merchantClient;
    }

    public async Task<List<MerchantShareholderCompanyResponse>> GetShareholderCompaniesAsync(ShareholderCompaniesRequest shareholderCompaniesRequest)
    {
        if (shareholderCompaniesRequest.MerchantId != null && shareholderCompaniesRequest.MerchantId != Guid.Empty)
        {
            var result = await merchantClient.GetShareholderCompaniesBase(shareholderCompaniesRequest);

            return Json.Deserialize<List<MerchantShareholderCompanyResponse>>(result, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        logger.LogCritical("Error when trying to get Shareholder Companies." +
                           " Error was {error} .", Errors.InvalidGetShareholderCompaniesRequest.Message);
        throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGetShareholderCompaniesRequest);
    }

    public async Task<List<ShareholderCompanyResponse>> SearchShareholderCompaniesAsync(ShareholderCompaniesRequest shareholderCompaniesRequest)
    {
        if (!string.IsNullOrWhiteSpace(shareholderCompaniesRequest.Keyword))
        {
            shareholderCompaniesRequest.Take = shareholderSearchConfiguration.CurrentValue.ShareholderCompanySearchDefaultTake ??
                                               ShareholderCompanySearchDefaultTake;

            var result = await merchantClient.GetShareholderCompaniesBase(shareholderCompaniesRequest, true);

            return Json.Deserialize<List<ShareholderCompanyResponse>>(result, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        logger.LogCritical("Error when trying to get Shareholder Companies." +
                           " Error was {error} .", Errors.InvalidGetShareholderCompaniesRequest.Message);
        throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGetShareholderCompaniesRequest);
    }

    public async Task<List<MerchantIndividualCore>> SearchShareholderIndividualsAsync(
        ShareholderIndividualsSearchRequest searchRequest)
    {
        if (string.IsNullOrWhiteSpace(searchRequest.Keyword))
        {
            logger.LogCritical("Error when trying to search shareholder individuals." +
                               " Error was {error} .", Errors.InvalidGetShareholderIndividualsRequest.Message);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidGetShareholderIndividualsRequest);
        }

        searchRequest.Take = shareholderSearchConfiguration.CurrentValue.ShareholderIndividualSearchDefaultTake ??
                             ShareholderIndividualSearchDefaultTake;

        return await RetrieveShareholderIndividualsAsync(searchRequest);
    }

    public async Task<ShareholderCompanyResponse> CreateShareholderCompanyAsync(ShareholderCompanyCreateRequest request)
    {
        await ValidateShareholderCompanyCreateRequestAsync(request);

        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/shareholdercompanies/create";

        using (logger.BeginScope("CreateShareholderCompanyAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to create Shareholder Company");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

            var response = await client.PostAsync(serviceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
                return Json.Deserialize<ShareholderCompanyResponse>(responseBody);

            logger.LogCritical("Error when calling merchant API to Create Shareholder Company for merchant." +
                               " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
            throw new PassthroughException(response);
        }
    }

    public async Task<MerchantShareholderCompanyResponse> PatchShareholderCompanyAsync(ShareholderCompanyPatchRequest request)
    {
        await ValidateShareholderCompanyPatchRequestAsync(request);

        string uri = $"{MerchantServiceShareholderBaseUrl}/shareholdercompanies";
        var reqBody = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

        using (logger.BeginScope("PatchShareholderCompanyAsync({@url})", uri))
        {
            logger.LogInformation("Calling merchant service to update merchant shareholder company for Ids '{shareholderCompanyId}' and  '{merchantId}'.",
                request.ShareholderCompanyId, request.MerchantId);

            var resp = await client.PatchAsync(uri, reqBody);
            var responseBody = await resp.Content.ReadAsStringAsync();

            if (!resp.IsSuccessStatusCode) 
            {
                logger.LogCritical("Error when calling merchant service to update shareholder company for Ids '{shareholderCompanyId}' and  '{merchantId}'." +
                    "Error was {StatusCode} {@responseBody}",
                    request.ShareholderCompanyId, request.MerchantId, (int)resp.StatusCode, responseBody);

                throw new PassthroughException(resp);
            }
            logger.LogInformation("Updated shareholder company for ids  Ids '{shareholderCompanyId}' and  '{merchantId}'.", request.ShareholderCompanyId, request.MerchantId);

            return Json.Deserialize<MerchantShareholderCompanyResponse>(responseBody);
        }
    }

    public async Task<List<MerchantShareholderIndividual>> GetMerchantIndividualsAsync(Guid merchantId)
    {
        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individuals/{merchantId}";

        using (logger.BeginScope("GetMerchantIndividuals({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to get all shareholder individuals for merchant with id {merchantId}", merchantId);

            var response = await client.GetAsync(serviceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to get Shareholder Individuals by merchant id." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            var merchantIndividualsCore = Json.Deserialize<MerchantShareholderIndividualsCore>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return Map(merchantIndividualsCore);
        }
    }

    public async Task CreateShareholderCompanyMerchantAssociationAsync(ShareholderCompanyMerchantAssociationRequest request)
    {
        ValidateShareholderCompanyMerchantAssociationRequest(request);

        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/shareholdercompanies/create-association";

        using (logger.BeginScope("CreateShareholderCompanyMerchantAssociationAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to create Shareholder Company to Merchant association.");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

            var response = await client.PostAsync(serviceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant API to CreateShareholderCompanyMerchantAssociationAsync." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }
        }
    }

    public async Task CreateShareholderIndividualAsync(ShareholderIndividualCreateRequest request)
    {
        await validationService.ValidateCreateIndividualRequest(request);
        PopulateNationalIdIfNeeded(request);
        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individual";

        using (logger.BeginScope("CreateShareholderIndividualAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to create Shareholder individual.");

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

            var response = await client.PostAsync(serviceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant API to CreateShareholderIndividualAsync." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }
        }
    }

    public async Task CreateShareholderIndividualAssociations(ShareholderIndividualAssociationsCreateRequest request)
    {
        await validationService.ValidateShareholderIndividualAssociationsCreateRequest(request);
        await merchantClient.CreateShareholderIndividualAssociations(request);
    }

    public async Task PatchShareholderIndividualAsync(ShareholderIndividualPatchRequest request)
    {
        validationService.ValidateIndividualsPatchTemp(request);

        var uri = $"{MerchantServiceShareholderBaseUrl}/individual/patch";
        var reqBody = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

        using (logger.BeginScope("PatchShareholderIndividualAsync({@url})", uri))
        {
            logger.LogInformation("Calling merchant service to update merchant shareholder individual for person of interest ID '{poiId}' and merchant ID  '{merchantId}'.",
                request.PersonOfInterestId, request.MerchantId);

            var resp = await client.PatchAsync(uri, reqBody);
            var responseBody = await resp.Content.ReadAsStringAsync();

            if (!resp.IsSuccessStatusCode)
            {
                logger.LogCritical(
                    "Error when calling merchant service to update shareholder individual for person of interest ID '{poiId}' and merchant ID  '{merchantId}'." +
                    "Error was {StatusCode} {@responseBody}",
                    request.PersonOfInterestId, request.MerchantId, (int) resp.StatusCode, responseBody);

                throw new PassthroughException(resp);
            }
            logger.LogInformation("Updated shareholder company for ids  Ids '{shareholderCompanyId}' and  '{merchantId}'.", request.PersonOfInterestId, request.MerchantId);
        }
    }

    public async Task<ShareholderIndividualEditableStatus> GetShareholderIndividualEditableStatusAsync(Guid shareholderIndividualId)
    {
        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individual/{shareholderIndividualId}/isEditable";

        using (logger.BeginScope("GetShareholderIndividualEditableStatusAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to get the editable status of the individual with ID {shareholderIndividualId}", shareholderIndividualId);

            var response = await client.GetAsync(serviceUrl);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant service to get the editable status of the individual." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<ShareholderIndividualEditableStatus>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
    }

    private async Task<List<MerchantIndividualCore>> RetrieveShareholderIndividualsAsync(ShareholderIndividualsSearchRequest shareholderIndividualsSearchRequest)
    {
        var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individuals/search";

        using (logger.BeginScope("GetShareholderIndividualsAsync({@serviceUrl})", serviceUrl))
        {
            logger.LogInformation("Calling merchant service to get Shareholder Companies");

            var body = new StringContent(JsonConvert.SerializeObject(shareholderIndividualsSearchRequest), Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync(serviceUrl, body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling merchant API to search Shareholder individuals." +
                                   " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                throw new PassthroughException(response);
            }

            return Json.Deserialize<List<MerchantIndividualCore>>(responseBody, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
    }

    private async Task ValidateShareholderCompanyCreateRequestAsync(ShareholderCompanyCreateRequest request)
    {
        await CheckShareHolderCompanyUnicityAsync(request.CompanyLicense);

        await validationService.ValidateCreateCompanyRequest(request);
    }

    private async Task CheckShareHolderCompanyUnicityAsync(string companyLicense)
    {
        var existingCompanies = await SearchShareholderCompaniesAsync(new ShareholderCompaniesRequest()
        {
            Keyword = companyLicense
        });

        if (existingCompanies.Any(x => x.CompanyLicense.Equals(companyLicense, StringComparison.InvariantCultureIgnoreCase)))
        {
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.AlreadyExistingShareholderCompany);
        }
    }

    private async Task ValidateShareholderCompanyPatchRequestAsync(ShareholderCompanyPatchRequest request)
    {
        var companyLicenseOperation = FindOperation(request.JsonPatchDocument, GetShareHolderCompanyFullPropertyName(nameof(ShareholderCompanyResponse.CompanyLicense)));
        if (companyLicenseOperation is not null)
        {
            await CheckShareHolderCompanyUnicityAsync((string)companyLicenseOperation.value);
        }

        await validationService.ValidateEditCompanyRequest(request);
    }

    private static Operation<MerchantShareholderCompanyResponse>? FindOperation(JsonPatchDocument<MerchantShareholderCompanyResponse> o, string pathName)
     => o.Operations.Find(x => x.path.Equals(pathName, StringComparison.InvariantCultureIgnoreCase));

    private static string GetShareHolderCompanyFullPropertyName(string propName) =>
        string.Format("/{0}/{1}", shareholderCompanyPathName, propName);

    private List<MerchantShareholderIndividual> Map(MerchantShareholderIndividualsCore merchantShareholderIndividualsCore)
    {
        var allIndividuals = new List<MerchantShareholderIndividual>();

        allIndividuals.AddRange(merchantShareholderIndividualsCore.MerchantIndividuals
            .Select(MapDirectIndividual));
        allIndividuals.AddRange(merchantShareholderIndividualsCore.ShareholderCompanyIndividuals
            .Select(MapCompanyIndividual));

        var mergedIndividuals = allIndividuals
            .GroupBy(i => i.PersonOfInterestId)
            .Select(group =>
            {
                if (group.Count() == 1)
                {
                    return group.First();
                }
                var mergedRelations = group.SelectMany(x => x.Relations);
                var result = group.First();
                result.Relations = mergedRelations.Where(x => x.OrganizationRole != Constants.ShareholderIndividualsRelationTypes.Member).ToList();
                return result;
            })
            .ToList();

        return mergedIndividuals;
    }

    private MerchantShareholderIndividual MapCompanyIndividual(ShareholderCompanyIndividualCore individual)
    {
        var companyIndividual = mapper.Map<MerchantShareholderIndividual>(individual);
        companyIndividual.Relations.Add(new IndividualRelation
        {
            Type = Constants.ShareholderIndividualsRelationTypes.Company,
            OrganizationRole = individual.OrganizationRole,
            ShareholderCompanyId = individual.ShareholderCompanyId,
            ShareholderCompanyName = individual.ShareholderCompanyName
        });
        return companyIndividual;
    }

    private MerchantShareholderIndividual MapDirectIndividual(MerchantIndividualCore individual)
    {
        var merchantIndividual = mapper.Map<MerchantShareholderIndividual>(individual);   
        merchantIndividual.Relations.Add(new IndividualRelation
        {
            Type = MapMerchantIndividualRelation(individual),
            MerchantId = individual.MerchantId,
            IsPrincipal = individual.IsPrincipal,
            OrganizationRole = individual.OrganizationRole,
            OwnershipPercentage = individual.OwnershipPercentage,
            WathqRelationId = individual.WathqRelationId
        });
        return merchantIndividual;
    }

    private static string MapMerchantIndividualRelation(MerchantIndividualCore merchantIndividual)
    {
        if (merchantIndividual.WathqRelationId != null && !merchantIndividual.IsPrincipal)
            return Constants.ShareholderIndividualsRelationTypes.WathqAuthorizedSignatory;

        return merchantIndividual.OrganizationRole == Constants.ShareholderIndividualsRelationTypes.Owner ? 
            Constants.ShareholderIndividualsRelationTypes.DirectShareholder : merchantIndividual.OrganizationRole;
    }

    private void ValidateShareholderCompanyMerchantAssociationRequest(ShareholderCompanyMerchantAssociationRequest request)
    {
        if (request.MerchantId == Guid.Empty || request.ShareholderCompanyId == Guid.Empty)
        {
            logger.LogCritical("Error when trying to Create Shareholder Companies to Merchant Association." +
                               " Error was {error} .", Errors.InvalidCreateShareholderCompanyMerchantAssociationRequest.Message);
            throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidCreateShareholderCompanyMerchantAssociationRequest);
        }
    }

    private static void PopulateNationalIdIfNeeded(ShareholderIndividualCreateRequest request)
    {
        if (string.IsNullOrEmpty(request.NationalId))
        {
            request.NationalId = request.PassportNo;
            request.IdExpiryDate = request.PassportExpirationDate;
        }
    }
}