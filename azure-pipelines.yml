trigger:
- dev
- test
- release/*
- uae-dev
- uae-test

pool:
  name: 'GD-Azure' 
  #demands:
  #  - agent.name -equals devWeuAZDNodeVM1

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'
  build.MajorVersion: 1
  build.MinorVersion: 1

name: $(build.MajorVersion).$(build.MinorVersion).$(rev:r)

resources:
  repositories:
    - repository: devops
      type: git
      name: GeideaPaymentGateway/DevOps.Scripts

steps:
- checkout: self
  persistCredentials: true

- template: config-service.yml@devops

- task: NuGetAuthenticate@0
- task: NuGetToolInstaller@1

- task: Bash@3
  displayName: 'Check if the the source branch is allowed for the PR'
  inputs:
   targetType: 'inline'
   script: |
     echo
     echo 'The only and only source branch allowed for merging into UAE-TEST is the UAE-DEV branch!!!'
     echo
     exit 1
  condition: and(ne(variables['System.PullRequest.SourceBranch'], 'refs/heads/uae-dev'), contains(variables['System.PullRequest.TargetBranch'], 'refs/heads/uae-test'))

- task: SonarQubePrepare@5
  enabled: false
  inputs:
    SonarQube: 'Geidea SonarQube instance'
    scannerMode: 'MSBuild'
    projectKey: 'BackofficeAPI'
    projectVersion: '$(build.MajorVersion).$(build.MinorVersion)'
    extraProperties: |
      # Additional properties that will be passed to the scanner, 
      # Put one key=value per line, example:
      sonar.cs.opencover.reportsPaths=$(Build.SourcesDirectory)\src\*\coverage.opencover.xml

- task: UseDotNet@2
  displayName: 'Use .Net SDK 6.0.x'
  inputs:
    packageType: 'sdk'
    version: 6.0.x
    installationPath: $(Agent.TempDirectory)/dotnet

- task: DotNetCoreCLI@2
  displayName: Restore
  inputs:
    command: 'restore'
    projects: '**/*.sln'
    feedsToUse: 'config'
    nugetConfigPath: '$(Build.SourcesDirectory)/nuget.config'
    noCache: true

- task: DotNetCoreCLI@2
  displayName: Build
  inputs:
    command: build
    projects: |
        **/*.sln
    arguments: --configuration $(BuildConfiguration) --no-restore -p:Version=$(Build.BuildNumber)

- task: DotNetCoreCLI@2
  displayName: Run unit tests
  inputs:
    command: test
    projects: |
        **/*.Test*.csproj
    arguments: --configuration $(BuildConfiguration) --no-build /p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:IncludeTestAssembly=false
  
- task: SonarQubeAnalyze@5
  displayName: Run Sonar Analysis
  enabled: false
  
- task: SonarQubePublish@5
  displayName: Publish Sonar Results
  inputs:
    pollingTimeoutSec: '300'  
  enabled: false


- task: DotNetCoreCLI@2
  inputs:
    command: 'publish'
    publishWebProjects: true
    arguments: '--configuration Release -p:Version=$(Build.BuildNumber) --output "$(build.artifactstagingdirectory)" --no-cache --runtime=linux-musl-x64 --source "https://pkgs.dev.azure.com/GeideaPaymentGateway/_packaging/f50ad5dc-f078-4d5c-a6d3-d384c0414d73/nuget/v3/index.json"'
    zipAfterPublish: false

- task: DotNetCoreCLI@2
  displayName: 'Publish OrderExportOptimization '
  inputs:
    command: 'publish'
    projects: '**/OrderExportOptimization/OrderExportOptimization.csproj'
    publishWebProjects: false
    arguments: '--configuration Release --output "$(build.artifactstagingdirectory)/BackofficeApi" --no-cache --runtime=linux-musl-x64 --source "https://pkgs.dev.azure.com/GeideaPaymentGateway/_packaging/f50ad5dc-f078-4d5c-a6d3-d384c0414d73/nuget/v3/index.json" /p:PublishSingleFile=true'
    zipAfterPublish: false

- task: DotNetCoreCLI@2
  displayName: 'Publish LeadUpdate '
  inputs:
    command: 'publish'
    projects: '**/LeadUpdate/LeadUpdate.csproj'
    publishWebProjects: false
    arguments: '--configuration Release --output "$(build.artifactstagingdirectory)/BackofficeApi" --no-cache --runtime=linux-musl-x64 --source "https://pkgs.dev.azure.com/GeideaPaymentGateway/_packaging/f50ad5dc-f078-4d5c-a6d3-d384c0414d73/nuget/v3/index.json" /p:PublishSingleFile=true'
    zipAfterPublish: false

- task: Docker@2
  displayName: Build
  inputs:
    containerRegistry: geidea
    command: build
    buildContext: '$(build.artifactstagingdirectory)/BackofficeApi'
    repository: backoffice-service
    tags: |
     $(Build.SourceBranchName)-$(Build.BuildNumber)

- task: Bash@3
  displayName: "Scan the docker image with trivy"
  inputs:
    targetType: 'inline'
    script: |
      trivy image --exit-code 0 --severity LOW,MEDIUM geidea.azurecr.io/backoffice-service:$(Build.SourceBranchName)-$(Build.BuildNumber)
      trivy image --exit-code 1 --severity HIGH,CRITICAL geidea.azurecr.io/backoffice-service:$(Build.SourceBranchName)-$(Build.BuildNumber)

- task: Docker@2
  displayName: Push
  inputs:
    containerRegistry: geidea
    command: push
    repository: backoffice-service
    tags: |
     $(Build.SourceBranchName)-$(Build.BuildNumber)
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))

- task: GitTag@5
  inputs:
    workingdir: '$(SYSTEM.DEFAULTWORKINGDIRECTORY)'
    tag: '$(Build.BuildNumber)'
    tagMessage: '$(Build.BuildNumber)'
  condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))