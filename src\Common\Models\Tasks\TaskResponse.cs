﻿using System;

namespace Common.Models.Tasks
{
    public class TaskResponse
    {
        public Guid TaskId { get; set; }
        public int TaskNumber { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? BusinessName { get; set; }
        public Guid MerchantId { get; set; }
        public Guid? AssigneeUserId { get; set; }
        public string TaskType { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public string TaskStatus { get; set; } = string.Empty;
        public string? Assignee { get; set; }
        public string? Resolution { get; set; }
        public int TaskCommentsCount { get; set; }
    }
}
