﻿using System;
using Common.Models.Checkout;
using FluentValidation;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace Common.Validators
{
    public class OrderUpdateRequestValidator : AbstractValidator<JsonPatchDocument<OrderUpdateRequest>>
    {
        public OrderUpdateRequestValidator()
        {
            RuleFor(x => FindOperation(x, "PaymentReference"))
                .Must(o => o.value.ToString()?.Length <= 64)
                .When(o => FindOperation(o, "PaymentReference")?.value != null)
                .WithErrorCode(Errors.PaymentReferenceLengthValidation.Code)
                .WithMessage(Errors.PaymentReferenceLengthValidation.Message);

            RuleFor(x => FindOperation(x, "TrackingNumber"))
                .Must(o => o.value.ToString()?.Length <= 32)
                .When(o => FindOperation(o, "TrackingNumber")?.value != null)
                .WithErrorCode(Errors.TrackingNumberLengthValidation.Code)
                .WithMessage(Errors.TrackingNumberLengthValidation.Message);

            RuleFor(x => FindOperation(x, "TrackingUrl"))
                .Must(o => o.value.ToString()?.Length <= 256)
                .When(o => FindOperation(o, "TrackingUrl")?.value != null)
                .WithErrorCode(Errors.TrackingUrlLengthValidation.Code)
                .WithMessage(Errors.TrackingUrlLengthValidation.Message);

            RuleFor(x => FindOperation(x, "Shipper"))
                .Must(o => o.value.ToString()?.Length <= 64)
                .When(o => FindOperation(o, "Shipper")?.value != null)
                .WithErrorCode(Errors.ShipperLengthValidation.Code)
                .WithMessage(Errors.ShipperLengthValidation.Message);

            RuleFor(x => FindOperation(x, "CouponCode"))
                .Must(o => o.value.ToString()?.Length <= 32)
                .When(o => FindOperation(o, "CouponCode")?.value != null)
                .WithErrorCode(Errors.CouponCodeLengthValidation.Code)
                .WithMessage(Errors.CouponCodeLengthValidation.Message);

            RuleFor(x => FindOperation(x, "PaymentMethod"))
                .Must(o => o.value.ToString()?.Length <= 32)
                .When(o => FindOperation(o, "PaymentMethod")?.value != null)
                .WithErrorCode(Errors.PaymentMethodLengthValidation.Code)
                .WithMessage(Errors.PaymentMethodLengthValidation.Message);

            RuleFor(x => FindOperation(x, "CompanyRegNo"))
                .Must(o => o.value.ToString()?.Length <= 32)
                .When(o => FindOperation(o, "CompanyRegNo")?.value != null)
                .WithErrorCode(Errors.CompanyRegNoLengthValidation.Code)
                .WithMessage(Errors.CompanyRegNoLengthValidation.Message);

            RuleFor(x => FindOperation(x, "SalesName"))
                .Must(o => o.value.ToString()?.Length <= 64)
                .When(o => FindOperation(o, "SalesName")?.value != null)
                .WithErrorCode(Errors.SalesNameLengthValidation.Code)
                .WithMessage(Errors.SalesNameLengthValidation.Message);

            RuleFor(x => FindOperation(x, "SubscriptionPlan"))
                .Must(o => o.value.ToString()?.Length <= 32)
                .When(o => FindOperation(o, "SubscriptionPlan")?.value != null)
                .WithErrorCode(Errors.SubscriptionPlanLengthValidation.Code)
                .WithMessage(Errors.SubscriptionPlanLengthValidation.Message);

            RuleFor(x => FindOperation(x, "ProjectName"))
                .Must(o => o.value.ToString()?.Length <= 32)
                .When(o => FindOperation(o, "ProjectName")?.value != null)
                .WithErrorCode(Errors.ProjectNameLengthValidation.Code)
                .WithMessage(Errors.ProjectNameLengthValidation.Message);

            RuleFor(x => FindOperation(x, "Note"))
                .Must(o => o.value.ToString()?.Length <= 512)
                .When(o => FindOperation(o, "Note")?.value != null)
                .WithErrorCode(Errors.NoteLengthValidation.Code)
                .WithMessage(Errors.NoteLengthValidation.Message);

            RuleFor(x => FindOperation(x, "Currency"))
                .Must(o => o.value.ToString()?.Length <= 3)
                .When(o => FindOperation(o, "Currency")?.value != null)
                .WithErrorCode(Errors.CurrencyLengthValidation.Code)
                .WithMessage(Errors.CurrencyLengthValidation.Message);

            RuleFor(x => FindOperation(x, "OrderStatus"))
                .Must(o => o.value.ToString()?.Length <= 32)
                .When(o => FindOperation(o, "OrderStatus")?.value != null)
                .WithErrorCode(Errors.OrderStatusLengthValidation.Code)
                .WithMessage(Errors.OrderStatusLengthValidation.Message);

            RuleFor(x => FindOperation(x, "MerchantName"))
                .Must(o => o.value.ToString()?.Length <= 64)
                .When(o => FindOperation(o, "MerchantName")?.value != null)
                .WithErrorCode(Errors.MerchantNameLengthValidation.Code)
                .WithMessage(Errors.MerchantNameLengthValidation.Message);
        }

        private Operation<OrderUpdateRequest> FindOperation(JsonPatchDocument<OrderUpdateRequest> o, string pathName)
        {
            return o.Operations.Find(x => x.path.Contains(pathName, StringComparison.InvariantCultureIgnoreCase))!;
        }
    }
}