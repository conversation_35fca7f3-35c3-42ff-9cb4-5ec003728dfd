﻿using Common.Models.Merchant;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BackofficeApi.Formatters
{
    public class MerchantResponseCsvFormatter : TextOutputFormatter
    {
        public MerchantResponseCsvFormatter()
        {
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/csv"));
            SupportedEncodings.Add(Encoding.UTF8);
        }

        protected override bool CanWriteType(Type? type)
        {
            return typeof(MerchantExportResponse).IsAssignableFrom(type)
                || typeof(IEnumerable<MerchantExportResponse>).IsAssignableFrom(type);
        }

        public override async Task WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
        {
            if (context?.Object == null) return;

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                InjectionOptions = InjectionOptions.Escape
            };

            using var streamWriter = context.WriterFactory(context.HttpContext.Response.Body, selectedEncoding);
            using var csv = new CsvWriter(streamWriter, config);

            // Write CSV headers
            WriteHeaders(csv);
            await csv.NextRecordAsync();

            // Async handling WriteResponseBodyAsync
            if (context.Object is IEnumerable<MerchantExportResponse> merchants)
            {
                foreach (var merchant in merchants)
                {
                    await WriteMerchant(csv, merchant);
                }
            }
            else if (context.Object is MerchantExportResponse singleMerchant)
            {
                await WriteMerchant(csv, singleMerchant);
            }

            await streamWriter.FlushAsync();
        }

        private void WriteHeaders(CsvWriter csv)
        {
            csv.WriteHeader(typeof(MerchantExport));
            csv.WriteHeader(typeof(MerchantAddress));
            csv.WriteHeader(typeof(MerchantContact));
            csv.WriteHeader(typeof(MerchantExportStore));
            csv.WriteHeader(typeof(MerchantExternalIdentifier));
            csv.WriteHeader(typeof(MerchantExportPersonOfInterest));
            csv.WriteHeader(typeof(MerchantBankAccount));
        }

        private async Task WriteMerchant(CsvWriter csv, MerchantExportResponse merchant)
        {
            if (merchant?.Merchant == null) return;

            csv.WriteRecord(merchant.Merchant);
            await csv.NextRecordAsync();

            if (merchant.Merchant.Addresses != null)
            {
                foreach (var address in merchant.Merchant.Addresses)
                {
                    AddPadding(PaddingPoint.Addresses, csv);
                    csv.WriteRecord(address);
                    await csv.NextRecordAsync();
                }
            }

            if (merchant.Merchant.Contacts != null)
            {
                foreach (var contact in merchant.Merchant.Contacts)
                {
                    AddPadding(PaddingPoint.Contacts, csv);
                    csv.WriteRecord(contact);
                    await csv.NextRecordAsync();
                }
            }

            if (merchant.Merchant.MerchantStores != null)
            {
                foreach (var store in merchant.Merchant.MerchantStores)
                {
                    AddPadding(PaddingPoint.Stores, csv);
                    csv.WriteRecord(store);
                    await csv.NextRecordAsync();

                    if (store.StoreAddresses != null)
                    {
                        foreach (var address in store.StoreAddresses)
                        {
                            AddPadding(PaddingPoint.StoreAddresses, csv);
                            csv.WriteRecord(address);
                            await csv.NextRecordAsync();
                        }
                    }
                }
            }

            if (merchant.Merchant.ExternalIdentifiers != null)
            {
                foreach (var identifier in merchant.Merchant.ExternalIdentifiers)
                {
                    AddPadding(PaddingPoint.ExternalIdentifiers, csv);
                    csv.WriteRecord(identifier);
                    await csv.NextRecordAsync();
                }
            }

            if (merchant.Merchant.PersonsOfInterest != null)
            {
                foreach (var poi in merchant.Merchant.PersonsOfInterest)
                {
                    AddPadding(PaddingPoint.PersonsOfInterest, csv);
                    csv.WriteRecord(poi);
                    await csv.NextRecordAsync();

                    if (poi.PersonsOfInterestContacts != null)
                    {
                        foreach (var contact in poi.PersonsOfInterestContacts)
                        {
                            AddPadding(PaddingPoint.POIContact, csv);
                            csv.WriteRecord(contact);
                            await csv.NextRecordAsync();
                        }
                    }
                }
            }

            if (merchant.Merchant.BankAccounts != null)
            {
                foreach (var account in merchant.Merchant.BankAccounts)
                {
                    AddPadding(PaddingPoint.BankAccounts, csv);
                    csv.WriteRecord(account);
                    await csv.NextRecordAsync();
                }
            }
        }

        private void AddPadding(PaddingPoint paddingPoint, CsvWriter csv)
        {
            // Default object instances to avoid null pointer dereferences
            var defaultExport = new MerchantExport();
            var defaultAddress = new MerchantAddress();
            var defaultContact = new MerchantContact();
            var defaultStore = new MerchantExportStore();
            var defaultIdentifier = new MerchantExternalIdentifier();
            var defaultPersonOfInterest = new MerchantExportPersonOfInterest();
            var defaultBankAccount = new MerchantBankAccount();

            switch (paddingPoint)
            {
                case PaddingPoint.Addresses:
                    csv.WriteRecord(defaultExport);
                    break;
                case PaddingPoint.Contacts:
                    csv.WriteRecord(defaultExport);
                    csv.WriteRecord(defaultAddress);
                    break;
                case PaddingPoint.Stores:
                    csv.WriteRecord(defaultExport);
                    csv.WriteRecord(defaultAddress);
                    csv.WriteRecord(defaultContact);
                    break;
                case PaddingPoint.StoreAddresses:
                    csv.WriteRecord(defaultExport);
                    csv.WriteRecord(defaultAddress);
                    csv.WriteRecord(defaultContact);
                    csv.WriteRecord(defaultStore);
                    break;
                case PaddingPoint.ExternalIdentifiers:
                    csv.WriteRecord(defaultExport);
                    csv.WriteRecord(defaultAddress);
                    csv.WriteRecord(defaultContact);
                    csv.WriteRecord(defaultStore);
                    csv.WriteRecord(defaultIdentifier);
                    break;
                case PaddingPoint.PersonsOfInterest:
                    csv.WriteRecord(defaultExport);
                    csv.WriteRecord(defaultAddress);
                    csv.WriteRecord(defaultContact);
                    csv.WriteRecord(defaultStore);
                    csv.WriteRecord(defaultIdentifier);
                    csv.WriteRecord(defaultPersonOfInterest);
                    break;
                case PaddingPoint.POIContact:
                    csv.WriteRecord(defaultExport);
                    csv.WriteRecord(defaultAddress);
                    csv.WriteRecord(defaultContact);
                    csv.WriteRecord(defaultStore);
                    csv.WriteRecord(defaultIdentifier);
                    csv.WriteRecord(defaultPersonOfInterest);
                    csv.WriteRecord(defaultContact);
                    break;
                case PaddingPoint.BankAccounts:
                    csv.WriteRecord(defaultExport);
                    csv.WriteRecord(defaultAddress);
                    csv.WriteRecord(defaultContact);
                    csv.WriteRecord(defaultStore);
                    csv.WriteRecord(defaultIdentifier);
                    csv.WriteRecord(defaultPersonOfInterest);
                    csv.WriteRecord(defaultBankAccount);
                    break;
            }
        }
    }

    public enum PaddingPoint
    {
        Addresses,
        Contacts,
        Stores,
        StoreAddresses,
        ExternalIdentifiers,
        PersonsOfInterest,
        POIContact,
        BankAccounts
    }
}