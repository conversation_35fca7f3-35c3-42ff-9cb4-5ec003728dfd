﻿using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Product;
using Common.Models.Search;
using Common.Services;
using Geidea.Utils.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Services.OrderExport
{
    public class OrderExportService : IOrderExportService
    {
        private readonly IReferenceService referenceService;
        private readonly ISearchService searchService;

        private readonly List<string> supportedTypes = new List<string>()
        {
            "WSB",
            "WSB_ADDON",
            "ACCESSORIES",
            "ECR_ADDON",
            "GWAY",
            "GWAY_ADDON",
            "MINI_ECR",
            "SERVICES"
        };

        public OrderExportService(IReferenceService referenceService, ISearchService searchService)
        {
            this.referenceService = referenceService;
            this.searchService = searchService;
        }

        public async Task<OrderExportResponse> ExportOrderByIdAsync(Guid orderId) 
        {
            var orderSearchCriteria = new OrderSearchCriteria() { OrderId = orderId };
            var result = (await ExportOrdersAsync(orderSearchCriteria)).FirstOrDefault();

            if (result == null)
            {
                throw new ServiceException(HttpStatusCode.NotFound);
            }

            return result;
        }

        public async Task<List<OrderExportResponse>> ExportOrdersAsync(OrderSearchCriteria orderSearchCriteria)
        {
            var orders = await searchService.ExportOrdersAsync(orderSearchCriteria);
            var catalogues = await referenceService.GetCataloguesAsync(new string[] { Constants.Catalogues.CommercialRegistration, Constants.Catalogues.Cities, 
                                                                                  Constants.Catalogues.Areas, Constants.Catalogues.AccountNumber,
                                                                                  Constants.Catalogues.AcquiringLedger, Constants.Catalogues.LedgerToAccount, 
                                                                                  Constants.Catalogues.OrderStatus, Constants.Catalogues.MerchantStatus, 
                                                                                  Constants.Catalogues.ReferralChannel, Constants.Catalogues.ProjectName,
                                                                                  Constants.Catalogues.CompanyCheckStatus, Constants.Catalogues.TransactionType,
                                                                                  Constants.Catalogues.AcceptedPaymentMethods});
            var ordersExport = MapOrderExport(orders, catalogues);

            return ordersExport.ToList();
        }

        private List<OrderExportResponse> MapOrderExport(List<OrdersExport> orders, Catalogue[] catalogues)
        {
            var ordersExports = new List<OrderExportResponse>();

            foreach(var order in orders)
            {
                OrderExportResponse orderExport = new OrderExportResponse();

                SetOrderExport(order, orderExport, catalogues);
                SetValuesForCatalogues(order, orderExport, catalogues);

                if (order.OrderItem != null)
                {
                    SetProductExport(order, orderExport, catalogues);
                }
                ordersExports.Add(orderExport);
            }
            return ordersExports; 
        }

        private static void SetOrderExport(OrdersExport order, OrderExportResponse orderExport,Catalogue[] catalogues)
        {
            var firstBankAccount = order.BankAccounts!.FirstOrDefault();

            orderExport.UserId = order.UserId;
            orderExport.BusinessType = order.BusinessType;
            orderExport.Phone = order.PhoneNumber;
            orderExport.OrderId = order.OrderId;
            orderExport.MerchantId = order.MerchantId;
            orderExport.StoreId = order.StoreId;
            orderExport.AddressCR = order.AddressCr;
            orderExport.CheckoutDate = order.CheckoutDate;
            orderExport.CityCR = order.CityCr;
            orderExport.Email = order.Email;
            orderExport.AccountHolderName = firstBankAccount != null ? firstBankAccount.AccountHolderName : string.Empty;
            orderExport.FirstName = order.FirstName;
            orderExport.FirstNameAr = order.FirstNameAr;
            orderExport.IBAN = firstBankAccount != null ? firstBankAccount.Iban : string.Empty;
            orderExport.LastName = order.LastName;
            orderExport.LastNameAr = order.LastNameAr;
            orderExport.LeadCreationDate = order.LeadCreationDate;
            orderExport.MCC = order.MCC;
            orderExport.MerchantName = order.MerchantName;
            orderExport.MerchantStatus = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.MerchantStatus
                                            && x.Key == order.MerchantStatus) !=null ? catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.MerchantStatus
                                           && x.Key == order.MerchantStatus)!.Value : order.MerchantStatus;
            orderExport.NationalId = order.NationalId;
            orderExport.NationalName = string.Empty;
            orderExport.OrderNumber = order.OrderNumber;
            orderExport.OrderStatus = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.OrderStatus
                                            && x.Key == order.OrderStatus) != null ? catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.OrderStatus
                                            && x.Key == order.OrderStatus)!.Value : order.OrderStatus;
            orderExport.ProjectName = order.ProjectName;
            orderExport.ReferralChannel = order.ReferralChannel;
            orderExport.BankCheckStatus = firstBankAccount != null && firstBankAccount.BankCheckStatus != null ? firstBankAccount.BankCheckStatus : Constants.Checks.BankCheckEmpty;

            SetCommercialRegistrationAndUnifiedID(order, orderExport);
        }

        private static void SetCommercialRegistrationAndUnifiedID(OrdersExport order, OrderExportResponse orderExport)
        {
            switch (order.BusinessType!)
            {
                case Constants.BusinessType.Limited:
                    orderExport.CommercialRegistration = order.CrNumber.ToString();
                    orderExport.UnifiedId = order.UnifiedId;
                    break;
                case Constants.BusinessType.MunicipalEntity:
                    orderExport.CommercialRegistration = order.MunicipalLicenseNumber;
                    orderExport.UnifiedId = null;
                    break;
                case Constants.BusinessType.SoleTrader:
                    orderExport.CommercialRegistration = order.RegistrationNumber;
                    orderExport.UnifiedId = null;
                    break;
                case Constants.BusinessType.LegalEnterprise:
                    orderExport.CommercialRegistration = order.LegalId;
                    orderExport.UnifiedId = null;
                    break;
                default:
                    break;
            }
        }

        private static void SetValuesForCatalogues(OrdersExport order, OrderExportResponse orderExport, Catalogue[] catalogues)
        {
            orderExport.Area = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.Areas && x.Key == order.Area).Select(x => x.Value).FirstOrDefault();
            orderExport.City = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.Cities && x.Key == order.City).Select(x => x.Value).FirstOrDefault();
            orderExport.ReferralChannel = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.ReferralChannel && x.Key == order.ReferralChannel).Select(x => x.Value).FirstOrDefault();
            orderExport.ProjectName = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.ProjectName && x.Key == order.ProjectName).Select(x => x.Value).FirstOrDefault();
            orderExport.BankCheckStatus = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.CompanyCheckStatus && x.Key == orderExport.BankCheckStatus).Select(x => x.Value).FirstOrDefault();
            var TransactionTypes = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.TransactionType).ToList();
            orderExport.TransactionType = string.IsNullOrEmpty(order.TransactionType) ? string.Empty : string.Join(",", TransactionTypes.Where(x => order.TransactionType!.Split(',').ToList().Contains(x.Key)).Select(x => x.Value));
            var AcceptedPaymentMethods = catalogues.Where(x => x.CatalogueName == Constants.Catalogues.AcceptedPaymentMethods).ToList();
            orderExport.AcceptedPaymentMethods = string.IsNullOrEmpty(order.AcceptedPaymentMethods) ? string.Empty : string.Join(",", AcceptedPaymentMethods.Where(x => order.AcceptedPaymentMethods!.Split(',').ToList().Contains(x.Key)).Select(x => x.Value));
        }

        private void SetProductExport(OrdersExport order, OrderExportResponse orderExport, Catalogue[] catalogues)
        {
            var orderItems = order.OrderItem!.Where(x => x.ProductInstances != null).ToList();
            var productInstances = orderItems.SelectMany(x => x.ProductInstances!).ToList();
                
            if (!productInstances.Any())
            {
                foreach (var orderItem in order.OrderItem!)
                {
                    var exportedProduct = new ProductExportResponse();
                    exportedProduct.ProductCode = orderItem.ProductCode;

                    orderExport.ProductExport.Add(exportedProduct);
                }
            }

            foreach (var prodInstance in productInstances)
            {
                var exportedProduct = new ProductExportResponse();
                if (supportedTypes.Contains(prodInstance.ProductType!))
                {
                    AddCardSchemaAndCode(exportedProduct, prodInstance);
                    orderExport.ProductExport.Add(exportedProduct);
                }
                else if (prodInstance.ProductType == Constants.ProductType.Mpos || prodInstance.ProductType == Constants.ProductType.Terminal)
                {
                    var terminalData = prodInstance.Metadata as TerminalData;

                    AddTerminalData(exportedProduct, terminalData!, catalogues);
                    AddCardSchemaAndCode(exportedProduct, prodInstance);

                    exportedProduct.TerminalModel = prodInstance.ProductType;                    

                    orderExport.ProductExport.Add(exportedProduct);
                }

                if (prodInstance.Children != null)
                {
                    orderExport.ProductExport.AddRange(SetProductInstanceChildren(order, catalogues, prodInstance));
                }
            }
        }

        private List<ProductExportResponse> SetProductInstanceChildren(OrdersExport order, Catalogue[] catalogues, ProductInstanceResponse prodInstance)
        {
            var list = new List<ProductExportResponse>();
            foreach (var children in prodInstance.Children!)
            {
                var exportedProduct = new ProductExportResponse();
                if (supportedTypes.Contains(children.ProductType!))
                {
                    AddCardSchemaAndCode(exportedProduct, children);                    
                }
                else if (children.ProductType == Constants.ProductType.Mpos || children.ProductType == Constants.ProductType.Terminal)
                {
                    var terminalData = children.Metadata as TerminalData;
                    
                    AddTerminalData(exportedProduct, terminalData!, catalogues);
                    AddCardSchemaAndCode(exportedProduct, children);

                    exportedProduct.TerminalModel = prodInstance.ProductType;                   
                }
                exportedProduct.MerchantName = string.IsNullOrEmpty(exportedProduct.MerchantName) ? order.MerchantName : exportedProduct.MerchantName;
                exportedProduct.MID = string.IsNullOrEmpty(exportedProduct.MID) ? children.MID : exportedProduct.MID;

                list.Add(exportedProduct);
            }
            return list;
        }

        private static void AddCardSchemaAndCode(ProductExportResponse exportedProduct, BaseProductInstance prodInstance)
        {
            exportedProduct.ProductCode = prodInstance.ProductCode;
            exportedProduct.CardSchema = prodInstance.Children != null ? string.Join(",", prodInstance.Children.Where(c => c.ProductType == ProductTypes.SCHEME.ToString())
                                            .Select(s => s.ProductCode)) : string.Empty;   
        }

        private static void AddTerminalData(ProductExportResponse exportedProduct, TerminalData terminalData, Catalogue[] catalogue)
        {
            exportedProduct.MerchantName = terminalData?.ShortName_EN;
            exportedProduct.MID = terminalData?.MIDMerchantReference;
            exportedProduct.FullTID = terminalData?.FullTId;
            exportedProduct.POSDataCode = terminalData?.POSDataCode;
            exportedProduct.TID = terminalData?.TId;

            var providerBankCatalog = catalogue.FirstOrDefault(x =>
                    x.CatalogueName == Constants.Catalogues.AcquiringLedger && x.Key == terminalData?.ProviderBank);
            var providerBankName = providerBankCatalog?.Value;
            var providerBankKey = providerBankCatalog?.Key;
            exportedProduct.BankName = providerBankName;

            if (!string.IsNullOrEmpty(providerBankKey))
            {
                var ledgerToAccountCatalog = catalogue.FirstOrDefault(x =>
                    x.CatalogueName == Constants.Catalogues.LedgerToAccount && x.Key == providerBankKey);
                var accountKey = ledgerToAccountCatalog?.Value;

                if (!string.IsNullOrEmpty(accountKey))
                {
                    var accountValue = catalogue.FirstOrDefault(x =>
                        x.CatalogueName == Constants.Catalogues.AccountNumber && x.Key == accountKey);
                    exportedProduct.AccountNo = accountValue?.Value;
                }
            }
        }
    }
}
