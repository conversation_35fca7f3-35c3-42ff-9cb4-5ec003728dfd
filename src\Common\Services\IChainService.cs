﻿using Common.Models.Account;
using Common.Models.Chain;
using Common.Models.Merchant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Common.Services;

public interface IChainService
{
    Task<List<ChainExport>> ExportChainsAsync(ChainSearchFilters searchCriteria);
    Task<ChainSearchResponse<ChainResult>> FindAsync(ChainSearchFilters filters, CancellationToken cancellationToken = default);
    Task<Chain> CreateChain(ChainCreateRequest request);
    Task<Chain> GetChain(string chainId);
    Task DeleteChain(string chainId);
    Task<Chain> UpdateChain(ChainUpdateRequest chain);
    Task<List<Merchant>> AssociateChainMerchants(string chainId, List<ChainMerchantsLinkRequest> request);
    Task<List<ChainMerchant>> GetChainMerchants(string chainId);
    Task<List<ChainContact>> GetAllContactsOfChain(string chainId);
    Task<List<ChainContact>> CreateChainContacts(ChainContactsCreateRequest request);
    Task<List<ChainContact>> UpdateChainContacts(ChainContactsUpdateRequest request);
    Task DeleteChainContact(Guid chainContactId);
    Task<ChainContact> GetContactById(Guid contactId);
}
