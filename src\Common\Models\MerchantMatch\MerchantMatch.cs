﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class MerchantMatch : MerchantBaseClass
{
    [Display(Name = "Principal Matches")]
    public PrincipalMatch[] PrincipalMatch { get; set; } = Array.Empty<PrincipalMatch>();

    [Display(Name = "Matched Url's")]
    public UrlMatch[] UrlMatch { get; set; } = Array.Empty<UrlMatch>();

    /// <summary>
    /// Ovveride the base property.
    /// </summary>
    [Display(Name = "Concatinated Address Match")]
    public string? Address { get; set; }

}

