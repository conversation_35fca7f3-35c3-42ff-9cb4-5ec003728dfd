﻿using System;

namespace Common.Models
{
    public class DocumentMetadata
    {
        public Guid Id { get; set; }
        public Guid StreamId { get; set; }
        public Guid OwnerUserId { get; set; }
        public Guid? MerchantId { get; set; }
        public Guid? PersonOfInterestId { get; set; }
        public string? DocumentType { get; set; }
        public string? Language { get; set; }
        public string? ProvidedBy { get; set; }
        public string? MimeType { get; set; }
        public string Uri { get; set; } = null!;
        public int Version { get; set; }
        public int? Size { get; set; }
        public string Name { get; set; } = null!;
        public string CreatedBy { get; set; } = null!;
        public DateTimeOffset CreatedDateUtc { get; set; }
        public string? UpdatedBy { get; set; } = null!;
        public DateTimeOffset? UpdatedDateUtc { get; set; }
        public bool IsDeleted { get; set; } = false;
    }
}
