﻿using Common.Models;
using Common.Models.Lead;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Common.Services
{
    public interface ILeadService
    {
        Task<LeadSearchResponse> SearchLeadsAsync(LeadSearchParameters searchParameters);
        Task<Lead> UpdateLeadAsync(Guid leadId, JsonPatchDocument<Lead> leadPatch);
        Task<List<LeadCreationResponse>> GetLeadCreationAsync(IdsRequest leadIds);
        Task<LeadExportResponse[]> ExportLeadsAsync(LeadExportParameters exportParameters);
        Task UpdateSalesIdAsync(string initialSalesId, string updatedSalesId);
        Task<LeadWithDocumentsResponse> CreateLeadAsync(LeadCreateRequest leadCreateRequest, Guid userId);
        Task<Lead> PatchLeadByIdAsync(Guid leadId, JsonPatchDocument<Lead> leadPatch);
    }
}
