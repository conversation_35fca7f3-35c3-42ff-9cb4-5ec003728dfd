﻿using Common.Models.Sales;
using Common.Models.User;
using Common.Services;
using FluentAssertions;
using Moq;
using NUnit.Framework;
using Services;
using System;
using System.Net;
using System.Threading.Tasks;
using Common;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class SaleServiceTests
    {
        private readonly Mock<ILogger<SaleService>> logger = new Mock<ILogger<SaleService>>();
        private SaleService saleService = null!;
        private readonly Mock<IUserService> userService = new Mock<IUserService>();
        private readonly Mock<ILeadService> leadService = new Mock<ILeadService>();
        private readonly Mock<IMerchantService> merchantService = new Mock<IMerchantService>();

        public SaleServiceTests()
        {
            saleService = new SaleService(userService.Object, leadService.Object, merchantService.Object, logger.Object);
        }

        [Test]
        public void UpdateUserSalesIdAsync_WhenServicesRespondWithSuccess_DoesNotThrowException()
        {
            var userId = Guid.NewGuid();
            var initialSalesId = "GDS00001";
            var updatedSalesId = "GDS00002";

            userService.Setup(x => x.UpdateUserSalesIdAsync(It.IsAny<UserSalesIdRequest>()))
                   .Returns(Task.FromResult(new UserSalesIdResponse { UserId = userId, InitialSalesId = initialSalesId, UpdatedSalesId = updatedSalesId }));
            leadService.Setup(x => x.UpdateSalesIdAsync(initialSalesId, updatedSalesId))
                   .Returns(Task.CompletedTask);
            merchantService.Setup(x => x.UpdateSalesIdAsync(initialSalesId, updatedSalesId))
                .Returns(Task.CompletedTask);

            saleService.Invoking(x => x.UpdateUserSalesIdAsync(new UserSalesIdRequest { UserId = userId, UpdatedSalesId = updatedSalesId }))
                .Should().NotThrowAsync<Exception>();

            userService.Verify(X => X.UpdateUserSalesIdAsync(It.Is<UserSalesIdRequest>(x => x.UserId == userId)), Times.Once());
            leadService.Verify(X => X.UpdateSalesIdAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Once());
            merchantService.Verify(X => X.UpdateSalesIdAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Once());
        }

        [Test]
        public async Task UpdateUserSalesIdAsync_WhenUserDidNotHaveInitialSalesId_DoesNotCallLeadService()
        {
            var userId = Guid.NewGuid();
            var updatedSalesId = "GDS00003";

            userService.Setup(x => x.UpdateUserSalesIdAsync(It.IsAny<UserSalesIdRequest>()))
                   .Returns(Task.FromResult(new UserSalesIdResponse { UserId = userId, InitialSalesId = null, UpdatedSalesId = updatedSalesId }));

            await saleService.UpdateUserSalesIdAsync(new UserSalesIdRequest { UserId = userId, UpdatedSalesId = updatedSalesId });

            userService.Verify(X => X.UpdateUserSalesIdAsync(It.Is<UserSalesIdRequest>(x => x.UserId == userId)), Times.Once());
            leadService.Verify(X => X.UpdateSalesIdAsync(It.IsAny<string>(), updatedSalesId), Times.Never());
            merchantService.Verify(X => X.UpdateSalesIdAsync(It.IsAny<string>(), updatedSalesId), Times.Never());
        }

        [Test]
        [TestCase("zxudobaozknvwniyo")]
        public void UpdateUserSalesIdAsync_UserSalesIdRequestValidator_ShouldThrowValidationException(string updatedSalesId)
        {
            var userId = Guid.NewGuid();
            var userSalesIdRequest = new UserSalesIdRequest()
            {
                UserId = userId,
                UpdatedSalesId = updatedSalesId
            };

            saleService.Invoking(x => x.UpdateUserSalesIdAsync(userSalesIdRequest)).Should()
                .ThrowAsync<ValidationException>()
                .Where(x => x.StatusCode == HttpStatusCode.BadRequest && TestsHelper.HasErrorCode(x, Errors.UpdatedSalesIdLengthValidation.Code));
        }

        [Test]
        [TestCase("test")]
        public void UpdateUserSalesIdAsync_UserSalesIdRequestValidator_ShouldNotThrowValidationException(string updatedSalesId)
        {
            var userId = Guid.NewGuid();
            var userSalesIdRequest = new UserSalesIdRequest()
            {
                UserId = userId,
                UpdatedSalesId = updatedSalesId
            };

            saleService.Invoking(x => x.UpdateUserSalesIdAsync(userSalesIdRequest)).Should()
                .NotThrowAsync<ValidationException>();
        }
    }
}
