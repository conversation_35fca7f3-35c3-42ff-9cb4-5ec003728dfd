﻿using System.Net.Http;
using System.Linq;
using System.Threading.Tasks;
using Common.Models;
using Common.Options;
using Common.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Text;
using Microsoft.AspNetCore.JsonPatch;
using System;
using System.Dynamic;
using System.Net;
using Geidea.Utils.Exceptions;
using Common.Models.Merchant;
using Common;
using Common.Models.Checkout;
using Common.Models.Comment;
using Common.Models.Gsdk;
using Common.Validators;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Json;
using Geidea.Utils.Validation;
using ReferenceUtils = Geidea.Utils.ReferenceData;
using System.Text.Json;
using AutoMapper;
using Common.Models.Product;
using Common.Models.Tasks;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Constants = Common.Constants;
using FluentValidation.Results;
using System.Threading;
using Common.Models.TerminalDataSet;
using Common.Models.ProductInstance;
using static Common.Constants;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Common.Models.Checks;
using System.Collections.Concurrent;
using System.Xml.XPath;
using AutoMapper.Internal;
using System.Net.NetworkInformation;
using Common.Models.Shareholder;
using static System.Formats.Asn1.AsnWriter;
using Newtonsoft.Json.Linq;

namespace Services
{
    public class MerchantService : IMerchantService
    {
        private readonly ILogger<MerchantService> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly ApplicationOptions appOptions;
        private readonly HttpClient client;
        private readonly IUserService userService;
        private readonly ICheckoutService checkoutService;
        private readonly ICounterpartyProvider counterpartyProvider;
        private readonly IActiveCampaignService activeCampaignService;
        private readonly IMapper mapper;
        private readonly IReferenceService referenceService;
        private readonly IMerchantClient merchantClient;
        private readonly ISearchService searchService;
        private readonly ISubordinateMerchantService subordinateMerchantService;
        private readonly IOptions<TmsIntegrationFeatureToggle> tmsIntegrationFeatureToggle;
        private readonly IProductService productService;
        private readonly IShareholderService shareholderService;
        private readonly IDueDiligenceService dueDiligenceService;


        private string MerchantServiceBaseUrl => $"{urlSettingsOptions.MerchantServiceBaseUrl}/api/v1";
        private string SearchServiceBaseUrl => $"{urlSettingsOptions.SearchServiceBaseUrl}/api/v1";
        private const string MerchantValidateIfLicenseIdUsedEndpoint = "/merchant/isLicenseIdUsed";

        private const string RiskCalculationEndPoint = "/Merchant/CalculateRisk";

        private static readonly string[] CompanyCheckRulesForMerchantStatus =
         {
            MerchantStatus.BoardingCompleted, MerchantStatus.ComplianceApproval, MerchantStatus.RiskApproval
        };
        private static readonly string[] NewMerchantStatusAllowed =
        {
            MerchantStatus.RiskApproval, MerchantStatus.ComplianceApproval, MerchantStatus.Verified
        };
        private static readonly int[] RiskApprovalEcomTransactionTypes =
        {
            RiskApprovalTransactionType.MOTO, RiskApprovalTransactionType.KEY_IN, RiskApprovalTransactionType.REFUND, RiskApprovalTransactionType.TOKENIZATION
        };
        private static readonly int[] RiskApprovalPosTransactionTypes =
        {
            RiskApprovalTransactionType.MOTO, RiskApprovalTransactionType.KEY_IN, RiskApprovalTransactionType.REFUND
        };

        public MerchantService(
            ILogger<MerchantService> logger,
            IOptionsMonitor<UrlSettings> urlSettingsOptions,
            IOptionsMonitor<ApplicationOptions> appOptions,
            HttpClient client,
            IUserService userService,
            ICheckoutService checkoutService,
            ICounterpartyProvider counterpartyProvider,
            IActiveCampaignService activeCampaignService,
            IMapper mapper,
            IReferenceService referenceService,
            IMerchantClient merchantClient,
            ISearchService searchService,
            ISubordinateMerchantService subordinateMerchantService, IOptions<TmsIntegrationFeatureToggle> tmsIntegrationFeatureToggle,
            IProductService productService,
            IShareholderService shareholderService,
            IDueDiligenceService dueDiligenceService
            )
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.appOptions = appOptions.CurrentValue;
            this.client = client;
            this.userService = userService;
            this.checkoutService = checkoutService;
            this.counterpartyProvider = counterpartyProvider;
            this.activeCampaignService = activeCampaignService;
            this.mapper = mapper;
            this.referenceService = referenceService;
            this.merchantClient = merchantClient;
            this.searchService = searchService;
            this.subordinateMerchantService = subordinateMerchantService;
            this.tmsIntegrationFeatureToggle = tmsIntegrationFeatureToggle;
            this.productService = productService;
            this.shareholderService = shareholderService;
            this.dueDiligenceService = dueDiligenceService;
        }

        public async Task<MerchantSearchResponse<MerchantApiResult>> FindAsync(MerchantSearchFilters filters, CancellationToken cancellationToken = default)
        {
            var searchServiceUrl = $"{SearchServiceBaseUrl}/Merchant/advancedSearch";

            using (logger.BeginScope("AdvancedSearchAsync({@searchServiceUrl})", searchServiceUrl))
            {
                logger.LogInformation("Calling search service to search for merchants");

                var body = new StringContent(JsonConvert.SerializeObject(filters), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(searchServiceUrl, body, cancellationToken);
                var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling search service to search for merchants. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var merchants = Json.Deserialize<MerchantSearchResponse<MerchantSearchResult>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                var mappedResponse = Map(merchants);
                return mappedResponse;
            }
        }

        public async Task<dynamic> GetContactDetailsAsync(Guid contactId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/poi/contact/{contactId}";

            using (logger.BeginScope("GetContactDetailsAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to get contact by id");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to get contact by id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<dynamic>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<MerchantDetails[]> GetMerchantsDetailsAsync()
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchantDetails";

            using (logger.BeginScope("GetMerchantsDetailsAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to get merchants details");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to get contact by id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var result = Json.Deserialize<MerchantDetails[]>(responseBody,
                     new JsonSerializerOptions
                     {
                         PropertyNameCaseInsensitive = true
                     });
                return result;
            }
        }

        public async Task<ContactDetails?> GetMerchantContactsAsync(Guid merchantId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/{merchantId}/contact";

            using (logger.BeginScope("GetMerchantContactsAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to get default contact by merchant id");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to get default contact by merchant id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<ContactDetails[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    }).SingleOrDefault(a => a.MerchantContactReason == appOptions.DefaultContactReasonName);
            }
        }

        public async Task DeleteMerchantsAsync(MerchantDeleteRequest merchantDeleteRequest)
        {
            foreach (var merchantId in merchantDeleteRequest.MerchantId)
            {
                await DeleteMerchantAsync(merchantId);
                await userService.RemoveRolesForMerchantAsync(merchantId);
            }
        }

        private async Task DeleteMerchantAsync(Guid merchantId)
        {
            string merchantServiceUrl = $"{MerchantServiceBaseUrl}/Merchant/{merchantId}";

            using (logger.BeginScope("DeleteMerchantAsync({@merchantServiceUrl})", merchantServiceUrl))
            {
                logger.LogInformation("Calling merchant service to delete merchant with id '{merchantId}'.", merchantId);

                var response = await client.DeleteAsync(merchantServiceUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();

                    logger.LogCritical("Error when calling merchant service to delete merchant with id '{merchantId}'. Error was {StatusCode} {@responseBody}.",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation("Deleted merchant with id '{merchantId}'.", merchantId);
            }
        }

        private async Task UpdateMerchantOrders(Guid merchantId, string newMerchantStatus)
        {
            switch (newMerchantStatus)
            {
                case MerchantStatus.Verified:
                    await UpdateOrderStatusToVerified(merchantId);
                    break;
                case MerchantStatus.Rejected:
                    await UpdateOrderStatusToRejected(merchantId);
                    break;
                case MerchantStatus.VerificationError:
                    SendMessageToActiveCampaign(merchantId,
                    Geidea.Utils.Common.Constants.ActiveCampaign.MerchantVerificationCompleted);
                    break;
            }
        }

        private async Task UpdateOrderStatusToVerified(Guid merchantId)
        {
            var ordersInPendingBusinessVerification =
                    await checkoutService.SearchOrderAsync(new OrderSearchCriteria { MerchantId = merchantId, OrderStatus = new List<string> { OrderStatus.VerificationInProgress } });

            if (ordersInPendingBusinessVerification.Length > 0)
            {
                foreach (var order in ordersInPendingBusinessVerification)
                {
                    var orderPatch = new JsonPatchDocument<OrderUpdateRequest>();
                    orderPatch.Add(x => x.OrderStatus, OrderStatus.Verified);
                    await checkoutService.UpdateOrderAsync(order.OrderId, orderPatch, null, false);
                }

                SendMessageToActiveCampaign(merchantId,
                    Geidea.Utils.Common.Constants.ActiveCampaign.MerchantVerificationCompleted);
            }
        }
        private async Task UpdateOrderStatusToRejected(Guid merchantId)
        {
            var merchantOrders =
                   await checkoutService.SearchOrderAsync(new OrderSearchCriteria { MerchantId = merchantId, OrderStatus = new List<string> { OrderStatus.Submitted, OrderStatus.VerificationInProgress } });

            if (merchantOrders.Length > 0)
            {
                foreach (var order in merchantOrders)
                {
                    var orderPatch = new JsonPatchDocument<OrderUpdateRequest>();
                    orderPatch.Add(x => x.OrderStatus, OrderStatus.Rejected);
                    await checkoutService.UpdateOrderAsync(order.OrderId, orderPatch, null, false);
                }
                SendMessageToActiveCampaign(merchantId,
                    Geidea.Utils.Common.Constants.ActiveCampaign.MerchantVerificationCompleted);
            }
        }

        private async Task<MerchantUpdateResponse> UpdateMerchantTag(Guid merchantId, string newMerchantTag, JsonPatchDocument corePatchDocument)
        {
            await subordinateMerchantService.PerformValidationForUpdatingMerchantTag(merchantId);

            string tagUpdateMessage;
            if (newMerchantTag == Constants.MerchantTag.SubWholesaler ||
                newMerchantTag == Constants.MerchantTag.SubBusiness)
            {
                tagUpdateMessage = Errors.UpdateMerchantTagOperationSuccessfulWithWarning.Code;
            }
            else
            {
                tagUpdateMessage = Errors.OperationSuccessful.Code;
            }

            var response = await PatchMerchantAsync(merchantId, corePatchDocument);
            response.TagUpdateMessage = tagUpdateMessage;
            return response;
        }

        public async Task<MerchantUpdateResponse> PatchMerchantAsync(Guid merchantId, JsonPatchDocument<PatchMerchantRequest> patchDocument)
        {
            new ValidationHelpers().Validate(patchDocument, new MerchantPatchValidator(), logger, "MerchantPatch validation failed!");

            if (counterpartyProvider.GetCode().Equals(Constants.CounterParty.Saudi) && tmsIntegrationFeatureToggle.Value.EnableTmsIntegration && patchDocument.Operations.Any(x => x.path == "merchantStatus") && patchDocument.Operations.Any(x => x.value.ToString() == "VERIFIED"))
            {
                await ValidateMerchantBusinessInformation(merchantId, patchDocument);
            }

            var patchOperations = patchDocument.Operations.Select(operation =>
                new Operation(operation.op, operation.path, operation.from, operation.value))
                .ToList();

            var merchantStatusOperation = patchOperations.FirstOrDefault(op => op.path?.ToLowerInvariant() == nameof(PatchMerchantRequest.MerchantStatus).ToLowerInvariant());
            if (merchantStatusOperation?.value != null)
            {
                await ValidateMerchantStatus(merchantId, merchantStatusOperation.value.ToString());
                await UpdateMerchantOrders(merchantId, merchantStatusOperation.value.ToString()!);
                if (merchantStatusOperation!.value.ToString() == MerchantStatus.BoardingCompleted)
                {
                    await dueDiligenceService.SendWorldCheckOneRequest(merchantId);
                }
            }

            //UAE new merchant enablement flow to check the order status for the business.
            var merchantOrdersInPricingApproval =
                await checkoutService.SearchOrderAsync(new OrderSearchCriteria { MerchantId = merchantId, OrderStatus = new List<string> { OrderStatus.VerificationInProgress } });

            if (merchantOrdersInPricingApproval.Length > 0 && merchantStatusOperation?.value != null && merchantStatusOperation.value.ToString() == MerchantStatus.PendingPricingApproval)
            {
                patchOperations = patchDocument.Operations.Select(x => new Operation(x.op, x.path, x.from, $"{MerchantStatus.BoardingCompleted}")).ToList();

                await dueDiligenceService.SendWorldCheckOneRequest(merchantId);
            }

            var merchantMccOperation = patchOperations.FirstOrDefault(op => op.path.Equals(MerchantPatchOperation.UpdateMccOperation));
            if (counterpartyProvider.GetCode().Equals(Constants.CounterParty.Egypt) && merchantMccOperation?.value != null)
            {
                await UpdateMerchantOrdersWithMCC(merchantMccOperation.value.ToString(), merchantId);
            }
            if (counterpartyProvider.GetCode().Equals(Constants.CounterParty.Uae) && merchantMccOperation?.value != null)
            {
                patchOperations = await GetBusinessDomainToMcc(merchantMccOperation.value.ToString(), patchDocument, patchOperations);
                await UpdateStoreMCC(merchantMccOperation.value.ToString(), merchantId);
            }

            var corePatchDocument = new JsonPatchDocument(patchOperations, patchDocument.ContractResolver);

            var merchantTag = patchOperations.FirstOrDefault(op => op.path?.ToLowerInvariant() == nameof(PatchMerchantRequest.Tag).ToLowerInvariant());
            if (merchantTag == null || counterpartyProvider.GetCode().Equals(Constants.CounterParty.Saudi))
            {
                return await PatchMerchantAsync(merchantId, corePatchDocument);
            }



            return await UpdateMerchantTag(merchantId, merchantTag?.value.ToString()!, corePatchDocument);
        }

        private async Task UpdateMerchantOrdersWithMCC(string? mcc, Guid merchantId)
        {
            var merchantOrdersNotPassedProductRegisterd = await checkoutService.GetMerchantOrdersNotPassedProductRegisterd(merchantId);

            if (merchantOrdersNotPassedProductRegisterd != null && merchantOrdersNotPassedProductRegisterd.OrdersIds?.Count > 0)
            {
                var productIntancesWithTerminalData = await searchService.GetOrdersProductInstancesWithTerminalDataAsync(merchantOrdersNotPassedProductRegisterd.OrdersIds);

                if (productIntancesWithTerminalData?.Count > 0 && productIntancesWithTerminalData.Any(order => order.Instances?.Count > 0))
                {
                    var merchant = await merchantClient.GetCoreMerchantAsync(merchantId);
                    var terminals = await productService.UpdateOrderTerminalDataSetsMcc(ConstructTerminalDataSetMidTidRequest(productIntancesWithTerminalData, merchant, null, mcc));

                    await UpdateTerminalProductInstancesMeta(merchant, terminals, merchant.MerchantDetails?.TradingCurrency, mcc);
                }
            }
        }

        private async Task<List<Operation>> GetBusinessDomainToMcc(string? MCC, JsonPatchDocument<PatchMerchantRequest> patchDocument, List<Operation> patchOperations)
        {
            var refData = (await referenceService.GetCataloguesAsync(new string[] { Catalogues.BusinessDomain, Catalogues.MccToBusinessDomain }))
                .Select(c => new ReferenceUtils.ReferenceData { CatalogueName = c.CatalogueName, Key = c.Key, Value = c.Value }).ToList();
            var businessDomain = refData.FindAll(c => c.CatalogueName == Catalogues.MccToBusinessDomain).SingleOrDefault(c => c.Value == MCC);
            var addPatchOperation = patchDocument.Operations.Select(x => new Operation(x.op, "merchantDetails/businessDomain", x.from, businessDomain?.Key)).ToList();
            patchOperations = patchOperations.Concat(addPatchOperation).ToList();
            return patchOperations;
        }
        private async Task UpdateStoreMCC(string? MCC, Guid merchantId)
        {
            var stores = await merchantClient.GetStoresAsync(merchantId);

            foreach (var store in stores)
            {
                var storePatch = new JsonPatchDocument<PatchMerchantRequest>();
                storePatch.Operations.Add(new Operation<PatchMerchantRequest>
                {
                    op = "replace",
                    path = "merchantDetails/mcc",
                    value = MCC
                });
                await merchantClient.PatchMerchantAsync(store.MerchantId, storePatch);
            }
        }

        private async Task<List<Guid>> UpdateTerminalProductInstancesMeta(Merchant merchant,
            List<TerminalDataSetResponse> terminalDataSets, string? orderCurrency, string? mcc)
        {
            var updateProductInstancesMetaRequest = terminalDataSets.Select(t => new UpdateProductInstanceMetaRequest
            {
                LegalName = merchant.MerchantDetails?.LegalName,
                LegalNameAr = merchant.MerchantDetails?.LegalNameAr,
                MCC = mcc,
                TradingCurrency = orderCurrency,
                MIDMerchantReference = t.MIDMerchantReference,
                ProductInstanceId = t.ProductInstanceId,
                ProviderBank = t.ProviderBank,
                TId = t.TId,
                FullTId = t.FullTId,
                Trsm = t.Trsm,
                ConnectionType = t.ConnectionType,
                ChannelType = t.ChannelType
            })
            .ToList();

            return await productService.UpdateTerminalProductInstancesMeta(updateProductInstancesMetaRequest);
        }

        private static TerminalDataRequestMcc ConstructTerminalDataSetMidTidRequest(List<OrdersWithInstancesResponse> orderData, Merchant merchant, List<MerchantStoresDefaultCity>? merchantData, string? mcc)
        {
            return new TerminalDataRequestMcc
            {
                StoresIds = merchantData?.Select(s => s.StoreId).ToList(),
                TerminalDataSets = orderData.Where(order => order.Instances?.Count > 0)
                    .SelectMany(order => order.Instances.Select(instance =>
                        new TerminalDataSetBasicInfo
                        {
                            OrderNumber = order.OrderNumber,
                            AcquiringLedger = merchant.MerchantDetails?.AcquiringLedger,
                            ProductInstanceId = instance.ProductInstanceId,
                            StoreId = order.StoreId,
                            MerchantTag = merchant.Tag,
                            MCC = mcc,
                            ConnectionType = HandleConnectionTypeValue(instance.ProductType, instance.ProductCode),
                            ChannelType = HandleChannelTypeValue(instance.ProductType, instance.ProductCode)
                        })
                    .ToList())
                    .ToList()
            };
        }

        public async Task<MerchantExportResponse> ExportMerchantAsync(Guid merchantId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/export/{merchantId}";

            using (logger.BeginScope("ExportMerchantAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to export merchant by id");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {

                    logger.LogCritical("Error when calling merchant API to export merchant by id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<MerchantExportResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<MerchantExportResponse[]> ExportAllMerchantsAsync()
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/export";

            using (logger.BeginScope("ExportAllMerchantsAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to export all merchants");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {

                    logger.LogCritical("Error when calling merchant API to export all merchants. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<MerchantExportResponse[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<List<EgyptMerchantExportResponse>> ExportEgyptMerchantsAsync(MerchantSearchFilters searchCriteria)
        {
            var merchants = await GetEgyptMerchantsBySearchCriteria(searchCriteria);
            var catalogues = await referenceService.GetCataloguesAsync(new string[] {Constants.Catalogues.Governorates, Constants.Catalogues.Cities,
                Constants.Catalogues.NbeGovernarotes, Constants.Catalogues.NbeCities, Constants.Catalogues.AlxGovernarotes, Constants.Catalogues.AlxCities,
                Constants.Catalogues.MerchantStatus, Constants.Catalogues.Product, Constants.Catalogues.BankCheckStatus });
            var merchantsExport = MapEgyptMerchantsForExport(merchants, catalogues);

            return merchantsExport;
        }

        private async Task ValidateMerchantStatus(Guid merchantId, string? newMerchantStatus)
        {
            var merchant = await merchantClient.GetCoreMerchantAsync(merchantId);

            ValidateMerchantMCC(merchant, newMerchantStatus);

            if (counterpartyProvider.GetCode().Equals(CounterParty.Uae) && CompanyCheckRulesForMerchantStatus.Any(c => c.Equals(merchant.MerchantStatus)) &&
                    NewMerchantStatusAllowed.Any(c => c.Equals(newMerchantStatus)))
            {
                await ValidateCompanyCheckRules(merchantId, merchant.MerchantStatus, newMerchantStatus);
                await ValidateKycCheckStatus(merchantId, merchant.MerchantStatus);

            }

            string? currentMerchantStatus = merchant.MerchantStatus;

            if (currentMerchantStatus?.ToLowerInvariant() ==
                Constants.MerchantStatus.RequireCACApproval.ToLowerInvariant() &&
                newMerchantStatus?.ToLowerInvariant() != Constants.MerchantStatus.RequireCACApproval.ToLowerInvariant())
            {
                var merchantTasks =
                    await searchService.TaskAdvancedSearch(new TaskSearchRequest { MerchantId = merchantId, Take = int.MaxValue });

                if (merchantTasks.Records.Any(task => !string.Equals(task.TaskStatus, Constants.TaskStatusesKeys.Done, StringComparison.InvariantCultureIgnoreCase)))
                {
                    logger.LogError(
                        "Error when trying to update merchant status from {currentMerchantStatus} to {newMerchantStatus} for merchant with id {merchantId}. Not all associated tasks are done.",
                        currentMerchantStatus,
                        newMerchantStatus,
                        merchantId);

                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.MerchantCACStatusTaskError);
                }
            }
        }

        private void ValidateMerchantMCC(Merchant merchant, string? merchantStatus)
        {
            if ((counterpartyProvider.GetCode().Equals(CounterParty.Egypt) || counterpartyProvider.GetCode().Equals(CounterParty.Uae)) && merchantStatus == MerchantStatus.Verified &&
                string.IsNullOrEmpty(merchant.MerchantDetails?.MCC))
            {
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.MerchantMCCRequired);
            }
        }

        private static List<EgyptMerchantExportResponse> MapEgyptMerchantsForExport(List<EgyptMerchantExport> merchants, Catalogue[] catalogues)
        {
            var merchantsExport = new List<EgyptMerchantExportResponse>();

            foreach (var merchant in merchants)
            {
                string governorate = ReturnCatalogue(merchant.AcquiringLedger, catalogues, Constants.Catalogues.Governorates);
                string city = ReturnCatalogue(merchant.AcquiringLedger, catalogues, Constants.Catalogues.Cities);


                EgyptMerchantExportResponse merchantExport = new EgyptMerchantExportResponse();

                merchantExport.FirstName = merchant.FirstName;
                merchantExport.LastName = merchant.LastName;
                merchantExport.NationalId = merchant.NationalId;
                merchantExport.City = catalogues.FirstOrDefault(x => x.CatalogueName == city && x.Key == merchant.City)?.Value;
                merchantExport.Governorate = catalogues.FirstOrDefault(x => x.CatalogueName == governorate && x.Key == merchant.Governorate)?.Value;
                merchantExport.AddressLine = merchant.AddressLine;
                merchantExport.BusinessName = merchant.BusinessName;
                merchantExport.MemberId = merchant.MemberId;
                merchantExport.LicenseNumber = merchant.RegistrationNumber;
                merchantExport.Mcc = merchant.Mcc;
                merchantExport.MerchantStatus = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.MerchantStatus && x.Key == merchant.MerchantStatus)?.Value;
                merchantExport.PosMid = merchant.PosMid;
                merchantExport.Website = merchant.Website;
                merchantExport.Products = FormatMerchantProductsForExport(merchant.Orders, catalogues);
                merchantExport.BankCheckStatus = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.BankCheckStatus && x.Key == merchant.BankCheckStatus)?.Value ?? string.Empty;
                merchantExport.BankCheckStatusDate = merchant.BankCheckStatusDate.ToString() ?? string.Empty;

                merchantsExport.Add(merchantExport);
            }

            return merchantsExport;
        }

        private static string ReturnCatalogue(string? acquiringLedger, Catalogue[] catalogues, string catalogueName)
        {
            if (acquiringLedger != null && catalogues.Any(r => r.CatalogueName.StartsWith(acquiringLedger, StringComparison.OrdinalIgnoreCase)))
                catalogueName = ConcatAcquirerWithCatalogueName(acquiringLedger, catalogueName);

            return catalogueName;
        }
        public static string ConcatAcquirerWithCatalogueName(string acquirer, string catalogueName)
        {
            return $"{acquirer.ToUpper()}_{catalogueName}";
        }

        private static string FormatMerchantProductsForExport(List<OrderResponse> orders, Catalogue[] catalogues)
        {
            var products = GetMerchantProductsList(orders);

            var formatedString = string.Empty;

            foreach (var product in products)
            {
                var productName = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.Product && x.Key == product.Code)?.Value;
                formatedString = $"{formatedString}{productName} ({product.Quantity}), ";
            }

            var indexOfPenultimateCharacters = formatedString.Length - 2;
            formatedString = string.IsNullOrEmpty(formatedString) ? string.Empty : formatedString.Remove(indexOfPenultimateCharacters);

            return formatedString;
        }

        private async Task<List<EgyptMerchantExport>> GetEgyptMerchantsBySearchCriteria(MerchantSearchFilters searchCriteria)
        {
            string serviceUrl = $"{SearchServiceBaseUrl}/merchant/egypt/export";
            var requestBody = new StringContent(JsonConvert.SerializeObject(searchCriteria), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetEgyptMerchantsBySearchCriteria({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to export all egypt merchants by search criteria");

                var response = await client.PostAsync(serviceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {

                    logger.LogCritical("Error when calling merchant API to export egypt merchants. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<EgyptMerchantExport>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<MerchantPersonOfInterest[]> GetPeopleByMerchantIdAsync(Guid merchantId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/{merchantId}/poi";

            using (logger.BeginScope("GetMerchantContactsAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service, get poi.");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service, get poi by merchant id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<MerchantPersonOfInterest[]>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<List<MerchantStatusResponse>> GetMerchantStatusHistoryAsync(Guid merchantId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/{merchantId}/status/history";

            using (logger.BeginScope("GetMerchantStatusHistoryAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service, get status history.");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service, get status history by merchant id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var merchantStatusResponses = Json.Deserialize<List<MerchantStatusResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                var users = await userService.GetAllUsersAsync();
                foreach (var merchantStatusResponse in merchantStatusResponses)
                {
                    if (!Guid.TryParse(merchantStatusResponse.UpdatedBy, out var userId)) continue;

                    var user = users.FirstOrDefault(u => u.Id.Equals(userId));
                    merchantStatusResponse.UpdatedBy = user != null ? $"{user.FirstName} {user.LastName}" : Constants.User.DefaultUserValue;
                }

                return merchantStatusResponses;
            }
        }

        public async Task<List<MerchantShortExportResponse>> GetMerchantShortExportAsync(IdsRequest idsRequest)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/export";

            using (logger.BeginScope("GetMerchantShortExportAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service, get short merchant export.");

                var content = new StringContent(JsonConvert.SerializeObject(idsRequest), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(serviceUrl, content);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service, get short merchant export. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<List<MerchantShortExportResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<IReadOnlyCollection<AdditionalMccUpdate>> UpdateMerchantAdditionalMccsAsync(Guid merchantId, IReadOnlyCollection<AdditionalMccUpdate> updateMerchantAdditionalMccs)
        {
            new ValidationHelpers().Validate(updateMerchantAdditionalMccs, new UpdateMerchantAdditionalMccsValidator(), logger, "updateMerchantAdditionalMccs length validation failed!");

            string requestUrl = $"{MerchantServiceBaseUrl}/merchant/{merchantId}/additionalMccs";
            var requestBody = new StringContent(JsonConvert.SerializeObject(updateMerchantAdditionalMccs), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateMerchantAdditionalMccsAsync({@requestUrl})", requestUrl))
            {
                logger.LogInformation("Calling merchant service, update merchant with id: '{merchantId}'.", merchantId);

                var response = await client.PutAsync(requestUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service, update merchant with id '{merchantId}'. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                return Json.Deserialize<IReadOnlyCollection<AdditionalMccUpdate>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<bool> CreateMerchantExternalIdentifier(MerchantExternalIdentifier externalIdentifier)
        {
            var requestUri = $"{MerchantServiceBaseUrl}/Merchant/ExternalIdentifier";
            var requestData = new StringContent(JsonConvert.SerializeObject(externalIdentifier), Encoding.UTF8, "application/json");

            using (logger.BeginScope($"CreateMerchantExternalIdentifier({requestUri})"))
            {
                logger.LogInformation("Calling merchant service to create a new external identifier for merchant with {@id}.", externalIdentifier.MerchantId);

                HttpResponseMessage response;

                try
                {
                    response = await client.PostAsync(requestUri, requestData);
                }
                catch (HttpRequestException ex)
                {
                    logger.LogError("A call to {@requestUri} failed with exception {@ex}", requestUri, ex.Message);
                    return false;
                }

                if (!response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                    logger.LogError($"Error when calling merchant service to create a new external identifier. Error was {response.StatusCode} {@responseBody}");
                    return false;
                }

                logger.LogInformation($"Created external identifier for merchant with id '{externalIdentifier.MerchantId}'.");

                return true;
            }
        }

        public async Task<IReadOnlyCollection<GsdkMerchant>> SearchGsdkMerchants(SearchGsdkMerchantsRequest searchGsdkMerchantsRequest)
        {
            var requestUri = $"{MerchantServiceBaseUrl}/Search/gsdk-users{GetSearchQueryString(searchGsdkMerchantsRequest)}";

            using (logger.BeginScope($"SearchGsdkMerchants({requestUri})"))
            {
                logger.LogInformation($"Calling merchant service to search for GSDK merchants.");

                IReadOnlyCollection<GsdkMerchant> merchantsResponse = new List<GsdkMerchant>();
                HttpResponseMessage response;

                try
                {
                    response = await client.GetAsync(requestUri);
                }
                catch (HttpRequestException ex)
                {
                    logger.LogError("A call to {@requestUri} failed with exception {@ex}", requestUri, ex.Message);
                    return merchantsResponse;
                }

                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical($"Error when calling merchant service to create new store. Error was {response.StatusCode} {@responseBody}");
                    throw new PassthroughException(response);
                }

                merchantsResponse = Json.Deserialize<IReadOnlyCollection<GsdkMerchant>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                logger.LogInformation($"GSDK Merchants search completed.");

                return merchantsResponse;
            }
        }

        private static string GetSearchQueryString(SearchGsdkMerchantsRequest searchMerchantsRequest)
        {
            var filters = new List<string>();

            if (searchMerchantsRequest.MerchantId != Guid.Empty)
                filters.Add($"{nameof(searchMerchantsRequest.MerchantId)}={searchMerchantsRequest.MerchantId}");

            filters.Add($"{nameof(searchMerchantsRequest.HasGsdkOrganizationId)}={searchMerchantsRequest.HasGsdkOrganizationId}");

            filters.Add($"{nameof(searchMerchantsRequest.VerifiedStatusFilter)}={searchMerchantsRequest.VerifiedStatusFilter}");

            return "?" + string.Join("&", filters);
        }

        private async Task<MerchantUpdateResponse> PatchMerchantAsync(Guid merchantId, JsonPatchDocument patch)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/Merchant/?merchantId={merchantId}";

            var body = new StringContent(JsonConvert.SerializeObject(patch), Encoding.UTF8, "application/json");

            using (logger.BeginScope("PatchMerchantAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation($"Calling merchant service to patch merchant with id '{merchantId}'.");

                var response = await client.PatchAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to patch merchant with id '{merchantId}'. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var merchantExtendedResponse = response.Content.ReadAsAsync<dynamic>();
                return Json.Deserialize<MerchantUpdateResponse>(merchantExtendedResponse.Result.ToString(),
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<MerchantCommentResponse> CreateCommentAsync(Guid merchantId, CommentCreateRequest commentCreateRequest)
        {
            new ValidationHelpers().Validate(commentCreateRequest.CommentText, new CommentValidator(), logger, "CommentText length validation failed!");

            string requestUrl = $"{MerchantServiceBaseUrl}/Merchant/{merchantId}/comment";
            var requestBody = new StringContent(JsonConvert.SerializeObject(commentCreateRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("CreateCommentAsync({@requestUrl})", requestUrl))
            {
                logger.LogInformation("Calling merchant service, create new comment.");

                var response = await client.PostAsync(requestUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service, create new comment. Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                logger.LogInformation("New comment created.");

                MerchantCommentResponse comment = Json.Deserialize<MerchantCommentResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                return comment;
            }
        }

        public async Task<List<MerchantCommentResponse>> GetCommentByMerchantIdAsync(Guid merchantId)
        {
            string requestUrl = $"{MerchantServiceBaseUrl}/Merchant/{merchantId}/comment";

            using (logger.BeginScope("GetCommentByMerchantIdAsync({@requestUrl})", requestUrl))
            {
                logger.LogInformation($"Calling merchant service, get comments for merchant with id: '{merchantId}'.");

                var response = await client.GetAsync(requestUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical(
                        "Error when calling merchant service, get comments for merchant with id '{merchantId}'. Error was {StatusCode} {@responseBody}",
                        merchantId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                List<MerchantCommentResponse> comments =
                    Json.Deserialize<List<MerchantCommentResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                return comments;
            }
        }

        public async Task<MerchantCommentResponse> GetCommentByIdAsync(Guid commentId)
        {
            string requestUrl = $"{MerchantServiceBaseUrl}/comment/{commentId}";

            using (logger.BeginScope("GetCommentByIdAsync({@requestUrl})", requestUrl))
            {
                logger.LogInformation($"Calling merchant service, get comment with id: '{commentId}'.");

                var response = await client.GetAsync(requestUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling checkout service, get comment with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                        commentId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                MerchantCommentResponse comment = Json.Deserialize<MerchantCommentResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                return comment;
            }
        }

        public async Task<MerchantCommentResponse> UpdateCommentAsync(Guid commentId, CommentUpdateRequest commentUpdateRequest)
        {
            new ValidationHelpers().Validate(commentUpdateRequest.CommentText, new CommentValidator(), logger, "CommentText length validation failed!");

            string requestUrl = $"{MerchantServiceBaseUrl}/comment/{commentId}";
            var requestBody = new StringContent(JsonConvert.SerializeObject(commentUpdateRequest), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateCommentAsync({@requestUrl})", requestUrl))
            {
                logger.LogInformation($"Calling merchant service, update comment with id: '{commentId}'.");

                var response = await client.PutAsync(requestUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service, update comment with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                        commentId, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                MerchantCommentResponse updatedComment = Json.Deserialize<MerchantCommentResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                return updatedComment;
            }
        }

        public async Task DeleteCommentAsync(Guid commentId)
        {
            string requestUrl = $"{MerchantServiceBaseUrl}/comment/{commentId}";

            using (logger.BeginScope("DeleteCommentAsync({@requestUrl})", requestUrl))
            {
                logger.LogInformation($"Calling merchant service in order to delete comment with id: '{commentId}'.");

                var response = await client.DeleteAsync(requestUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogError("Error when calling merchant service, delete coment with id '{commentId}'. Error was {StatusCode} {@responseBody}",
                        commentId, (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
                logger.LogInformation($"Deleted comment with id '{commentId}'.");
            }
        }

        public async Task UpdateSalesIdAsync(string initialSalesId, string updatedSalesId)
        {
            string merchantServiceUrl = $"{MerchantServiceBaseUrl}/salesId";
            var requestBody = new StringContent(JsonConvert.SerializeObject(new { initialSalesId, updatedSalesId }), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateSalesIdAsync({@merchantServiceUrl})", merchantServiceUrl))
            {
                logger.LogInformation($"Calling merchant service to update salesId for all merchants with salesId '{initialSalesId}' to '{updatedSalesId}'.");

                var response = await client.PutAsync(merchantServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to update merchant salesId . Error was {StatusCode} {@responseBody}",
                        (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }
            }
        }

        public async Task UpdateMerchantSalesIdByLead(Guid leadId, string salesId)
        {
            string merchantServiceUrl = $"{MerchantServiceBaseUrl}/merchant/lead/salesId";
            var requestBody = new StringContent(JsonConvert.SerializeObject(new { leadId, salesId }), Encoding.UTF8, "application/json");

            using (logger.BeginScope("UpdateSalesIdAsync({@merchantServiceUrl})", merchantServiceUrl))
            {
                logger.LogInformation($"Calling merchant service to update salesId for merchant with leadId: {leadId}" +
                                      $" with salesId '{salesId}'.");

                var response = await client.PostAsync(merchantServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to update merchant salesId . " +
                                       "Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }
            }
        }

        public async Task PatchMerchantByLeadIdAsync(Guid leadId, JsonPatchDocument<Merchant> merchantPatch)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/Merchant/lead/{leadId}";

            var body = new StringContent(JsonConvert.SerializeObject(merchantPatch), Encoding.UTF8, "application/json");

            using (logger.BeginScope("PatchMerchantAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to patch merchant with leadId '{@leadId}'.", leadId);

                var response = await client.PatchAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to patch merchant with leadId '{@leadId}'." +
                                       " Error was {StatusCode} {@responseBody}", leadId, (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
            }
        }

        public async Task ValidateLicenseIdIsUsedAsync(Guid? merchantId, string? businessType, string? registrationNumber = null, string? municipalityLicense = null, string? legalId = null)
        {
            if (!IsLicenseIdIsUsedValidateRequestValid(!merchantId.HasValue, string.IsNullOrWhiteSpace(businessType), string.IsNullOrWhiteSpace(registrationNumber), string.IsNullOrWhiteSpace(municipalityLicense), string.IsNullOrWhiteSpace(legalId)))
            {
                return;
            }

            var requestUri = ($"{MerchantServiceBaseUrl}{MerchantValidateIfLicenseIdUsedEndpoint}?" +
                             $"{(merchantId.HasValue ? $"merchantId={merchantId.Value}&" : string.Empty)}" +
                             $"{(businessType != null ? $"businessType={businessType}&" : string.Empty)}" +
                             $"{(registrationNumber != null ? $"registrationNumber={registrationNumber}&" : string.Empty)}" +
                             $"{(municipalityLicense != null ? $"municipalityLicense={municipalityLicense}&" : string.Empty)}" +
                             $"{(legalId != null ? $"legalId={legalId}" : string.Empty)}").Trim('&');

            using (logger.BeginScope("ValidateLicenseIdIsUsedAsync({uri})", requestUri))
            {
                logger.LogInformation($"Calling merchant service to validate license id is not used");

                var response = await client.GetAsync(requestUri);

                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to check if validate if license id is used. Error was {StatusCode} {@responseBody}", response.StatusCode, @responseBody);
                    throw new PassthroughException(response);
                }

                var checkResult = Json.Deserialize<IsLicenseIdUsedRespone>(responseBody);

                if (checkResult.Used)
                {
                    var licenseNumber = registrationNumber ?? municipalityLicense ?? legalId;

                    logger.LogWarning("License number {licenseNumber} for {businessType} already exists.", licenseNumber, businessType);
                    throw new ServiceException(System.Net.HttpStatusCode.BadRequest, Errors.LicenseIdExistsError);
                }
            }
        }

        private static bool IsLicenseIdIsUsedValidateRequestValid(bool merchantIdIsNull, bool businessTypeIsNull, bool registrationNumberIsNull, bool municipalityLicenseIsNull, bool legalIdIsNull)
        {
            var licensesAreNull = registrationNumberIsNull && municipalityLicenseIsNull && legalIdIsNull;
            if (businessTypeIsNull && licensesAreNull)
            {
                return false;
            }

            if (!businessTypeIsNull && merchantIdIsNull && licensesAreNull)
            {
                return false;
            }

            if (!licensesAreNull && businessTypeIsNull && merchantIdIsNull)
            {
                return false;
            }

            return true;
        }

        private void SendMessageToActiveCampaign(Guid merchantId, string onboardingStatus)
        {
            try
            {
                var counterParty = counterpartyProvider.GetCode();

                var activeCampaignRequest = new CreateActiveCampaignRequest
                {
                    LeadId = null,
                    UserId = null,
                    MerchantId = merchantId,
                    Language = null,
                    CounterParty = counterParty,
                    OnboardingStatus = onboardingStatus
                };

                activeCampaignService.SendActiveCampaignRequest(activeCampaignRequest);
            }
            catch (Exception ex)
            {
                logger.LogError($"Error when sending active campaign message to RabbitMQ, the error was {@ex}",
                    ex.Message);
            }
        }

        private MerchantSearchResponse<MerchantApiResult> Map(MerchantSearchResponse<MerchantSearchResult> coreResponse)
        {
            var result = new MerchantSearchResponse<MerchantApiResult>()
            {
                ReturnedRecordCount = coreResponse.ReturnedRecordCount,
                TotalRecordCount = coreResponse.TotalRecordCount
            };

            var records = coreResponse.Records
                .Select(m =>
                {
                    var prods = GetMerchantProductsList(m.Orders);

                    var apiResult = mapper.Map<MerchantApiResult>(m);
                    if (apiResult != null)
                    {
                        apiResult.Products = prods;
                    }

                    return apiResult;
                });

            result.Records = records.OfType<MerchantApiResult>().ToList();
            return result;
        }

        private static List<MerchantProduct> GetMerchantProductsList(List<OrderResponse> orders)
        {
            return orders
                .SelectMany(order => order.OrderItem ?? new List<OrderItemResponse>())
                .GroupBy(oi => oi.ProductCode)
                .Select(g => new MerchantProduct()
                {
                    Code = g.Key,
                    Quantity = g.Count(),
                    Categories = g.FirstOrDefault()?.OrderItemCategories.Select(c => c.CategoryCode).ToList()
                }).ToList();
        }
        public async Task ValidateMerchantBusinessInformation(Guid merchantId, JsonPatchDocument<PatchMerchantRequest> patchDocument)
        {
            var merchantBusiness = await merchantClient.GetMerchantBusinessInformationAsync(new Guid[] { merchantId });
            new ValidationHelpers().Validate(merchantBusiness.First(), new MerchantBusinessInformationValidator(), logger, "Merchant business validation faild!");
        }
        private async Task ValidateCompanyCheckRules(Guid merchantId, string? currentMerchantStatus, string? newMerchantStatus)
        {
            var merchantChecks = (await merchantClient.GetChecksForMerchant(merchantId)).ToList();

            if (merchantChecks.Count < 4)
            {
                logger.LogError(
                        "Error when trying to update merchant status from {currentMerchantStatus} to {newMerchantStatus} for merchant with id {merchantId}. Not all associated tasks are done.",
                        currentMerchantStatus, newMerchantStatus, merchantId);
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.RequiredMerchantCheck);
            }

            switch (currentMerchantStatus, newMerchantStatus)
            {
                case (MerchantStatus.BoardingCompleted, MerchantStatus.ComplianceApproval):
                    await RulesForMerchantUnderWritingToRiskAndComplianceApproval(merchantChecks);
                    break;
                case (MerchantStatus.BoardingCompleted, MerchantStatus.RiskApproval):
                    await RulesForMerchantUnderWritingToRiskAndComplianceApproval(merchantChecks);
                    break;
                case (MerchantStatus.BoardingCompleted, MerchantStatus.Verified):
                    await RulesForMerchantUnderWritingToVerified(merchantChecks);
                    await RuleForVerifiedStatus(merchantId, false);
                    break;
                case (MerchantStatus.ComplianceApproval, MerchantStatus.RiskApproval):
                    await RulesForComplianceToRiskApproval(merchantId, merchantChecks);
                    break;
                case (MerchantStatus.ComplianceApproval, MerchantStatus.Verified):
                    await RulesForComplianceApprovalToVerified(merchantChecks);
                    await RuleForVerifiedStatus(merchantId, false);
                    break;
                case (MerchantStatus.RiskApproval, MerchantStatus.Verified):
                    await RulesForRiskApprovalToVerified(merchantChecks);
                    break;
            }

        }
        private static Task RulesForMerchantUnderWritingToVerified(List<MerchantCheck> merchantChecks)
        {
            foreach (var check in merchantChecks)
            {
                if (check.CheckType == CheckType.BankCheck && !check.CheckStatus.Equals(CheckStatus.BankCheckPassed) ||
                    check.CheckType == CheckType.MatchCheck && (check.CheckStatus.Equals(CheckStatus.MatchCheckPending) || check.CheckStatus.Equals(CheckStatus.MatchCheckPositiveHit)) ||
                    check.CheckType == CheckType.RiskScoring && (check.CheckStatus.Equals(CheckStatus.RiskScoringProhibited) || check.CheckStatus.Equals(CheckStatus.RiskScoringPending)) ||
                    check.CheckType == CheckType.WorldCheckOne && (check.CheckStatus.Equals(CheckStatus.WorldCheckOneKYCPending) || check.CheckStatus.Equals(CheckStatus.WorldCheckOneKYCPositiveHit)))
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.MerchantCheckMustPass);
                }
            }

            return Task.CompletedTask;
        }
        private static Task RulesForMerchantUnderWritingToRiskAndComplianceApproval(List<MerchantCheck> merchantChecks)
        {
            foreach (var check in merchantChecks)
            {
                if (check.CheckType == CheckType.MatchCheck && (check.CheckStatus.Equals(CheckStatus.MatchCheckPending) || check.CheckStatus.Equals(CheckStatus.MatchCheckPositiveHit)) ||
                    check.CheckType == CheckType.RiskScoring && (check.CheckStatus.Equals(CheckStatus.RiskScoringProhibited) || check.CheckStatus.Equals(CheckStatus.RiskScoringPending)) ||
                    check.CheckType == CheckType.WorldCheckOne && (check.CheckStatus.Equals(CheckStatus.WorldCheckOneKYCPending) || check.CheckStatus.Equals(CheckStatus.WorldCheckOneKYCPositiveHit)))
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.MerchantCheckMustPass);
                }
            }

            return Task.CompletedTask;
        }

        private async Task RulesForComplianceToRiskApproval(Guid merchantId, List<MerchantCheck> merchantChecks)
        {
           var isRiskApprovalForTransactionType = await RuleForVerifiedStatus(merchantId, true);

            foreach (var check in merchantChecks)
            {
                if (check.CheckType == CheckType.RiskScoring && (!check.CheckStatus.Equals(CheckStatus.RiskScoringHigh) && !check.CheckStatus.Equals(CheckStatus.RiskScoringVeryHigh)) && !isRiskApprovalForTransactionType)
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.RiskScoringNotHigh);
                }
            }
        }
        private static Task RulesForComplianceApprovalToVerified(List<MerchantCheck> merchantChecks)
        {
            foreach (var check in merchantChecks)
            {
                if (check.CheckType == CheckType.RiskScoring && (!check.CheckStatus.Equals(CheckStatus.RiskScoringLow) && !check.CheckStatus.Equals(CheckStatus.RiskScoringMedium)) ||
                    (check.CheckType == CheckType.BankCheck && !check.CheckStatus.Equals(CheckStatus.BankCheckPassed)))
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.ViolationOfBankAndRiskCheckRule);
                }
            }

            return Task.CompletedTask;
        }

        private static Task RulesForRiskApprovalToVerified(List<MerchantCheck> merchantChecks)
        {
            foreach (var check in merchantChecks)
            {
                if (check.CheckType == CheckType.BankCheck && !check.CheckStatus.Equals(CheckStatus.BankCheckPassed))
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.MerchantBankCheckMustPass);
                }
            }

            return Task.CompletedTask;
        }

        private async Task ValidateKycCheckStatus(Guid merchantId, string? currentMerchantStatus)
        {
            var shareholderIndividual = await shareholderService.GetMerchantIndividualsAsync(merchantId);

            switch (currentMerchantStatus)
            {
                case (MerchantStatus.BoardingCompleted):
                    await RulesForMerchantUnderWritingKYCCheck(shareholderIndividual);
                    break;
            }
        }

        private static Task RulesForMerchantUnderWritingKYCCheck(List<MerchantShareholderIndividual> merchantShareholderIndividuals)
        {
            foreach (var individual in merchantShareholderIndividuals)
            {
                if (individual.KYCCheck != KycCheckStatus.Approved)
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.ShareholderIndividualKYCCheckMustPass);
                }
            }
            return Task.CompletedTask;
        }

        //GDADV005-70298: Rule for moving merchant from compliance approval or merchant underwriting status to verified
        //when a specific TransactionTypes are included in account level
        private async Task<bool> RuleForVerifiedStatus(Guid merchantId, bool checkRiskApprovalTransactionType)
        {            
            var stores = await merchantClient.GetStoresAsync(merchantId);
            foreach (var store in stores)
            {
                var channelType = store.MerchantDetails?.ChannelType;
                var riskApprovalTransactionTypes = channelType switch
                {
                    ChannelType.CardPresent => RiskApprovalPosTransactionTypes,
                    ChannelType.CardNotPresent => RiskApprovalEcomTransactionTypes,
                    _ => Enumerable.Empty<int>()
                };

                if (store?.AccountConfig?.TransactionType == ",0")
                {
                    store.AccountConfig.TransactionType = store?.AccountConfig?.TransactionType.Replace(",", "");
                }

                if (string.IsNullOrEmpty(store?.AccountConfig?.TransactionType))
                {
                    continue;
                }                

                var transactionTypes = store.AccountConfig.TransactionType
                    .Split(',')
                    .Select(int.Parse)
                    .ToArray();                         

                if (riskApprovalTransactionTypes.Any(transactionTypes.Contains) && !checkRiskApprovalTransactionType)
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.RiskOfficerApprovalRequired);
                }
                else if (riskApprovalTransactionTypes.Any(transactionTypes.Contains) && checkRiskApprovalTransactionType)
                {
                    return true;                     
                }
            }
            return false;
        }

        private static string? HandleChannelTypeValue(string? productType, string? productCode)
        {
            if (productType == ProductType.Terminal && productCode == ProductCode.SoftPos)
            {
                return TerminalChannelType.Softpos;
            }

            if ((productCode != ProductCode.SoftPos && productType == ProductType.Terminal)
                 || productType == ProductType.Mpos)
            {
                return TerminalChannelType.Smartpos;
            }

            return null;
        }

        private static string? HandleConnectionTypeValue(string? productType, string? productCode)
        {
            if (productType == ProductType.Terminal && productCode == ProductCode.SoftPos)
            {
                return TerminalConnectionType.MPGS;
            }

            if ((productCode != ProductCode.SoftPos && productType == ProductType.Terminal)
                 || productType == ProductType.Mpos)
            {
                return TerminalConnectionType.HostToHost;
            }

            return null;
        }


        public async Task<(ComplianceRiskModel, AcquiringRiskModel)> CalculateRisk(CompanyDetails companyDetails)
        {
            string url = $"{MerchantServiceBaseUrl}/merchant/riskComponent{RiskCalculationEndPoint}";

            using (logger.BeginScope("PatchMerchantAsync({@merchantServiceUrl})", url))
            {
                logger.LogInformation($"Calling merchant service to Post merchant.");

                var requestBody = new StringContent(JsonConvert.SerializeObject(companyDetails), Encoding.UTF8, "application/json");

                var response = await client.PostAsync (url, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to patch . Error was {StatusCode} {@responseBody}",
                         (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var riskResponse = JsonConvert.DeserializeObject<RiskResponseModel>(responseBody);

                if (riskResponse?.ComplianceRisk == null || riskResponse.AcquiringRisk == null)
                {
                    logger.LogCritical("Received null risk components from the service.");
                    throw new InvalidOperationException("Risk components cannot be null.");
                }

                var complianceRisk = riskResponse.ComplianceRisk;
                var acquiringRisk = riskResponse.AcquiringRisk;

                return (complianceRisk, acquiringRisk);
            }
        }

        public async Task<CalculatedRisk> GetRiskCalculation(string businessId)
        {
            string serviceUrl = $"{MerchantServiceBaseUrl}/merchant/riskComponent/Merchant/GetRiskCalculation/{businessId}";

            using (logger.BeginScope("GetContactDetailsAsync({@merchantServiceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant API to get contact by id");

                var response = await client.GetAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant API to get contact by id. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }


                CalculatedRisk calculatedRisk = Json.Deserialize<CalculatedRisk>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                return calculatedRisk;
            }
        }
    }
}