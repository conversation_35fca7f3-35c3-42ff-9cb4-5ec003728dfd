﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Chain;

public class ChainContactsCreateRequest
{
    public string? ChainId { get; set; }
    public List<ChainContactModel> ChainContacts { get; set; } = new List<ChainContactModel>();
}

public class ChainContactModel
{
    public Guid ChainContactId { get; set; }

    public string? Email { get; set; }

    public string? CountryPrefix { get; set; }

    public string? PhoneNumber { get; set; }
    public string? ContactName { get; set; }
    public string? RoleDesignation { get; set; }
    public bool IsSpoc { get; set; } = false;
}