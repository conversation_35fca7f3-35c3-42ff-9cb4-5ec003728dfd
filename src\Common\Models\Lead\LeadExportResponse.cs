﻿using System;

namespace Common.Models.Lead
{
    public class LeadExportResponse
    {
        public Guid LeadId { get; set; }
        public string? LeadStatus { get; set; }
        public string? PhoneNumberWithPrefix { get; set; }
        public string? OwnerEmail { get; set; }
        public string? OwnerFirstName { get; set; }
        public string? OwnerLastName { get; set; }
        public string? OwnerFirstNameAr { get; set; }
        public string? OwnerLastNameAr { get; set; }
        public string? SalesId { get; set; }
        public string? SalesFirstName { get; set; }
        public string? SalesLastName { get; set; }
        public string? SalesPartnerId { get; set; }
        public string? UTM { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? ReferralChannel { get; set; }
        public string? Product { get; set; }
    }
}
