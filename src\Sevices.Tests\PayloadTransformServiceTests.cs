﻿using Common.Services;
using NUnit.Framework;
using Services;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class PayloadTransformServiceTests
    {
        private const string Xml =
            @"<?xml version=""1.0"" encoding=""UTF-8""?>
<saml2p:Response xmlns:saml2p=""urn:oasis:names:tc:SAML:2.0:protocol"">
    <saml2:Issuer Format=""urn:oasis:names:tc:SAML:2.0:nameid-format:entity"" xmlns:saml2=""urn:oasis:names:tc:SAML:2.0:assertion"">https://www.iam.gov.sa/samlsso</saml2:Issuer>
    <saml2p:Status>
        <saml2p:StatusCode Value = ""urn:oasis:names:tc:SAML:2.0:status:Success"" />
    </saml2p:Status>
    <saml2:Assertion ID=""emofkocnfoffnnabhebfcpdinagncigchdmpneie"" IssueInstant=""2018-11-25T15:07:15.187Z"" Version=""2.0"" xmlns:saml2=""urn:oasis:names:tc:SAML:2.0:assertion"" xmlns:xs=""http://www.w3.org/2001/XMLSchema"">
        <saml2:AttributeStatement>
            <saml2:Attribute Name=""http://iam.gov.sa/claims/englishFirstName"" NameFormat=""urn:oasis:names:tc:SAML:2.0:attrname-format:basic"">
                <saml2:AttributeValue xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" xsi:type=""xs:string"">MEZNAH</saml2:AttributeValue>
            </saml2:Attribute>
        <saml2:Attribute Name=""http://iam.gov.sa/claims/arabicFirstName"" NameFormat=""urn:oasis:names:tc:SAML:2.0:attrname-format:basic"">
                <saml2:AttributeValue xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" xsi:type=""xs:string"">HANZEM</saml2:AttributeValue>
            </saml2:Attribute>
        </saml2:AttributeStatement>
    </saml2:Assertion>
</saml2p:Response>";

        private const string Json =
            @"{
	            ""crName"": ""شركة جيديا للتقنية شركة شخص واحد"",
                ""crNumber"": 1010332533,
                ""issueDate"": ""1433/04/26"",
                ""crMainNumber"": null,
                ""businessType"": {
                    ""id"": ""204"",
                    ""name"": ""ذات مسئوليةمحدودة""
                },
                ""fiscalYear"": null,
                ""status"": {
                    ""id"": ""active"",
                    ""name"": ""السجل التجاري قائم"",
                    ""nameEn"": ""Some english name""
                },
                ""activities"": {
                    ""description"": ""تجارة الجملةوالتجزئة في اجهزة التكنولوجيا والآلات والملابس"",
                    ""isic"": []
                }
             }";

        private const string JsonArray =
            @"[
                {
                    ""name"":""الاسم العربي"",
                    ""birthDate"":null,
                    ""sharesCount"":39000,
                    ""identity"":{
                        ""id"":""1010123456"",
                        ""type"":""crno""
                    },
                    ""nationality"":{
                        ""id"":""SA"",
                        ""name"":""سعودي""
                    }
                },
                {
                    ""name"":""John Doe"",
                    ""birthDate"":""1981/01/25"",
                    ""nationality"":{
                        ""id"":""GB"",
                        ""name"":""British""
                    }
                }
            ]";

        private IPayloadTransformService payloadTransformService = null!;

        [SetUp]
        public void Setup()
        {
            payloadTransformService = new PayloadTransformService();
        }

        [Test]
        public void GetIamAttributesReturnsCorrectResult()
        {
            var attributesObject = payloadTransformService.GetXmlObject(Xml);

            Assert.That(attributesObject, Is.Not.Null);
            AssertThatAttributesAreTheSameAsXml(attributesObject);
        }

        [Test]
        public void GetJsonAttributesReturnsCorrectResult()
        {
            dynamic[] attributesObject= payloadTransformService.GetJsonObjects(Json);

            Assert.That(attributesObject, Is.Not.Null);
            AssertThatAttributesAreTheSameAsJson(attributesObject);
        }

        [Test]
        public void GetJsonAttributesReturnsCorrectResultWhenJsonIsArray()
        {
            dynamic[] attributesObject = payloadTransformService.GetJsonObjects(JsonArray);

            Assert.That(attributesObject, Is.Not.Null);
            AssertThatAttributesAreTheSameAsJsonArray(attributesObject);
        }

        private static void AssertThatAttributesAreTheSameAsXml(dynamic attributesObject)
        {
            Assert.That(attributesObject.englishFirstName, Is.EqualTo("MEZNAH"));
            Assert.That(attributesObject.arabicFirstName, Is.EqualTo("HANZEM"));
        }

        private static void AssertThatAttributesAreTheSameAsJson(dynamic[] attributesObject)
        {
            Assert.That(attributesObject[0].crName, Is.EqualTo("شركة جيديا للتقنية شركة شخص واحد"));
            Assert.That(attributesObject[0].crNumber, Is.EqualTo("1010332533"));
            Assert.That(attributesObject[0].issueDate, Is.EqualTo("1433/04/26"));
            
            Assert.That(attributesObject[0].crMainNumber, Is.EqualTo(string.Empty));
            Assert.That(attributesObject[0].businessType_id, Is.EqualTo("204"));
            Assert.That(attributesObject[0].businessType_name, Is.EqualTo("ذات مسئوليةمحدودة"));

            Assert.That(attributesObject[0].fiscalYear, Is.EqualTo(string.Empty));
            Assert.That(attributesObject[0].status_id, Is.EqualTo("active"));
            Assert.That(attributesObject[0].status_name, Is.EqualTo("السجل التجاري قائم"));

            Assert.That(attributesObject[0].status_nameEn, Is.EqualTo("Some english name"));
            Assert.That(attributesObject[0].activities_description, Is.EqualTo("تجارة الجملةوالتجزئة في اجهزة التكنولوجيا والآلات والملابس"));
            Assert.That(attributesObject[0].activities_isic, Is.EqualTo("[]"));
        }

        private static void AssertThatAttributesAreTheSameAsJsonArray(dynamic[] attributes)
        {
            Assert.That(attributes[0].name, Is.EqualTo("الاسم العربي"));
            Assert.That(attributes[0].birthDate, Is.EqualTo(string.Empty));
            Assert.That(attributes[0].sharesCount, Is.EqualTo("39000"));
            Assert.That(attributes[0].identity_id, Is.EqualTo("1010123456"));
            Assert.That(attributes[0].identity_type, Is.EqualTo("crno"));
            Assert.That(attributes[0].nationality_id, Is.EqualTo("SA"));
            Assert.That(attributes[0].nationality_name, Is.EqualTo("سعودي"));

            Assert.That(attributes[1].name, Is.EqualTo("John Doe"));
            Assert.That(attributes[1].birthDate, Is.EqualTo("1981/01/25"));
            Assert.That(attributes[1].nationality_id, Is.EqualTo("GB"));
            Assert.That(attributes[1].nationality_name, Is.EqualTo("British"));
        }
    }
}
