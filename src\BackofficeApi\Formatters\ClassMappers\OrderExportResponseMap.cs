﻿using Common.Models.Checkout;
using CsvHelper.Configuration;

namespace BackofficeApi.Formatters.ClassMappers
{
    public class OrderExportResponseMap : ClassMap<OrderExportResponse>
    {
        public OrderExportResponseMap()
        {
            Map(m => m.OrderNumber).Name("Order Number");
            Map(m => m.OrderStatus).Name("Order Status");
            Map(m => m.MerchantStatus).Name("Merchant Status");
            Map(m => m.ProjectName).Name("Project Name");
            Map(m => m.ReferralChannel).Name("Referral Channel");
            Map(m => m.FirstName).Name("First Name EN");
            Map(m => m.LastName).Name("Last Name EN");
            Map(m => m.FirstNameAr).Name("First Name AR");
            Map(m => m.LastNameAr).Name("Last Name AR");
            Map(m => m.NationalId).Name("Emirates ID");
            Map(m => m.CommercialRegistration).Name("License ID");
            Map(m => m.UnifiedId).Name("Unified ID");
            Map(m => m.LeadCreationDate).Name("Lead Creation Date");
            Map(m => m.CheckoutDate).Name("Order Date");
            Map(m => m.City).Name("City - Onboarding");           
            Map(m => m.Area).Name("Area - Onboarding");           
            Map(m => m.MerchantName).Name("Merchant Name Arabic");           
            Map(m => m.CityCR).Name("City - CR");           
            Map(m => m.AddressCR).Name("Address - CR");
            Map(m => m.BankCheckStatus).Name("Bank Check Status");
            Map(m => m.IBAN).Name("IBAN");            
            Map(m => m.AccountHolderName).Name("Account Holder Name");            
            Map(m => m.Phone).Name("Phone No.");            
            Map(m => m.Email).Name("Email");
            Map(m => m.MCC).Name("MCC");
            Map(m => m.TransactionType).Name("Transaction Type");
            Map(m => m.AcceptedPaymentMethods).Name("Accepted Payment Methods");


            Map(m => m.OrderId).Ignore();            
            Map(m => m.UserId).Ignore();            
            Map(m => m.MerchantId).Ignore();
            Map(m => m.StoreId).Ignore();
            Map(m => m.OrderItem).Ignore();
            Map(m => m.NationalName).Ignore();
        }
    }
}
