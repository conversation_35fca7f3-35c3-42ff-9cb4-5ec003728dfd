﻿using Common;
using Common.Models;
using Common.Models.Product;
using FluentAssertions;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class GsdkHelperServiceTests
    {
        private GsdkHelperService gsdkHelperService = null!;
        private readonly ILogger<GsdkHelperService> logger = Substitute.For<ILogger<GsdkHelperService>>();

        private static string geideaLedger = "DEFAULT_BANK";
        private static string geideaLedgerSABB = "SABB";
        private static string gsdkLedger = "RIYAD-LEDGER";
        private static string gsdkLedgerSABB = "SABB-LEDGER";
        private static string gwayProduct = "GWAY";

        private static Catalogue[] catalogues = new[] {
        new Catalogue { Key = geideaLedger, Value = gsdkLedger, CatalogueName = Constants.Catalogues.AcquiringLedgerToGsdkMapping},
        new Catalogue { Key = geideaLedgerSABB, Value = gsdkLedgerSABB, CatalogueName = Constants.Catalogues.AcquiringLedgerToGsdkMapping},
        new Catalogue { Key = gwayProduct, Value = geideaLedger, CatalogueName = Constants.Catalogues.ProductTypeDefaultLedgerMapping}
    };

        [SetUp]
        public void Setup()
        {
            gsdkHelperService = new GsdkHelperService(logger);
        }

        [Test]
        public void GetLedgerKey_WhenNoProviderBank_ShouldThrowException()
        {
            var productInstance = new ProductInstance
            {
                Data = JsonSerializer.Serialize(new TerminalData()),
                Product = new ProductShortResponse
                {
                    Type = "TERMINAL",
                    Code = "SOFT_POS"
                }
            };

            gsdkHelperService.Invoking(x =>
                                 x.GetLedgerKey(productInstance, catalogues))
                             .Should().Throw<ServiceException>()
                             .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                                         x.ProblemDetails.Type == Errors.LedgerMappingNotFound.Code);
        }

        [Test]
        public void GetLedgerKey_WhenGatewayNoMapping_ShouldThrowException()
        {
            var productInstance = new ProductInstance
            {
                Data = JsonSerializer.Serialize(new TerminalData()),
                Product = new ProductShortResponse
                {
                    Type = "GWAY",
                    Code = "SOFT_POS"
                }
            };

            gsdkHelperService.Invoking(x =>
                                 x.GetLedgerKey(productInstance, Array.Empty<Catalogue>()))
                             .Should().Throw<ServiceException>()
                             .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                                         x.ProblemDetails.Type == Errors.LedgerMappingNotFound.Code);
        }

        [Test]
        public void GetLedgerKey_WhenProviderBank_ShouldReturnKey()
        {
            var productInstance = new ProductInstance
            {
                Data = JsonSerializer.Serialize(new TerminalData
                {
                    ProviderBank = geideaLedger
                }),
                Product = new ProductShortResponse
                {
                    Type = "TERMINAL",
                    Code = "SOFT_POS"
                }
            };

            var ledger = gsdkHelperService.GetLedgerKey(productInstance, catalogues);
            ledger.Should().Be(geideaLedger);
        }

        [Test]
        public void GetLedgerKey_WhenNoProviderBankAndGateway_ShouldReturnDefaultKey()
        {
            var productInstance = new ProductInstance
            {
                Data = JsonSerializer.Serialize(new TerminalData
                {
                    ProviderBank = geideaLedger
                }),
                Product = new ProductShortResponse
                {
                    Type = "GWAY",
                    Code = "SOFT_POS"
                }
            };

            var ledger = gsdkHelperService.GetLedgerKey(productInstance, catalogues);
            ledger.Should().Be(geideaLedger);
        }

        [Test]
        public void GetLedgerValue_WhenAquiringLedgerToGsdkMappingNotFound_ShouldThrowException()
        {
            var productInstance = new ProductInstance
            {
                Data = JsonSerializer.Serialize(new TerminalData
                {
                    ProviderBank = geideaLedger
                }),
                Product = new ProductShortResponse
                {
                    Type = "TERMINAL",
                    Code = "SOFT_POS"
                }
            };

            gsdkHelperService.Invoking(x =>
                                 x.GetLedgerValue(Array.Empty<Catalogue>(), productInstance))
                             .Should().Throw<ServiceException>()
                             .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                                         x.ProblemDetails.Type == Errors.LedgerMappingNotFound.Code);
        }

        [Test]
        public void GetLedgerValue_WhenMappingFound_ShouldReturnKeyAndValue()
        {
            var productInstance = new ProductInstance
            {
                Data = JsonSerializer.Serialize(new TerminalData
                {
                    ProviderBank = geideaLedger
                }),
                Product = new ProductShortResponse
                {
                    Type = "TERMINAL",
                    Code = "SOFT_POS"
                }
            };

            var ledger  = gsdkHelperService.GetLedgerValue(catalogues, productInstance);
            ledger.Should().Be(gsdkLedger);
        }

    }
}
