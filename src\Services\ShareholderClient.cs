﻿using Common.Models.Shareholder;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Services
{
    public class ShareholderClient : IShareholderClient
    {
        private readonly ILogger<ShareholderClient> logger;
        private readonly HttpClient client;
        private readonly IOptionsMonitor<UrlSettings> urlSettingsOptions;

        private string MerchantServiceShareholderBaseUrl => $"{urlSettingsOptions.CurrentValue.MerchantServiceBaseUrlNS}/api/v1/shareholder";

        public ShareholderClient(ILogger<ShareholderClient> logger,
        HttpClient client,
        IOptionsMonitor<UrlSettings> urlSettingsOptions)
        {
            this.logger = logger;
            this.client = client;
            this.urlSettingsOptions = urlSettingsOptions;
        }

        public async Task<ShareholderCompanyResponse> CreateShareholderCompanyAsync(ShareholderCompanyCreateRequest request)
        {
            var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/shareholdercompanies/create";

            using (logger.BeginScope("CreateShareholderCompanyAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to create Shareholder Company");

                var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

                var response = await client.PostAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    if (responseBody != null)
                        return Json.Deserialize<ShareholderCompanyResponse>(responseBody);
                    else
                        return new ShareholderCompanyResponse();
                }
                else
                {
                    logger.LogCritical("Error when calling merchant Service to Create Shareholder Company for merchant." +
                                       " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
            }
        }

        public async Task DeleteShareholderCompanyAsync(Guid shareholderCompanyId)
        {
            var serviceUrl = $"{MerchantServiceShareholderBaseUrl}shareholdercompanies/delete/{shareholderCompanyId}";

            using (logger.BeginScope("DeleteShareholderCompanyAsync({@shareholderCompanyId})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to delete the shareholder company with {@shareholderCompanyId}.", shareholderCompanyId);

                var response = await client.DeleteAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to delete shareholder company. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
            }
        }

        public async Task<ShareholderIndividualResponse> CreateShareholderIndividualAsync(ShareholderIndividualCreateRequest request)
        {
            var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individual";

            using (logger.BeginScope("CreateShareholderIndividualAsync({@serviceUrl})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to create Shareholder individual.");

                var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

                var response = await client.PostAsync(serviceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    if (responseBody != null)
                        return Json.Deserialize<ShareholderIndividualResponse>(responseBody);
                    else
                        return new ShareholderIndividualResponse();
                }
                else
                {
                    logger.LogCritical("Error when calling merchant Service to Create Shareholder Individual." +
                                       " Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
            }
        }

        public async Task DeleteShareholderIndividualAsync(Guid shareholderIndividualId)
        {
            var serviceUrl = $"{MerchantServiceShareholderBaseUrl}/individual/{shareholderIndividualId}";

            using (logger.BeginScope("DeleteShareholderIndividualAsync({@shareholderIndividualId})", serviceUrl))
            {
                logger.LogInformation("Calling merchant service to delete the shareholder individual with {@shareholderIndividualId}.", shareholderIndividualId);

                var response = await client.DeleteAsync(serviceUrl);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling merchant service to delete shareholder individual. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }
            }
        }
    }
}
