﻿using Common.Tests.InputModels;
using FluentValidation.TestHelper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;

namespace Common.Tests;

public class ShareholderCompanyPatchRequestValidatorTests
{
    [TestCase(Constants.CounterParty.Egypt)]
    [TestCase(Constants.CounterParty.Saudi)]
    [TestCase("another")]
    public void ValidationShouldPass(string counterparty)
    {  
        var request = ShareholderCompanyRequestInputHelper.GetValidJsonPatchRequestByCounterparty(counterparty);
        var result = new ShareholderCompanyJsonPatchDocumentRequestValidator(counterparty).TestValidate(request);

        Assert.Multiple(() =>
        {
            Assert.That(result.IsValid, Is.True);
            Assert.That(result.Errors, Is.Empty);
        });
    }


    [Test, TestCaseSource(nameof(InvalidRequestData))]
    public void ValidationShouldFail(JsonPatchDocument<MerchantShareholderCompanyResponse> request, string counterparty, (string Message, string Code) expectedError)
    {
        var result = new ShareholderCompanyJsonPatchDocumentRequestValidator(counterparty).TestValidate(request);

        Assert.That(result.IsValid, Is.False);
        result.ShouldHaveAnyValidationError()
            .WithErrorMessage(expectedError.Message)
            .WithErrorCode(expectedError.Code);
    }

    private static IEnumerable<TestCaseData> InvalidRequestData()
    {
        var jsonPatchDocument = new JsonPatchDocument<MerchantShareholderCompanyResponse>();
        jsonPatchDocument.Operations.Add(new Operation<MerchantShareholderCompanyResponse>()
        {
            op = "replace",
            path = nameof(MerchantShareholderCompanyResponse.ShareholderCompany.ShareholderCompanyAddress.Address),
            value = "invalid"
        });
        yield return new TestCaseData(
            jsonPatchDocument, Constants.CounterParty.Saudi, Errors.InvalidPatch
        );
        yield return new TestCaseData(
           new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.CompanyName, ""),
           Constants.CounterParty.Saudi, Errors.ShareholderCompanyNameLength
        );
        yield return new TestCaseData(
           new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.CompanyLicense, ""),
           Constants.CounterParty.Saudi, Errors.InvalidShareholderCompanyLicenseLength
        );
        yield return new TestCaseData(
           new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.LicenseExpiryDate, DateTime.MinValue),
           Constants.CounterParty.Saudi, Errors.InvalidShareholderCompanyLicenseExpiryDate
        );
        yield return new TestCaseData(
           new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.IssueDate, DateTime.MinValue),
           Constants.CounterParty.Saudi, Errors.InvalidShareholderIssueDate
        );
        yield return new TestCaseData(
          new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.MccCode, "this is an invalid mcc code"),
          Constants.CounterParty.Saudi, Errors.ShareholderCompanyMccLengthValidation
       );
        yield return new TestCaseData(
          new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.OwnershipPercentage, 0),
          Constants.CounterParty.Saudi, Errors.InvalidShareholderCompanyOwnershipPercentage
       );
        yield return new TestCaseData(
          new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.OwnershipPercentage, 100),
          Constants.CounterParty.Saudi, Errors.InvalidShareholderCompanyOwnershipPercentage
       );
        yield return new TestCaseData(
              new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.PhoneNumber, "invalid"),
              Constants.CounterParty.Saudi, Errors.PhoneValidation
           );
        yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.PhoneNumber, ""),
             Constants.CounterParty.Saudi, Errors.PhoneValidation
          );
        yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.PhonePrefix, "invalid"),
             Constants.CounterParty.Saudi, Errors.InvalidCountryPrefix
          );
        yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.PhonePrefix, ""),
             Constants.CounterParty.Saudi, Errors.InvalidCountryPrefix
          );
        yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Country, ""),
             Constants.CounterParty.Saudi, Errors.CountryLengthValidation
          );
        yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Country, "invalid"),
             Constants.CounterParty.Saudi, Errors.CountryLengthValidation
         );
        yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.CompanyType, ""),
             Constants.CounterParty.Saudi, Errors.InvalidShareholderCompanyType
         );
         yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.CompanyType, "invalid"),
             Constants.CounterParty.Saudi, Errors.InvalidShareholderCompanyType
         );
         yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.CompanyType, "invalid"),
             Constants.CounterParty.Egypt, Errors.InvalidShareholderCompanyType
         );
        yield return new TestCaseData(
            new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Governorate, "Valid"),
            Constants.CounterParty.Saudi, Errors.DisabledProperty
        );
        yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Governorate, ""),
             Constants.CounterParty.Egypt, Errors.GovernorateLengthValidation
          );
         yield return new TestCaseData(
             new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.City, ""),
             Constants.CounterParty.Saudi, Errors.CityLengthValidation
          );
         yield return new TestCaseData(
            new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.City, ""),
            Constants.CounterParty.Egypt, Errors.CityLengthValidation
         );
         yield return new TestCaseData(
            new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.City, "valid"),
            "other counterparty", Errors.DisabledProperty
         );
         yield return new TestCaseData(
            new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Area, "valid"),
            "other counterparty", Errors.DisabledProperty
         );
         yield return new TestCaseData(
            new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.ShareholderCompanyAddress.Governorate, "valid"),
            "other counterparty", Errors.DisabledProperty
         );
         yield return new TestCaseData(
            new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.CompanyType, ""),
            "other counterparty", Errors.InvalidShareholderCompanyType
         );
         yield return new TestCaseData(
            new JsonPatchDocument<MerchantShareholderCompanyResponse>().Replace(x => x.ShareholderCompany.CompanyType, "any"),
            "other counterparty", Errors.InvalidShareholderCompanyType
         );
    }
}
