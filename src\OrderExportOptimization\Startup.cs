﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Common.Services;
using Common.Options;
using Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Geidea.Utils.Counterparty;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.HttpClientExtensions;

namespace OrderExportOptimization
{
    public class Startup
    {
        private readonly IConfiguration _configuration;

        public Startup(IConfiguration configuration)
        {
            _configuration = configuration;
        }
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddPollyHttpClientExternal<IDocumentService, DocumentService>(_configuration);
            services.AddPollyHttpClientExternal<IMerchantService, MerchantService>(_configuration);
            services.AddPollyHttpClientExternal<IUserService, UserService>(_configuration);
            services.AddPollyHttpClientExternal<ICheckoutService, CheckoutService>(_configuration);
            services.AddPollyHttpClientExternal<IProductService, ProductService>(_configuration);
            services.AddPollyHttpClientExternal<IOrderExportOptimizationService, OrderExportOptimizationService>(_configuration);

            services.AddTransient<IChecksService, ChecksService>();

            services.AddScoped<ICounterpartyProvider, CounterpartyProvider>();
            services.AddOptions<UrlSettings>().Bind(_configuration.GetSection("UrlSettings"));

            services.AddAutoMapper(typeof(Startup));

            services.BuildServiceProvider();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IOptionsMonitor<ApplicationOptions> appOptions, ILogger<Startup> logger)
        {
            app.UseMiddleware<Geidea.Utils.Logging.SerilogRequestLogger>();
            app.UseMiddleware<Geidea.Utils.Exceptions.ExceptionHandler>();
            app.UseMiddleware<CounterpartyHandler>();
        }

    }
}
