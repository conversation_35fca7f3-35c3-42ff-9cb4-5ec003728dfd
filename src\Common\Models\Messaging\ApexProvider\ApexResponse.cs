﻿using System;
using System.Diagnostics.CodeAnalysis;


namespace Common.Models.Messaging.ApexProvider
{
    [ExcludeFromCodeCoverage]
    public class ApexResponse
    {
        public string? BusinessId { get; set; }
        public string? MID { get; set; }
        public string? OrderId { get; set; }
        public string? OrderNumber { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? ErrorMessage { get; set; }
        public bool IsSuccess { get; set; }
        public string? Counterparty { get; set; }
    }
}