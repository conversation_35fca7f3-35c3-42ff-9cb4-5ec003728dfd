﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Common.Models.Match;

public class TerminationInquiry
{
    public int PageOffset { get; set; }

    public string Ref { get; set; }
        = string.Empty;

    public string TransactionReferenceNumber { get; set; }
        = string.Empty;

    [Display(Name = "Possible Merchant Matches")]
    public PossibleMerchantMatch[] PossibleMerchantMatches { get; set; }
        = Array.Empty<PossibleMerchantMatch>();
}
