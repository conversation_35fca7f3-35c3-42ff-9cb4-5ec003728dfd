﻿using Common;
using Common.Models.Tasks;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Geidea.Utils.Json;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services;

public class TaskService : ITaskService
{
    private readonly HttpClient client;
    private readonly ILogger<TaskService> logger;
    private readonly UrlSettings urlSettingsOptions;

    public TaskService(HttpClient client,
        IOptions<UrlSettings> urlSettingsOptions,
        ILogger<TaskService> logger)
    {
        this.client = client;
        this.urlSettingsOptions = urlSettingsOptions.Value;
        this.logger = logger;
    }

    private string TaskServiceUrl => $"{urlSettingsOptions.MerchantServiceBaseUrlNS}/api/v1/merchant/task";

    public async Task<TaskModel> UpdateTaskAsync(Guid taskId, JsonPatchDocument<TaskUpdateRequest> request)
    {
        using (logger.BeginScope("TaskService({taskServiceUrl})", TaskServiceUrl))
        {
            logger.LogInformation("Parsing provided document to check if it is necessary to update the assigneeId.");

            ApplyAssigneeUpdates(request);
            ApplyResolutionUpdates(request);

            logger.LogInformation("Calling task service to update task with id : {taskId}.", taskId);

            var body = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");
            var response = await client.PatchAsync($"{TaskServiceUrl}/{taskId}", body);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                logger.LogCritical("Error when calling task service to update task with id : {taskId}. Error was {StatusCode} {@responseBody}",
                    taskId, (int)response.StatusCode, responseBody);

                throw new PassthroughException(response);
            }

            var task = Json.Deserialize<TaskModel>(responseBody,
                new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

            return task;
        }
    }

    private void ApplyResolutionUpdates(JsonPatchDocument<TaskUpdateRequest> request)
    {
        var statusPatchOperation = request.Operations.SingleOrDefault(x =>
            x.path.ToLowerInvariant() == nameof(TaskUpdateRequest.Status).ToLowerInvariant());

        var resolutionPatchOperation = request.Operations.SingleOrDefault(x =>
            x.path.ToLowerInvariant() == nameof(TaskUpdateRequest.Resolution).ToLowerInvariant());

        if (statusPatchOperation != null
            && statusPatchOperation.value.ToString() != Constants.TaskStatusesKeys.Done
            && resolutionPatchOperation == null)
        {
            logger.LogInformation("Status different from Done provided. Removing current resolution.");

            request.Operations.Add(new Operation<TaskUpdateRequest>(
                OperationType.Replace.ToString(), nameof(TaskUpdateRequest.Resolution), default));
        }
    }

    private void ApplyAssigneeUpdates(JsonPatchDocument<TaskUpdateRequest> request)
    {
        var statusPatchExists = request.Operations.SingleOrDefault(x =>
            x.path.ToLowerInvariant() == nameof(TaskUpdateRequest.Status).ToLowerInvariant());

        var assigneeIdPatchExists = request.Operations.SingleOrDefault(x =>
            x.path.ToLowerInvariant() == nameof(TaskUpdateRequest.AssigneeUserId).ToLowerInvariant());

        if (statusPatchExists != null
            && statusPatchExists.value.ToString() == Constants.TaskStatusesKeys.Open
            && assigneeIdPatchExists == null)
        {
            logger.LogInformation("Status open provided without assigneeId. Removing current assigneeId.");

            request.Operations.Add(new Operation<TaskUpdateRequest>(
                OperationType.Replace.ToString(), nameof(TaskUpdateRequest.AssigneeUserId), default));
        }
    }
}
