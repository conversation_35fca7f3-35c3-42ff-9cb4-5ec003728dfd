﻿using Common;
using Common.Models;
using Common.Models.Shareholder;
using Common.Services;
using Common.Tests.InputModels;
using FluentAssertions;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using Services.Validation;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests;

public class ShareholderValidationServiceTests
{
    private readonly Mock<ILogger<ShareholderValidationService>> logger = new();
    private readonly Mock<IReferenceService> referenceService = new();
    private readonly Mock<IMerchantClient> merchantClient = new();
    private ShareholderValidationService validationService = null!;
    private readonly Mock<ICounterpartyProvider> counterpartyProvider = new();

    private readonly string ValidNationalityEg = "EG";
    private readonly string ValidNationalitySa = "SA";
    private readonly static string ValidCountry = "CO";
    private readonly static string ValidRelationToCompany = "1";
    private readonly static Guid ShareholderCompanyId = Guid.NewGuid();

    [Test]
    public async Task ValidateRequest_InvalidNationality_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                    x.ProblemDetails.Type == Errors.NationalityValidation.Code);
    }

    [Test]
    public async Task ValidateRequest_InvalidCountry_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Nationality = ValidNationalitySa;

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [TestCase(Constants.CounterParty.Saudi, Constants.CountryCodes.GeideaSaudi, "something", true)]
    [TestCase(Constants.CounterParty.Egypt, Constants.CountryCodes.GeideaEgypt, "", true)]
    [TestCase(Constants.CounterParty.Egypt, Constants.CountryCodes.GeideaEgypt, "", false)]
    public async Task ValidateRequest_SameNationalityAndCounterpartyAndHasPassportNumber_ShouldThrowException(string counterparty, string nationality,
        string passportNo, bool hasPassportExpiryDate)
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = counterparty;
        request.Nationality = nationality;
        request.Address = BuildValidAddress(counterparty);
        request.PassportNo = passportNo;
        request.PassportExpirationDate = hasPassportExpiryDate ? DateTime.Now : null;

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }



    [TestCase(Constants.CounterParty.Saudi, Constants.CountryCodes.GeideaSaudi, "test")]
    [TestCase(Constants.CounterParty.Egypt, Constants.CountryCodes.GeideaEgypt, "test")]
    public async Task ValidateRequest_SameNationalityAndCounterpartyAndHasPassportInfo_ShouldThrowException(string counterparty, string nationality, string passportNo)
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = counterparty;
        request.Nationality = nationality;
        request.Address = BuildValidAddress(counterparty);
        request.PassportNo = passportNo;

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest );
    }

    [TestCase("")]
    [TestCase("sdasfafsadfasdfasfdsfas")]
    [TestCase("asdjg sa23")]
    [TestCase("11111111111")]
    [TestCase("2111111111")]
    public async Task ValidateRequest_SaudiNationalityAndInvalidSaudiNationalid_ShouldThrowException(string nationalId)
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Address = BuildValidAddress(Constants.CounterParty.Saudi);
        request.Counterparty = Constants.CounterParty.Saudi;
        request.Nationality = Constants.CountryCodes.GeideaSaudi;
        request.IdExpiryDate = DateTime.Now;
        request.NationalId = nationalId;

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [TestCase("")]
    [TestCase("sdasfafsadfasdfasfdsfas")]
    [TestCase("asdjg sa23")]
    public async Task ValidateRequest_EgyptNationalityAndInvalidSaudiNationalid_ShouldThrowException(string nationalId)
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = Constants.CounterParty.Egypt;
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.Address = BuildValidAddress(Constants.CounterParty.Egypt);
        request.NationalId = nationalId;

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [TestCase(Constants.CounterParty.Saudi, Constants.CountryCodes.GeideaEgypt, "")]
    [TestCase(Constants.CounterParty.Egypt, Constants.CountryCodes.GeideaSaudi, "something")]
    public async Task ValidateRequest_DifferentNationalityAndCounterpartyAndInvalidPassportNo_ShouldThrowException(string counterparty, string nationality, string passportNo)
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = counterparty;
        request.Nationality = nationality;
        request.Address = BuildValidAddress(counterparty);
        request.NationalId = passportNo;

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task ValidateRequest_DifferentNationalityAndCounterpartyAndInvalidIdExpiry_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = Constants.CounterParty.Saudi;
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.Address = BuildValidAddress(Constants.CounterParty.Saudi);
        request.IdExpiryDate = DateTime.Now;

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }


    [Test]
    public async Task ValidateRequest_DifferentNationalityAndCounterpartyAndMissingPassportNo_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = Constants.CounterParty.Saudi;
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.Address = BuildValidAddress(Constants.CounterParty.Saudi);

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task ValidateRequest_DifferentNationalityAndCounterpartyAndMissingPassportExpiry_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = Constants.CounterParty.Saudi;
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.Address = BuildValidAddress(Constants.CounterParty.Saudi);
        request.PassportNo = "number";

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task ValidateRequest_DifferentNationalityAndCounterpartyAndMissingIdExpiry_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = Constants.CounterParty.Saudi;
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.Address = BuildValidAddress(Constants.CounterParty.Saudi);
        request.NationalId = "number";

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task ValidateRequest_NationalidIsNotUnique_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Counterparty = Constants.CounterParty.Egypt;
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.Address = BuildValidAddress(Constants.CounterParty.Saudi);
        request.PassportNo = "number";
        request.PassportExpirationDate = DateTime.Now;
        merchantClient.Setup(x => x.SearchPersonOfInterest(It.IsAny<PoiSearchCriteriaRequest>()))
            .Returns(Task.FromResult(new PoiSearchResponse { TotalRecordCount = 1 }));


        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest);
    }

    [Test]
    public async Task ValidateRequest_InvalidCompanyRelationValue_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.PassportNo = "number";
        request.PassportExpirationDate = DateTime.Now;
        request.ShareholderCompanies = new List<ShareholderCompanyIndividualLink> {
            new ShareholderCompanyIndividualLink()
            {
                OrganizationRole = "invalid role",
                ShareHolderCompanyId= Guid.NewGuid(),
            }
        };
        request.Merchant = new MerchantIndividualLink() { MerchantId = Guid.NewGuid() };
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false))
            .Returns(Task.FromResult(JsonSerializer.Serialize(
                new List<MerchantShareholderCompanyResponse> { new MerchantShareholderCompanyResponse { ShareholderCompanyId = ShareholderCompanyId } })));

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                    x.ProblemDetails.Type == Errors.InvalidShareholderCompanyIndividualLinkRole.Code);
    }

    [Test]
    public async Task ValidateRequest_CompanyIdInRelationDoesNotBelongToMerchant_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = BuildBasicRequest();
        request.Nationality = Constants.CountryCodes.GeideaEgypt;
        request.PassportNo = "number";
        request.PassportExpirationDate = DateTime.Now;
        request.ShareholderCompanies = new List<ShareholderCompanyIndividualLink> {
            new ShareholderCompanyIndividualLink()
            {
                OrganizationRole = ValidRelationToCompany,
                ShareHolderCompanyId= Guid.NewGuid(),
            }
        };
        request.Merchant = new MerchantIndividualLink() { MerchantId = Guid.NewGuid() };

        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false))
            .Returns(Task.FromResult(JsonSerializer.Serialize(
                new List<MerchantShareholderCompanyResponse> { new MerchantShareholderCompanyResponse { ShareholderCompanyId = ShareholderCompanyId } })));

        await validationService.Invoking(x => x.ValidateCreateIndividualRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                    x.ProblemDetails.Type == Errors.InvalidShareholderCompanyIndividualLinkCompanyId.Code);
    }

    [Test]
    public async Task ValidateRequestOfTypeShareholderIndividualAssociationsCreateRequest_NoCompanyLinks_ShouldNotThrowException()
    {
        CreateShareholderRequestValidationService();
        var input = BuildCreateRelationsRequest();
        input.Merchant!.OwnershipPercentage = 10;

        await validationService.Invoking(x => x.ValidateShareholderIndividualAssociationsCreateRequest(input)).Should().NotThrowAsync();
    }

    [Test]
    public async Task ValidateRequestOfTypeShareholderIndividualAssociationsCreateRequest_InvalidRole_ShouldNotThrowException()
    {
        CreateShareholderRequestValidationService();
        var input = BuildCreateRelationsRequest();
        input.ShareholderCompanies = new List<ShareholderCompanyIndividualLink>
        {
            new ShareholderCompanyIndividualLink
            {
                OrganizationRole = "invalid role",
                ShareHolderCompanyId = ShareholderCompanyId
            }
        };
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false))
           .Returns(Task.FromResult(JsonSerializer.Serialize(
               new List<MerchantShareholderCompanyResponse> { new MerchantShareholderCompanyResponse { ShareholderCompanyId = ShareholderCompanyId } })));

        await validationService.Invoking(x => x.ValidateShareholderIndividualAssociationsCreateRequest(input)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                    x.ProblemDetails.Type == Errors.InvalidShareholderCompanyIndividualLinkRole.Code);
    }

    [Test]
    public async Task ValidateRequestOfTypeShareholderIndividualAssociationsCreateRequest_InvalidCompany_ShouldNotThrowException()
    {
        CreateShareholderRequestValidationService();
        var input = BuildCreateRelationsRequest();
        input.ShareholderCompanies = new List<ShareholderCompanyIndividualLink>
        {
            new ShareholderCompanyIndividualLink
            {
                OrganizationRole = ValidRelationToCompany,
                ShareHolderCompanyId = Guid.NewGuid(),
            }
        };
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false))
           .Returns(Task.FromResult(JsonSerializer.Serialize(
               new List<MerchantShareholderCompanyResponse> { new MerchantShareholderCompanyResponse { ShareholderCompanyId = ShareholderCompanyId } })));

        await validationService.Invoking(x => x.ValidateShareholderIndividualAssociationsCreateRequest(input)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest &&
                    x.ProblemDetails.Type == Errors.InvalidShareholderCompanyIndividualLinkCompanyId.Code);
    }

    [Test]
    public async Task ValidateRequestOfTypeShareholderIndividualAssociationsCreateRequest_ValidInput_ShouldNotThrowException()
    {
        CreateShareholderRequestValidationService();
        var input = BuildCreateRelationsRequest();
        input.Merchant!.OwnershipPercentage = 10;
        input.ShareholderCompanies = new List<ShareholderCompanyIndividualLink>
        {
            new ShareholderCompanyIndividualLink
            {
                ShareHolderCompanyId = ShareholderCompanyId,
                OrganizationRole = ValidRelationToCompany
            }
        };
        merchantClient.Setup(x => x.GetShareholderCompaniesBase(It.IsAny<ShareholderCompaniesRequest>(), false))
            .Returns(Task.FromResult(JsonSerializer.Serialize(
            new List<MerchantShareholderCompanyResponse> { new MerchantShareholderCompanyResponse { ShareholderCompanyId = ShareholderCompanyId } })));

        await validationService.Invoking(x => x.ValidateShareholderIndividualAssociationsCreateRequest(input)).Should().NotThrowAsync();
    }

    [Test]
    public async Task ValidateEditCompanyRequest_InvlaidCountry_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = ShareholderCompanyRequestInputHelper.GetInvalidCountryJsonPatchRequest(),
            MerchantId = Guid.NewGuid(),
            ShareholderCompanyId = Guid.NewGuid()
        };

        counterpartyProvider.Setup(x => x.GetCode()).Returns(Constants.CounterParty.Saudi);
        await validationService.Invoking(x => x.ValidateEditCompanyRequest(request)).Should()
            .ThrowAsync<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.CountryValidation.Code);
    }

    [Test]
    public void ValidateIndividualsPatchTemp_NationalIdMissingAndPassportNoIsNotPresent_ShouldThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = CreateShareholderIndividualPatchRequest();
        request.ShareholderIndividualJsonPatch.Replace(x => x.NationalId, null);
        validationService.Invoking(x => x.ValidateIndividualsPatchTemp(request)).Should()
            .Throw<ServiceException>()
            .Where(x => x.StatusCode == HttpStatusCode.BadRequest && x.ProblemDetails.Type == Errors.ShareholderIndividualPassportOrNationalIdMandatory.Code);
    }

    [Test]
    public void ValidateIndividualsPatchTemp_NationalIdMissingAndPassportNoIsPresent_ShouldNotThrowException()
    {
        CreateShareholderRequestValidationService();
        var request = CreateShareholderIndividualPatchRequest();
        request.ShareholderIndividualJsonPatch.Replace(x => x.NationalId, null);
        request.ShareholderIndividualJsonPatch.Replace(x => x.PassportNo, "any");
        request.MerchantShareholderIndividualJsonPatch.Replace(x => x.OwnershipPercentage, null);

        validationService.Invoking(x => x.ValidateIndividualsPatchTemp(request)).Should()
            .NotThrow<ServiceException>();
    }

    [TestCase(Constants.CounterParty.Saudi)]
    [TestCase(Constants.CounterParty.Egypt)]
    public async Task ValidateEditCompanyRequest_ValidRequest_ShouldNotThrowException(string counterParty)
    {
        CreateShareholderRequestValidationService();

        var request = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = counterParty == Constants.CounterParty.Saudi ? ShareholderCompanyRequestInputHelper.GetSaudiValidJsonPatchRequest() 
            : ShareholderCompanyRequestInputHelper.GetEgyptValidJsonPatchRequest(),
            MerchantId = Guid.NewGuid(),
            ShareholderCompanyId = Guid.NewGuid()
        };
        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterParty);

        await validationService.Invoking(x => x.ValidateEditCompanyRequest(request)).Should()
            .NotThrowAsync<Exception>();
    }

    [TestCase(Constants.CounterParty.Saudi)]
    [TestCase(Constants.CounterParty.Egypt)]
    public async Task ValidateEditCompanyRequest_MerchantIdAndSharegolderIdNotValid_ShouldThrowException(string counterParty)
    {
        CreateShareholderRequestValidationService();

        var request = new ShareholderCompanyPatchRequest()
        {
            JsonPatchDocument = counterParty == Constants.CounterParty.Saudi ? ShareholderCompanyRequestInputHelper.GetSaudiValidJsonPatchRequest()
            : ShareholderCompanyRequestInputHelper.GetEgyptValidJsonPatchRequest(),
            MerchantId = Guid.Empty,
            ShareholderCompanyId = Guid.Empty
        };
        counterpartyProvider.Setup(x => x.GetCode()).Returns(counterParty);

        await validationService.Invoking(x => x.ValidateEditCompanyRequest(request)).Should()
            .ThrowAsync<ValidationException>();
    }

    private static ShareholderIndividualCreateRequest BuildBasicRequest()
    {
        return new ShareholderIndividualCreateRequest
        {
            FirstName = "valid fn",
            LastName = "valid ln",
            DOB = DateTime.Now,
            PhoneNumber = "*********",
            PhonePrefix = "+40",
            Address = new ShareholderIndividualAddress
            {
                AddressInfo = "something valid",
                Email = "<EMAIL>"
            }
        };
    }

    private static ShareholderIndividualAssociationsCreateRequest BuildCreateRelationsRequest()
    {
        return new ShareholderIndividualAssociationsCreateRequest
        {
            ShareholderIndividualId = Guid.NewGuid(),
            Merchant = new MerchantIndividualLink
            {
                MerchantId = Guid.NewGuid()
            }
        };
    }

    private static ShareholderIndividualAddress BuildValidAddress(string counterparty)
    {
        return new ShareholderIndividualAddress
        {
            Country = ValidCountry,
            AddressInfo = "some valid address info",
            Governorate = counterparty == Constants.CounterParty.Saudi ? null : "guvernorate",
            Area = counterparty == Constants.CounterParty.Saudi ? "area" : null,
            City = counterparty == Constants.CounterParty.Saudi ? "city" : null
        };
    }

    private void CreateShareholderRequestValidationService()
    {
        referenceService.Setup(x => x.GetCataloguesAsync(It.IsAny<string[]>(), It.IsAny<string>()))
            .Returns(Task.FromResult(new Catalogue[]
            {
                new Catalogue()
                {
                    CatalogueName = Constants.Catalogues.Nationalities, Key = ValidNationalityEg
                },
                new Catalogue()
                {
                    CatalogueName = Constants.Catalogues.Nationalities, Key = ValidNationalitySa
                },
                new Catalogue()
                {
                    CatalogueName = Constants.Catalogues.CountriesIso3166, Key = ValidCountry
                },
                new Catalogue()
                {
                    CatalogueName = Constants.Catalogues.RelationToCompany, Key = ValidRelationToCompany
                }
            }));
        validationService = new ShareholderValidationService(referenceService.Object, merchantClient.Object, logger.Object, counterpartyProvider.Object);
    }

    private static ShareholderIndividualPatchRequest CreateShareholderIndividualPatchRequest()
    {
        var request = new ShareholderIndividualPatchRequest();
        request.ShareholderIndividualJsonPatch = new JsonPatchDocument<ShareholderIndividual>();
        request.MerchantShareholderIndividualJsonPatch = new JsonPatchDocument<MerchantShareholderIndividualRelation>();
        request.ShareholderCompanyPersonOfInterestEditRequests = new List<ShareholderCompanyPersonOfInterestEditRequest>();
        return request;
    }
}