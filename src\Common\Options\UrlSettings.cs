﻿namespace Common.Options
{
    public class UrlSettings
    {
        public string MerchantServiceBaseUrl { get; set; } = null!;
        public string GeideaFederationServiceBaseUrl { get; set; } = null!;
        public string DocumentServiceBaseUrl { get; set; } = null!;
        public string NotificationServiceBaseUrl { get; set; } = null!;
        public string CheckoutServiceBaseUrl { get; set; } = null!;
        public string UserServiceBaseUrl { get; set; } = null!;
        public string ReferenceServiceBaseUrl { get; set; } = null!;
        public string LeadServiceBaseUrl { get; set; } = null!;
        public string SearchServiceBaseUrl { get; set; } = null!;
        public string ProductServiceBaseUrl { get; set; } = null!;
        public string DueDiligenceServiceBaseUrl { get; set; } = null!;
        public string NexusBridgeApiBaseUrl { get; set; } = null!;
    }
}
