﻿using System;

namespace Common.Models
{
    public class DocumentWithContent
    {
        public Guid? MerchantId { get; set; }
        public Guid? PersonOfInterestId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public Guid Id { get; set; }
        public DateTime CreatedDate { get; set; }


        public string? DocumentType { get; set; }
        public byte[] Content { get; set; } = null!;
    }
}
