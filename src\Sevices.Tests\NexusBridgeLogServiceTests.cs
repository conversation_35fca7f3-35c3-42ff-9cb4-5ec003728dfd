﻿using Common.Models.NexusBridgeLog;
using Common.Options;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json;
using NUnit.Framework;
using Services;
using System;
using System.Collections.Generic;
using System.Net;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class NexusBridgeLogServiceTests
    {
        private NexusBridgeLogService searchService = null!;
        private Mock<ILogger<NexusBridgeLogService>> loggerMock = new Mock<ILogger<NexusBridgeLogService>>();
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();

        private readonly RequestLog requestLogsResponse = new()
        {
            Entity = "Merchant"
        };

        [Test]
        public void RequestLogSearch()
        {
            urlSettingsOptions.Setup(opt => opt.CurrentValue)
                .Returns(new UrlSettings
                {
                    RequestLogServiceBaseUrl = "http://example.com/api",
                });

            searchService = new NexusBridgeLogService(loggerMock.Object,

            TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonConvert.SerializeObject(new List<RequestLog> { requestLogsResponse })),
            urlSettingsOptions.Object);


            searchService.Invoking(x => x.Search(new SearchFilters() { ProviderId = new Guid(), Entity = "Merchant" })).Should().NotThrowAsync<Exception>();
        }
    }
}
