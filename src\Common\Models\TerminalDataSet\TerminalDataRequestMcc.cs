﻿using System;
using System.Collections.Generic;

namespace Common.Models.TerminalDataSet
{
    public class TerminalDataRequestMcc
    {
        public List<TerminalDataSetBasicInfo> TerminalDataSets { get; set; } = new List<TerminalDataSetBasicInfo>();
        public List<Guid>? StoresIds { get; set; } = null;
    }

    public class TerminalDataSetBasicInfo
    {
        public string? AcquiringLedger { get; set; } = null!;
        public string? OrderNumber { get; set; }
        public Guid? ProductInstanceId { get; set; }
        public Guid? StoreId { get; set; }
        public string? MerchantTag { get; set; }
        public string? MCC { get; set; }
        public string? ConnectionType { get; set; }
        public string? ChannelType { get; set; }
    }
}