﻿using System;
using System.Threading.Tasks;
using Common.Models.Merchant;
using Common.Services;
using static Common.Constants;

namespace Services
{
    public class BaseGsdkService
    {
        protected IMerchantService MerchantService { get; }

        protected BaseGsdkService(IMerchantService merchantService)
        {
            MerchantService = merchantService;
        }

        protected async Task<bool> StoreOrganizationId(Guid merchantId, string organizationId)
        {
            var externalIdentifier = new MerchantExternalIdentifier
            {
                MerchantId = merchantId,
                IdentifierKey = Gsdk.IdentifierKey,
                IdentifierValue = organizationId,
                ExternalSourceId = Gsdk.ExternalSourceId
            };

            return await MerchantService.CreateMerchantExternalIdentifier(externalIdentifier);
        }
    }
}