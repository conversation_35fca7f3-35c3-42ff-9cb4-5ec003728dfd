﻿using System;
using System.Collections.Generic;

namespace Common.Models.TerminalDataSet
{
    public class OrdersWithInstancesResponse
    {
        public DateTime? CheckoutDate { get; set; }
        public Guid OrderId { get; set; }
        public bool OrderIsDeleted { get; set; }
        public string? OrderNumber { get; set; }
        public string OrderStatus { get; set; } = null!;
        public Guid StoreId { get; set; }
        public List<InstanceWithTerminalData> Instances { get; set; } = new();
    }

    public class InstanceWithTerminalData
    {
        public Guid ParentProductInstanceId { get; set; }
        public Guid? ProductInstanceId { get; set; }
        public string? ParentProductCode { get; set; }
        public string? ProductCode { get; set; }
        public string? ParentProductType { get; set; }
        public string? ProductType { get; set; }
        public bool? ParentProductInstanceIsDeleted { get; set; }
        public bool? ProductInstanceIsDeleted { get; set; }
        public Guid? TerminalId { get; set; }
        public string? Mid { get; set; }
        public string? Tid { get; set; }
        public string? Trsm { get; set; }
    }
}