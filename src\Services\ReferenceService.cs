﻿using Common.Models;
using Common.Options;
using Common.Services;
using Geidea.Utils.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Services
{
    public class ReferenceService : IReferenceService
    {
        private readonly ILogger<ReferenceService> logger;
        private readonly UrlSettings urlSettingsOptions;
        private readonly HttpClient client;

        private string ReferenceServiceBaseUrl => $"{urlSettingsOptions.ReferenceServiceBaseUrl}/api/v1";

        public ReferenceService(ILogger<ReferenceService> logger, IOptionsMonitor<UrlSettings> urlSettingsOptions, HttpClient client)
        {
            this.logger = logger;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.client = client;
        }

        public async Task<Catalogue[]> GetCataloguesAsync(string[] catalogueNames, string language = "en")
        {
            string catalogueNameQueryParam = string.Empty;
            catalogueNames.ToList().ForEach(x => catalogueNameQueryParam += $"&catalogueName={x}");

            string referenceServiceUrl = $"{ReferenceServiceBaseUrl}/simpleType/catalogues?{catalogueNameQueryParam}&language={language}";

            using (logger.BeginScope("GetCataloguesAsync({@checkoutServiceUrl})", referenceServiceUrl))
            {
                var response = await client.GetAsync(referenceServiceUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();

                    logger.LogCritical("Error when calling reference service API  with  catalogue names {@catalogueNames}. Error was {StatusCode} {@responseBody}",
                        catalogueNames, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                Catalogue[] catalogues = await response.Content.ReadAsAsync<Catalogue[]>();

                return catalogues;
            }
        }
    }
}
