﻿using Common.Models.Chain;
using FluentValidation;
using Geidea.Utils.ReferenceData;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Validators.Chain;

public class ChainUpdateRequestValidator : AbstractValidator<ChainUpdateRequest>
{
    public ChainUpdateRequestValidator(List<ReferenceData> refData)
    {
        RuleFor(x => x.ChainId).NotNull().NotEmpty();
        RuleFor(x => x.ChainName).NotNull().NotEmpty().MaximumLength(255);
        RuleFor(x => x.RelationshipManager).MaximumLength(255);
        RuleFor(x => x.Segment).MaximumLength(255);
        RuleFor(x => x.ChainAddress).SetValidator(new ChainAddressRequestValidator());
    }
}
