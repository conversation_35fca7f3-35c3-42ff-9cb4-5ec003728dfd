﻿using System;

namespace Common.Models.Shareholder;

public class ShareholderCompanyCreateRequest : BaseShareholderModel
{
    public Guid MerchantId { get; set; }

    public string CompanyName { get; set; } = null!;

    public string CompanyType { get; set; } = null!;

    public string CompanyLicense { get; set; } = null!;

    public DateTime? IssueDate { get; set; }

    public DateTime? LicenseExpiryDate { get; set; }

    public string? MccCode { get; set; }

    public string? PhonePrefix { get; set; }

    public string? PhoneNumber { get; set; }

    public decimal? OwnershipPercentage { get; set; }

    public string? City { get; set; }

    public string? Area { get; set; }

    public string? Governorate { get; set; }

    public string? Country { get; set; }

    public string? Address { get; set; }
}