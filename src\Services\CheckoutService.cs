﻿using Common.Models.Checkout;
using Common.Services;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Common;
using Geidea.Utils.Exceptions;
using Common.Models.Product;
using AutoMapper;
using Common.Models.Comment;
using Common.Validators;
using Geidea.Utils.Counterparty.Providers;
using Geidea.Utils.Validation;
using Common.Models.Merchant;
using Common.Models.Gle;
using static Common.Constants;
using Services.Messaging;
using Search = Common.Models.Search;
using Common.Models.TerminalDataSet;
using Common.Models.Postilion;
using Common.Models.ProductInstance;
using RefUtils = Geidea.Utils.ReferenceData;
using Common.Models;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Newtonsoft.Json.Linq;
using Common.Models.NexusBridge;
using System.Threading;



namespace Services
{
    public class CheckoutService : ICheckoutService
    {
        private readonly ICounterpartyProvider counterpartyProvider;
        private readonly ILogger<CheckoutService> logger;
        private readonly IMapper mapper;
        private readonly IProductService productService;
        private readonly IUserService userService;
        private readonly IGSDKMerchantAdapterService gsdkMerchantAdapterService;
        private readonly IReferenceService referenceService;
        private readonly IMerchantClient merchantClient;
        private readonly IContractService contractService;
        private readonly ISearchService searchService;
        private readonly GleComposePayloadMessagingClient gleComposePayloadMessagingClient;
        private readonly ICheckoutClient checkoutClient;
        private readonly IEPosMessagingService ePosMessagingService;
        private readonly IPostilionService postilionService;
        private readonly IGsdkService gsdkService;
        private readonly INotificationService notificationService;
        private readonly IShareholderService shareholderService;
        private readonly IDueDiligenceService dueDiligenceService;
        private readonly INexusBridgeService nexusBridgeService;

        private readonly List<string> federateProductType = new()
        {
            Constants.ProductType.Terminal,
            Constants.ProductType.Mpos,
            Constants.ProductType.Gway
        };

        public CheckoutService(
            ILogger<CheckoutService> logger,
            IUserService userService,
            IProductService productService,
            IGSDKMerchantAdapterService gsdkMerchantAdapterService,
            IMapper mapper,
            ICounterpartyProvider counterpartyProvider,
            IReferenceService referenceService,
            IMerchantClient merchantClient,
            IContractService contractService,
            ISearchService searchService,
            GleComposePayloadMessagingClient gleComposePayloadMessagingClient,
            ICheckoutClient checkoutClient,
            IEPosMessagingService ePosMessagingService,
            IPostilionService postilionService,
            IGsdkService gsdkService,
            INotificationService notificationService,
            IShareholderService shareholderService,
            IDueDiligenceService dueDiligenceService,
            INexusBridgeService nexusBridgeService
            )
        {
            this.logger = logger;
            this.userService = userService;
            this.productService = productService;
            this.gsdkMerchantAdapterService = gsdkMerchantAdapterService;
            this.mapper = mapper;
            this.counterpartyProvider = counterpartyProvider;
            this.referenceService = referenceService;
            this.merchantClient = merchantClient;
            this.contractService = contractService;
            this.searchService = searchService;
            this.gleComposePayloadMessagingClient = gleComposePayloadMessagingClient;
            this.checkoutClient = checkoutClient;
            this.ePosMessagingService = ePosMessagingService;
            this.postilionService = postilionService;
            this.gsdkService = gsdkService;
            this.notificationService = notificationService;
            this.shareholderService = shareholderService;
            this.dueDiligenceService = dueDiligenceService;
            this.nexusBridgeService = nexusBridgeService;
        }
        public async Task<OrderCommentResponse> CreateCommentAsync(Guid orderId, CommentCreateRequest commentCreateRequest)
        {
            new ValidationHelpers().Validate(commentCreateRequest.CommentText, new CommentValidator(), logger, "CommentText length validation failed!");

            return await checkoutClient.CreateCommentAsync(orderId, commentCreateRequest);
        }

        public async Task DeleteCommentAsync(Guid commentId)
        {
            await checkoutClient.DeleteCommentAsync(commentId);
        }

        public async Task DeleteOrderAsync(OrderDeleteRequest orderDeleteRequest)
        {
            await checkoutClient.DeleteOrderAsync(orderDeleteRequest);
        }

        public async Task DeleteOrderItemAsync(Guid[] orderItemIds)
        {
            await checkoutClient.DeleteOrderItemAsync(orderItemIds);
        }

        public async Task<OrderResponse[]> GetAllOrdersAsync(bool includeDeleted)
        {
            return await checkoutClient.GetAllOrdersAsync(includeDeleted);
        }

        public async Task<OrderCommentResponse> GetCommentByIdAsync(Guid commentId)
        {
            return await checkoutClient.GetCommentByIdAsync(commentId);
        }

        public async Task<OrderCommentResponse[]> GetCommentByOrderIdAsync(Guid orderId)
        {
            return await checkoutClient.GetCommentByOrderIdAsync(orderId);
        }

        public async Task<OrderResponse> GetByOrderNumberAsync(string orderNumber)
        {
            return await checkoutClient.GetByOrderNumberAsync(orderNumber);
        }

        public async Task<List<OrderItem>> GetOrderItemByOrderIdAsync(Guid orderId)
        {
            return await checkoutClient.GetOrderItemByOrderIdAsync(orderId);
        }

        public async Task<OrderItemResponse[]> GetOrderItemsByIdAsync(Guid[] orderItemIds)
        {
            return await checkoutClient.GetOrderItemsByIdAsync(orderItemIds);
        }

        public async Task<List<OrderStatusResponse>> GetOrdersStatusHistoryAsync(Guid orderId)
        {
            var orderStatusResponses = await checkoutClient.GetOrdersStatusHistoryAsync(orderId);

            var users = await userService.GetAllUsersAsync();
            foreach (var orderStatusResponse in orderStatusResponses)
            {
                if (!Guid.TryParse(orderStatusResponse.UpdatedBy, out var userId)) continue;

                var user = users.FirstOrDefault(user => user.Id.Equals(userId));
                orderStatusResponse.UpdatedBy = user != null ? $"{user.FirstName} {user.LastName}" : Constants.User.DefaultUserValue;
            }

            return orderStatusResponses;
        }

        public async Task<MerchantOrders> GetMerchantOrdersNotPassedProductRegisterd(Guid merchantId)
        {
            return await checkoutClient.GetMerchantOrdersNotPassedProductRegisterd(merchantId);
        }

        public async Task<OrderResponse[]> SearchOrderAsync(OrderSearchCriteria orderSearchCriteria)
        {
            var coreOrderSearchCriteria = await BuildOrderSearchCriteria(orderSearchCriteria);

            if (coreOrderSearchCriteria == null)
            {
                return Array.Empty<OrderResponse>();
            }

            return await checkoutClient.SearchOrderAsync(coreOrderSearchCriteria);
        }

        public async Task<OrderCommentResponse> UpdateCommentAsync(Guid commentId, CommentUpdateRequest commentUpdateRequest)
        {
            new ValidationHelpers().Validate(commentUpdateRequest.CommentText, new CommentValidator(), logger, "CommentText length validation failed!");

            return await checkoutClient.UpdateCommentAsync(commentId, commentUpdateRequest);
        }

        public async Task UpdateOrderAsync(Guid orderId, JsonPatchDocument<OrderUpdateRequest> updateOrderRequest,
            Guid? userId = null, bool checkOrderStatus = true)
        {
            await ValidateProjectNameToBundleCompatibility(orderId, updateOrderRequest);

            var orderRequestData = new OrderUpdateRequest();
            try
            {
                updateOrderRequest.ApplyTo(orderRequestData);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Invalid patch.");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatch);
            }

            var productsConfigurationResponse = new ProductsConfigurationResponse
            {
                AreConfigured = false,
            };

            new ValidationHelpers().Validate(updateOrderRequest, new OrderUpdateRequestValidator(), logger,
                "OrderUpdateRequest length validation failed!");

            if (!string.IsNullOrWhiteSpace(orderRequestData.OrderStatus) && orderRequestData.OrderStatus.ToUpper()
                    .Equals(Constants.OrderStatus.ProductRegistered))
            {
                productsConfigurationResponse = await CheckIfConditionsForFederationAreMetAsync(orderId);

                if (counterpartyProvider.GetCode() != CounterParty.Uae)
                {
                    await ValidateAssignGsdkContractsWithOrderProductInstances(productsConfigurationResponse.ProductInstances,
                                                                                productsConfigurationResponse.Merchant, orderId);
                }

            }
            else if (checkOrderStatus)
            {
                await ValidateMerchantAndOrderVerifiedStatus(orderId, orderRequestData.OrderStatus, updateOrderRequest);
                updateOrderRequest.ApplyTo(orderRequestData);
            }

            await checkoutClient.UpdateOrderAsync(updateOrderRequest, orderId);

            string? orderCurrency = await TriggerGleLogic(orderId, orderRequestData.OrderStatus, userId);

            await MakeChangesBasedOnTheOrderStatusAsync(orderRequestData, orderId, productsConfigurationResponse, orderCurrency);
        }

        private async Task ValidateMerchantAndOrderVerifiedStatus(Guid orderId, string orderStatus, JsonPatchDocument<OrderUpdateRequest> updateOrderRequest)
        {
            if ((counterpartyProvider.GetCode().Equals(CounterParty.Egypt) || counterpartyProvider.GetCode().Equals(CounterParty.Uae)) &&
                !string.IsNullOrWhiteSpace(orderStatus))
            {
                var orderResponse = await checkoutClient.GetOrderByIdAsync(orderId);
                var merchant = await merchantClient.GetCoreMerchantAsync(orderResponse.MerchantId);

                if ((merchant.MerchantStatus == MerchantStatus.Verified || merchant.MerchantStatus == MerchantStatus.Active)
                    && orderStatus.ToUpper().Equals(OrderStatus.VerificationInProgress))
                {
                    var existingOrderStatusOperation = updateOrderRequest.Operations.FirstOrDefault(op => op.path.Equals("orderStatus", StringComparison.OrdinalIgnoreCase));
                    if (existingOrderStatusOperation != null) { existingOrderStatusOperation.value = OrderStatus.Verified; }
                }

                if (orderStatus.ToUpper().Equals(OrderStatus.Verified) && merchant.MerchantStatus != MerchantStatus.Active && merchant.MerchantStatus != MerchantStatus.Verified)
                {
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.MerchantStatusNotVerified);
                }
            }

        }

        public async Task<List<OrderBulkUploadError>> UpdateOrdersToProductRegisteredAsync(List<string> orders, Guid? userId)
        {
            var errors = new List<OrderBulkUploadError>();

            if (orders.Count == 0)
                return errors;

            orders = ProcessOrderNumberList(orders);

            var counterpartyCode = counterpartyProvider.GetCode();
            Merchant merchant = new();
            OrderResponse orderResponse = new();
            string orderNumber = string.Empty;

            foreach (var item in orders)
            {
                try
                {
                    orderNumber = item.Trim();
                    orderResponse = await GetByOrderNumberAsync(orderNumber);
                    var orderId = orderResponse.OrderId;

                    if (!IsOrderResponseInValidStatus(orderResponse))
                    {
                        AddErrorToErrorList(Errors.OrderIsNotSubmittedOrVerified.Code, Errors.OrderIsNotSubmittedOrVerified.Message, orderNumber, errors);
                        continue;
                    }

                    merchant = await GetMerchant(merchant, orderResponse);

                    if (merchant.MerchantStatus != Constants.MerchantStatus.Verified)
                    {
                        AddErrorToErrorList(Errors.InvalidMerchantStatus.Code, Errors.InvalidMerchantStatus.Message, orderNumber, errors);
                        continue;
                    }

                    var productsConfigurationResponse = await ValidateProductsConfigurationAsync(orderResponse, counterpartyCode, merchant.MerchantDetails?.MCC);

                    if (!productsConfigurationResponse.AreConfigured)
                    {
                        AddErrorToErrorList(Errors.ProductsNotConfigured.Code, Errors.ProductsNotConfigured.Message, orderNumber, errors);
                        continue;
                    }

                    var updateOrderRequest = new JsonPatchDocument<OrderUpdateRequest>();
                    updateOrderRequest.Replace(e => e.OrderStatus, Constants.OrderStatus.ProductRegistered);

                    if (!await IsValidAssignGsdkContractsWithOrderProductInstances(productsConfigurationResponse.ProductInstances, merchant))
                    {
                        AddErrorToErrorList(Errors.DefaultContractNotMatchMerchantAcquirer.Code, Errors.DefaultContractNotMatchMerchantAcquirer.Message, orderNumber, errors);
                        continue;
                    }

                    await checkoutClient.UpdateOrderAsync(updateOrderRequest, orderId);
                    gsdkMerchantAdapterService.SendOrderUpdateProductRegisteredMessage(new OrderUpdateMessage
                    {
                        OrderId = orderId
                    });
                    await SendMessageToPostilionOnProductRegistered(orderId, productsConfigurationResponse);

                    if (counterpartyCode == Geidea.Utils.Common.Constants.CounterpartyEgypt)
                    {
                        await ePosMessagingService.CreateOrderEPosTicketAsync(orderId);
                        await TriggerGleLogic(orderId, OrderStatus.ProductRegistered, userId);
                    }
                }
                catch (Exception ex)
                {
                    HandleUpdateOrderException(ex, errors, orderNumber);
                }
            }

            return errors;
        }

        public async Task UpdateOrderItemAsync(Guid orderItemId, JsonPatchDocument<OrderItemUpdateRequest> updateOrderItemRequest)
        {
            var orderItemRequestData = new OrderItemUpdateRequest();
            try
            {
                updateOrderItemRequest.ApplyTo(orderItemRequestData);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Invalid patch.");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidPatch);
            }

            new ValidationHelpers().Validate(orderItemRequestData, new OrderItemUpdateRequestValidator(), logger, "OrderItemUpdateRequest length validation failed!");

            await checkoutClient.UpdateOrderItemAsync(orderItemId, updateOrderItemRequest);
        }

        private async Task<Merchant> GetMerchant(Merchant merchant, OrderResponse orderResponse)
        {
            if (merchant == null || orderResponse.MerchantId != merchant.MerchantId || merchant.MerchantId == Guid.Empty)
            {
                merchant = await merchantClient.GetCoreMerchantAsync(orderResponse.MerchantId);
            }

            return merchant;
        }

        private async Task<ProductsConfigurationResponse> CheckIfConditionsForFederationAreMetAsync(Guid orderId)
        {
            var counterpartyCode = counterpartyProvider.GetCode();
            var orderResponse = await checkoutClient.GetOrderByIdAsync(orderId);

            var merchant = await merchantClient.GetCoreMerchantAsync(orderResponse.MerchantId);

            if (merchant.MerchantStatus != MerchantStatus.Active && merchant.MerchantStatus != MerchantStatus.Verified)
            {
                logger.LogError("Invalid merchant status to complete this action");
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidMerchantStatus);
            }

            var validationResponse = await ValidateProductsConfigurationAsync(orderResponse, counterpartyCode, merchant.MerchantDetails?.MCC);

            validationResponse.Merchant = merchant;

            return validationResponse;
        }

        private async Task MakeChangesBasedOnTheOrderStatusAsync(OrderUpdateRequest orderRequestData, Guid orderId, ProductsConfigurationResponse productsConfigurationResponse, string? orderCurrency)
        {
            var counterpartyCode = counterpartyProvider.GetCode();

            if (!string.IsNullOrWhiteSpace(orderRequestData.OrderStatus))
            {
                var orderStatus = orderRequestData.OrderStatus.ToUpper();

                if ((counterpartyCode == CounterParty.Egypt || counterpartyCode == CounterParty.Uae) && orderStatus.Equals(OrderStatus.Verified, StringComparison.OrdinalIgnoreCase))
                {
                    await AddUpdateTerminalDataSetBasedOnAcquiringLedger(orderId, orderCurrency);
                    await UpdateUaeMerchantStatusAndOrderConfigs(orderId);
                }
                else if (counterpartyCode == Constants.CounterParty.Saudi && orderStatus.Equals(Constants.OrderStatus.Cancelled))
                {
                    await contractService.UpdateMerchantContractAsDeletedAsync(orderId);
                }
                else if (counterpartyCode == CounterParty.Uae && (orderStatus.Equals(OrderStatus.VerificationInProgress) || orderStatus.Equals(OrderStatus.ProductRegistered)))
                {
                    await UpdateUaeMerchantStatusAndOrderConfigs(orderId);
                }
            }
        }
        private async Task UpdateUaeMerchantStatusAndOrderConfigs(Guid orderId)
        {
            var orderResponse = await checkoutClient.GetOrderByIdAsync(orderId);
            var merchant = await merchantClient.GetCoreMerchantAsync(orderResponse.MerchantId);
            var contacts = await shareholderService.GetMerchantIndividualsAsync(orderResponse.MerchantId);
            var owner = contacts.FirstOrDefault(p => p.Relations.Any(r => r.OrganizationRole == ShareholderIndividualsRelationTypes.Owner));
            var store = await merchantClient.GetStoreAsync(orderResponse.MerchantId, orderResponse.StoreId);

            SendWelcomeEmialRequest emialRequest = new()
            {
                Language = Language,
                Recipient = merchant?.MerchantDetails?.BusinessEmail,
                RecipientName = owner?.FirstName + " " + owner?.LastName
            };
            if (orderResponse.OrderStatus == OrderStatus.VerificationInProgress)
            {
                await AddUpdateOrderConfigsBasedOnAccountConfig(orderId, store);
            }
            if (merchant?.MerchantStatus == MerchantStatus.PendingPricingApproval && orderResponse.OrderStatus == OrderStatus.VerificationInProgress)
            {
                var merchantPatch = new JsonPatchDocument<PatchMerchantRequest>();
                merchantPatch.Add(x => x.MerchantStatus, MerchantStatus.BoardingCompleted);
                await merchantClient.PatchMerchantAsync(merchant.MerchantId, merchantPatch);
                await dueDiligenceService.SendWorldCheckOneRequest(merchant.MerchantId);

            }
            else if (merchant?.MerchantStatus == MerchantStatus.Verified && orderResponse.OrderStatus == OrderStatus.ProductRegistered)
            {
                var merchantPatch = new JsonPatchDocument<PatchMerchantRequest>();
                merchantPatch.Add(x => x.MerchantStatus, MerchantStatus.Active);
                await merchantClient.PatchMerchantAsync(merchant.MerchantId, merchantPatch);
                if (merchant?.MerchantDetails?.ReferralChannel?.ToUpper() != ReferralChannel.HSBC)
                {
                    await TriggerEmailNotification(store, emialRequest);
                }
            }
            else if (merchant?.MerchantStatus == MerchantStatus.Active &&
                orderResponse.OrderStatus == OrderStatus.ProductRegistered &&
                merchant?.MerchantDetails?.ReferralChannel?.ToUpper() != ReferralChannel.HSBC)
            {
                await TriggerEmailNotification(store, emialRequest);
            }
            //this condition will hit when merchant status gets changed to verified from merchant underwriting.
            else if ((merchant?.MerchantStatus == MerchantStatus.BoardingCompleted || merchant?.MerchantStatus == MerchantStatus.RiskApproval || merchant?.MerchantStatus == MerchantStatus.ComplianceApproval) && orderResponse.OrderStatus.Equals(OrderStatus.Verified))
            {
                try
                {
                    await nexusBridgeService.CreateMerchant(merchant!, contacts, store, orderResponse, false);
                    Thread.Sleep(4000);
                }
                catch (Exception ex)
                {
                    logger.LogCritical(ex, "Error when calling nexusbridge service");
                }
            }
            else if ((merchant?.MerchantStatus == MerchantStatus.Verified || merchant?.MerchantStatus == MerchantStatus.Active) && orderResponse.OrderStatus.Equals(OrderStatus.Verified))
            {
                try
                {
                    await nexusBridgeService.CreateMerchant(merchant!, contacts, store, orderResponse, true);

                }
                catch (Exception ex)
                {
                    logger.LogCritical(ex, "Error when calling nexusbridge service");
                }
            }
        }

        private async Task TriggerEmailNotification(Merchant store, SendWelcomeEmialRequest emialRequest)
        {
            if (store?.MerchantDetails?.ChannelType == ChannelType.CardPresent)
            {
                await notificationService.SendWelcomeEmialForCPMerchantAccount(emialRequest);
            }
            else if (store?.MerchantDetails?.ChannelType == ChannelType.CardNotPresent)
            {
                await notificationService.SendWelcomeEmialForCNPMerchantAccount(emialRequest);
            }
        }
        private async Task ValidateProjectNameToBundleCompatibility(Guid orderId, JsonPatchDocument<OrderUpdateRequest> updateOrderRequest)
        {
            if (!updateOrderRequest.Operations.Any(x => x.path.ToLower().Contains(Constants.PatchPath.ProjectName.ToLower())))
                return;

            var newProjectNames = updateOrderRequest.Operations.FirstOrDefault(x => x.path.ToLower().Contains(Constants.PatchPath.ProjectName.ToLower()))!.value.ToString();
            if (!string.IsNullOrWhiteSpace(newProjectNames))
            {
                var catalogues = await referenceService.GetCataloguesAsync(new string[] { Constants.Catalogues.ReferralChannelRestrictions }, "EN");
                var referralChannelRestrictionForNewValue = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.ReferralChannelRestrictions && x.Key == newProjectNames);

                if (referralChannelRestrictionForNewValue != null)
                {
                    logger.LogError("The selected ProjectName is not compatible with the Bundle");
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidProjectNameForBundleBadRequest);
                }

                var order = await checkoutClient.GetOrderByIdAsync(orderId);
                var oldProjectName = order!.ProjectName;

                var referralChannelRestrictionForOldValue = catalogues.FirstOrDefault(x => x.CatalogueName == Constants.Catalogues.ReferralChannelRestrictions && x.Key == oldProjectName);

                if (referralChannelRestrictionForOldValue != null)
                {
                    logger.LogError("The selected ProjectName is not compatible with the Bundle");
                    throw new ServiceException(HttpStatusCode.BadRequest, Errors.InvalidProjectNameForBundleBadRequest);
                }
            }

        }

        private async Task<CoreOrderSearchCriteria?> BuildOrderSearchCriteria(OrderSearchCriteria orderSearchCriteria)
        {
            var coreOrderSearchCriteria = mapper.Map<CoreOrderSearchCriteria>(orderSearchCriteria);

            if (orderSearchCriteria.ProductCodes.Count > 0)
            {
                var productIds = await productService.GetRelatedProductsAsync(new ProductCodesRequest
                {
                    ProductCodes = orderSearchCriteria.ProductCodes.ToArray()
                });

                if (productIds.Length == 0)
                {
                    return null;
                }

                coreOrderSearchCriteria.ProductIds.AddRange(productIds);
            }

            return coreOrderSearchCriteria;
        }

        private async Task<ProductsConfigurationResponse> ValidateProductsConfigurationAsync(OrderResponse orderResponse, string counterpartyCode, string? mcc)
        {
            var productInstanceIds = orderResponse.OrderItem!.SelectMany(x => x.ProductInstanceIds).ToList();

            var productInstances = await productService.GetProductInstances(productInstanceIds);

            if (productInstances.Count == 0)
            {
                logger.LogError("No product instances found for order with {@orderId}", orderResponse.OrderId);
                throw new ServiceException(Errors.ProductInstanceNotFound);
            }

            var productInstanceColapsedList = productInstances.Where(p => p.Product.Type.Equals(Constants.ProductType.Bundle))
                                                          .SelectMany(x => x.Children)
                                                          .Where(x => !x.DeletedFlag && x.Metadata != null && federateProductType.Contains(x.Product.Type))
                                                          .ToList();

            productInstanceColapsedList.AddRange(
            productInstances.Where(x => !x.DeletedFlag && x.Metadata != null && federateProductType.Contains(x.Product.Type)));

            var hasBillPayment = false;

            if (!string.IsNullOrEmpty(orderResponse.BillPayments) && orderResponse.BillPayments != OrderBillPaymentsStatus.NotAdded)
            {
                hasBillPayment = productInstances.FirstOrDefault(p => p.Product.Code == ProductCode.BillPayment) != null &&
                     (productInstances.FirstOrDefault(p => p.Product.Code == ProductCode.GoSmart || p.Product.Code == ProductCode.GoSmartBP) == null);

            }

            if (productInstanceColapsedList is null || productInstanceColapsedList.Count == 0)
            {
                if (!productInstances.Any(x => x.Product.Type != Constants.ProductType.Accesorries) || productInstances.All(x => x.Product.Type == Constants.ProductType.Bundle))
                {
                    return new ProductsConfigurationResponse
                    {
                        AreConfigured = false,
                        ProductInstances = productInstanceColapsedList
                    };
                }

                if (!hasBillPayment)
                {
                    logger.LogError("No product instances suitable for federation found in order with id {@orderId}", orderResponse.OrderId);
                    throw new ServiceException(Errors.ProductInstanceNotFound);
                }
            }

            if (hasBillPayment)
            {
                logger.LogError("The order {@orderId} has only Bill Payment enablement, 'Products Registered' status is not necessary.", orderResponse.OrderId);
                throw new ServiceException(Errors.BillPaymentNoProductRegisteredStatus);
            }

            ValidateProductsConfiguration(productInstanceColapsedList, orderResponse.OrderId, counterpartyCode, mcc);

            return new ProductsConfigurationResponse
            {
                AreConfigured = true,
                ProductInstances = productInstanceColapsedList
            };
        }

        private void ValidateProductsConfiguration(List<ProductInstance>? productInstanceColapsedList, Guid orderId, string counterpartyCode, string? mcc)
        {
            if (productInstanceColapsedList != null)
            {
                foreach (var productInstance in productInstanceColapsedList)
                {
                    var hasConfiguration = ProductIsConfigured(productInstance, counterpartyCode, mcc);

                    if (!hasConfiguration)
                    {
                        logger.LogError("One or more products not configured for order with id {@orderId}", orderId);
                        throw new ServiceException(HttpStatusCode.BadRequest, Errors.ProductsNotConfigured);
                    }
                }
            }
        }

        private static bool ProductIsConfigured(ProductInstance productInstance, string counterpartyCode, string? mcc)
        {
            if (productInstance.Metadata is TerminalData terminalData)
            {
                if (counterpartyCode == Constants.CounterParty.Saudi)
                {
                    return !string.IsNullOrWhiteSpace(terminalData?.FullTId) &&
                        !string.IsNullOrWhiteSpace(terminalData?.Mcc) &&
                        !string.IsNullOrWhiteSpace(terminalData?.POSDataCode);
                }
                else if (counterpartyCode == Constants.CounterParty.Egypt || counterpartyCode == Constants.CounterParty.Uae)
                {
                    return !string.IsNullOrWhiteSpace(terminalData?.TId) &&
                        !string.IsNullOrWhiteSpace(terminalData?.MIDMerchantReference) &&
                        !string.IsNullOrWhiteSpace(terminalData?.Mcc);
                }
            }
            else if (productInstance.Metadata is GatewayData gatewayData)
            {
                if (gatewayData.IsTest)
                {
                    return true;
                }

                return !string.IsNullOrWhiteSpace(mcc);

            }
            return false;
        }

        private async Task ValidateAssignGsdkContractsWithOrderProductInstances(List<ProductInstance>? productInstanceColapsedList, Merchant merchant, Guid orderId)
        {
            if (!await IsValidAssignGsdkContractsWithOrderProductInstances(productInstanceColapsedList, merchant))
            {
                logger.LogError("The business not has TMSC contract assigned for one of the payment ways of the order with id {@orderId}", orderId);
                throw new ServiceException(HttpStatusCode.BadRequest, Errors.DefaultContractNotMatchMerchantAcquirer);
            }
        }

        private async Task<bool> IsValidAssignGsdkContractsWithOrderProductInstances(List<ProductInstance>? productInstanceColapsedList, Merchant merchant)
        {
            if (HasProductInstancesForEgypt(merchant, productInstanceColapsedList))
            {
                return await gsdkService.ValidateTmscContractsForProducts(productInstanceColapsedList!, merchant);
            }

            return true;
        }

        private static bool HasProductInstancesForEgypt(Merchant merchant, List<ProductInstance>? productInstances)
        {
            return merchant.Counterparty == CounterParty.Egypt && productInstances != null;
        }

        private static void HandleUpdateOrderException(Exception ex, List<OrderBulkUploadError> errors, string orderNumber)
        {
            (string? code, string? message) error;
            if (ex.GetType() == typeof(PassthroughException))
                error = ((PassthroughException)ex).StatusCode == HttpStatusCode.NotFound
                    ? (Errors.OrderOrProductsNotFound.Code, Errors.OrderOrProductsNotFound.Message)
                    : (Errors.GenericError.Code, Errors.GenericError.Message);
            else if (ex.GetType() == typeof(ServiceException))
                error = ((ServiceException)ex).ProblemDetails != null
                    ? (((ServiceException)ex).ProblemDetails.Type, ((ServiceException)ex).ProblemDetails.Detail)
                    : (Errors.GenericError.Code, Errors.GenericError.Message);
            else error = (Errors.GenericError.Code, Errors.GenericError.Message);

            AddErrorToErrorList(error.code ?? "", error.message ?? "", orderNumber, errors);
        }

        private static void AddErrorToErrorList(string errorCode, string errorMessage, string orderNumber, List<OrderBulkUploadError> errors)
        {
            var error = new OrderBulkUploadError { ErrorCode = errorCode, ErrorMessage = errorMessage, OrderNumber = orderNumber };
            errors.Add(error);
        }

        private static bool IsOrderResponseInValidStatus(OrderResponse orderResponse)
        {
            return orderResponse.OrderStatus == Constants.OrderStatus.Verified || orderResponse.OrderStatus == Constants.OrderStatus.Submitted;
        }

        private static List<string> ProcessOrderNumberList(List<string> orders)
        {
            return orders
                .Where(order => !string.IsNullOrWhiteSpace(order))
                .Select(order => order.Trim())
                .Distinct()
                .ToList();
        }

        private async Task<string?> TriggerGleLogic(Guid orderId, string orderStatus, Guid? userId)
        {
            if ((counterpartyProvider.GetCode() != CounterParty.Egypt && counterpartyProvider.GetCode() != CounterParty.Uae) || string.IsNullOrWhiteSpace(orderStatus))
            {
                return null;
            }

            var order = await checkoutClient.GetOrderByIdAsync(orderId);

            if (order.OrderItem == null || !order.OrderItem.Any() || counterpartyProvider.GetCode() == CounterParty.Uae)
            {
                return order.Currency;
            }

            var productIds = order.OrderItem.Where(oi => oi.ProductId.HasValue).Select(oi => oi.ProductId!.Value).Distinct().ToArray();
            var bpTypesOnOrder = await productService.GetBpProductTypesInListOfProducts(new() { Ids = productIds });

            var orderStatusUpper = orderStatus.ToUpper();
            var isCorrectStatusForBillPaymentBundle = bpTypesOnOrder.HasBillPaymentBundle && orderStatusUpper == OrderStatus.ProductRegistered;
            var isCorrectStatusForBillPaymentService = bpTypesOnOrder.HasBillPaymentService && orderStatusUpper is OrderStatus.Submitted or OrderStatus.Verified;

            if (isCorrectStatusForBillPaymentBundle || isCorrectStatusForBillPaymentService)
            {
                var composeMessage = new GleComposePayloadRequest
                {
                    OrderId = orderId,
                    UserId = userId
                };
                gleComposePayloadMessagingClient.SendGleComposePayloadMessage(composeMessage);
            }

            return order.Currency;
        }

        private async Task TriggerEPosLogic(Guid orderId, OrderUpdateRequest orderRequestData)
        {
            if (counterpartyProvider.GetCode() != Geidea.Utils.Common.Constants.CounterpartyEgypt)
            {
                return;
            }

            if (string.IsNullOrWhiteSpace(orderRequestData.OrderStatus) || !orderRequestData.OrderStatus.ToUpper()
                    .Equals(OrderStatus.ProductRegistered))
            {
                return;
            }

            await ePosMessagingService.CreateOrderEPosTicketAsync(orderId);
        }

        private async Task AddUpdateTerminalDataSetBasedOnAcquiringLedger(Guid orderId, string? orderCurrency)
        {
            try
            {
                var productIntancesWithTerminalData = await searchService.GetOrderProductInstancesWithTerminalDataAsync(orderId, new List<string> { ProductType.Terminal, ProductType.Mpos });

                if (productIntancesWithTerminalData != null && productIntancesWithTerminalData.Instances?.Count > 0)
                {
                    var merchantAcquiringLedger = await merchantClient.GetMerchantAcquiringLedgerByStoreIdAsync(productIntancesWithTerminalData.StoreId);

                    if (IsAutoGenerateTIDAndMIDAcquirer(merchantAcquiringLedger) || counterpartyProvider.GetCode() == Constants.CounterParty.Uae)
                    {
                        var terminalsResponse = await GenerateTIDAndMIDAndAddEditTerminalDataSets(merchantAcquiringLedger, productIntancesWithTerminalData);

                        var updateInstanceIds = await UpdateTerminalProductInstancesMeta(merchantAcquiringLedger, terminalsResponse, orderCurrency);

                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Error when calling AddUpdateTerminalDataSetBasedOnAcquiringLedger for order with Id {@orderId}", orderId);
            }
        }

        private async Task SendMessageToPostilionOnProductRegistered(Guid orderId, ProductsConfigurationResponse productsConfigurationResponse)
        {
            try
            {
                if (counterpartyProvider.GetCode() != Constants.CounterParty.Egypt) { return; }

                var productIntancesWithTerminalData = await searchService.GetOrderProductInstancesWithTerminalDataAsync(orderId, new List<string> { ProductType.Terminal, ProductType.Mpos });

                if (productIntancesWithTerminalData != null && productIntancesWithTerminalData.Instances?.Count > 0 && productsConfigurationResponse?.ProductInstances?.Count > 0)
                {
                    var merchantAcquiringLedger = await merchantClient.GetMerchantAcquiringLedgerByStoreIdAsync(productIntancesWithTerminalData.StoreId);

                    List<Guid> productInstanceIds = productsConfigurationResponse.ProductInstances.Select(x => x.ProductInstanceId).ToList();

                    var productInstancesWithTerminalData = productsConfigurationResponse.ProductInstances
                        .Select(productInstance => new { ProductInstanceId = productInstance.ProductInstanceId, TerminalData = productInstance.Metadata as TerminalData }).ToList();

                    var terminalDataSet = productInstancesWithTerminalData
                        .Select(terminalDataSet => new TerminalDataSetResponse
                        {
                            ProductInstanceId = terminalDataSet.ProductInstanceId,
                            TId = terminalDataSet.TerminalData?.TId,
                            MIDMerchantReference = terminalDataSet.TerminalData?.MIDMerchantReference,
                            ProviderBank = terminalDataSet.TerminalData?.ProviderBank,
                            FullTId = terminalDataSet.TerminalData?.FullTId,
                            ConnectionType = terminalDataSet.TerminalData?.ConnectionType,
                            ChannelType = terminalDataSet.TerminalData?.ChannelType,
                            MPGSMID = terminalDataSet.TerminalData?.MPGSMID,
                            MPGSKEY = terminalDataSet.TerminalData?.MPGSKEY
                        }).ToList();

                    await SendMessageToPostilionAsync(productInstanceIds, merchantAcquiringLedger, terminalDataSet);

                }
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Error when calling SendMessageToPostilionOnProductRegistered for order with Id {@orderId}", orderId);
            }
        }

        private static bool IsAutoGenerateTIDAndMIDAcquirer(MerchantAcquiringLedgerInfo merchantAcquiringLedger)
        {
            return merchantAcquiringLedger != null &&
                !string.IsNullOrEmpty(merchantAcquiringLedger.AcquiringLedger) &&
                (merchantAcquiringLedger.AcquiringLedger.Equals(AcquiringLedger.NBE,
                StringComparison.OrdinalIgnoreCase) ||
                merchantAcquiringLedger.AcquiringLedger.Equals(AcquiringLedger.ALX,
                StringComparison.OrdinalIgnoreCase));
        }

        private async Task<List<TerminalDataSetResponse>> GenerateTIDAndMIDAndAddEditTerminalDataSets(MerchantAcquiringLedgerInfo merchantAcquiringLedger,
            Search.OrderWithInstances productIntancesWithTerminalData)
        {

            var merchant = await merchantClient.GetCoreMerchantAsync(productIntancesWithTerminalData.StoreId);

            TerminalDataSetRequest terminallDataSetsRequest = new()
            {
                AcquiringLedger = merchantAcquiringLedger.AcquiringLedger,
                OrderNumber = productIntancesWithTerminalData.OrderNumber,
                StoreId = productIntancesWithTerminalData.StoreId,
                MerchantTag = merchantAcquiringLedger.MerchantTag,
                MerchantStoresIds = merchantAcquiringLedger.MerchantStoreIds,
                Mid = merchant?.MerchantDetails?.Mid ?? string.Empty,
                MCC = merchant?.MerchantDetails?.MCC ?? string.Empty
            };


            List<ProductInstanceData> productInstancesData = new();

            foreach (var productInstance in productIntancesWithTerminalData.Instances)
            {
                productInstancesData.Add(new ProductInstanceData()
                {
                    ProductInstanceId = productInstance.ProductInstanceId,
                    ConnectionType = productInstance.ProductType == Constants.ProductType.Terminal ? Constants.TerminalConnectionType.HostToHost : Constants.TerminalConnectionType.MPGS,
                    ChannelType = productInstance.ProductType == Constants.ProductType.Terminal ? Constants.TerminalChannelType.Smartpos : Constants.TerminalChannelType.Softpos
                });
            }

            terminallDataSetsRequest.ProductInstancesData = productInstancesData;

            return await productService.GenerateTIDAndMIDAndAddEditTerminalDataSets(terminallDataSetsRequest);
        }

        private async Task<List<Guid>> UpdateTerminalProductInstancesMeta(MerchantAcquiringLedgerInfo merchantAcquiringLedger, List<TerminalDataSetResponse> terminalDataSets, string? orderCurrency)
        {
            var updateProductInstancesMetaRequest = terminalDataSets.Select(t => new UpdateProductInstanceMetaRequest
            {
                LegalName = merchantAcquiringLedger.LegalName,
                LegalNameAr = merchantAcquiringLedger.LegalNameAr,
                TradingCurrency = orderCurrency,
                MIDMerchantReference = t.MIDMerchantReference,
                ProductInstanceId = t.ProductInstanceId,
                ProviderBank = t.ProviderBank,
                TId = t.TId,
                FullTId = t.FullTId,
                MCC = t.MCC,
                Trsm = t.Trsm,
                ChannelType = t.ChannelType,
                ConnectionType = t.ConnectionType
            })
            .ToList();

            return await productService.UpdateTerminalProductInstancesMeta(updateProductInstancesMetaRequest);
        }

        private async Task SendMessageToPostilionAsync(List<Guid> updateInstanceIds, MerchantAcquiringLedgerInfo merchantAcquiringLedger,
            List<TerminalDataSetResponse> terminalDataSets)
        {
            var catalogues = await referenceService.GetCataloguesAsync(new[] { Catalogues.AccountNumber, Catalogues.LedgerToAccount,
                Catalogues.NbeCityToEPos, Catalogues.AlxCityToEPos ,Catalogues.AcquiringLedger });

            var accountNumber = RefUtils.AcquirerHelper.GetAccountNumberBasedOnCounterpartyAndAcquirer(GetReferenceCatalouges(catalogues), counterpartyProvider.GetCode(), merchantAcquiringLedger.AcquiringLedger);

            updateInstanceIds.ForEach(instanceId =>
            {
                var terminalData = terminalDataSets.First(p => p.ProductInstanceId == instanceId);
                var mappedCity = MapCityCodetoEpos(merchantAcquiringLedger.AcquiringLedger, merchantAcquiringLedger.DefaultMerchantCity, catalogues);
                string? acquiringBank = catalogues.Where(x => x.CatalogueName == Catalogues.AcquiringLedger && x.Key == merchantAcquiringLedger.AcquiringLedger)
                                        .Select(x => x.Value).FirstOrDefault();

                postilionService.SendMessageToPostilion(new PostilionMessageBody
                {
                    ProductInstanceId = instanceId,
                    AccountNumber = accountNumber ?? string.Empty,
                    CountryCode = CountryCodes.GeideaEgypt,
                    MidMerchantReference = terminalData.MIDMerchantReference,
                    LegalName = GetFullLegalName(merchantAcquiringLedger),
                    City = mappedCity,
                    FullTID = terminalData.TId,
                    ProviderBank = acquiringBank ?? string.Empty,
                    ChannelType = terminalData.ChannelType,
                    ConnectionType = terminalData.ConnectionType,
                    MPGSKEY = terminalData.MPGSKEY,
                    MPGSMID = terminalData.MPGSMID
                });
            });
        }

        private static string? MapCityCodetoEpos(string? acquiringLedger, string? locationCode, Catalogue[] catalogues)
        {
            var acquiringBanks = new string[] { AcquiringLedger.NBE, AcquiringLedger.ALX };

            if (acquiringLedger != null && !acquiringBanks.Contains(acquiringLedger))
                return locationCode;

            var mappedCityCode = catalogues.Where(x => x.CatalogueName == $"{acquiringLedger}_{CityToEPos}")
                      .FirstOrDefault(x => x.Key == locationCode);

            return mappedCityCode?.Value ?? locationCode;
        }

        private static List<RefUtils.ReferenceData> GetReferenceCatalouges(Common.Models.Catalogue[] catalogues)
        {
            return catalogues.Select(c => new RefUtils.ReferenceData
            {
                CatalogueName = c.CatalogueName,
                Key = c.Key,
                Value = c.Value
            }).ToList();
        }

        private static string? GetFullLegalName(MerchantAcquiringLedgerInfo merchantAcquiringLedger)
        {
            if (merchantAcquiringLedger.StoreName == DefaultStoreName)
                return merchantAcquiringLedger.LegalName;

            return $"{merchantAcquiringLedger.LegalName} {merchantAcquiringLedger.StoreName}";
        }
        private async Task AddUpdateOrderConfigsBasedOnAccountConfig(Guid orderId, Merchant? store)
        {
            try
            {
                var orderConfig = await checkoutClient.GetOrderConfigAsync(orderId);

                var orderAccountConfig = mapper.Map<OrderAccountConfig>(store!.AccountConfig);
                orderAccountConfig.OrderId = orderId;
                var orderCommissionConfigs = mapper.Map<List<OrderCommissionConfig>>(store.CommissionTypes);

                if (orderConfig != null && orderConfig.OrderCommissionConfigs!.Count > 0)
                {
                    var orderConfigPatch = new JsonPatchDocument<OrderConfigurationResponse>();

                    orderAccountConfig.Id = orderConfig.OrderAccountConfig!.Id;
                    orderConfigPatch.Add(x => x.OrderAccountConfig, orderAccountConfig);

                    orderConfigPatch.Add(x => x.OrderCommissionConfigs, BuildCommissionConfig(orderConfig.OrderCommissionConfigs!, orderCommissionConfigs!));

                    orderConfigPatch.Add(x => x.OrderId, orderId);
                    await checkoutClient.PatchOrderConfigAsync(orderId, orderConfigPatch);
                }
                else
                {
                    var orderConfigCreated = new OrderConfigurationResponse
                    {
                        OrderAccountConfig = orderAccountConfig,
                        OrderCommissionConfigs = orderCommissionConfigs,
                        OrderId = orderId
                    };
                    await checkoutClient.AddOrderConfigurationAsync(orderConfigCreated);
                }
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Error when calling AddUpdateOrderConfigsBasedOnAccountConfig for order with Id {@orderId}", orderId);
            }
        }
        private static IList<OrderCommissionConfig> BuildCommissionConfig(IList<OrderCommissionConfig> currentCommissionConfig, List<OrderCommissionConfig?> commissionTypes)
        {
            if (commissionTypes != null && commissionTypes.Any())
            {
                foreach (var commission in commissionTypes)
                {
                    var itemToChange = currentCommissionConfig.FirstOrDefault(d => d.ProductCode == commission?.ProductCode);

                    if (itemToChange != null)
                    {
                        itemToChange.Value = commission?.Value;
                        itemToChange.CommissionType = commission?.CommissionType;
                    }
                    else
                    {
                        if (commission != null)
                        {
                            currentCommissionConfig.Add(commission);
                        }
                    }
                }
            }
            return currentCommissionConfig;
        }
    }
}