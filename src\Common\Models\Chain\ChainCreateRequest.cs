﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.Models.Chain;

public class ChainCreateRequest
{
    public string? ChainName { get; set; }
    public string? Segment { get; set; }
    public string? RelationshipManager { get; set; }
    public ChainAddressRequest ChainAddress { get; set; } = null!;
}

public class ChainAddressRequest
{
    public Guid AddressId { get; set; }
    public string? BuildingNumber { get; set; }
    public string? BuildingName { get; set; }
    public string? AddressLine1 { get; set; }
    public string? AddressLine2 { get; set; }
    public string? City { get; set; } = string.Empty;
    public string? Country { get; set; } = string.Empty;
    public string? Zip { get; set; } = string.Empty;
}
