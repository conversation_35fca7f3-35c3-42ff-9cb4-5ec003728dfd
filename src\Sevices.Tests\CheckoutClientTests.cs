﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using BackofficeApi;
using Common;
using Common.Models;
using Common.Models.Checkout;
using Common.Models.Comment;
using Common.Options;
using Common.Services;
using FluentAssertions;
using Geidea.BackofficePortal.Backoffice.Services.Tests.Helpers;
using Geidea.Utils.Exceptions;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NSubstitute;
using NUnit.Framework;
using Services;

namespace Geidea.BackofficePortal.Backoffice.Services.Tests
{
    public class CheckoutClientTests
    {
        private readonly ILogger<CheckoutClient> logger = Substitute.For<ILogger<CheckoutClient>>();
        private readonly Mock<IOptionsMonitor<UrlSettings>> urlSettingsOptions = new Mock<IOptionsMonitor<UrlSettings>>();
        private ICheckoutClient? checkoutClient;

        [SetUp]
        public void Setup()
        {
            logger.ClearReceivedCalls();
        }

        public CheckoutClientTests()
        {
            urlSettingsOptions.Setup(x => x.CurrentValue).Returns(TestsHelper.UrlSettingsOptions);
            var profile = new AutoMapping();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
        }

        private readonly OrderResponse orderResponse = new OrderResponse()
        {
            OrderId = Guid.NewGuid(),
            AgreementId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            StoreId = Guid.NewGuid(),
            UserId = Guid.NewGuid(),
            AddressId = Guid.NewGuid(),
            OrderStatus = "OrderStauts",
            PaymentReference = "PaymentReference",
            TrackingNumber = "TrackingNumber",
            TrackingUrl = "TrackingUrl",
            Shipper = "Shipper",
            ShippedDate = DateTime.UtcNow,
            CouponCode = "CouponCode",
            PaymentMethod = "PaymentMethod",
            CompanyRegNo = "CompanyRegNo",
            SalesName = "SalesName",
            SubscriptionPlan = "SubscriptionPlan",
            ProjectName = "SABB_GENESIS",
            ReferralChannel = "UNASSIGNED",
            Note = "Note",
            MerchantName = "MerchantName",
            OrderItem = new List<OrderItemResponse>()
            {
                new OrderItemResponse()
                {
                    OrderId = Guid.NewGuid(),
                    OrderItemId = Guid.NewGuid(),
                    MerchantId = Guid.NewGuid(),
                    AddressId = Guid.NewGuid(),
                    ProductType = "ProductType",
                    DeletedFlag = false,
                    EposTicketId = "EposTicketId",
                    ProductCode = "GENESIS_SMART",
                    ProductInstanceIds = new List<Guid>()
                    {
                        Guid.Empty,
                        Guid.Empty
                    }
                }
            },
            CreatedDate = DateTime.UtcNow,
            UpdatedDate = DateTime.UtcNow,
            OrderNumber = "EX_1132",
            OrderCommentsCount = 1,
            DeletedFlag = false,
            CheckoutDate = DateTime.UtcNow,
            Subtotal = 1,
            Discount = 1,
            VatPercent = 1,
            Vat = 1,
            Total = 1,
            MonthlySubtotal = 1,
            MonthlyVat = 1,
            MonthlyTotal = 1,
            MonthlyDiscount = 1,
            YearlyDiscount = 1,
            YearlySubtotal = 1,
            YearlyTotal = 1,
            YearlyVat = 1,
            Currency = "SAR"
        };

        private readonly OrderDeleteRequest orderDeleteRequest = new OrderDeleteRequest()
            {OrderId = new Guid[] {Guid.NewGuid()}};

        private readonly OrderItemResponse orderItemResponse = new OrderItemResponse()
        {
            OrderItemId = Guid.NewGuid(),
            MerchantId = Guid.NewGuid(),
            OrderId = Guid.NewGuid(),
            AddressId = Guid.NewGuid(),
            CreatedUnixTimestampUtc = 1,
            UpdatedUnixTimestampUtc = 1,
            DeletedFlag = false,
            ProductType = "ProductType",
            EposTicketId = "EposTicketId"
        };

        private readonly CoreOrderSearchCriteria orderSearchCriteria = new CoreOrderSearchCriteria()
        {
            OrderStatus = new List<string> {"OrderStatus"},
            MerchantStatus = new List<string> {"MerchantStatus"},
            MerchantId = Guid.NewGuid(),
            AmountIntervals = new List<AmountInterval>()
            {
                new AmountInterval() {AmountMin = 1, AmountMax = 100},
                new AmountInterval() {AmountMin = 1000, AmountMax = 2000}
            },
            DateInterval = new DateInterval
            {
                FromDate = DateTime.UtcNow,
                ToDate = DateTime.UtcNow
            },
            ProjectName = new List<string> {"ProjectName"}
        };

        private readonly CommentCreateRequest commentCreateRequest = new CommentCreateRequest()
        {
            CommentText = "text"
        };

        private readonly CommentUpdateRequest commentUpdateRequest = new CommentUpdateRequest()
        {
            CommentText = "new text"
        };

        private readonly OrderCommentResponse orderCommentResponse = new OrderCommentResponse()
        {
            OrderId = Guid.NewGuid(),
            CommentId = Guid.NewGuid(),
            CommentText = "text",
            DeletedFlag = false,
            CreatedBy = "user",
            UpdatedBy = "user",
            CreatedDate = DateTime.UtcNow,
            UpdatedDate = DateTime.UtcNow,
            AuthorName = "name",
            AuthorRole = "role"
        };

        private readonly List<OrderStatusResponse> orderStatusResponse = new List<OrderStatusResponse>()
        {
            new OrderStatusResponse()
            {
                OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                UpdatedBy = new Guid("5e72fe84-1e25-41a9-be0a-17453b2d0b68").ToString(),
                OrderStatusStartDate = DateTime.Today,
                OrderStatus = "status"
            },
            new OrderStatusResponse()
            {
                OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                UpdatedBy = Guid.NewGuid().ToString(),
                OrderStatusStartDate = DateTime.Today,
                OrderStatus = "status"
            }
        };

        private CheckoutClient CreateCheckoutClientInstance(HttpClient client)
        {
            return new CheckoutClient(client, logger, urlSettingsOptions.Object);
        }

        [Test]
        public async Task GetOrderByIdAsync()
        {
            checkoutClient =
                CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                    JsonSerializer.Serialize(orderResponse)));

            var result = await checkoutClient.GetOrderByIdAsync(Guid.NewGuid());

            result.Should().BeEquivalentTo(orderResponse);
        }

        [Test]
        public async Task GetByOrderNumberAsync_WhenValid_ShouldReturnExpectedData()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK, JsonSerializer.Serialize(orderResponse)));

            var result = await checkoutClient.GetByOrderNumberAsync("id");

            result.Should().BeEquivalentTo(orderResponse);
        }

        [Test]
        public void GetOrderByIdAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.GetOrderByIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void GetByOrderNumberAsync_WhenInvalid_ShouldThrowPassthroughtError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.GetByOrderNumberAsync("id"))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }


        [Test]
        public void UpdateOrderAsync()
        {
            checkoutClient =
                CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                    JsonSerializer.Serialize(orderResponse)));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "OrderStatus ");
            patch.Replace(e => e.MerchantName, "MerchantName");
            patch.Replace(e => e.OrderStatus, "PRODUCTS_REGISTERED");
            patch.Replace(e => e.ProjectName, "test");

            checkoutClient.Invoking(x => x.UpdateOrderAsync(patch, orderResponse.OrderId)).Should()
                .NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateOrderAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            var patch = new JsonPatchDocument<OrderUpdateRequest>();

            patch.Replace(e => e.OrderStatus, "OrderStatus ");
            patch.Replace(e => e.MerchantName, "MerchantName");

            checkoutClient.Invoking(x => x.UpdateOrderAsync(patch, Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteOrderAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            checkoutClient.Invoking(x => x.DeleteOrderAsync(orderDeleteRequest)).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteOrderAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.DeleteOrderAsync(orderDeleteRequest))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetAllOrdersAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(
                TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                    JsonSerializer.Serialize(new OrderResponse[] {orderResponse})));

            var result = await checkoutClient.GetAllOrdersAsync(false);

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderResponse);
        }

        [Test]
        public void GetAllOrdersAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.GetAllOrdersAsync(false))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetOrderItemsByIdAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(new[] {orderItemResponse})));

            var result = await checkoutClient.GetOrderItemsByIdAsync(new Guid[] {Guid.NewGuid()});

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderItemResponse);
        }

        [Test]
        public void GetOrderItemsByIdAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.GetOrderItemsByIdAsync(new Guid[] {Guid.NewGuid()}))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task SearchOrderAsync()
        {
            var orderSearchResponse = new OrderSearchResponse
                {TotalRecordCount = 1, ReturnedRecordCount = 1, Records = new OrderResponse[] {orderResponse}};
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(orderSearchResponse)));

            var result = await checkoutClient.SearchOrderAsync(orderSearchCriteria);

            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderResponse);
        }

        [Test]
        public void SearchOrderAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.SearchOrderAsync(orderSearchCriteria))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteOrderItemAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            checkoutClient.Invoking(x => x.DeleteOrderItemAsync(new Guid[] {Guid.NewGuid()})).Should()
                .NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteOrderItemAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.DeleteOrderItemAsync(new Guid[] {Guid.NewGuid()}))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }


        [Test]
        public void UpdateOrderItemAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            var patch = new JsonPatchDocument<OrderItemUpdateRequest>();

            checkoutClient.Invoking(x => x.UpdateOrderItemAsync(Guid.NewGuid(), patch)).Should()
                .NotThrowAsync<Exception>();
        }

        [Test]
        public void UpdateOrderItemAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            var patch = new JsonPatchDocument<OrderItemUpdateRequest>();

            checkoutClient.Invoking(x => x.UpdateOrderItemAsync(Guid.NewGuid(), patch))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task CreateCommentAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(orderCommentResponse)));

            var result = await checkoutClient.CreateCommentAsync(Guid.NewGuid(), commentCreateRequest);
            result.Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void CreateCommentAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.CreateCommentAsync(Guid.NewGuid(), commentCreateRequest))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetCommentByIdAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(orderCommentResponse)));

            var result = await checkoutClient.GetCommentByIdAsync(Guid.NewGuid());
            result.Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void GetCommentByIdAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.GetCommentByIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task UpdateCommentAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(orderCommentResponse)));

            var result = await checkoutClient.UpdateCommentAsync(Guid.NewGuid(), commentUpdateRequest);
            result.Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void UpdateCommentAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.UpdateCommentAsync(Guid.NewGuid(), commentUpdateRequest))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public void DeleteCommentAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK));

            checkoutClient.Invoking(x => x.DeleteCommentAsync(Guid.NewGuid())).Should().NotThrowAsync<Exception>();
        }

        [Test]
        public void DeleteCommentAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.DeleteCommentAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetCommentByOrderIdAsync()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(new[] {orderCommentResponse})));

            var result = await checkoutClient.GetCommentByOrderIdAsync(Guid.NewGuid());
            result.Length.Should().Be(1);
            result[0].Should().BeEquivalentTo(orderCommentResponse);
        }

        [Test]
        public void GetCommentByOrderIdAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.GetCommentByOrderIdAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }

        [Test]
        public async Task GetOrdersStatusHistoryAsync()
        {
            var expectedOrderStatusResponses = new List<OrderStatusResponse>()
            {
                new OrderStatusResponse()
                {
                    OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                    UpdatedBy = "FirstName LastName",
                    OrderStatusStartDate = DateTime.Today,
                    OrderStatus = "status"
                },
                new OrderStatusResponse()
                {
                    OrderId = new Guid("8e5514bb-4a79-41c1-a601-d3135dbe18dd").ToString(),
                    UpdatedBy = Constants.User.DefaultUserValue,
                    OrderStatusStartDate = DateTime.Today,
                    OrderStatus = "status"
                }
            };

            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.OK,
                JsonSerializer.Serialize(expectedOrderStatusResponses)));

            var result = await checkoutClient.GetOrdersStatusHistoryAsync(Guid.NewGuid());
            result.Count.Should().Be(2);
            result.Should().BeEquivalentTo(expectedOrderStatusResponses);
        }

        [Test]
        public void GetOrdersStatusHistoryAsync_PassthroughError()
        {
            checkoutClient = CreateCheckoutClientInstance(TestsHelper.CreateHttpClient(HttpStatusCode.NotFound));

            checkoutClient.Invoking(x => x.GetOrdersStatusHistoryAsync(Guid.NewGuid()))
                .Should().ThrowAsync<PassthroughException>()
                .Where(x => x.StatusCode == HttpStatusCode.NotFound);
        }
    }
}