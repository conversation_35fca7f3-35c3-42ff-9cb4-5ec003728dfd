﻿using System.Collections.Generic;

namespace Common
{
    public static class Constants
    {
        public const string DefaultStoreName = "Default Store";
        public const string CityToEPos = "CITY_TO_EPOS";
        public const string Language = "en";

        public static readonly IReadOnlyCollection<string> BusinessTagsOnly = new List<string>()
        {
            MerchantTag.Wholesaler,
            MerchantTag.MasterBusiness,
            MerchantTag.SubWholesaler,
            MerchantTag.SubBusiness
        };

        public static class ShareholderIndividualsRelationTypes
        {
            public const string Company = "COMPANY";
            public const string WathqAuthorizedSignatory = "WATHQ_AUTHORIZED_SIGNATORY";
            public const string Member = "MEMBER";
            public const string PoaHolder = "POA_HOLDER";
            public const string Ubo = "UBO";
            public const string Stakeholder = "STAKEHOLDER";
            public const string DirectShareholder = "DIRECT_SHAREHOLDER";
            public const string Owner = "OWNER";
        }

        public static class Nexus
        {
            public const string Country = "United Arab Emirates";
            public const string RiskLevel = "Low";
            public const string IsInterChange = "N";
            public const int Vat = 5;
            public const string Ecom = "ECOM";
            public const string PGW = "PGW";
        }

        public static class Risk
        {
            public const string Low = "Low";
            public const string High = "High";
            public const string Medium = "Medium";
            public const string MRisk = "Medium risk";
            public const string HRisk = "High risk";
            public const string Sas = "SAS_CHECK";
        }

        public static class Checks
        {
            public const string FullInfoCheck = "FULL_INFO_CHECK";
            public const string IdentityCheck = "ID_CHECK";
            public const string BankCheckEmpty = "BANK_CHECK_EMPTY";
        }

        public static class Catalogues
        {
            public const string AccountNumber = "ACCOUNT_NUMBER";
            public const string AcquiringLedger = "ACQUIRING_LEDGER";
            public const string AcquiringLedgerToEpos = "ACQUIRING_LEDGER_TO_EPOS";
            public const string LedgerToAccount = "LEDGER_TO_ACCOUNT";
            public const string CommercialRegistration = "COMMERCIAL_REGISTRATION";
            public const string DefaultKey = "DEFAULT";
            public const string Cities = "CITIES";
            public const string Areas = "AREAS";
            public const string Banks = "BANKS";
            public const string Governorates = "GOVERNORATES";
            public const string MerchantCategoryCode = "MERCHANT_CATEGORY_CODE";
            public const string ProductCodeToReferralChannel = "PRODUCT_CODE_TO_REFERAL_CHANNEL";
            public const string ReferralChannel = "REFERRAL_CHANNEL";
            public const string ReferralChannelToProjectName = "REFERRAL_CHANNEL_TO_PROJECT_NAME";
            public const string ReferralChannelRestrictions = "REFERRAL_CHANNEL_RESTRICTIONS";
            public const string OrderStatus = "ORDER_STATUS";
            public const string MerchantStatus = "MERCHANT_STATUS";
            public const string ProjectName = "PROJECT_NAME";
            public const string BusinessDomain = "BUSINESS_DOMAINS";
            public const string MccToBusinessDomain = "MCC_TO_BUSINESS_DOMAINS";
            public const string MerchantTag = "MERCHANT_TAG";
            public const string CompanyCheckStatus = "COMPANY_CHECKS_STATUS";
            public const string Product = "PRODUCT";
            public const string TaskStatuses = "TASK_STATUSES";
            public const string TerminationReasonCode = "TERMINATION_REASON_CODE";
            public const string BankCheckStatus = "BANK_CHECK_STATUS";
            public const string Nationalities = "NATIONALITY";
            public const string CountriesIso3166 = "COUNTRIES_ISO3166";
            public const string NbeCityToEPos = "NBE_BANK_CITY_TO_EPOS";
            public const string AlxCityToEPos = "ALX_BANK_CITY_TO_EPOS";
            public const string NbeCities = "NBE_BANK_CITIES";
            public const string NbeGovernarotes = "NBE_BANK_GOVERNORATES";
            public const string RelationToCompany = "RELATION_TO_COMPANY";
            public const string Designation = "DESIGNATION";
            public const string Segment = "SEGMENT";
            public const string AcquiringLedgerToGsdkMapping = "ACQUIRING_LEDGER_TO_GSDK_LEDGER";
            public const string ProductTypeDefaultLedgerMapping = "PRODUCT_TYPE_TO_DEFAULT_LEDGER";
            public const string NbeBusinessDomain = "NBE_BANK_BUSINESS_DOMAINS";
            public const string NbeMerchantCategoryCode = "NBE_BANK_MERCHANT_CATEGORY_CODE";
            public const string NbeCitiesToGovernarotes = "NBE_BANK_CITY_TO_GOVERNORATE";
            public const string CitiesToGovernarotes = "CITY_TO_GOVERNORATE";
            public const string AlxCities = "ALX_BANK_CITIES";
            public const string AlxCitiesToGovernarotes = "ALX_BANK_CITY_TO_GOVERNORATE";
            public const string AlxGovernarotes = "ALX_BANK_GOVERNORATES";
            public const string AlxBusinessDomain = "ALX_BANK_BUSINESS_DOMAINS";
            public const string AlxMerchantCategoryCode = "ALX_BANK_MERCHANT_CATEGORY_CODE";
            public const string GsdkOutAccountProviderToAcquirer = "GSDK_OUT_PROVIDER_TO_ACQUIRER";
            public const string TransactionType = "TRANSACTION_TYPE";
            public const string AcceptedPaymentMethods = "ACCEPTED_PAYMENT_METHODS";
        }

        public static class MerchantStatus
        {
            public const string BoardingInProgress = "BOARDING_IN_PROGRESS";
            public const string BoardingCompleted = "BOARDING_COMPLETED";
            public const string Verified = "VERIFIED";
            public const string VerificationError = "VERIFICATION_ERROR";
            public const string VerificationInProgress = "VERIFICATION_IN_PROGRESS";
            public const string RequireCACApproval = "CAC_APPROVAL";
            public const string PendingPricingApproval = "PENDING_PRICING_APPROVAL";
            public const string Returned = "RETURNED";
            public const string Rejected = "BOARDING_DECLINED";
            public const string Active = "MERCHANT_ACTIVE";
            public const string ComplianceApproval = "COMPLIANCE_APPROVAL";
            public const string RiskApproval = "RISK_APPROVAL";
        }

        public static class RecipientName
        {
            public const string BoardingInProgress = "Sale Support Team";
            public const string BoardingCompleted = "Merchant Enablement Team";
            public const string ComplianceApproval = "Compliance Team";
            public const string RiskApproval = "Risk Team";
            public const string Submitted = "Sales Manager";
            public const string CPVerified = "Merchant Enablement Team";
            public const string CNPVerified = "Merchant Enablement and Payment Gateway Team";
        }

        public static class OrderStatus
        {
            public const string Verified = "VERIFIED";
            public const string ProductRegistered = "PRODUCTS_REGISTERED";
            public const string TicketCreated = "TICKET_CREATED";
            public const string Installed = "INSTALLED";
            public const string PaymentConfirmed = "PAYMENT_CONFIRMED";
            public const string Closed = "CLOSED";
            public const string New = "NEW";
            public const string Submitted = "SUBMITTED";
            public const string VerificationInProgress = "VERIFICATION_IN_PROGRESS";
            public const string Refunded = "REFUNDED";
            public const string Cancelled = "CANCELLED";
            public const string Returned = "RETURNED";
            public const string Rejected = "REJECTED";
        }

        public static class LeadStatus
        {
            public const string New = "NEW";
            public const string Converted = "CONVERTED";
        }

        public static class ProductTypes
        {
            public const string Terminal = "TERMINAL";
            public const string Gateway = "GWAY";
            public const string MPOS = "M_POS";
            public const string Bundle = "BUNDLE";
        }

        public static class GsdkContractPaymentWay
        {
            public const string Terminal = "Terminal";
            public const string Gateway = "Online Payment";
            public const string SoftPos = "SoftPOS";
        }

        public static class MmsLedgers
        {
            public const string SaudiDefault = "DEFAULT_BANK";
            public const string SaudiSABB = "SABB";
            public const string EgyptDefault = "DEFAULT_BANK";
            public const string EgyptNBEBank = "NBE_BANK";
            public const string NEW = "New";
            public const string DUAL = "Dual";
        }

        public static class GsdkLedgers
        {
            public const string EgyptDefault = "MISR-LEDGER";
            public const string SaudiSABB = "SABB-LEDGER";
            public const string SaudiDefault = "RIYAD-LEDGER";
        }

        public static class OutProviderAccounts
        {
            public const string EgyptMisrBank = "MisrBank";
            public const string EgyptNBEBank = "NbeBank";
        }

        public static class User
        {
            public const string DefaultUserValue = "n/a";
        }

        public static class Gsdk
        {
            public const string AuthorizationHeaderName = "Authorization";
            public const string IdentifierKey = "GsdkOrganizationId";
            public const string ExternalSourceId = "Gsdk";
        }

        public static class ProductType
        {
            public const string Mpos = "M_POS";
            public const string Terminal = "TERMINAL";
            public const string Gway = "GWAY";
            public const string Bundle = "BUNDLE";
            public const string Accesorries = "ACCESSORIES";
        }

        public static class ProductCode
        {
            public const string BillPayment = "BILL_PAYMENT";
            public const string GoSmart = "GO_SMART";
            public const string GoSmartBP = "GO_SMART_BP";
            public const string GoAir = "GO_AIR";
            public const string SoftPos = "SOFT_POS";
        }

        public static class TerminalConnectionType
        {
            public const string MPGS = "MPGS";
            public const string HostToHost = "H2H";
        }

        public static class TerminalChannelType
        {
            public const string Softpos = "SOFT_POS";
            public const string Smartpos = "SMART_POS";
        }

        public static class OrderBillPaymentsStatus
        {
            public const string NotAdded = "N_A";
        }

        public static class ForbiddenProductCodes
        {
            public const string SpectraSp530 = "SPECTRA_SP_530";
            public const string Vx675 = "VX_675";
        }

        public static class DocumentType
        {
            public const string BankStatement = "BANK_STATEMENT";
            public const string GeneralContract = "GENERAL_CONTRACT";
            public const string MunicipalityLicense = "MUNICIPAL_LICENSE";
            public const string LegalEnterpriseLicense = "LEGAL_ENTERPRISE_LICENSE";
            public const string Passport = "PASSPORT";
            public const string NationalId = "NATIONAL_ID";
            public const string FreelanceId = "FREELANCE_ID";
            public const string CommercialRegistration = "COMMERCIAL_REG";
            public const string ShopPhoto = "SHOP_PHOTO";
            public const string AdditionalDocuments = "ADDITIONAL_DOCUMENTS";
        }

        public static class BusinessType
        {
            public const string Limited = "LIMITED";
            public const string SoleTrader = "SOLE_TRADER";
            public const string MunicipalEntity = "MUNICIPAL_ENTITY";
            public const string LegalEnterprise = "LEGAL_ENTERPRISE";
            public const string Other = "OTHER";
            public const string LLC = "LLC";
            public const string SoleEstablishment = "SE";
            public const string PSC = "PSC";
            public const string JSC = "JSC";
            public const string Partnership = "PARTNERSHIP";
            public const string Branch = "BRANCH";
        }

        public static class ChargeType
        {
            public const string Reccurrring = "RECCURRING_CHARGE";
            public const string SetupCharge = "SETUP_CHARGE";
            public const string RetailPrice = "RETAIL_PRICE";
        }

        public static class MerchantTag
        {
            public const string Retail = "RETAIL";
            public const string MasterBusiness = "MASTER_BUSINESS";
            public const string Wholesaler = "WHOLESALER";
            public const string SubBusiness = "SUB_BUSINESS";
            public const string SubWholesaler = "SUB_WHOLESALER";
        }

        public static class HierarchyType
        {
            public const string Structural = "STRUCTURAL_HIERARCHY";
            public const string Business = "BUSINESS_HIERARCHY";
        }

        public static class ReferralChannel
        {
            public const string UNASSIGNED = "UNASSIGNED";
            public const string HSBC = "HSBC";
        }

        public static class PatchPath
        {
            public const string ProjectName = "projectName";
        }

        public static class LeadPatch
        {
            public const string LeadReferralChannel = "referralChannel";
        }

        public static class CounterParty
        {
            public const string Saudi = "GEIDEA_SAUDI";
            public const string Egypt = "GEIDEA_EGYPT";
            public const string Uae = "GEIDEA_UAE";
        }

        public static class CountryPrefix
        {
            public const string Saudi = "+966";
        }

        public static class CountryCodes
        {
            public const string GeideaSaudi = "SA";
            public const string GeideaEgypt = "EG";
            public const string GeideaUae = "AE";
        }

        public static class TaskStatusesKeys
        {
            public const string Open = "OPEN";
            public const string Done = "DONE";
        }

        public static class MatchTemplatesFilesSettings
        {
            public const string TableHeaderFileName = "/TableHeaderTemplate.html";
            public const string TableRowFileName = "/TableRowTemplate.html";
            public const string ReportTempalteFileName = "/ReportTemplate.html";

            public const string TableRowPropertyPlaceholder = "PropertyName";
            public const string TableRowPropertyValuePlaceholder = "PropertyValue";
            public const string TableRowPropertyMatchPlaceholder = "MatchValue";
            public const string FolderPath = "/Templates";
        }

        public static class MatchStatus
        {
            public const string Failed = "FAILED";
            public const string Pending = "PENDING";
            public const string Passed = "PASSED";
        }

        public static class AcquiringLedger
        {
            public const string MB = "DEFAULT_BANK";
            public const string NBE = "NBE_BANK";
            public const string ALX = "ALX_BANK";
        }

        public static class GsdkHeaders
        {
            public const string AuthorizationHeaderName = "Authorization";
            public const string LedgerHeaderName = "Ledger";
            public const string OrganizationIdHeaderName = "Editable-Organization-Id";
        }

        public static class MerchantPatchOperation
        {
            public const string UpdateMccOperation = "merchantDetails/mcc";
        }

        public static class OrderSearchProperties
        {
            public const string TerminalTID = "TerminalTID";
            public const string StoreCity = "StoreCity";
        }

        public static class CheckType
        {
            public const string MatchCheck = "MATCH_CHECK";
            public const string WorldCheckOne = "WORLD_CHECK_ONE";
            public const string BankCheck = "BANK_CHECK";
            public const string RiskScoring = "RISK_SCORING";
            public const string AcquiringRisk = "ACQUIRING_RISK";
            public const string SASCheck = "SAS_CHECK";
            public const string FreelanceCheck = "FREELANCE_CHECK";
            public const string FullInfoCheck = "FULL_INFO_CHECK";
            public const string FinscanCheck = "FINSCAN_CHECK";
        }

        public static class CheckStatus
        {
            public const string MatchCheckNoHit = "MATCH_CHECK_NO_HIT";
            public const string MatchCheckPending = "MATCH_CHECK_PENDING";
            public const string MatchCheckPositiveHit = "MATCH_CHECK_POSITIVE_HIT";
            public const string WorldCheckOneKYCNoHit = "WORLD_CHECK_ONE_NO_HIT";
            public const string WorldCheckOneKYCPending = "WORLD_CHECK_ONE_PENDING";
            public const string WorldCheckOneKYCPositiveHit = "WORLD_CHECK_ONE_POSITIVE_HIT";
            public const string BankCheckPassed = "BANK_CHECK_PASSED";
            public const string RiskScoringLow = "RISK_SCORING_LOW";
            public const string RiskScoringMedium = "RISK_SCORING_MEDIUM";
            public const string RiskScoringHigh = "RISK_SCORING_HIGH";
            public const string RiskScoringVeryHigh = "RISK_SCORING_VERY_HIGH";
            public const string RiskScoringProhibited = "RISK_SCORING_PROHIBITED";
            public const string RiskScoringPending = "RISK_SCORING_PENDING";
            public const string AcquiringRiskLow = "ACQUIRING_RISK_LOW";
            public const string AcquiringRiskMedium = "ACQUIRING_RISK_MEDIUM";
            public const string AcquiringRiskHigh = "ACQUIRING_RISK_HIGH";
            public const string AcquiringRiskVeryHigh = "ACQUIRING_RISK_VERY_HIGH";
            public const string AcquiringRiskProhibited = "ACQUIRING_RISK_PROHIBITED";
        }

        public static class KsaCheckStatus
        {
            public const string MatchCheckPassed = "MATCH_CHECK_PASSED";
            public const string FinscanCheckPassed = "FINSCAN_CHECK_PASS";
            public const string BankCheckPassed = "BANK_CHECK_PASSED";
            public const string SasCheckPassed = "SAS_CHECK_PASSED";
            public const string FullInfoCheckPass = "FULL_INFO_CHECK_PASS";
            public const string FreelanceCheckPassed = "FREELANCE_CHECK_PASSED";
        }

        public static class PersonCheck
        {
            public const string FinscanCheck = "FINSCAN_CHECK";
            public const string IdentityCheck = "ID_CHECK";
        }

        public static class PersonCheckStatus
        {
            public const string IdentityCheckPassed = "ID_CHECK_PASS";
            public const string FinscanCheckPassed = "FINSCAN_CHECK_PASS";
            public const string FinscanCheckPending = "FINSCAN_CHECK_PENDING";

        }

        public static class ChannelType
        {
            public const string CardPresent = "POS";
            public const string CardNotPresent = "PGW";
        }

        public static class KycCheckStatus
        {
            public const string Approved = "APPROVED";
        }

        public static class RiskApprovalTransactionType
        {
            public const int MOTO = 9;
            public const int KEY_IN = 10;
            public const int REFUND = 5;
            public const int TOKENIZATION = 11;
        }

        public static class Status
        {
            public const string Active = "Active";
            public const string InActive = "InActive";
        }

        public static class MigrationRequestStatus
        {
            public const string Enabled = "Yes";
            public const string Disabled = "No";
        }

        public static class RabbitMqExchanges
        {
            public const string ApexResponseExchange = "NexusBridge.Apex.Response";
        }

        public static class RabbitMqQueues
        {
            public const string ApexResponseQueue = "NexusBridge.Apex.Response";
        }

        public static class RabbitMqRoutingKeys
        {
            public const string ApexResponseRoutingKey = "NexusBridge.Apex.Response";
        }
    }
}