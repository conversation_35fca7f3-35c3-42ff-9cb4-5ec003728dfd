﻿using System;
using System.Collections.Generic;

namespace Common.Models.Shareholder
{
    public class MerchantShareholderIndividual
    {
        public Guid PersonOfInterestId { get; set; }
        public string NationalId { get; set; } = string.Empty;
        public string? Nationality { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? FirstNameAr { get; set; }
        public string? LastNameAr { get; set; }
        public DateTime? IdExpiryDate { get; set; }
        public DateTime? DOB { get; set; }
        public string? PhonePrefix { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PassportNo { get; set; }
        public DateTime? PassportExpirationDate { get; set; }
        public string? KYCCheck { get; set; }
        public string? Email { get; set; }

        public bool? PEP { get; set; }
        public ShareholderIndividualAddress? Address { get; set; }
        public List<IndividualRelation> Relations { get; set; } = new();
    }
}
