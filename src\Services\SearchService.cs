﻿using Common.Services;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Common.Models.Checkout;
using System.Collections.Generic;
using Geidea.Utils.Json;
using Geidea.Utils.Exceptions;
using System.Net.Http;
using AutoMapper;
using Common.Options;
using Microsoft.Extensions.Options;
using System.Text.Json;
using Newtonsoft.Json;
using System.Text;
using Common.Models;
using Common.Models.Product;
using Common.Models.Search;
using Common.Models.Tasks;
using System;
using Common.Models.Merchant.SubordinateMerchant;
using Common.Models.TerminalDataSet;
using Common.Models.User;
using Geidea.Utils.Counterparty.Providers;
using Common.Models.Lead;

namespace Services
{
    public class SearchService : ISearchService
    {
        private readonly ILogger<SearchService> logger;
        private readonly HttpClient client;
        private readonly IMapper mapper;
        private readonly UrlSettings urlSettingsOptions;
        private readonly IProductService productService;
        private readonly ICounterpartyProvider counterpartyProvider;

        public SearchService(ILogger<SearchService> logger, IMapper mapper,
                             HttpClient client, IOptionsMonitor<UrlSettings> urlSettingsOptions,
                             IProductService productService, ICounterpartyProvider counterpartyProvider)
        {
            this.logger = logger;
            this.mapper = mapper;
            this.client = client;
            this.urlSettingsOptions = urlSettingsOptions.CurrentValue;
            this.productService = productService;
            this.counterpartyProvider = counterpartyProvider;
        }

        private const string OrderExportEndpoint = "/api/v1/Order/export";
        private const string OrderProductInstancesWithTerminalDataEndpoint = "/api/v1/Order/terminalInstances";
        private string SearchServiceBaseUrl => $"{urlSettingsOptions.SearchServiceBaseUrlNS}";
        private string TaskSearchServiceEndpoint => $"{SearchServiceBaseUrl}/api/v1/task/advancedSearch";
        private const string MerchantEndpoint = "/api/v1/Merchant";
        private const string UserEndpoint = "/api/v1/user";
        private const string OrdersProductInstancesEndpoint = "/api/v1/Order/terminalInstances";

        public async Task<List<OrdersExport>> ExportOrdersAsync(OrderSearchCriteria orderSearchCriteria)
        {
            var coreOrderSearchCriteria = await BuildOrderSearchCriteria(orderSearchCriteria);

            if (coreOrderSearchCriteria == null)
            {
                return new List<OrdersExport>();
            }

            string url = $"{SearchServiceBaseUrl}{OrderExportEndpoint}";
            var requestBody = new StringContent(JsonConvert.SerializeObject(coreOrderSearchCriteria), Encoding.UTF8, "application/json");

            using (logger.BeginScope("ExportOrderAsync({@url})", url))
            {
                logger.LogInformation($"Calling search service to export orders'.");

                var response = await client.PostAsync(url, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling search service to export orders. Error was {StatusCode} {@responseBody}",
                       (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var orderResponse = Json.Deserialize<List<OrdersExport>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                logger.LogInformation($"Found '{orderResponse.Count}' order records.");

                return orderResponse;
            }
        }

        public async Task<SearchResponse<TaskResponse>> TaskAdvancedSearch(TaskSearchRequest searchRequest)
        {
            using (logger.BeginScope("TaskService({taskSearchServiceUrl})", TaskSearchServiceEndpoint))
            {
                logger.LogInformation("Calling search service to get tasks by search criteria : {@searchCriteria}.", searchRequest);

                var body = new StringContent(JsonConvert.SerializeObject(searchRequest), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(TaskSearchServiceEndpoint, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling search service to get tasks by search criteria : {@searchCriteria}. Error was {StatusCode} {@responseBody}",
                        searchRequest, (int)response.StatusCode, responseBody);

                    throw new PassthroughException(response);
                }

                var tasks = Json.Deserialize<SearchResponse<TaskResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                return tasks;
            }
        }

        public async Task<OrderWithInstances> GetOrderProductInstancesWithTerminalDataAsync(Guid orderId, List<string> productTypes)
        {
            var types = JsonConvert.SerializeObject(productTypes);

            string url = $"{SearchServiceBaseUrl}{OrderProductInstancesWithTerminalDataEndpoint}/{orderId}?productTypes={types}";

            using (logger.BeginScope("GetOrderProductInstancesWithTerminalDataAsync({@url})", url))
            {
                logger.LogInformation($"Calling search service GetOrderProductInstancesWithTerminalDataAsync'.");

                var response = await client.GetAsync(url);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling GetOrderProductInstancesWithTerminalDataAsync. Error was {StatusCode} {@responseBody}",
                       (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var orderResponse = Json.Deserialize<OrderWithInstances>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                return orderResponse;
            }
        }

        public async Task<SubordinateMerchantSearchResponse> SearchSubordinateMerchants(SubordinateMerchantSearchFilters filters)
        {
            var searchServiceUrl = $"{SearchServiceBaseUrl}{MerchantEndpoint}/subordinateMerchants/advancedSearch";

            using (logger.BeginScope("Subordinate Merchants ({@searchServiceUrl})", searchServiceUrl))
            {
                logger.LogInformation("Calling search service to search for subordinate merchants");

                var body = new StringContent(JsonConvert.SerializeObject(filters), Encoding.UTF8, "application/json");
                var response = await client.PostAsync(searchServiceUrl, body);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling search service to search for subordinate merchants. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var subordinateMerchants = Json.Deserialize<SubordinateMerchantSearchResponse>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                logger.LogInformation("Finished Subordinate Merchant Search.");
                return subordinateMerchants;
            }
        }

        public async Task<List<OrdersWithInstancesResponse>> GetOrdersProductInstancesWithTerminalDataAsync(List<Guid> ordersIds)
        {
            var types = JsonConvert.SerializeObject(new List<string> { Common.Constants.ProductType.Terminal, Common.Constants.ProductType.Mpos });

            string url = $"{SearchServiceBaseUrl}{OrdersProductInstancesEndpoint}?productTypes={types}";
            var requestBody = new StringContent(JsonConvert.SerializeObject(ordersIds), Encoding.UTF8, "application/json");

            using (logger.BeginScope("GetOrdersProductInstancesWithTerminalDataAsync({@url})", url))
            {
                logger.LogInformation($"Calling search service GetOrdersProductInstancesWithTerminalDataAsync'.");

                var response = await client.PostAsync(url, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling GetOrdersProductInstancesWithTerminalDataAsync. Error was {StatusCode} {@responseBody}",
                       (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                var ordersResponse = Json.Deserialize<List<OrdersWithInstancesResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                return ordersResponse;
            }
        }

        private async Task<CoreOrderSearchCriteria?> BuildOrderSearchCriteria(OrderSearchCriteria orderSearchCriteria)
        {
            var coreOrderSearchCriteria = mapper.Map<CoreOrderSearchCriteria>(orderSearchCriteria);

            if (orderSearchCriteria.ProductCodes.Count > 0)
            {
                var productIds = await productService.GetRelatedProductsAsync(new ProductCodesRequest
                {
                    ProductCodes = orderSearchCriteria.ProductCodes.ToArray()
                });

                if (productIds.Length == 0)
                {
                    return null;
                }

                coreOrderSearchCriteria.ProductIds.AddRange(productIds);
            }
            return coreOrderSearchCriteria;
        }

        public async Task<UserSearchResponse<UserAdvancedSearchResponse>> FindUsersAsync(UserAdvancedSearchFilter filter)
        {
            string userServiceUrl = $"{SearchServiceBaseUrl}{UserEndpoint}/advancedSearch";

            if (filter.Counterparty == null)
                filter.Counterparty = new List<string> { counterpartyProvider.GetCode() };

            using (logger.BeginScope("FindAsync({@userServiceUrl})", userServiceUrl))
            {
                logger.LogInformation($"Calling user service to get all users");

                var requestBody = new StringContent(JsonConvert.SerializeObject(filter), Encoding.Default, "application/json");
                var response = await client.PostAsync(userServiceUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to get users. Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                return Json.Deserialize<UserSearchResponse<UserAdvancedSearchResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }

        public async Task<UserRoleSearchResponse<UserRoleAdvancedSearchResponse>> FindUsersRoleAsync(UserRoleSearchFilter filter)
        {
            string userRoleSearchUrl = $"{SearchServiceBaseUrl}{UserEndpoint}/roles";

            if (filter.Counterparty == null)
                filter.Counterparty = new List<string> { counterpartyProvider.GetCode() };

            using (logger.BeginScope("FindAsync({@userServiceUrl})", userRoleSearchUrl))
            {
                logger.LogInformation("Calling search service to get all users roles with {URL}", userRoleSearchUrl);

                var requestBody = new StringContent(JsonConvert.SerializeObject(filter), Encoding.Default, "application/json");
                var response = await client.PostAsync(userRoleSearchUrl, requestBody);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    logger.LogCritical("Error when calling user service to get users roles . Error was {StatusCode} {@responseBody}", (int)response.StatusCode, responseBody);
                    throw new PassthroughException(response);
                }

                logger.LogInformation("Calling search service to get all users roles with {response}", responseBody);

                return Json.Deserialize<UserRoleSearchResponse<UserRoleAdvancedSearchResponse>>(responseBody,
                    new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
            }
        }
    }
}