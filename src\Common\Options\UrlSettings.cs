﻿namespace Common.Options
{
    public class UrlSettings
    {
        public string MerchantServiceBaseUrl { get; set; } = null!;
        public string GeideaFederationServiceBaseUrl { get; set; } = null!;
        public string DocumentServiceBaseUrl { get; set; } = null!;
        public string NotificationServiceBaseUrl { get; set; } = null!;
        public string CheckoutServiceBaseUrl { get; set; } = null!;
        public string UserServiceBaseUrl { get; set; } = null!;
        public string ReferenceServiceBaseUrl { get; set; } = null!;
        public string LeadServiceBaseUrl { get; set; } = null!;
        public string SearchServiceBaseUrl { get; set; } = null!;
        public string ProductServiceBaseUrl { get; set; } = null!;
        public string DueDiligenceServiceBaseUrl { get; set; } = null!;
        public string NexusBridgeApiBaseUrl { get; set; } = null!;
        public string RequestLogServiceBaseUrl { get; set; } = null!;

        public string EposOrderFederationApiBaseUrl { get; set; } = null!;
        public string MerchantServiceBaseUrlNS { get; set; } = null!;
        public string DocumentServiceBaseUrlNS { get; set; } = null!;
        public string NotificationServiceBaseUrlNS { get; set; } = null!;
        public string CheckoutServiceBaseUrlNS { get; set; } = null!;
        public string UserServiceBaseUrlNS { get; set; } = null!;
        public string ReferenceServiceBaseUrlNS { get; set; } = null!;
        public string LeadServiceBaseUrlNS { get; set; } = null!;
        public string SearchServiceBaseUrlNS { get; set; } = null!;
        public string ProductServiceBaseUrlNS { get; set; } = null!;
        public string DueDiligenceServiceBaseUrlNS { get; set; } = null!;

    }
}
