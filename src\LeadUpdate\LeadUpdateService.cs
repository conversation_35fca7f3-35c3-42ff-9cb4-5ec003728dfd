﻿using System;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LeadUpdate.Models;
using LeadUpdate.Services;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace LeadUpdate
{
    public class LeadUpdateService
    {
        private readonly ILeadService leadService;
        private readonly IDocumentService documentService;
        private readonly ILogger<LeadUpdateService> logger;

        public LeadUpdateService(ILeadService leadService, IDocumentService documentService, ILogger<LeadUpdateService> logger)
        {
            this.leadService = leadService;
            this.documentService = documentService;
            this.logger = logger;
        }

        public async Task UpdateLeadsAsync(int batchSize, int startIndex, LeadUpdateType leadUpdateType, string sort)
        {
            var leadsCount = await leadService.GetLeadsCountAsync();

            for (int i = startIndex; i <= leadsCount / batchSize; i++)
            {
                try
                {
                    logger.LogInformation($"Getting leads and documents for batch {i}");

                    var leadsBatch = await leadService.GetIncompleteLeadsAsync(i * batchSize, batchSize, leadUpdateType, sort);

                    var documents = await documentService.GetDocumentsAsync(new DocumentSearchRequest
                    {
                        LeadIds = leadsBatch.Select(l => l.LeadId).ToArray(),
                        DocumentType = Constants.IdentityCheckDocumentType
                    });

                    var leadsWithIdentityCheck = leadsBatch.Join(
                        documents.GroupBy(d => d.LeadId).Select(g => g.First()),
                        l => l.LeadId,
                        d => d.LeadId,
                        (lead, document) => (lead, document));

                    foreach (var leadWithIdentityCheck in leadsWithIdentityCheck)
                    {
                        await UpdateLeadAsync(leadWithIdentityCheck.lead, leadWithIdentityCheck.document, leadUpdateType);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, $"Couldn't process batch {i}.");
                }
            }
        }

        private async Task UpdateLeadAsync(Lead lead, DocumentWithContent document, LeadUpdateType leadUpdateType)
        {
            try
            {
                var identityCheck =
                    JsonConvert.DeserializeObject<TahakomResponse>(Encoding.UTF8.GetString(document.Content));

                var leadPatch = new JsonPatchDocument<Lead>();

                if (leadUpdateType == LeadUpdateType.NamesAndSasInfo)
                {
                    if (string.IsNullOrWhiteSpace(lead.OwnerFirstName))
                    {
                        leadPatch.Add(x => x.OwnerFirstName,
                            !string.IsNullOrWhiteSpace(identityCheck.user_info[0].english_first_name)
                                ? GetTrimedValue(identityCheck.user_info[0].english_first_name)
                                : GetTrimedValue(identityCheck.user_info[0].arabic_first_name));
                    }

                    if (string.IsNullOrWhiteSpace(lead.OwnerLastName))
                    {
                        leadPatch.Add(x => x.OwnerLastName,
                            !string.IsNullOrWhiteSpace(identityCheck.user_info[0].english_family_name)
                                ? GetTrimedValue(identityCheck.user_info[0].english_family_name)
                                : GetTrimedValue(identityCheck.user_info[0].arabic_family_name));
                    }

                    if (string.IsNullOrWhiteSpace(lead.OwnerFirstNameAr))
                        leadPatch.Add(x => x.OwnerFirstNameAr,
                            GetTrimedValue(identityCheck.user_info[0].arabic_first_name));
                    if (string.IsNullOrWhiteSpace(lead.OwnerLastNameAr))
                        leadPatch.Add(x => x.OwnerLastNameAr,
                            GetTrimedValue(identityCheck.user_info[0].arabic_family_name));
                    if (string.IsNullOrWhiteSpace(lead.Gender))
                        leadPatch.Add(x => x.Gender, GetTrimedValue(identityCheck.user_info[0].gender));
                    if (string.IsNullOrWhiteSpace(lead.Nationality))
                        leadPatch.Add(x => x.Nationality, GetTrimedValue(identityCheck.user_info[0].nationality));
                    if (lead.DOB == null)
                        leadPatch.Add(x => x.DOB, GetDateTime(identityCheck.user_info[0].dob));
                }

                if (leadUpdateType == LeadUpdateType.ExpiryDate)
                {
                    leadPatch.Add(x => x.IdExpiryDate, GetDateTime(identityCheck.user_info[0].id_expiry_date_gregorian));
                }

                if (leadPatch.Operations.Any())
                {
                    await leadService.PatchLeadAsync(lead.LeadId, leadPatch);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Couldn't update lead with id {lead.LeadId}");
            }
        }


        private string? GetTrimedValue(string? value)
        {
            if (!string.IsNullOrEmpty(value))
                return value.Trim();

            return null;
        }

        private DateTimeOffset? GetDateTime(string? dateTimeString)
        {
            if (dateTimeString == null)
                return null;
            try
            {
                return DateTimeOffset.ParseExact(dateTimeString, "yyyy/MM/dd", CultureInfo.InvariantCulture);
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, $"Date '{dateTimeString}' not in the expected format 'yyyy/mm/dd'.");
                return null;
            }
        }
    }
}
