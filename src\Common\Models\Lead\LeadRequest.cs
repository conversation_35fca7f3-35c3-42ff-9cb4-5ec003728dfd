﻿using System;
using System.Collections.Generic;

namespace Common.Models.Lead
{
    public class LeadRequest
    {
        public string? LeadStatus { get; set; }
        public string? NationalId { get; set; }
        public string? BusinessDomain { get; set; }
        public string PhoneNumber { get; set; } = null!;
        public string CountryPrefix { get; set; } = null!;
        public string PhoneWithPrefix => CountryPrefix + PhoneNumber;
        public string? OwnerFirstName { get; set; }
        public string? OwnerLastName { get; set; }
        public string? OwnerFirstNameAr { get; set; }
        public string? OwnerLastNameAr { get; set; }
        public string? OwnerEmail { get; set; }
        public string? LegalName { get; set; }
        public string? LegalNameAr { get; set; }
        public string? AddressLine { get; set; }
        public string? City { get; set; }
        public string? Governorate { get; set; }
        public string? Area { get; set; }
        public string? Country { get; set; }
        public string? UTM { get; set; }
        public string? CRMLeadId { get; set; }
        public string? TahakomTransactionId { get; set; }
        public string? SalesId { get; set; }
        public string? SalesPartnerId { get; set; }
        public string? Nationality { get; set; }
        public string? Gender { get; set; }
        public string? ReferralChannel { get; set; }
        public DateTimeOffset? DOB { get; set; }
        public bool ElmCheck { get; set; }
        public List<LeadProductRequest> LeadProducts { get; set; } = new List<LeadProductRequest>();
        public LeadDetails? LeadDetails { get; set; }
    }
}
